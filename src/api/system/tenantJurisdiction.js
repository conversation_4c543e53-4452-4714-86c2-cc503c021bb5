import request from "@/utils/request";

// 查询分页
export function page(params) {
  return request({
    url: "/user/tenantJurisdiction",
    method: "get",
    params: params
  });
}

// 查询当前管辖的租户Ids
export function getJurisdictionTenantIds(data) {
  return request({
    url: "/user/tenantJurisdiction/getJurisdictionTenantIds",
    method: "post",
    data: data
  });
}

// 查询当前管辖的租户
export function getJurisdictionTenantList(data) {
  return request({
    url: "/user/tenantJurisdiction/getJurisdictionTenantList",
    method: "post",
    data: data
  });
}

// 添加管辖的租户
export function addJurisdictionTenant(data) {
  return request({
    url: "/user/tenantJurisdiction/addJurisdictionTenant",
    method: "post",
    data: data
  });
}
