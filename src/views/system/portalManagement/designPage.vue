<template>
  <el-drawer :visible.sync="visible" :with-header="false" size="100%">
    <iframe
      :key="timeStr"
      v-if="item.menuId"
      ref="iframe"
      :src="`/edit/index-cn#uid=${item.menuId}`"
      frameborder="0"
      class="iframe"
      @load="loadfrom"
    ></iframe>
  </el-drawer>
</template>

<script>
import { editTableMenuUpdate } from "@/api/system/portalManagement";
import defaultData from "./defaultData.json";
export default {
  name: "DesignPage",
  data() {
    return {
      visible: false,
      item: {},
      timeStr: new Date().getTime(),
      portalDetails: {},
    };
  },
  created() {},
  methods: {
    open(item, portalDetails) {
      this.portalDetails = portalDetails;
      this.item = item;
      this.visible = true;
      const menuContent = item.menuContent ? JSON.parse(item.menuContent) : {};
      const data = {
        id: this.item.menuId,
        attributes: menuContent.config ? menuContent : defaultData,
      };
      console.log("data", data);
      localStorage.removeItem(this.item.menuId);
      localStorage.setItem(this.item.menuId, JSON.stringify(data));
      window.addEventListener("message", this.delEvent);
    },
    loadfrom(val) {},
    delEvent(e) {
      const that = this;
      if (e.data.type == "saveData") {
        console.log(e.data);
        that.saveData(e.data.data.data, () => {
          e.source.postMessage(
            {
              type: "saveLoad",
            },
            e.origin
          );
        });
      } else if (e.data.type == "onPreview") {
         window.open(
            `/landing/${this.portalDetails.eipCode}/${this.item.menuId}`
          );
      } else if (e.data.type == "closeWindow") {
        that.close();
      }
    },
    close() {
      const that = this;
      this.$confirm("确定关闭窗口?", "警告", {
        confirmButtonText: "关闭",
        cancelButtonText: "取消",
        type: "warning",
      }).then(function () {
        localStorage.removeItem(that.item.menuId);
        window.removeEventListener("message", that.delEvent);
        that.$emit("closeDesign");
      });
    },
    saveData(data, callBacl) {
      editTableMenuUpdate({
        ...this.item,
        menuContent: JSON.stringify(data),
      }).then((response) => {
        if (!response.success) {
          this.$message.error(response.message);
        } else {
          this.$emit("handleQuery");
          this.$message.success("保存成功!");
          callBacl();
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.iframe {
  width: 100%;
  height: 100%;
}
</style>