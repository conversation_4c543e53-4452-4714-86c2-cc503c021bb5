import request from '@/utils/request'

export function saveRole(data) {
  return request({
    method: "post",
    url: "/sanquan/role/save",
    data
  });
}
export function saveUser(data) {
  return request({
    method: "post",
    url: "/sanquan/user/save",
    data
  });
}

export function findUser(params) {
  return request({
    method: "get",
    url: "/sanquan/user/findOne",
    params
  });
}

export function deleteUser(params) {
  return request({
    method: "get",
    url: "/sanquan/user/delete",
    params
  });
}
export function findRole(params) {
  return request({
    method: "get",
    url: "/sanquan/role/findOne",
    params
  });
}

export function findAllProduct(params) {
  return request({
    method: "get",
    url: "/sanquan/product/findProductList",
    params
  });
}

export function findMenuById(params) {
  return request({
    method: "get",
    url: "/sanquan/core/menu/findByRoleId",
    params
  });
}
export function deletedRole(params) {
  return request({
    method: "get",
    url: "/sanquan/role/delete",
    params
  });
}


export function menuTrees(params) {
  return request({
    method: "get",
    url: "/sanquan/core/menu/findTreeList",
    params
  });
}

export function rolesList(params) {
  return request({
    method: "get",
    url: "/sanquan/role/findList",
    params
  });
}

export function saveUserRole(data) {
  return request({
    method: "post",
    url: "/sanquan/user/grant",
    data
  });
}


export function findByUserId(params) {
  return request({
    method: "get",
    url: "/sanquan/role/findByUserId",
    params
  });
}

// 查询用户列表
export function findUserList(params) {
  return request({
    method: "get",
    url: "/sanquan/user/findList",
    params
  });
}

