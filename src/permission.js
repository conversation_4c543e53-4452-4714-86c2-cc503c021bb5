import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken } from '@/utils/auth'

NProgress.configure({ showSpinner: false })

const whiteList = ['/login', '/loginsso', '/redirect', '/bind', '/ssoRedirect',
  '/register', '/LTIndex/**', '/show/homePage', '/show/newLogin', '/sw', '/sw/',
  '/sw/**', '/sw/5gpn', '/sw/opportunityDetailsNew/1/**']

router.beforeEach((to, from, next) => {
  // 加载进度条 开始
  NProgress.start()
  // 存在在白名单
  // 没有token
  let rootPath = '/' + to.path.split('/')[1] + '/**' //多级白名单
  if (whiteList.indexOf(to.path) !== -1 || whiteList.indexOf(rootPath) !== -1) {
    if (to.path === '/show/homePage') {
      if (store.getters.token) {
        if (store.getters.roles.length === 0) {
          next()

          // 判断当前用户是否已拉取完user_info信息
          //store
            // .dispatch('GetInfo')
            // .then(() => {
            //   const { path } = to
            //   const pathStr = path.split('/')[1]
            //   store
            //     .dispatch('GenerateRoutes', {
            //       permissionScope: pathStr === '' ? 'portal' : pathStr
            //     })
            //     .then(accessRoutes => {
            //       // 根据roles权限生成可访问的路由表
            //       router.addRoutes(accessRoutes) // 动态添加可访问路由表
            //       next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
            //     })
            // })
            // .catch(err => {
            //   store.dispatch('LogOut').then(() => {
            //     next(`/show/newLogin?redirect=${to.fullPath}`)
            //   })
            // })
        } else {
          next()
        }
      } else {
        next()
      }

    } else {
      next()
    }
    // 不存在在白名单
  } else {
    if (to.path === '/login') {
      next({ path: '/system/base/home' })
      NProgress.done()
    } else {
      if (store.getters.roles.length === 0) {
        next()
        // 判断当前用户是否已拉取完user_info信息
        // store
        //   .dispatch('GetInfo')
        //   .then(() => {
        //     const { path } = to
        //     const pathStr = path.split('/')[1]
        //     store
        //       .dispatch('GenerateRoutes', {
        //         permissionScope: pathStr === '' ? 'portal' : pathStr
        //       })
        //       .then(accessRoutes => {
        //         // 根据roles权限生成可访问的路由表
        //         router.addRoutes(accessRoutes) // 动态添加可访问路由表
        //         next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
        //       })
        //   })
        //   .catch(err => {
        //     store.dispatch('LogOut').then(() => {
        //       if (from.path.indexOf('/show') !== -1) {
        //         next(`/show/newLogin?redirect=${to.fullPath}`)
        //       } else {
        //         next(`/login?redirect=${to.fullPath}`)
        //       }
        //       // Message.error(err);
        //       // next({ path: "/" });
        //       // next(`/login`)
        //     })
        //   })
      } else {
        next()
      }
    }
  }

})

router.afterEach(() => {
  NProgress.done()
})
