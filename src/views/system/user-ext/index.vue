<template>
  <div class="app-container">
    <el-card shadow="never">
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        :inline="true"
      >
        <el-form-item label="账号名称" prop="staffName">
          <el-input
            v-model="queryParams.staffName"
            placeholder="请输入账号名称"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="人员编码" prop="staffCode">
          <el-input
            v-model="queryParams.staffCode"
            placeholder="请输入人员编码"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="昵称" prop="agentNickname">
          <el-input
            v-model="queryParams.agentNickname"
            placeholder="请输入昵称"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
          >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="dataList">
        <el-table-column label="账号名称" align="left" prop="staffName" :show-overflow-tooltip="true" />
        <el-table-column label="租户名称" align="left" prop="tenantName" :show-overflow-tooltip="true" />
        <el-table-column label="用户端昵称" align="left" prop="agentNickname" :show-overflow-tooltip="true" />
        <el-table-column label="最大接待量" align="left" prop="chatCount" :show-overflow-tooltip="true" />
        <el-table-column label="技能组" align="left" prop="skillId" :show-overflow-tooltip="true" />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="260"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['sys:staffExt:edit']"
              :loading="
                reloadId === scope.row.staffExtId && reloadType === 'edit'
              "
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['sys:staffExt:delete']"
              :loading="
                reloadId === scope.row.staffExtId && reloadType === 'remove'
              "
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改参数配置对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="open"
        width="1000px"
        append-to-body
        :close-on-press-escape="false"
        @close="cancel"
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="150px">
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="所属租户">
                <el-input
                  v-model="form.tenantName"
                  placeholder="所属租户"
                  maxlength="64"
                  disabled
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="用户端昵称" prop="agentNickname">
                <el-input
                  v-model="form.agentNickname"
                  placeholder="请输入用户端昵称"
                  maxlength="64"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="技能编码" prop="skillId">
                <el-input
                  v-model="form.skillId"
                  placeholder="请输入技能编码"
                  maxlength="64"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="最大接待量" prop="chatCount">
                <el-input-number
                  v-model="form.chatCount"
                  controls-position="right"
                  :min="0"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="saveLoading"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import { page, getById, update, del } from "@/api/system/userExt";
export default {
  name: "UserExtend",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 参数表格数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否有效数据字典
      effectiveStatusOptions: [],
      // 状态数据字典
      orgKindOptions: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        staffName: undefined,
        staffCode: undefined,
        agentNickname: undefined,
        deleteFlag: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        agentNickname: [
          { required: true, message: "用户端昵称不能为空", trigger: "blur" },
        ],
        skillId: [
          { required: true, message: "技能编码不能为空", trigger: "blur" },
        ],
        chatCount: [
          { required: true, message: "最大接待量不能为空", trigger: "blur" },
        ]
      },
      reloadId: undefined,
      reloadType: undefined,
      saveLoading: false,
      defaultOrgId: this.$store.getters.orgId,
      loadTenantId: undefined,
    };
  },
  created() {
    this.getList();
    this.getDicts("effective_status").then((response) => {
      this.effectiveStatusOptions = response.data;
    });
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true;
      page({...this.addDateRange({}, this.dateRange).params, ...this.queryParams}).then(
        (response) => {
          this.dataList = response.data.records;
          this.total = response.data.total;
          this.loading = false;
        }
      );
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reloadId = undefined;
      this.reloadType = undefined;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        tenantName: undefined,
        agentNickname: undefined,
        skillId: undefined,
        chatCount: undefined,
      };
      this.resetForm("queryForm");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.queryParams.deleteFlag = undefined
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const staffExtId = row.staffExtId;
      this.reloadId = staffExtId;
      this.reloadType = "edit";
      getById(staffExtId).then((response) => {
        if (response.data) {
          this.form = response.data;
          this.open = true;
          this.title = "修改租户";
        } else {
          this.$message.error("数据异常！");
        }
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.saveLoading = true;
          update(this.form).then((response) => {
            if (!response.success) {
              this.$message.error(response.message);
              this.saveLoading = false;
            } else {
              this.msgSuccess("修改成功");
              this.open = false;
              this.saveLoading = false;
              this.getList();
            }
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const staffExtId = row.staffExtId;
      this.reloadId = staffExtId;
      this.reloadType = "remove";
      this.$confirm(
        '是否确认删除"' + row.staffName + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return del(staffExtId);
        })
        .then(() => {
          this.reloadId = undefined;
          this.reloadType = undefined;
          this.getList();
          this.msgSuccess("删除成功");
        })
        .catch(() => {
          this.reloadId = undefined;
        });
    },
  },
};
</script>
