import request from "@/utils/request";

// 根据主键获取标签分类信息(tree型结构包含本身)
export function findTreeById(id) {
  return request({
    url: "/sanquan/labelClassify/findTreeById/" + id,
    method: "get",
  });
}

// 根据主键获取标签分类信息
export function findAllList() {
  return request({
    url: "/sanquan/labelClassify/findAllList",
    method: "get",
  });
}

/**
 * 获取标签分类树型结构集合
 * @param params  标签分类名称
 * @returns {*}
 */
export function getAllTree(params) {
  return request({
    url: "/sanquan/labelClassify/findAllTree",
    method: "get",
    params
  });
}

/**
 * 根据主键获取标签分类信息
 * @param id  标签分类主键
 * @returns {*}
 */
export function findById(id) {
  return request({
    url: "/sanquan/labelClassify/findById/" + id,
    method: "get",
  });
}

/**
 * 表格数据
 * @param params  检索条件
 * @returns {*}
 */
export function findPageListByParentId(params) {
  return request({
    method: "get",
    url: "/sanquan/labelClassify/findPageListByParentId",
    params,
  });
}

/**
 * 根据主键删除标签分类(下级分类一块删除)
 * @param id 分类主键
 * @returns {*}
 */
export function deleteById(id) {
  return request({
    url: "/sanquan/labelClassify/deleteById/" + id,
    method: "post",
  });
}

/**
 * 添加或修改标签分类
 * @param data  标签分类信息
 * @returns {*}
 */
export function addOrUpdate(data) {
  return request({
    method: "post",
    url: "/sanquan/labelClassify/addOrUpdate",
    data,
  });
}

export function checkCode(params) {
  return request({
    method: "post",
    url: "/sanquan/labelClassify/checkCode",
    params,
  });
}

export function checkName(params) {
  return request({
    method: "post",
    url: "/sanquan/labelClassify/checkName",
    params,
  });
}

export default { getAllTree, findById, addOrUpdate, findPageListByParentId, deleteById, checkCode, checkName }
