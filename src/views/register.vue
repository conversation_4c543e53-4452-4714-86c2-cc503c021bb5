<template>
  <div class="app-container">
    <el-card shadow="never">
      <div class="main">
        <div class="title-contianer">
          <span class="title">注册账号</span>
        </div>
        <el-form
          :model="registForm"
          :rules="rules"
          ref="registForm"
          label-width="0px"
          class="registForm"
        >
          <!-- <el-form-item label="" prop="phone">
            <el-input
              type="input"
              placeholder="手机号码"
              v-model="registForm.phone"
            ></el-input>
          </el-form-item>
          <el-form-item label="" prop="smsCode">
            <div style="display: flex; justify-content: space-between">
              <el-input
                type="input"
                placeholder="短信验证码"
                v-model="registForm.smsCode"
              ></el-input>
              <el-button
                type="primary"
                style="width: 40%; margin-left: 10px"
                plain
                :disabled="smsTime > 0"
                @click="sendSms()"
                >{{
                  smsTime <= 0 ? "获取验证码" : `${smsTime}s 后重新获取`
                }}</el-button
              >
            </div>
          </el-form-item> -->
          <el-form-item label="" prop="loginName">
            <el-input
              type="input"
              placeholder="用户名"
              v-model="registForm.loginName"
            ></el-input>
          </el-form-item>
          <el-form-item label="" prop="passwd">
            <el-input
              type="password"
              placeholder="密码请设置8~20个字符"
              v-model="registForm.passwd"
            ></el-input>
          </el-form-item>
          <el-form-item label="" prop="rePasswd">
            <el-input
              type="password"
              placeholder="再次输入密码"
              v-model="registForm.rePasswd"
            ></el-input>
          </el-form-item>
          <el-form-item label="" prop="cellphone">
            <el-input
                type="input"
                placeholder="请输入手机号"
                v-model="registForm.cellphone"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-cascader placeholder="请选择区域" size="large" :options="options" v-model="selectedOptions"
                         @change="handleChange" style="width: 100%;"
            />
          </el-form-item>
          <div v-if="errorMsg" style="margin: -55px -71px 10px;float: right">
            <span class="error-msg">{{ errorMsg }}</span>
          </div>
          <el-form-item label="" prop="captcha">
            <div style="display: flex; justify-content: space-between">
              <el-input
                type="input"
                placeholder="图形验证码"
                v-model="registForm.captcha"
              ></el-input>
              <img
                v-if="captchaUrl"
                class="captcha"
                @click="refreshCaptcha"
                alt="图形验证码"
                :src="captchaUrl"
              />
            </div>
          </el-form-item>
          <el-form-item class="submit-container">
            <el-button
              :loading="loading"
              :disabled="!registForm.agreeContract"
              type="primary"
              style="width: 100%"
              @click="register"
            >注册
            </el-button
            >
            <!-- <el-button
              type="primary"
              @click="registForm.callback = registForm.callback == 0 ? 1 : 0"
              >新增回复</el-button
            > -->
            <el-checkbox v-model="registForm.agreeContract"
            >我已经阅读并同意
            </el-checkbox
            >
            <a class="service-terms" href="/LTIndex/service-terms" target="_blank">《数字工厂服务条款》</a>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script>
import {sendSms, register} from "/src/api/register";
import doEncrypt from "@/utils/crypto";
import { provinceAndCityData, CodeToText } from 'element-china-area-data'

export default {
  data() {
    var isChecked = (rule, value, callback) => {
      if (value === false) {
        // callback(new Error('请同意'));
      }
    };
    var isSamePd = (rule, value, callback) => {
      if (value !== this.registForm.passwd) {
        callback(new Error("两次密码输入不同"));
      } else {
        callback();
      }
    };
    return {
      errorMsg: "",
      options: provinceAndCityData, // 1.省市不带‘全部’的二级联动
      selectedOptions:['370000', '370100'],
      // 注册按钮遮罩层
      loading: false,
      // 验证码校验key
      token: 100000 + Math.floor(Math.random() * (999999 + 1 - 100000)),
      // 获取验证码api
      captchaApi: process.env.VUE_APP_BASE_API + "/auth/captcha",
      // 获取验证码url
      captchaUrl: undefined,
      smsTime: 0,
      registForm: {
        phone: "",
        cellphone: "",
        captcha: "",
        smsCode: "",
        loginName: "",
        passwd: "",
        rePasswd: "",
        agreeContract: "",
        staffKind: "customer",
        orgId: "370100",
      },
      rules: {
        phone: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
          {required: true, message: "请输入手机号码", trigger: "blur"},
        ],
        captcha: [{required: true, message: "请输入验证码", trigger: "blur"}],
        smsCode: [
          {required: true, message: "请输入短信验证码", trigger: "blur"},
        ],
        loginName: [
          {required: true, message: "请输入用户名", trigger: "blur"},
        ],
        passwd: [
          {required: true, message: "请输入密码", trigger: "blur"},
          {
            pattern: /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9]).{8,}$/,
            message: "密码必须包括字母大小写和数字，且长度大于8",
            trigger: "blur",
          },
        ],
        rePasswd: [
          {required: true, message: "请输入密码", trigger: "blur"},
          {validator: isSamePd, trigger: "blur"},
        ],
        cellphone: [
          { required: true, message: "手机号码不能为空", trigger: "blur" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
      },
      provinceOptions: [],
      cityOptions: [],
    };
  },
  created() {
    this.fetchAnswerRecords();
    this.refreshCaptcha();
  },
  methods: {
    // 刷新验证码
    refreshCaptcha() {
      this.captchaUrl = `${this.captchaApi}?token=${this.token}&t=${Math.random()}`;
      this.registForm.captcha = "";
    },
    register() {
      if (this.selectedOptions.length <=0 ){
        this.errorMsg = "请选择区域"
      } else {
        this.errorMsg = ""
      }
      this.$refs.registForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.registForm.staffName = this.registForm.loginName
          register(this.registForm, {
            "token": this.token,
            "captcha": this.registForm.captcha,
          }).then((res) => {
            if (res.code == 1) {
              this.$message({
                message: "注册成功",
                type: "success",
              });
              this.$router.push("/index");
            } else {
              this.$message.error(res.message);
              this.refreshCaptcha();
            }
            this.loading = false;
          }).catch((err) => {
            this.loading = false;
            this.refreshCaptcha();
          });
        } else {
          return false;
        }
      });
    },
    submitForm(formName) {
      if (formName === "registForm") {
        this.$refs[formName].validate((valid) => {
          if (valid) {

          } else {
            return false;
          }
        });
      }
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    fetchAnswerRecords() {
    },
    sendSms() {
      this.$refs.registForm.validateField("phone", (valid) => {
        if (!valid) {
          this.smsLoading = true;
          sendSms({
            authkey: doEncrypt(`0@bVS46ElU@${this.registForm.phone}`),
          }).then((r) => {
            this.smsLoading = true;
            if (r.success) {
              if (r.data.resultcode === 0) {
                this.msgSuccess("验证码发送成功！");
                const TIME_COUNT = 60;
                if (!this.timer) {
                  this.smsTime = TIME_COUNT;
                  this.show = false;
                  this.timer = setInterval(() => {
                    if (this.smsTime > 0 && this.smsTime <= TIME_COUNT) {
                      this.smsTime--;
                    } else {
                      this.show = true;
                      clearInterval(this.timer);
                      this.timer = null;
                    }
                  }, 1000);
                }
              } else if (r.data.resultcode === 4) {
                this.$message.error(r.data.resultmsg);
              } else if (r.data.resultcode === 6) {
                this.$message.error(r.data.resultmsg);
              }
            } else {
              this.msgError(r.message);
            }
          });
        }
      });
    },
    handleChange (value) {
      // 地市名称
      let arrText = []
      // 地市编码
      let arrCode = []
      for (let i = 0; i < this.selectedOptions.length; i++) {
        const selectedOption = this.selectedOptions[i]
        arrText.push(CodeToText[selectedOption])
        arrCode.push(selectedOption)
      }
      if (arrCode.length >= 2) {
        this.registForm.orgId = arrCode[1]
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.main {
  width: 350px;
  margin: 30px auto;
}

.title-contianer {
  text-align: center;
  padding: 30px 0;

  .title {
    font-size: 30px;
    display: block;
    color: #494c52;
    // color: green;
    font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
  }

  .title-detail {
  }
}

.el-form {
  .el-form-item {
    ::v-deep .el-form-item__error {
      top: 21%;
      left: 103% !important;
      left: unset;
      // display: inline-block;
      white-space: nowrap;
    }

    .el-input {
      ::v-deep .el-input__inner {
        border-radius: 0px;
        height: 40px;
      }
    }

    .el-button {
      border-radius: 0px;
    }

    .captcha {
      width: 40%;
      margin-left: 10px;
      cursor: pointer;
    }
  }

  .submit-container {
    // padding: 10px 0;
  }

  .formSelect {
    display: flex;
    justify-content: space-between;

    .el-select {
      width: 180px;
    }
  }
  .error-msg {
    color: #ff4949;
    font-size: 12px;
    padding-top: 4px;
  }
}
</style>
