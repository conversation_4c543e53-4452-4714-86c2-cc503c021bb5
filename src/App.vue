<template>
  <div id="app" style="width: 100%; height: 100%">
    <div style="width: 100%; height: 100%">
      <router-view/>
    </div>
  </div>
</template>

<script>
import { checkLoginSes, getCurrentUser, getTokenBySession } from '@/api/sw/analysis/analysis'

export default {
  name: 'App',
  data() {
    return {
      defaultStyle: {
        width: '100%',
        height: '100%'
      },
      default_width: 0 // 窗口宽度
    }
  },
  mounted() {

  },
  created() {
    this.initToken()
  },
  methods: {
    initToken() {

    },
    /* 动态设置html字体大小 */
    setHtmlFontSize() {
      this.default_width = window.innerWidth
      document.querySelector('html').style.fontSize = this.default_width / 16 + 'px'
    },
    initPage() {
      this.default_width = window.innerWidth
      this.setHtmlFontSize()
      // 监听窗口变化
      window.addEventListener('resize', this.setHtmlFontSize)
      // let authSessionId = this.$route.query.authSessionId
      // if (authSessionId) {
      //   const baseURL = process.env.VUE_APP_BASE_API
      //   window.location.href = window.location.href.split('/#/')[0] + baseURL + '/auth/sso/cloud/token?authSessionId=' + authSessionId
      // }
    }
  }
}
</script>

<style lang="scss">
html,
body {
  width: 100%;
  height: 100%;
  margin: 0px;
  padding: 0px;
}

.action-buttons {

  .action-button {
    padding: 0;
    font-size: 0.125rem;
    min-width: 0.125rem;
    background-color: transparent;
    border: none;
    cursor: pointer;
    color: #3377FF;
  }

  .action-button-disabled {
    padding: 0;
    font-size: 0.125rem;
    min-width: 0.125rem;
    background-color: transparent;
    border: none;
    color: #ABABAB;
  }

  .separator {
    margin: 0 0.04rem;
    position: relative;
    min-height: 18px;
  }

  .separator::before {
    content: "";
    position: absolute;
    top: 2px;
    bottom: 0;
    left: 0;
    width: 0.012rem;
    height: 0.13rem;
    background-color: #D9D9D9;
  }
}

.hide-bar::-webkit-scrollbar {
  display: none;
}

::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

::-webkit-scrollbar-thumb {
  background: #b1b1b1;
  border-radius: 20px;
}

::-webkit-scrollbar-track-piece {
  background: #fff;
}

#app {
  position: absolute;
  left: 0px;
  right: 0px;
  top: 0px;
  bottom: 0px;
  /* margin: auto; */
  margin: 0;
}

.portal-body {
  font-size: 120px;
  padding: 0 20px;
  background: #ffffff;
  margin-bottom: 50px;
}

.portal-body .el-form-item__label {
  font-size: 0.125rem;
}

.portal-body .el-button {
  font-size: 0.125rem;
  min-width: 0.6rem;
  padding: 0.09rem;
  font-family: PingFangSC-Regular !important;
}

.portal-body .el-table {
  //font-size: 0.117rem;
  font-family: PingFangSC-Regular;
  font-size: 0.125rem;
  color: rgba(0, 0, 0, 0.88);
  letter-spacing: 0;
  line-height: 22px;
  font-weight: 400;
}

.portal-body .el-table .el-table__header-wrapper th,
.el-table .el-table__fixed-header-wrapper th {
  font-size: 0.125rem;
  background-color: #f0f2f5;
}

.portal-body .el-checkbox {
  font-size: 0.125rem;
}

.portal-body .list-table .el-button--text {
  width: 0.3rem;
}

.portal-body .list-table .el-pagination span:not([class*="suffix"]),
.el-pagination button {
  font-size: 0.125rem;
}

.portal-body .list-table .el-pagination .btn-prev .el-icon,
.el-pagination .btn-next .el-icon {
  font-size: 0.125rem;
}

.portal-body .list-table .el-pager .number {
  font-size: 0.125rem;
}

.portal-body .list-table .el-pager .active {
  font-size: 0.125rem;
}

.portal-body .el-radio {
  font-size: 0.1166rem;
}

.portal-body .el-radio .el-radio__label {
  font-size: 0.125rem;
  padding-left: 0.083rem;
}

.portal-body .el-radio .el-radio__inner {
  width: 0.116rem;
  height: 0.1166rem;
}

.portal-body .el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 1.83rem;
}

.portal-body .el-input,
.el-textarea__inner {
  font-size: 0.125rem;
}

.portal-body .el-input .el-input__inner {
  height: 0.3rem;
  line-height: 0.3rem;
}

.portal-body .el-input .el-input__icon {
  line-height: 0.3rem;
}

.portal-body .el-select .el-input .el-select__caret {
  font-size: 0.115rem;
}

.portal-body .el-input__suffix {
  right: 0.005rem;
}

.portal-body .el-radio-button--medium .el-radio-button__inner {
  padding: 0.05rem 0.055rem;
  font-size: 0.125rem;
  border-radius: 0;
}

.portal-body .el-descriptions .el-descriptions--mini {
  font-size: 0.125rem !important;
}

.portal-body .el-message {
  font-size: 0.2rem;
}

.el-message {
  font-size: 24px !important;
  padding: 0.083rem !important;
}

.tab-tree {
  font-size: 0.125rem;
}

.el-tree {
  font-family: PingFangSC-Regular;
  font-size: 0.125rem;
  color: rgba(0, 0, 0, 0.88) !important;
}

.el-tree-node__content {
  margin: 4px 0px !important;
}

// 重置字体样式
.el-input__inner, .el-message-box__content, .el-dialog__body, .el-tree-node__label,
.el-select-dropdown__item, .el-checkbox__label, .el-form-item__content, .el-range-input, .el-descriptions,
.el-select-dropdown__empty, .el-transfer-panel .el-transfer-panel__empty, .el-tabs__item {
  font-size: 0.125rem !important;
  font-family: PingFangSC-Regular !important;
}

.el-dialog__title {
  font-size: 0.135rem !important;
  font-family: PingFangSC-Regular !important;
  font-weight: 600;
}

.el-menu-item, .el-page-header__title, .el-page-header__content, .el-message-box__title, .el-submenu__title {
  font-size: 0.135rem !important;
  font-family: PingFangSC-Regular !important;
}

.el-pagination .el-select .el-input .el-input__inner {
  height: auto;
  line-height: inherit;
}
.el-pagination .el-pagination__jump .el-input .el-input__inner {
  height: inherit;
  line-height: normal;
}
.el-pagination .el-pagination__jump {
  margin-left: 14px;
  margin-right: 14px;
  font-size: inherit !important;
}
.el-pagination .el-pagination__total {
  font-size: inherit !important;
}
.el-pagination span{
  vertical-align: inherit !important;
}

</style>
