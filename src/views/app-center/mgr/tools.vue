<template>
  <div class="app-container">
    <el-card
      v-loading="loading"
      class="app-card"
      shadow="never"
    >
      <div slot="header">
        <span class="card-title">
          <span>小工具管理 </span>
          <span>
            <el-button class="exec" type="text" @click="refresh"
            >刷新<i class="el-icon-refresh"></i
            ></el-button>
            <el-button class="exec" type="text" @click="toolsMar = true"
            >小工具管理<i class="el-icon-d-arrow-right"></i
            ></el-button>
          </span>
        </span>
      </div>
      <div class="tool" v-if="list[0].apps.length > 0">
        <div
          v-for="item in list[0].apps"
          :key="item.id"
          class="tool-icon"
          :style="`background-color: ${item.remark}20;`"
        >
          <svg-icon
            slot="prefix"
            :icon-class="item.logoTemp"
            class="el-input__icon"
            :style="`height: 30px;width: 30px; color: ${item.remark}`"
          />
          <span>{{ item.name }} </span>
        </div>
        <div
          style="background-color: #9fa2a520;"
          class="tool-icon"
          @click="openCenter(list[0])"
          v-hasPermi="['system:tools:add']"
        >
          <i
            class="icon iconfont icon-xinzeng"
            style="color: #9fa2a5;font-size: 30px;"
          ></i>
        </div>
      </div>
      <el-empty
        class="app-empty"
        v-if="list[0].apps.length <= 0"
        :image-size="200"
      >
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="openCenter(list[0])"
          v-hasPermi="['system:tools:add']"
        >添加应用
        </el-button
        >
      </el-empty>
    </el-card>
    <el-drawer :with-header="false" :visible.sync="toolsMar" size="70%">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>小工具管理</span>
          <el-button
            style="float: right; padding: 3px 0"
            type="text"
            icon="el-icon-plus"
            @click="openCenter(list[0])"
            v-hasPermi="['system:tools:add']"
          >新增应用
          </el-button
          >
        </div>
        <el-table :data="listAll[0].apps" style="width: 100%">
          <el-table-column label="序号" type="index" width="50">
          </el-table-column>
          <el-table-column property="logoTemp" width="100" label="图标">
            <template slot-scope="scope">
              <span
                class="tool-icon"
                :style="`background-color: ${scope.row.remark}20;`"
              >
                <svg-icon
                  slot="prefix"
                  :icon-class="scope.row.logoTemp"
                  class="el-input__icon"
                  :style="
                    `height: 30px;width: 30px; color: ${scope.row.remark}`
                  "
                />
              </span>
            </template>
          </el-table-column>
          <el-table-column
            property="name"
            label="应用名称"
            width="150"
          ></el-table-column>
          <el-table-column
            property="sort"
            label="排序"
            width="50"
          ></el-table-column>
          <el-table-column
            property="url"
            label="url"
            width="150"
          ></el-table-column>
          <el-table-column property="status" label="状态" width="80">
            <template slot-scope="scope">
              <el-switch
                :value="scope.row.status"
                @change="handleAppStatusChange(scope.row)"
              >
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column
            property="createTime"
            label="创建时间"
          ></el-table-column>
          <el-table-column property="remark" label="色值"></el-table-column>
          <el-table-column label="操作" width="130">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="editCenter(scope.row)" v-hasPermi="['system:tools:edit']"
              >编辑
              </el-button
              >
              <el-button @click="delCenter(scope.row)" type="text" size="small" v-hasPermi="['system:tools:remove']"
              >删除
              </el-button
              >
              <el-button
                @click="authCenter(scope.row)"
                type="text"
                size="small"
                v-hasPermi="['system:tools:auth']"
              >授权
              </el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-drawer>
    <el-drawer
      :title="centerTitle"
      :before-close="closeCenter"
      :visible.sync="centerVisble"
      ref="drawer"
      size="50%"
      destroy-on-close
    >
      <div class="body">
        <el-form :model="centerForm" :rules="centerRules" ref="centerForm">
          <el-form-item label="名称" label-width="80px" prop="name">
            <el-input v-model="centerForm.name" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="系统地址" label-width="80px" prop="url">
            <el-input v-model="centerForm.url" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="图标" label-width="80px" prop="logoTemp">
            <el-popover
              placement="bottom-start"
              width="460"
              trigger="click"
              @show="$refs['iconSelect'].reset()"
            >
              <IconSelect ref="iconSelect" @selected="selected"/>
              <el-input
                slot="reference"
                v-model="centerForm.logoTemp"
                placeholder="点击选择图标"
                readonly
              >
                <svg-icon
                  v-if="centerForm.logoTemp"
                  slot="prefix"
                  :icon-class="centerForm.logoTemp"
                  class="el-input__icon"
                  style="height: 32px;width: 16px;"
                />
                <i v-else slot="prefix" class="el-icon-search el-input__icon"/>
              </el-input>
            </el-popover>
          </el-form-item>
          <el-form-item label="排序" label-width="80px" prop="sort">
            <el-input-number
              :min="1"
              v-model="centerForm.sort"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="颜色" label-width="80px" prop="remark">
            <el-color-picker
              v-model="centerForm.remark"
              :predefine="[
                '#409EFF',
                '#1890ff',
                '#304156',
                '#212121',
                '#11a983',
                '#13c2c2',
                '#6959CD',
                '#f5222d'
              ]"
              class="theme-picker"
              popper-class="theme-picker-dropdown"
            />
          </el-form-item>
        </el-form>
        <div class="foot">
          <el-button @click="closeCenter" icon="el-icon-close">取 消</el-button>
          <el-button
            type="primary"
            icon="icon iconfont icon-save"
            @click="centerTitle == '新增应用' ? saveApp() : updApp()"
            :loading="centerloading"
          >{{ centerloading ? "提交中 ..." : "确 定" }}
          </el-button
          >
        </div>
      </div>
    </el-drawer>
    <el-drawer
      title="租户中心"
      :visible.sync="authVisible"
      size="50%"
    >
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        style="padding: 10px"
      >
        <el-form-item label="租户名称" prop="tenantName">
          <el-input
            v-model="queryParams.tenantName"
            placeholder="请输入租户名称"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleTenantQuery"
          />
        </el-form-item>
        <el-form-item label="租户登录名" prop="tenantLoginName">
          <el-input
            v-model="queryParams.tenantLoginName"
            placeholder="请输入租户登录名"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleTenantQuery"
          />
        </el-form-item>
        <el-form-item label="租户管理员" prop="displayName">
          <el-input
            v-model="queryParams.displayName"
            placeholder="请输入租户管理员"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleTenantQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleTenantQuery"
          >搜索
          </el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetTenantQuery"
          >重置
          </el-button
          >
        </el-form-item>
      </el-form>
      <el-table v-loading="authLoading" :data="tenantList" style="padding: 10px"
                @selection-change="selectTenantApp" ref="multipleTable">
        <el-table-column type="selection" width="55"/>
        <el-table-column label="租户名称" align="left" prop="tenantName" :show-overflow-tooltip="true" width="80px"/>
        <el-table-column label="租户登录名" align="left" prop="tenantLoginName" :show-overflow-tooltip="true"/>
        <el-table-column label="租户管理员" align="left" prop="displayName" :show-overflow-tooltip="true"/>
        <el-table-column label="有效截止时间" align="left" prop="effectiveDate" :show-overflow-tooltip="true"/>
        <el-table-column label="租户状态" align="left" prop="tenantStatus" :formatter="effectiveStatusFormat"
                         :show-overflow-tooltip="true"/>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getTenantList"
      />
      <div class="demo-drawer__footer">
        <el-button type="primary" @click="addTenantApp">确 定</el-button>
        <el-button @click="cancelAuth">取 消</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  list,
  listAll,
  addCenter,
  deleteCenter,
  updCenter, authApp, selectTenantIds
} from "@/api/apps/app";
import IconSelect from "@/components/IconSelect";
import {page} from "@/api/system/tenant";

export default {
  name: "apps",
  components: {IconSelect},
  data() {
    return {
      list: [
        {
          apps: []
        }
      ],
      loading: true,
      toolsMar: false,
      allloading: false,
      listAll: [
        {
          apps: []
        }
      ],
      // 以下下为应用的
      centerVisble: false,
      centerloading: false,
      centerTitle: "新增应用",
      centerForm: {
        sort: 1,
        logoTemp: ""
      },
      centerRules: {
        name: [
          {required: true, message: "请输入名称", trigger: "blur"},
          {min: 1, max: 64, message: "最大输入64个字符", trigger: "blur"}
        ],
        url: [
          {required: true, message: "请输入访问地址", trigger: "blur"},
          {min: 1, max: 255, message: "最大输入255个字符", trigger: "blur"}
        ],
        remark: [
          {min: 1, max: 255, message: "最大输入255个字符", trigger: "blur"}
        ]
      },
      imageUrl: "",
      authVisible: false,
      authLoading: true,
      tenantList: [],
      // 是否有效数据字典
      effectiveStatusOptions: [],
      // 总条数
      total: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenantName: undefined,
        tenantLoginName: undefined,
        displayName: undefined,
      },
      selectTenantIds: [],
      appId: undefined,
    };
  },
  created() {
    this.refresh();
    this.getTenantList();
  },
  methods: {
    refresh() {
      this.getList();
      this.all();
    },
    getList() {
      this.loading = true;
      list(1).then(res => {
        this.list = res.data;
        this.loading = false;
      });
    },
    all() {
      this.allloading = true;
      listAll(1).then(res => {
        this.listAll = res.data;
        this.allloading = false;
      });
    },
    // 以下下为应用的
    openCenter(row) {
      this.centerVisble = true;
      this.centerloading = false;
      this.centerTitle = "新增应用";
      this.centerForm = {
        sort: 1,
        categoryId: row.id
      };
    },
    selected(name) {
      this.centerForm = {
        ...this.centerForm,
        logoTemp: name
      };
    },
    closeCenter() {
      this.centerVisble = false;
      this.centerloading = false;
      this.centerTitle = "新增应用";
      this.imageUrl = "";
      this.centerForm = {
        sort: 1
      };
    },
    saveApp() {
      this.$refs["centerForm"].validate(valid => {
        if (valid) {
          this.centerloading = true;
          const centerForm = this.centerForm;
          addCenter(this.centerForm).then(r => {
            this.centerloading = false;
            if (r.success) {
              this.$message({
                message: "添加成功！",
                type: "success"
              });
              this.closeCenter();
              this.all(centerForm.categoryId);
              this.getList(centerForm.categoryId);
            } else {
              this.$message.error("添加失败！");
            }
          });
        } else {
          return false;
        }
      });
    },
    editCenter(row) {
      this.centerForm = {
        ...row
      };
      this.imageUrl = row.logoTemp;
      this.centerVisble = true;
      this.centerloading = false;
      this.centerTitle = "修改应用";
    },
    updApp() {
      this.$refs["centerForm"].validate(valid => {
        if (valid) {
          this.centerloading = true;
          const centerForm = this.centerForm;
          updCenter(this.centerForm).then(r => {
            this.centerloading = false;
            if (r.success) {
              this.$message({
                message: "修改成功！",
                type: "success"
              });
              this.closeCenter();
              this.all(centerForm.categoryId);
              this.getList(centerForm.categoryId);
            } else {
              this.$message.error("修改失败！");
            }
          });
        } else {
          return false;
        }
      });
    },
    delCenter(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "删除",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
        beforeClose: (action, instance, done) => {
          if (action === "confirm") {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = "删除中...";
            deleteCenter(row.id).then(r => {
              instance.confirmButtonLoading = false;
              if (r.success) {
                this.$message({
                  message: "删除成功！",
                  type: "success"
                });
                this.all(row.categoryId);
                this.getList(row.categoryId);
                done();
              } else {
                this.$message.error("删除失败！");
                done();
              }
            });
          } else {
            done();
          }
        }
      }).catch(() => {
        this.$message({
          type: "info",
          message: "已取消删除"
        });
      });
    },
    handleAppStatusChange(value) {
      updCenter({
        id: value.id,
        status: value.status ? false : true
      }).then(res => {
        if (res.success) {
          this.all(value.categoryId);
          this.getList(value.categoryId);
        } else {
          this.$message.error("修改失败！");
        }
      });
    },
    authCenter(row) {
      this.authVisible = true;
      this.appId = row.id;
      this.queryTenantIds(row.id);
      // this.showAppTenants(this.tenantList)
    },
    /** 查询参数列表 */
    getTenantList() {
      this.authLoading = true;
      page({...this.addDateRange({}, this.dateRange).params, ...this.queryParams}).then(
        (response) => {
          this.tenantList = response.data.records;
          this.total = response.data.total;
          this.authLoading = false;
        }
      );
    },
    // 参数系统内置字典翻译
    effectiveStatusFormat(row, column) {
      return this.selectDictLabel(this.effectiveStatusOptions, row.tenantStatus);
    },
    selectTenantApp(selection) {
      this.selectTenantIds = selection.map((item) => item.tenantId);
    },
    addTenantApp() {
      const data = {
        appCenterId: this.appId,
        tenantIds: this.selectTenantIds,
      }
      authApp(data).then(res => {
        this.$message.success("操作成功！")
      })
    },
    /** 搜索按钮操作 */
    handleTenantQuery() {
      this.authLoading = true;
      this.queryParams.pageNum = 1;
      // this.getTenantList();
      // this.queryTenantIds(this.appId);
      page({...this.addDateRange({}, this.dateRange).params, ...this.queryParams}).then(
        (response) => {
          this.tenantList = response.data.records;
          this.total = response.data.total;
          this.$nextTick(() => {
            this.$refs.multipleTable.clearSelection();
          })
          selectTenantIds(this.appId).then(res => {
            if (res.data.length > 0) {
              for (let i = 0; i < this.tenantList.length; i++) {
                if (res.data.indexOf(this.tenantList[i].tenantId) !== -1) {
                  this.$refs.multipleTable.toggleRowSelection(this.tenantList[i], true)
                }
              }
              this.authLoading = false;
            } else {
              this.authLoading = false;
            }
          })
        }
      );
    },
    /** 重置按钮操作 */
    resetTenantQuery() {
      this.resetForm("queryForm");
      this.handleTenantQuery();
    },
    queryTenantIds(appId) {
      this.authLoading = true;
      this.$nextTick(() => {
        this.$refs.multipleTable.clearSelection();
      })
      selectTenantIds(appId).then(res => {
        if (res.data.length > 0) {
          for (let i = 0; i < this.tenantList.length; i++) {
            if (res.data.indexOf(this.tenantList[i].tenantId) !== -1) {
              this.$refs.multipleTable.toggleRowSelection(this.tenantList[i], true)
            }
          }
          this.authLoading = false;
        } else {
          this.authLoading = false;
        }
      })
    },
    cancelAuth() {
      this.authVisible = false;
    }
  }
};
</script>

<style scoped lang="scss">
.app-card {
  margin: 0px 0px 10px 0px;
  min-height: calc(100vh - 200px);
  border: 0px solid #e6ebf5;

  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > span {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .icon {
      font-size: 22px;
      color: #419eee;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }

    .exec {
      padding: 3px 0;
    }
  }

  .tool {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }

  .app-empty {
    background-color: #f5f9fa;
  }
}

.box-card {
  height: 100%;
  width: 100%;
  overflow: auto;
  margin-top: 0;
}

.body {
  margin: 40px;

  .foot {
    text-align: center;
    margin-top: 50px;
  }
}

.tool-icon {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 15px 15px 0 0;
  border-radius: 5px;
  font-size: 14px;
  width: 75px;
  height: 75px;
  font-weight: bold;

  .img {
    font-size: 28px;
  }

  cursor: pointer;
}
</style>
<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.demo-drawer__footer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
}
</style>
