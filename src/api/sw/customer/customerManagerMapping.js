import request from '@/utils/request'

// 分页查询客户经理信息
export function getPageList(params) {
  return request({
    url: '/sanquan/customer/mapping/findPage',
    method: 'get',
    params,
  })
}

// 查询客户经理信息详细信息
export function getInfo(LOGIN) {
  return request({
    url: '/sanquan/customer/mapping/findInfo',
    method: 'get',
    params: {
        LOGIN
    }
  })
}
//查询客户经理
export function findCustomerManager(data) {
  return request({
    url: '/sanquan/customer/mapping/findCustomerManager',
    method: 'post',
    data,
  })
}
// 新增客户经理信息
export function addMapping(data) {
  return request({
    url: '/sanquan/customer/mapping/add',
    method: 'post',
    data,
  })
}

// 修改客户经理信息
export function updateMapping(data) {
  return request({
    url: '/sanquan/customer/mapping/update',
    method: 'post',
    data,
  })
}

// 删除客户经理信息
export function deleteMapping(LOGIN) {
  return request({
    url: '/sanquan/customer/mapping/delete',
    method: 'post',
    params: {
        LOGIN
    }
  })
}
