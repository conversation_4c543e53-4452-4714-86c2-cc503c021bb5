<template>
  <div class="con-main table-wrapper box-bg" style="margin-bottom:10px;">
    <slot name="mainHeader" />
    <div
      v-if="$slots.btn || title"
      class="flex-between"
    >
      <div v-if="title" class="table-title">{{ title }}</div>
      <slot name="btn" />
    </div>
    <slot />
  </div>
</template>
<script>
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    title: "",
  },
  data() {
    return {};
  },
  // 监听属性类似于data概念
  computed: {},
  // 方法集合
  methods: {},
};
</script>
<style lang="scss" scoped>
.con-main {
  flex: 1;
  .flex-between {
    padding-bottom: 0.08333rem !important;
    border-bottom: 0.00833rem solid #f1f1f1;
  }
}
</style>
