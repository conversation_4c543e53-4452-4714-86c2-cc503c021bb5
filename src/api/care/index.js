import request from "@/utils/request";

// 查询部门列表
export function getList(data) {
  return request({
    url: "/gh/tu-care/page",
    method: "get",
    params: data
  });
}

// 查询部门列表
export function getPage(action,data) {
  return request({
    url: "/gh/"+action,
    method: "get",
    params: data
  });
}
// 查询部门列表
export function getPageDb(action,data) {
  return request({
    url: "/gh/"+action,
    method: "get",
    params: data
  });
}
// 新增公告
export function save(data) {
  return request({
    url: "/gh/tu-care/save",
    method: "post",
    data: data
  });
}
// 修改
export function update(data) {
  return request({
    url: "/gh/tu-care/update",
    method: "post",
    data: data
  });
}

// 修改
export function deleteTable(data) {
  return request({
    url: "/gh/tu-care/deleteById/"+data,
    method: "get"
  });
}
// 审核
export function check(data) {
  return request({
    url: "/gh/tu-care/check/"+data.id+"/"+data.status,
    method: "get"
  });
}

// 通过id查询
export function findById(id) {
  return request({
    url: "/gh/tu-care/getById/"+id,
    method: "get"
  });
}

// 通过id查询
export function returnCheck(data) {
  return request({
    url: "/gh/tu-care/returnStatus",
    method: "post",
    data: data
  });
}
// 通过id查询
export function readCount(data) {
  return request({
    url: "/gh/tu-care/readCount",
    method: "post",
    data: data
  });
}
