<template>
  <div class="app-container">
    <el-breadcrumb separator="/" style="margin: 5px 0 10px 0">
      <el-breadcrumb-item :to="{ path: '/portal/home' }" replace
        >门户首页</el-breadcrumb-item
      >
      <el-breadcrumb-item  :to="{path: 'mailList', query: { code: '1' }}"
        ><a href="">{{
          title
        }}</a></el-breadcrumb-item
      >
      <el-breadcrumb-item><a>站内信内容</a></el-breadcrumb-item>
      <el-breadcrumb-item
        ><a href="#">{{
          mailTitle
        }}</a></el-breadcrumb-item
      >
    </el-breadcrumb>
    <el-card class="d-card" shadow="never">
      <!-- <div slot="header">
        <el-button class="left-desc" @click="goBack">返回</el-button>
      </div> -->
      <div class="contentMail">
        <h4 class="titleMail">[{{ mailTitle }}]</h4>
        <p class="content" v-html="mailInfo.content"></p>
      </div>
    </el-card>
  </div>
</template>

<script>
import { seeNoticeAdd } from "/src/api/system/notice";
export default {
  data() {
    return {
      info: {
        noticeId: "",
      },
      mailInfo: {
        time: "",
        content: "",
      },
      title: "",
      mailTitle: "",
    };
  },
  created() {
    this.info.noticeId = this.$route.query.noticeId;
    this.mailInfo.time = this.$route.query.time;
    this.mailInfo.content = this.$route.query.content;
    this.title = this.$route.query.title;
    this.mailTitle = this.$route.query.mailTitle;
    console.log(this.mailTitle);
    this.getAddInfo();
  },
  methods: {
    getAddInfo() {
      seeNoticeAdd(this.info).then((res) => {
        console.log(res);
      });
    },
    goBack() {
      this.$router.push({ path: "mailList", query: { code: "1" } });
    },
  },
};
</script>

<style lang="scss" scoped>
.d-card {
  width: 60%;
  margin: 0 auto;
  border: 0px;
  min-height: calc(100vh - 150px);
  padding: 0px 50px;
  .d-title {
    text-align: center;
  }
  .d-desc {
    text-align: right;
    margin: 5px 10px;
    .d-type {
      padding: 0px 20px;
    }
  }
}
.content {
  ::v-deep img {
    display: block;
    margin: 0 auto;
  }
}
.titleMail {
  font-size: 20px;
}
.contentMail {
  // width: 50%;
  margin: 0 auto;
}
</style>
