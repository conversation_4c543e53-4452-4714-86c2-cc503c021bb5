import axios from "axios";
import { Message } from "element-ui";

// 监控接口调用间隔时间,单位 秒(s)
const interval = 5;

// 支持的索引数据类型
const supportType = ["percent", "integer"];

// 监控接口调用同步锁
let syncSwitch = false;

const state = {
  monitorDatas: [],
  version: 0
}

const mutations = {
  SET_MONITOR_DATAS: (state, view) => {
    state.monitorDatas = view;
  },
  DEL_MONITOR_DATAS: (state, view) => {
    state.monitorDatas = [];
  },
  SET_VERSION: (state, view) => {
    state.version = view;
  }
}

const actions = {
  // 获取监控数据
  // @param view.monitorKey 非空,作为获取监控数据的索引 Key 来匹配数据
  // @param view.type 选填,传递时进行额外的格式校验,支持 supportType 参数所定义的数据类型
  // @param view.limitNum 选填,校验匹配到的数据是否超过限制条数
  // @describe 接口包含缓存逻辑,调用方仅考虑数据展示即可.
  getMonitordata({ commit }, view) {
    if (!isOk(view)) {
      return [];
    };
    // 根据当前时间戳判断是否需要更新缓存
    let timestamp = new Date().getTime();
    if ((timestamp - state.version) > (interval * 1000)
      || timestamp < state.version) {
      // 如果上次调用时间超过间隔时间,则异步着尝试发起调用
      asyncCallMonitorApi(commit, timestamp);
    }
    // 异步拉取数据的同时,获取当前缓存中的监控数据并返回
    let rawDatas = state.monitorDatas;
    let ripeDatas = [];
    try {
      rawDatas.forEach((item, index) => {
        let captureNum = 0;
        // 对每行数据做处理
        if (item.substr(0, 1) === '#') {
          return;
        }
        // 索引匹配成功
        if (item.split('{')[0] === view.monitorKey) {
          // 限制类型校验
          if (view.type !== undefined) {
            if (view.type === "percent") {
              if (parseFloat(item.split(' ')[1]) > 1) {
                ripeDatas.push(item);
                captureNum += 1;
              }
            } else if (view.type === "percent") {
              if (item.split(' ')[1].indexOf('.') !== -1) {
                ripeDatas.push(item);
                captureNum += 1;
              };
            }
            // switch (view.type) {
            //   case "percent":
            //     // 百分数必须小于等于1
            //     if (parseFloat(item.split(' ')[1]) > 1) {
            //       ripeDatas.push(item);
            //       captureNum += 1;
            //     }
            //     break;
            //   case "integer":
            //     // 整数不能有小数点
            //     if (item.split(' ')[1].indexOf('.') !== -1) {
            //       ripeDatas.push(item);
            //       captureNum += 1;
            //     };
            //     break;
            // }
          }
          // 限制数量校验
          if (view.limitNum !== undefined && captureNum >= view.limitNum) {
            throw new Error("匹配到的数据超过数量限制");
          }
          ripeDatas.push(item);
          captureNum += 1;
        }
      });
    } catch (e) {
      Message.error(e.message);
      return [];
    }
    return ripeDatas;
  }
}

// 入参有效性校验
function isOk(view) {
  if (typeof (view.monitorKey) !== 'string') {
    Message.error('监控索引参数异常,请联系管理员查看');
    return false;
  }
  if (view.limitNum !== undefined && typeof (view.limitNum) !== 'number') {
    Message.error('数据限制条数参数异常,请联系管理员查看');
    return false;
  }
  if (view.type !== undefined && supportType.indexOf(view.type) === -1) {
    Message.error('限制类型参数错误,请联系管理员查看');
    return false;
  }
  return true;
}

// 异步调用监控api
function asyncCallMonitorApi(commit, timestamp) {
  // 因接口内容大,请求响应较慢,需保证同一时间内仅有一次请求在等待
  if (!syncSwitch) {
    syncSwitch = true;
    axios({
      method: 'get',
      url: window.location.origin + process.env.VUE_APP_BASE_API + "/user/endpoints/prometheus",
    }).then(res => {
      commit('SET_VERSION', timestamp);
      syncSwitch = false;
      if (res.data) {
        let datas = res.data.split(/[(\r\n)\r\n]+/);
        commit('SET_MONITOR_DATAS', datas);
      } else {
        Message.error('监控接口无数据,请稍后再试');
      }
    }).catch(err => {
      syncSwitch = false;
      Message.error('获取监控数据异常,请确认参数是否正确');
    });
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
