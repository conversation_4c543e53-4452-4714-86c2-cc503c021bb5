<template>
  <div
    class="graphicnavigation"
    :style="{ backgroundImage: 'url(' + datas.config.bgImg + ')' }"
  >
    <!-- 默认导航 -->
    <section
      class="defaultNavigation"
      v-if="!datas.data[0]"
      :style="{
      background: datas.config.backgroundColor,
      display: datas.config.imgStyle === 0 ? 'flex' : '-webkit-box',
      'overflow-x': datas.config.imgStyle === 0 ? '' : 'scroll',
    }"
    >
      <!-- 导航 -->
      <div
        class="navigationList"
        v-for="index in datas.config.colNum"
        :key="index"
        :style="{
        width:
          datas.config.imgStyle === 0 ? 'auto' : 375 / datas.config.colNum - 1 + 'px',
      }"
      >
        <!-- 图片 -->
        <img
          src="../../../assets/images/imgs.png"
          alt="默认图片"
          draggable="false"
          :style="{ 'border-radius': datas.config.borderRadius + '%' }"
        />
        <!-- 文字 -->
        <p :style="{ color: datas.config.textColor }">导航</p>
      </div>
    </section>

    <!-- 导航列表 -->
    <section
      class="defaultNavigation"
      v-else
      :style="{
      background: datas.config.backgroundColor,
      display: datas.config.imgStyle === 0 ? 'flex' : '-webkit-box',
      'flex-wrap': datas.config.imgStyle === 0 ? 'wrap' : 'nowrap',
      'justify-content':
        datas.config.imgStyle === 0 ? 'space-evenly' : 'space-around',
      'overflow-x': datas.config.imgStyle === 0 ? '' : 'scroll',
      }"
      v-for="(resultImage, index) in datas.imageList"
      :key="index"
    >
      <!-- 导航 -->
      <div
        class="navigationList"
        v-for="(item, index) in resultImage"
        :key="index"
        :style="{
        width: datas.config.imgStyle === 0 ? '20%' : 375 / datas.config.colNum - 1 + 'px',
      }"
      >
        <!-- 图片 -->
        <img
          :src="item.routerIcon"
          alt="默认图片"
          draggable="false"
          :style="{ 'border-radius': datas.config.borderRadius + '%' }"
        />
        <!-- 文字 -->
        <p
          :style="{
          color: datas.config.textColor,
        }"
        >
          {{ item.routerTitle }}
        </p>
      </div>
    </section>
    <!-- 删除组件 -->
    <slot name="deles" />
  </div>
</template>

<script>
export default {
  name: 'graphicnavigation',
  props: {
    datas: Object,
  },
  created(){
    console.log(this.datas,'--------graphicnavigation')
  },
  data() {
    return {
    }
  }
}
</script>

<style scoped lang="less">
.graphicnavigation {
  position: relative;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  /* 默认导航 */
  .defaultNavigation {
    // overflow-x: scroll;
    justify-content: space-evenly;
    &::-webkit-scrollbar {
      height: 1px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #155bd4;
    }
    /deep/.el-collapse-item__header,
    /deep/.el-collapse-item__wrap {
      border-bottom: 0 !important;
    }
    /* 导航 */
    .navigationList {
      display: flex;
      flex-direction: column;
      align-items: center;
      img {
        margin-top: 5px;
        width: 45px;
        height: 45px;
      }
      p {
        font-size: 12px;
        margin-top: 5px;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: center;
        box-sizing: border-box;
      }
    }
  }
}
.uni-padding-wrap {
  padding: 0 30px;
}
.content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 70px;
  text-align: center;
}

.content-text {
  font-size: 14px;
  color: #666;
}
</style>
