<template>
  <div class="app-container" style="height: calc(100vh - 120px)">
    <split-pane
      :min-percent="10"
      :max-percent="30"
      :default-percent="15"
      split="vertical"
    >
      <template slot="paneL">
        <el-card class="dep-card" shadow="never" v-loading="treeLoading">
          <div slot="header" class="clearfix">
            <span>系统组织</span>
            <el-button
              style="float: right; padding: 3px 0"
              type="text"
              icon="el-icon-refresh"
              @click="reloadTree"
              >刷新</el-button
            >
            <el-form style="margin-top: 20px;margin-bottom: -20px;" v-if="$store.getters.customParam.userType === 'admin'">
              <el-form-item label="租户：">
                <el-select
                  v-model="queryParams.tenantId"
                  style="width: 120px;"
                  filterable
                  remote
                  :remote-method="getTenantList"
                  :loading="getTenantLoading"
                  @change="tenantChange"
                >
                  <el-option
                      v-for="item in tenantList"
                      :key="item.tenantId"
                      :label="item.tenantName"
                      :value="item.tenantId"
                  />
                </el-select>
              </el-form-item>
            </el-form>
          </div>
          <el-tree
            :props="lazyTreeProps"
            :load="loadNode"
            lazy
            :expand-on-click-node="false"
            ref="asyncTree"
            @node-click="handleNodeClick"
            :default-expanded-keys="[defaultOrgId]"
            node-key="orgId"
            highlight-current
          >
          </el-tree>
        </el-card>
      </template>
      <template slot="paneR">
        <el-card class="dep-card" shadow="never">
          <div slot="header" class="clearfix">
            <span>{{ selectNodeName }}</span>
            <el-button
              style="float: right; padding: 3px 0"
              type="text"
              icon="el-icon-refresh"
              @click="allUser"
              >全部组织</el-button
            >
          </div>
          <el-form
            :model="queryParams"
            ref="queryForm"
            :inline="true"
            v-show="showSearch"
            label-width="68px"
            @submit.native.prevent
          >
            <el-form-item label="组织名称" prop="orgName">
              <el-input
                v-model="queryParams.orgName"
                placeholder="请输入组织名称"
                clearable
                size="small"
                style="width: 240px"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
                >重置</el-button
              >
            </el-form-item>
          </el-form>

          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['system:dept:add']"
                v-if="selectNode && selectNode.orgType !== 'group'"
                >新增</el-button
              >
            </el-col>
            <right-toolbar
              :showSearch.sync="showSearch"
              @queryTable="getList"
              :columns="columns"
            ></right-toolbar>
          </el-row>

          <el-table v-loading="loading" :data="deptList">
            <el-table-column
              label="组织编号"
              align="left"
              key="code"
              prop="code"
              v-if="columns[0].visible"
            />
            <el-table-column
              label="组织名称"
              align="left"
              key="orgName"
              prop="orgName"
              v-if="columns[1].visible"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="组织类别"
              align="center"
              key="orgType"
              prop="orgType"
              v-if="columns[2].visible"
              :show-overflow-tooltip="true"
              :formatter="orgTypeFormat"
            >
            <template slot-scope="scope">
            <el-tag effect="plain" :type="selectDictRemark( orgTypeOptions, scope.row.orgType) ">
              {{ selectDictLabel( orgTypeOptions, scope.row.orgType) }}
            </el-tag>
          </template>
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              width="260"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  :loading="getDeptLoading && scope.row.orgId === loadOrgId"
                  @click="handleUpdate(scope.row)"
                  >修改<!-- v-hasPermi="['system:org:edit']" --></el-button
                >
                <el-button
                  v-if="scope.row.userId !== 1 && scope.row.orgId != defaultOrgId "
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  >删除<!-- v-hasPermi="['sys:org:delete']" --></el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-card>
      </template>
    </split-pane>

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body v-dialogDrag>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="上级组织" prop="parentName">
              <el-input
                v-model="form.parentName"
                placeholder="请选择上级组织"
                disabled
              >
                <template slot="append"
                  ><el-link @click="$refs.treeSelect.open()" :disabled="isTopNode"
                    >选择</el-link
                  ></template
                >
              </el-input>
            </el-form-item>
            <el-form-item hidden prop="parentId">
              <el-input v-model="form.parentId"> </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="组织名称" prop="orgName">
              <el-input
                v-model="form.orgName"
                placeholder="请输入组织名称"
                maxlength="64"
                :disabled="isTopNode"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="组织类别" prop="orgType">
              <el-select v-model="form.orgType" :disabled="isTopNode">
                <el-option
                  v-for="item in orgTypeOptions"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="组织电话" prop="phone">
              <el-input
                v-model="form.phone"
                placeholder="请输入组织电话"
                maxlength="11"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="组织传真" prop="fax">
              <el-input
                v-model="form.fax"
                placeholder="请输入组织传真"
                maxlength="60"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="组织邮箱" prop="email">
              <el-input
                v-model="form.email"
                placeholder="请输入组织邮箱"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="租户">
              <el-input
                  v-model="form.tenantName"
                  placeholder="租户"
                  maxlength="50"
                  disabled
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="submitLoading"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
      <dept-select ref="treeSelect" name="" value="" :tenant-id="queryParams.tenantId" @selected="selected" />
    </el-dialog>
  </div>
</template>

<script>
import {
  listDept,
  getDept,
  delDept,
  addDept,
  updateDept,
} from "@/api/system/dept";
import { list as tenantList } from "@/api/system/tenant";
import { treeselect } from "@/api/system/dept";
import DeptSelect from "@/components/DeptSelect";
import TransferTable from "@/components/TransferTable";

export default {
  name: "Dept",
  components: { DeptSelect, TransferTable },
  data() {
    //手机号验证
    var checkPhone = (rule,value,callback) =>{
      if(!value) {
        callback();
      } else {
        const reg = /^1[3456789]\d{9}$/
        if(reg.test(value)) {
          callback();
        } else {
          return callback(new Error('请输入正确的手机号'))
        }
      }
    }
    //邮箱验证
    var checkEmail = (rule ,value,callback)=>{
      if(!value) {
        callback()
      } else {
        const reg = /^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/;
        if(reg.test(value)) {
          callback()
        } else {
          return callback(new Error('请输入正确的邮箱'))
        }
      }
    }
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      submitLoading: false,
      getTenantLoading: false,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 状态数据字典
      orgTypeOptions: [],
      // 表单参数
      form: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orgName: undefined,
        orgId: this.$store.getters.orgId,
        tenantId: this.$store.getters.customParam.tenantId,
        tenantName: this.$store.getters.customParam.tenantName,
      },
      // 列信息
      columns: [
        { key: 0, label: `组织编号`, visible: true },
        { key: 1, label: `组织名称`, visible: true },
        { key: 2, label: `组织类别`, visible: true },
      ],
      // 表单校验
      rules: {
        parentName: [
          { required: true, message: "请选择上级组织", trigger: "blur" },
        ],
        orgName: [{ required: true, message: "请输入组织名称", trigger: "blur" }],
        orgType: [
          { required: true, message: "请选择组织类别", trigger: "blur" },
        ],
        phone :[
          {
            validator:checkPhone,trigger:'blur'
          },
        ],
        email :[
          {
            validator:checkEmail,trigger:'blur'
          }
        ]
      },
      lazyTreeProps: {
        children: "children",
        label: "orgName",
        isLeaf: "leaf",
      },
      treeLoading: false,
      selectNodeName: "全部人员",
      selectNode: undefined,
      getDeptLoading: false,
      loadOrgId: undefined,
      deptList: [],
      tenantList: [],
      open: false,
      defaultOrgId: this.$store.getters.orgId,
      isTopNode: false,
    };
  },
  created() {
    this.getTenantList();
    this.getList();
    this.getDicts("org_type").then((response) => {
      this.orgTypeOptions = response.data;
    });
  },
  methods: {
    getTenantList(tenantName) {
      this.getTenantLoading = true;
      let query = {}
      if (tenantName !== undefined && tenantName !== '') {
        query.tenantName = tenantName
        query.tenantId = undefined
      } else {
        query.tenantId = this.queryParams.tenantId
      }
      tenantList(query).then((response) => {
        this.tenantList = response.data;
      }).finally(() => {
        this.getTenantLoading = false;
      });
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listDept(this.addDateRange(this.queryParams, this.dateRange)).then(
        (response) => {
          this.deptList = response.data.records;
          this.total = response.data.total;
          this.loading = false;
        }
      );
    },
    orgTypeFormat(row, column) {
      return this.selectDictLabel(this.orgTypeOptions, row.orgType);
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.orgId = data.orgId;
      this.selectNode = data;
      this.selectNodeName = data.orgName;
      this.getList();
    },
    allUser() {
      const node = this.$refs.asyncTree.root;
      node.loaded = false;
      node.expand();
      this.queryParams.orgId = this.defaultOrgId;
      this.selectNodeName = "全部组织";
      this.selectNode = undefined;
      this.getList();
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        parentName: undefined,
        orgName: undefined,
        parentId: undefined,
        orgType: undefined,
        tenantName: undefined
      };
      this.isTopNode = false
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      if (this.selectNode) {
        this.form = {
          ...this.form,
          parentId: this.selectNode.orgId,
          parentName: this.selectNodeName,
        };
      }
      this.form.tenantName = this.queryParams.tenantName;
      this.form.tenantId = this.queryParams.tenantId;
      this.open = true;
      this.title = "添加组织";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.loadOrgId = row.orgId;
      if (row.orgId === this.defaultOrgId || (this.queryParams.tenantId !== 'system' && row.parentId === null)) {
        this.isTopNode = true
      }
      this.getDeptLoading = true;
      getDept(row.orgId)
        .then((response) => {
          getDept(response.data.parentId)
            .then((r) => {
              this.getDeptLoading = false;
              this.loadOrgId = undefined;
              if (response.data) {
                this.form = {
                  ...this.form,
                  ...response.data,
                  parentName: r.data.orgName,
                  parentId: response.data.parentId,
                };
                this.open = true;
                this.title = "修改组织";
              } else {
                this.$message.error("数据异常！");
              }
            })
            .catch((e) => {
              this.getDeptLoading = false;
              this.loadOrgId = undefined;
              this.$message.error("数据异常！");
            });
        })
        .catch((e) => {
          this.getDeptLoading = false;
          this.loadOrgId = undefined;
          this.$message.error("数据异常！");
        });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.submitLoading = true;
          if (this.form.orgId !== undefined) {
            updateDept(this.form).then((response) => {
              this.submitLoading = false;
              if (response.success) {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              } else {
                this.$message.error(response.message);
              }
            });
          } else {
            addDept(this.form).then((response) => {
              this.submitLoading = false;
              if (response.success) {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              } else {
                this.$message.error(response.message);
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const orgName = row.orgName;
      const orgId = row.orgId
      this.$confirm('是否确认删除组织名称为"' + orgName + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return delDept({orgId: orgId});
        })
        .then((reponse) => {
          this.getList();
          if(reponse.success === false) {
            this.$message.error(reponse.message)
          }else {
            this.msgSuccess("删除成功");
            this.reloadTree()
          }
        });
    },
    //懒加载树形结构的组织
    loadNode(node, resolve) {
      if (!node.data) {
        this.treeLoading = true;
      }
      treeselect({
        orgId: node.data ? node.data.orgId : this.defaultOrgId,
        queryType: node.data ? "down" : "current",
        tenantId: this.queryParams.tenantId
      }).then((response) => {
        resolve(response.data);
        this.treeLoading = false;
      });
    },
    // 重新加载树形结构的组织
    reloadTree() {
      if (this.selectNode) {
        const node = this.$refs.asyncTree.getNode(this.selectNode);
        node.childNodes = [];
        node.loaded = false;
        node.expand();
      } else {
        this.$refs.asyncTree.root.loaded = false;
        this.$refs.asyncTree.root.expand();
      }
    },
    // 组织选择的回调
    selected(data) {
      this.form = {
        ...this.form,
        parentId: data.orgId,
        parentName: data.orgName,
      };
      this.$refs.treeSelect.close();
    },
    tenantChange(tenantId) {
      if (tenantId !== '') {
        this.queryParams.tenantName = this.tenantList.find(item => item.tenantId === tenantId).tenantName
      }
      this.$refs.asyncTree.root.loaded = false;
      this.$refs.asyncTree.root.expand();
      this.selectNode = undefined;
      this.handleQuery()
    },
  },
};
</script>
<style lang="scss" scoped>
.dep-card {
  min-height: calc(100vh - 120px);
}
</style>
