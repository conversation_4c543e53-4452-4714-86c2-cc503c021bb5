<template>
  <div class="uni-table" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
    <el-table
      ref="multipleTable"
      :key="tableKey"
      :span-method="objectSpanMethod"
      :data="tableData"
      style="width: 100%"
      :row-class-name="tableRowClassName"
      @select="selectRow"
      @row-click="handleNodeClick"
      @select-all="selectRowAll"
      :height="maxHeight"
      :max-height="maxHeightFix"
      v-loading=loading
    >
      <el-table-column type="selection" v-if="selection"></el-table-column>
      <el-table-column type="index" label="序号" align="center" width="80" v-if="showIndex"></el-table-column>
      <el-table-column
        v-for="(column, index) in columns"
        :type="column.type"
        :key="column.props"
        :prop="column.props"
        :label="column.label"
        :width="column.width"
        :min-width="column.minWidth"
        :align="column.align"
        :sortable="column.sortable"
        :fixed="column.fixed"
      >
        <!-- 自定义表头 -->
        <template slot="header" slot-scope="scope">
          <div class="flex">
            <span style="padding-right: 5px;">{{ column.label }}</span>
            <el-tooltip
              class="item"
              effect="dark"
              :content="column.tooltipText"
              placement="top"
              v-if="column.tooltipText"
            >
              <i class="el-icon-info" style="color:#909399"></i>
            </el-tooltip>
          </div>
        </template>
         
      <!-- :show-overflow-tooltip="column.props !== 'opt' && !column.notShowTooltip" -->
        <el-table-column
          v-for="(item, index) in column.children"
          :key="item.props"
          :prop="item.props"
          :label="item.label"
          :width="item.width"
          :align="item.align"
          :sortable="item.sortable"
          :show-overflow-tooltip="item.props !== 'opt'"
        >
        </el-table-column>

        <template slot-scope="scope">
          <slot
            v-if="column.slot === true"
            :name="column.props"
            :row="scope.row"
            :scope="scope"
          />
          <div v-else :class="{ 'content-hide': !column.notShowTooltip }">{{ scope.row[column.props] }}</div>
        </template>
      </el-table-column>
    </el-table>
    <div class="flex-end" style="margin-top: 0.14rem">
      <el-pagination
        style="float: right; background: transparent"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageInfo.pageNum"
        :page-sizes="[10, 20, 30, 40, 50, 100]"
        :page-size="pageSize"
        layout="prev, pager, next, sizes, jumper, total "
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>
<script>
import { uniTableData } from '@/api/uni/uni'
import { min } from 'moment';

export default {
  name: 'UniTable',
  props: {
    rowClick: {
      type: Function, default: function(row, event, column) {
        console.log('调用的方法', row, event, column)
      }
    },
    selection: { type: Boolean, default: true },
    showIndex: {type: Boolean, default: false},
    checkBoxKey: { type: String, default: 'id' },
    mergeColIndex: { type: Array, default: () =>[] },
    mergeRow: { type: Boolean, default: false },
    columns: {
      type: Array,
      default: function() {
        return []
      }
    },
    params: { type: Object, default: () => ({}) },
    staticData: {
      type: Array,
      default: function() {
        return []
      }
    },
    maxHeight: { type: String, default: undefined },
    // 固定表头
    maxHeightFix: { type: Number, default: undefined },
    action: { type: String, default: '' },
    pageSize: { type: Number, default: 10 },
    reqMethod: { type: String, default: 'get' },
    keyField: { type: String, default: 'id' }, // 使用多选框时候的唯一的标识（用于判断当前数据是否被选中）
    selectedValues: { type: Array, default: () => [] }, // 选中的值
    mergeRows: {
      type: Function, default: function({ row, column, rowIndex, columnIndex }) {
        return {
          rowspan: 0,
          colspan: 0
        }
      }
    },
    mergeGroupName: { type: String, default: 'id' },
    isMounted: { type: Boolean, default: true },
  },
  data() {
    return {
      tableData: [],
      total: 100,
      pageInfo: { pageSize: 10, pageNum: 1 },
      selectRowItem: [],
      selectTableRow: [],
      mergeMap: {},
      hasMergeRow: [],
      mergeRowIndex: 0,
      mergerRowArrIndex: [],
      mergeRowSpanArr: {},
      tableKey:'',
      loading: false ,// loading状态
      hideScrollbarTimeout: null,
    }
  },
  watch: {
    // 监听 selectedValues 的变化
    selectedValues: {
      handler(newVal, oldVal) {
        console.log('新值', newVal)
        // 在 selectedValues 变化时更新 selectTableRow
        this.selectTableRow = [...newVal]
        this.selectRowItem = [...newVal]
      }
    }
  },
  mounted() {
    this.selectTableRow = [...this.selectedValues]
    if(this.isMounted){
      this.$nextTick(() => {
        this.loadData({})
      })
    }
  },
  methods: {
    // 点击查询右侧数据
    handleNodeClick(row, event, column) {
      this.rowClick(row, event, column)
    },
    selectionChange(selected) {
      console.log('unitalbe:selected:', selected)
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if ((this.mergeColIndex.indexOf(columnIndex) !== -1) && this.mergeRow) {
        let item = this.mergeRowSpanArr[row[this.mergeGroupName]]
        if (rowIndex === item.rowIndex) {
          return {
            rowspan: item.rowSpan,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },
    tableRowClassName({ row, rowIndex }) {
      let rowStyle = 'row-bg-high'
      if (rowIndex % 2 === 0) {
        rowStyle = 'row-bg-normal'
      }
      return rowStyle
    },
    getCheckBoxVal() {
      return this.selectTableRow
    },
    // 返回选中的对象
    getCheckBoxRows() {
      return this.selectRowItem
    },
    groupBy(array, key) {
      return array.reduce((result, currentItem) => {
        // 使用 key 函数提取分组键
        const groupKey = key(currentItem)

        // 确保 result 对象中有对应分组的数组
        if (!result[groupKey]) {
          result[groupKey] = []
        }

        // 将当前项添加到对应分组的数组中
        result[groupKey].push(currentItem)

        return result
      }, {})
    },
    loadData(data) {
      if (this.action) {
        this.loading = true
        this.pageInfo.pageNum = data.pageNum ? data.pageNum : this.pageInfo.pageNum
        this.pageInfo.pageSize = this.pageSize > this.pageInfo.pageSize ? this.pageSize : this.pageInfo.pageSize
        let reqData = { ...this.params, ...data, ...this.pageInfo }
        console.log(reqData, 'unitable 请求参数')
        this.tableData = []
        uniTableData(this.action, reqData, this.reqMethod).then((response) => {
          if (this.mergeRow) {
            this.mergeRowSpanArr={}
            this.mergeMap = this.groupBy(response.data.records, item => item[this.mergeGroupName])
            // console.log(this.mergeMap,'hebing')
            for (let i = 0; i < response.data.records.length; i++) {
              let item = response.data.records[i]
              let rowSpan = this.mergeMap[item[this.mergeGroupName]].length
              let obj = this.mergeRowSpanArr[item[this.mergeGroupName]]
              console.log(obj, '我是obj', rowSpan)
              if (obj === undefined || obj === null) {
                this.mergeRowSpanArr[item[this.mergeGroupName]] = {
                  rowIndex: i,
                  rowSpan: rowSpan
                }
              }

            }
          }
          this.tableData = response.data.records
          this.total = response.data.total
          this.tableKey=new Date().getTime()
          this.$nextTick(() => {
            this.tableData.map((item) => {
              this.selectTableRow.map((selectedVal) => {
                if (selectedVal === item[this.checkBoxKey]) {
                  this.$refs.multipleTable.toggleRowSelection(item)
                }
              })
            })

          })
          this.loading = false
        }).catch(() => {
          this.loading = false
        })
      } else {
        //this.tableData = this.staticData
        this.total = this.staticData.length
      }
    },
    handleCurrentChange(val) {
      this.pageInfo.pageNum = val
      this.loadData({})
    },
    // 查询的条数修改
    handleSizeChange(val) {
      console.log('每页显示条数', val);
      this.pageInfo.pageSize = val;
      this.loadData({})
    },
    // 选择触发事件
    selectRow(val, row) {
      let selectRow = row[this.checkBoxKey]
      // 检查是否已经存在相同的 customerId
      let index = this.selectTableRow.findIndex((item) => item === selectRow)
      console.log('index', index, selectRow)

      if (index === -1) {
        // 未找到相同的 customerId，表示选中操作
        this.selectTableRow.push(selectRow)
        this.selectRowItem.push(row)
      } else {
        // 找到相同的 customerId，表示取消选中操作
        this.selectTableRow.splice(index, 1)
        this.selectRowItem.splice(index, 1)
      }
      console.log('当前选择中的:', this.selectTableRow)
    },
    selectRowAll(val) {
      if (val.length > 0) {
        val.map((row) => {
          let selectRow = row[this.checkBoxKey]
          // 检查是否已经存在相同的 customerId
          if (!this.selectTableRow.some((item) => item === selectRow)) {
            this.selectTableRow.push(selectRow)
            this.selectRowItem.push(row)
          }
        })
      } else {
        // val 为空时，从 this.selectTableRow 中移除在 this.tableData 中存在的 customerId 的数据
        this.selectTableRow = this.selectTableRow.filter((selectRow) => {
          return !this.tableData.some(
            (item) => item[this.checkBoxKey] === selectRow
          )
        })
        this.selectRowItem = this.selectRowItem.filter((selectRow) => {
          return !this.tableData.some(
            (item) => item[this.checkBoxKey] === selectRow[this.checkBoxKey]
          )
        })
      }
    },
    resetPage() {
      this.pageInfo.pageNum = 1
    },
    handleMouseEnter() {
      clearTimeout(this.hideScrollbarTimeout); // 取消任何未完成的隐藏操作
      this.$refs.multipleTable.bodyWrapper.style.overflowX = 'auto';
      // this.$refs.multipleTable.bodyWrapper.style.bottom = '0px';
    },
    handleMouseLeave() {
      this.hideScrollbarTimeout = setTimeout(() => {
        this.$refs.multipleTable.bodyWrapper.style.overflowX = 'hidden';
        // this.$refs.multipleTable.bodyWrapper.style.bottom = '0px';
      }, 200); // 200ms 的延迟
    }
  }
}
</script>

<style lang="less">
.uni-table {
  width: 100%;
  height: 100%;

  tr {
    line-height: 23px !important;
  }

  .el-table {
    .el-table__cell {
      padding: 16px 0 !important;
      height: 23px;
      line-height: 23px;
      overflow: hidden;
    }

    .cell {
      overflow: hidden;
      // height: 23px;
      line-height: 23px;
    }

    .el-table__fixed {
      bottom: 0px !important;
    }
    .el-table__fixed::before {
      background-color: transparent;
    }
  }

  .el-table__header-wrapper {
    background: #f5f8ff !important;

    thead {
      th {
        background: #f5f8ff !important;
        font-family: PingFangSC-Regular;
        font-size: 0.135rem !important;
        color: rgba(0, 0, 0, 0.8);
        letter-spacing: 0;
        font-weight: 400;

        .el-checkbox {
          margin-left: 4px !important;
        }
      }
    }
  }

  .el-table__body-wrapper {
    overflow-x: hidden;
    .row-bg-high {
      opacity: 1;
      background: #f5f8ff !important;

      td {
        background: #f5f8ff !important;
      }
    }
  }

  .el-table__body-wrapper::-webkit-scrollbar {
    width: 0px !important;
    height: 10px !important;
  }

  .el-pagination {
    * {
      background: transparent;
    }

    button:disabled {
      background: transparent;
    }
  }

  // .content-hide {
  //   overflow: hidden;
  //   white-space: nowrap;
  //   text-overflow: ellipsis;
  //   font-size: 0.13rem !important;
  // }
}
</style>
