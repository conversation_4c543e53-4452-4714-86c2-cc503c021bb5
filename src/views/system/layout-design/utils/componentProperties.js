const componentProperties = new Map()

// 数据版本  每次修改组件数据  需要对版本进行修改
// componentProperties.set('componentPropertiesVersion', 'V1.0.0')

/** 基础组件区域 */
/** 搜索 */
componentProperties.set('search', {
  component: 'search',
  moduleTitle: '搜索',
  type: '1-1',
  active: true,
  style: 'searchstyle',
  itemContent: {
    config: {
      moduleTitle: '搜索',
      heights: 28, //搜索栏高度
      position: 0, //显示位置
      sweep: false, // 显示扫一扫
      borderRadius: 0, //框体样式
      textPosition: 0, //文本位置
      backgroundColor: 'rgb(249, 249, 249)', //背景颜色
      borderColor: 'rgb(255, 255, 255)', //框体颜色
      textColor: 'rgb(150, 151, 153)', //字体颜色
      hotords: [], //热词
    }
  },
})
/** 标题文本 */
componentProperties.set('captiontext', {
  component: 'captiontext',
  moduleTitle: '标题文本',
  type: '1-2',
  active: true,
  style: 'captiontextsstyle',
  itemContent: {
    config: {
      moduleTitle: '标题文本',
      name: '标题文本',//标题内容
      description: '',//描述内容
      wordSize: 16,//标题大小
      descriptionSize: 12,//描述大小
      wordWeight: 400,//标题粗细
      positions: 'left', //显示位置  可选left/center
      descriptionWeight: 200,//描述粗细
      wordColor: 'rgba(50, 50, 51, 10)',//标题颜色
      descriptionColor: 'rgba(150, 151, 153, 10)',//描述颜色
      backColor: 'rgba(255, 255, 255, 10)', //背景颜色
      borderBott: false, //底部分割线
      wordHeight: 24,//框体高度
      more: {    //查看更多
        show: false,//是否显示查看更多
        type: 1, // 样式选择
        text: '查看更多', //自定义文字
        httpType: 10,//链接类型
        http: '',//链接
      },
    }
  },
})
/** 图片轮播 */
componentProperties.set('swipe', {
  component: 'swipe',
  moduleTitle: '图片轮播',
  type: '1-3',
  active: true,
  style: 'swipestyle',
  itemContent: {
    config: {
      moduleTitle: '图片轮播',
    },
    data: [],  // 添加图片
  },
})
/** 图文导航 */
componentProperties.set('graphicnavigation', {
  component: 'graphicnavigation',
  moduleTitle: '图文导航',
  type: '1-4',
  active: true,
  style: 'graphicnavigationstyle',
  itemContent: {
    config: {
      moduleTitle: '图文导航',
      imgStyle: 1, //图片样式
      backgroundColor: 'rgb(255, 255, 255)', //背景颜色
      textColor: 'rgb(0, 0, 0)', //文字颜色
      borderRadius: 0, //图片倒角
      colNum: 5, //一屏显示个数
      rowNum: 1,
    },
    imageList: [], // 图片导航列表
    data: [],
  },
})
/** 底部导航-暂时隐藏 */
componentProperties.set('tabBar', {
  component: 'tabBar',
  moduleTitle: '底部导航',
  type: '1-5',
  active: true,
  style: 'tabBarStyle',
  itemContent: {
    config: {
      moduleTitle: '底部导航',
      activeColor: '#1989fa',
      inactiveColor: '#7d7e80',
      isShowBorder: true,
      iconWidth: '25',
      iconHeight: '25',
      fontSize: '14',
      Highlight: 0,
    },
    data: [],
  },
})
/** 选项卡 */
componentProperties.set('tab', {
  component: 'tab',
  moduleTitle: '选项卡',
  type: '1-6',
  active: true,
  style: 'tabstyle',
  itemContent: {
    config: {
      moduleTitle: '选项卡',
      color: '#007aff', //背景颜色
      tabType: 'button',
      tabList: ['选项卡1', '选项卡2', '选项卡3'],
    }
  },
})
/** 宫格 */
componentProperties.set('grid', {
  component: 'grid',
  moduleTitle: '宫格',
  type: '1-7',
  active: true,
  style: 'gridstyle',
  itemContent: {
    config: {
      moduleTitle: '宫格',
      isShowIcon: false,
      imgStyle: 1, //图片样式
      backgroundColor: 'rgb(255, 255, 255)', //背景颜色
      textColor: 'rgb(0, 0, 0)', //文字颜色
      borderRadius: 0, //图片倒角
      colNum: 5, //一屏显示个数
      rowNum: 1,
    },
    imageList: [], // 图片导航列表
    data: [],
  },
})
/** 公告 */
componentProperties.set('notify', {
  component: 'notify',
  moduleTitle: '公告',
  type: '1-8',
  active: true,
  style: 'notifystyle',
  itemContent: {
    config: {
      moduleTitle: '公告',
      notifyText: '请填写内容，如果过长，将会在手机上滚动显示', //内容
      backColor: 'rgb(255, 248, 233)', //背景颜色
      textColor: 'rgba(100, 101, 102)', //文字颜色
      routerType: "localPage",
      routerUrl: "",
    }
  },
})
/** 视频 */
componentProperties.set('videos', {
  component: 'videos',
  moduleTitle: '视频',
  type: '1-9',
  active: true,
  style: 'videostyle',
  itemContent: {
    config: {
      moduleTitle: '视频',
      src: 'http://clips.vorwaerts-gmbh.de/big_buck_bunny.mp4', // 视频地址
      coverUrl:null, // 封面地址
      autoplay: false, // 是否自动播放
    }
  },
})
/** 富文本 */
componentProperties.set('richtext', {
  component: 'richtext',
  moduleTitle: '富文本',
  type: '1-10',
  active: true,
  style: 'richtextstyle',
  itemContent: {
    config: {
      moduleTitle: '富文本',
      myValue: '', //富文本内容
      backColor: 'rgb(249, 249, 249)', //背景颜色
    }
  },
})
/** 辅助分割-暂时隐藏 */
componentProperties.set('divider', {
  component: 'divider',
  moduleTitle: '辅助分割',
  type: '1-11',
  active: true,
  style: 'dividerstyle',
  itemContent: {
    config: {
      moduleTitle: '辅助分割',
      blankHeight: 30, //空白高度
      segmentationtype: 0, //分割类型
      paddType: 0, //边距
      auxliarColor: 'rgb(229, 229, 229)', //辅助线颜色
      bordertp: 'solid', //线的类型
    }
  },
})
/** 表单元素 */
componentProperties.set('formitem', {
  component: 'formitem',
  moduleTitle: '表单元素',
  type: '1-12',
  active: true,
  style: 'formitemstyle',
  itemContent: {
    config: {
      moduleTitle: '表单元素',
      icon: '', // 左侧图标
      shopName: '左侧标题',
      copywriting: '右侧内容',
      type: '10',
      http: {},
    }
  },
})
/** 悬浮 */
componentProperties.set('suspension', {
  component: 'suspension',
  moduleTitle: '悬浮按钮',
  type: '1-13',
  active: true,
  style: 'suspensionstyle',
  itemContent: {
    config: {
      moduleTitle: '悬浮按钮',
      linktype: '10',
      http: {},
    }
  },
})
/** 卡片 */
componentProperties.set('card', {
  component: 'card',
  moduleTitle: '卡片',
  type: '1-14',
  active: true,
  style: 'cardstyle',
  itemContent: {
    config: {
      moduleTitle: '卡片',
      picture: true, // 是否显示分割线
    }
  },
})
/** 魔方-暂时隐藏 */
componentProperties.set('magiccube', {
  component: 'magiccube',
  moduleTitle: '魔方',
  type: '1-6',
  active: true,
  style: 'magiccubestyle',
  itemContent: {
    config: {
      moduleTitle: '魔方',
      rubiksCubeType: 0, // 魔方类型
      pageMargin: 0, //页面间距
      imgMargin: 0, //图片间隙
    },
    data: [], //图片列表
  },
})

/** 业务组件区域 */
/** 列表 */
componentProperties.set('list', {
  component: 'list',
  moduleTitle: '列表',
  type: '2-1',
  active: true,
  style: 'liststyle',
  itemContent: {
    config: {
      moduleTitle: '列表',
      border: true, // 是否显示分割线
    }
  },
})
/** 文章-暂时隐藏 */
componentProperties.set('articlelist', {
  component: 'articlelist',
  moduleTitle: '文章模块',
  type: '2-2',
  active: true,
  style: 'articleliststyle',
  itemContent: {
    config:{
      moduleTitle: '文章模块',
      name: '这里显示专题名称', //专题名称
      commodityType: 0, // 选择模板
      moditystyle: 0, // 卡片样式选择
      borderRadius: 0, // 图片边角
      textWeight: 400, // 标题粗细
      noteLabels: true, // 笔记标签
      readingNumber: true, // 阅读数
      praisePoints: true, //点赞数
      viewMore1: true, //更多1
      viewMore2: true, //更多2
      positions: 'bottom', //标题位置
      linktype: '10',
      http: {},
    },
    data:[],
  },
})
/** 表单 */
componentProperties.set('investigate', {
  component: 'investigate',
  moduleTitle: '表单',
  type: '2-3',
  active: true,
  style: 'investigatestyle',
  itemContent: {
    config: {
      moduleTitle: '表单',
      title: '表单',
    },
    data: [], //value1为sass显示内容，value2为前端显示内容
  },
})

export default componentProperties
