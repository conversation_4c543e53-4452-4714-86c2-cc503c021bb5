<template>
  <div class="portal-body">
    <div class="analysis">
      <slot name="header"></slot>
      <div class="container">
        <slot name="aside"></slot>
        <slot></slot>
      </div>
      <slot name="main"></slot>
      <slot name="footer"></slot>
    </div>
  </div>
</template>
<script>
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  data() {
    return {};
  },
  // 监听属性类似于data概念
  computed: {},
  // 方法集合
  methods: {},
};
</script>
<style lang="scss" scoped>
.container {
  display: flex;
  flex-wrap: wrap;
}
</style>
