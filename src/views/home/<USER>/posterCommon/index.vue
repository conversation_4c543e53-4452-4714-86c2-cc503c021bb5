<template>
  <el-card
    :class="this.bgColor === true ?'notice-card':'unshow-notice-card'"
    v-loading="noticeLoading"
    shadow="never"
  >
    <div slot="header" v-if="show">
      <span class="card-title">
        <div class="title">
          <span> <i class="icon iconfont icon-gonggao"></i>广告-普通 </span>
        </div>
      </span>
    </div>
    <div class="home-card-body">
      <div class="poster-content">
        <a :href="this.list.jumpAddress" v-if="list.imgList" target="_blank">
          <img :src="this.list.imgList[0].coverUrl" alt="" />
        </a>
        <img
          src="../../../../assets/images/nopicture.png"
          alt=""
          v-else
          style="display: block; margin: 0 auto"
        />
        <div class="font-title">广告</div>
      </div>
    </div>
  </el-card>
</template>

<script>
import { indexData } from "@/api/poster/index.js";
export default {
  name: "posterCommon",
  props: ["item"],
  data() {
    return {
      show: "",
      noticeLoading: false,
      list: [],
      form: {
        queryNum: 3,
        terminal:'PC',
      },
      bgColor:"",
    };
  },
  created() {
    this.getList();
    this.show = this.item.showTitle;
    this.bgColor = this.item.showBgColor;
  },
  watch: {
    "item.showTitle": function (val) {
      this.show = val;
    },
    "item.showBgColor" :function(val) {
      this.bgColor = val
    }
  },
  methods: {
    moreExt(type) {
      window.open(`/portal/home/<USER>/${type}`);
    },
    getList() {
      indexData(this.form).then((res) => {
        var news = res.data;
        news.forEach((item) => {
          if (item.advertisingType === "general") {
            this.list = item
          }
        });
      });
    },
  },
};
</script>

<style scoped lang="scss">
.home-card-body {
  width: 100%;
  height: 100%;

  .poster-content {
    width: 100%;
    height: 100%;
    position: relative;
    img {
      width: 100%;
      height: 100%;
    }
    .font-title {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 40px;
      height: 25px;
      background: #d1dae1;
      color: #4f7c87;
      text-align: center;
      line-height: 25px;
      z-index: 99;
    }
  }
}
.notice-card {
  height: 100%;
  background: #fff;
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 0;
    }
  }
  .notice {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    padding: 4px 0;
    font-size: 14px;
    .notice-title {
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: nowrap;
      flex-basis: 70%;
    }
    .notice-time {
      flex-basis: 20%;
      text-align: right;
    }
  }
}
.unshow-notice-card {
  border: 0px;
  height: 100%;
  background: #f5f9fa;
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 0;
    }
  }
  .notice {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    padding: 4px 0;
    font-size: 14px;
    .notice-title {
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: nowrap;
      flex-basis: 70%;
    }
    .notice-time {
      flex-basis: 20%;
      text-align: right;
    }
  }
}
.moreButtom {
  display: flex;
  justify-content: flex-end;
}
</style>
