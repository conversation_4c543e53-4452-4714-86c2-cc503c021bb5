<template>
  <div class="app-container" style="background: #fff">
    <div class="banner-top">
      <img src="../../assets/images/search/banner.png" />
      <div class="search-input">
        <el-input
          class="myInput"
          placeholder="请输入内容"
          v-model="queryParams.keyWords"
          @keyup.enter.native="noticeSearch"
          clearable
        ></el-input>
        <el-button class="myButton" @click="noticeSearch">搜索</el-button>
      </div>
    </div>
    <div class="container">
      <div class="container-title">
        <div class="title">
          <el-breadcrumb separator="/" class="breadcrumb">
            <i
              class="el-icon-location-outline"
              style="margin-right: 10px; font-size: 20px; color: #c3c3c3"
            ></i>
            <span style="color: #333333">您的位置：</span>
            <el-breadcrumb-item :to="{ path: '/portal/home' }" replace
              >门户首页</el-breadcrumb-item
            >
            <el-breadcrumb-item
              ><a>{{ title }}</a></el-breadcrumb-item
            >
          </el-breadcrumb>
          <div slot="header" class="card-title">
            <el-row>
              <el-col :span="19" class="c-r-t">
                <div class="filter">
                  <div>类型：</div>
                  <el-radio-group v-model="queryParams.source" @change="change">
                    <el-radio label="">全部</el-radio>
                    <el-radio
                      v-for="item in sysList"
                      :label="item.dictLabel"
                      :key="item.dictValue"
                      >{{ item.dictLabel }}</el-radio
                    >
                  </el-radio-group>
                </div></el-col
              >
            </el-row>
          </div>
        </div>
      </div>

      <el-card class="note-card" shadow="never" v-loading="loading">
        <div class="home-card-body">
          <a
            v-for="item in list"
            :key="item.id"
            :href="`/portal/home/<USER>/${item.programaType}/${item.id}`"
            target="view_window"
          >
            <div class="notice">
              <div class="noticeContent">
                <div class="imgs" v-if="item.coverUrl">
                  <img :src="item.coverUrl" alt="" />
                </div>
                <div class="noimgs" v-if="!item.coverUrl">
                  <img src="../../assets/images/nopicture.png" alt="" />
                </div>
                <div class="contentFont">
                  <div class="notice-title">
                    <div class="content" v-html="item.title"></div>
                    <div
                      style="
                        width: 60px;
                        height: 30px;
                        padding: 5px;
                        background-color: #4c86e3;
                        color: #ffffff;
                        display: inline-block;
                        text-align: center;
                        margin-left: 10px;
                        border-radius: 5px;
                      "
                    >
                      {{ item.source }}
                    </div>
                  </div>
                  <div class="content" v-html="item.noticeContent">
                    {{ item.noticeContent }}
                  </div>
                </div>
                <div class="notice-time">
                  {{ parseTime(item.createDate, "{y}-{m}-{d}") }}
                </div>
              </div>
              <el-button class="more" @click="item">MORE</el-button>
            </div>
          </a>
          <el-empty
            style="padding: 20px 0"
            v-if="list.length <= 0"
            :image-size="30"
            :description="`暂无${title}`"
          >
          </el-empty>
        </div>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.page"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
  </div>
</template>

<script>
import AppLink from "@/components/Link";
import { boolWildcard } from "@/api/system/notice";
export default {
  name: "detail",
  components: { AppLink },
  data() {
    return {
      sysList: [],
      list: [],
      total: 0,
      queryParams: {
        keyWords: "",
        page: 1,
        pageSize: 10,
      },
      loading: false,
      title: "",
    };
  },

  watch: {
    $route() {
      if (this.$route.path.includes("/listFuzzy")) {
        //获取数据
        this.getRouteParams();
        this.getList();
      }
    },
  },

  created() {
    this.getDicts("sys_list").then((response) => {
      this.sysList = response.data;
    });
    this.title = "搜索";
    this.getRouteParams();
    this.getList();
  },
  methods: {
    item() {
      this.$router.push({
        path: `/portal/home/<USER>/${item.programaType}/${item.id}`,
      });
    },
    noticeSearch() {
      let keyWords = this.queryParams.keyWords.trim();
      if (!keyWords) {
        return;
      }
      let routeData = this.$router.resolve({
        path: "/portal/home/<USER>",
        query: { keyWords },
      });

      if (this.$route.path.includes("/listFuzzy")) {
        this.$router.replace({
          path: "/portal/home/<USER>",
          query: { keyWords },
        });
      } else {
        window.open(routeData.href, "_blank");
        // this.$router.push({
        //   path: routeData.href,
        // });
        // window.open(routeData.href, "framename");
      }
    },
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.cities.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.cities.length;
    },
    getRouteParams() {
      this.queryParams.source = this.$route.query.source || "";
      this.queryParams.keyWords = this.$route.query.keyWords || "";
    },
    getList() {
      this.loading = true;
      if (!this.queryParams.keyWords.trim()) {
        this.list = [];
        this.total = 0;
        this.loading = false;
        return;
      }
      boolWildcard(this.queryParams).then((response) => {
        this.list = response.data.records;
        console.log(this.list);
        this.total = response.data.total;
        this.loading = false;
      });
    },
    change(value) {
      this.queryParams.source = value;
      this.getList();
    },
  },
};
</script>

<style scoped lang="scss">
@import "../../assets/styles/element-variables.scss";
@import "../../layout/portal/components/index.scss";
// 因为涉及自定义主题颜色 将搜索列表放在本页面 
.note-card {
  max-width: 1443px;
  margin: 0 auto;
  background: #fff;
  // background-color: #f5f9fa;
  min-height: calc(100vh - 150px);
  border: 0px;

  ::v-deep .el-card__body {
    margin: 0;
    padding: 0;
  }

  .home-card-body {
    display: flex;
    flex-direction: column;

    a {
      width: 100%;
      font-size: 14px;
      font-weight: 500;
      color: #606266;

      :hover {
        color: $--color-primary;
      }

      .notice {
        padding: 20px 0px 20px 0px;
        width: 100%;
        height: 198px;
        position: relative;
        border-bottom: 2px solid #e7e7e7;

        .noticeContent {
          height: 100%;
          display: -webkit-box;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          display: flex;
          justify-content: space-between;

          .contentFont {
            width: 62%;
            padding: 10px 0 10px 0;

            .notice-title {
              overflow: hidden;
              text-overflow: ellipsis;
              word-break: break-all;
              white-space: nowrap;
              flex-basis: 70%;
              margin-bottom: 15px;

              .content {
                width: 70%;
                display: inline;
                font-size: 18px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                color: #333333;
                text-overflow: ellipsis;
                word-break: break-all;
                white-space: nowrap;
              }

              .content {
                ::v-deep span {
                  color: red;
                }
              }
            }

            .content {
              text-overflow: -o-ellipsis-lastline;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              -webkit-box-orient: vertical;
              width: 100%;

              ::v-deep span {
                color: red;
              }
            }
          }

          .noimgs {
            width: 25%;
            height: 100%;
            margin-right: 10px;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .imgs {
            width: 25%;
            height: 100%;
            margin-right: 10px;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .notice-time {
            margin-top: 10px;
            width: 10%;
            text-align: center;
          }
        }

        .more {
          height: 38px;
          position: absolute;
          bottom: 40px;
          right: 20px;
          background: #4c86e3;
          border: 1px solid #4c86e3;
          color: #fff;
        }
      }
    }
  }
}
</style>
