<template>
  <div class="tab-content">
    <!-- 专线受理流程查询 -->
    <div class="query-header">
      <span class="query-title">专线受理流程查询</span>
    </div>

    <!-- 查询条件 -->
    <div class="search-wrapper">
      <el-form ref="form" :model="query" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="地市">
              <el-select v-model="query.city" placeholder="全部" clearable size="small" style="width: 100%;">
                <el-option label="全部" value=""></el-option>
                <el-option label="深圳" value="深圳"></el-option>
                <el-option label="广州" value="广州"></el-option>
                <el-option label="东莞" value="东莞"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="专线受理时间">
              <el-date-picker
                v-model="query.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                size="small"
                style="width: 100%;">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户名称">
              <el-input v-model="query.customerName" clearable placeholder="请输入客户名称" size="small"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="订单号">
              <el-input v-model="query.openCode" clearable placeholder="请输入订单号" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="装机地址">
              <el-input v-model="query.installAddress" clearable placeholder="请输入装机地址" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="业务号码">
              <el-input v-model="query.businessCode" clearable placeholder="请输入业务号码" size="small"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24" style="text-align: right;">
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">查询</el-button>
              <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
              <el-button type="success" icon="el-icon-download" size="small" @click="exportData">导出</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-wrapper">
      <div class="table-con">

        <el-table :data="tableData" border stripe>
          <el-table-column prop="orderNo" label="订单号"></el-table-column>
          <el-table-column prop="city" label="地市"></el-table-column>
          <el-table-column prop="productName" label="商品名称"></el-table-column>
          <el-table-column prop="customerName" label="客户名称"></el-table-column>
          <el-table-column prop="acceptTime" label="受理时间"></el-table-column>
          <el-table-column prop="installAddress" label="装机地址"></el-table-column>
          <el-table-column prop="businessCode" label="业务号码"></el-table-column>
          <el-table-column label="操作" align="center" header-align="center">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="viewDetail(scope.row)">查看流程详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="query.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="query.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "GxsxTab",
  data() {
    return {
      query: {
        pageNum: 1,
        pageSize: 10,
        city: '',
        dateRange: ['2025-06-03', '2025-06-10'],
        installAddress: '',
        openCode: '',
        customerName: '',
        businessCode: ''
      },
      total: 0,
      tableData: [
        {
          orderNo: '250117001710700',
          city: '深圳',
          productName: '专网云网大网（全国通用）',
          customerName: '公安局',
          acceptTime: '2025-01-17',
          installAddress: '深圳市南山区科技园',
          businessCode: '173*********'
        }
      ]
    }
  },
  methods: {
    handleQuery() {
      console.log('共享随行专网查询', this.query);
      this.$message.success('查询成功');
    },

    resetQuery() {
      this.query = {
        pageNum: 1,
        pageSize: 10,
        city: '',
        dateRange: ['2025-06-03', '2025-06-10'],
        installAddress: '',
        openCode: '',
        customerName: '',
        businessCode: ''
      };
    },

    exportData() {
      this.$message.success('导出成功');
    },

    viewDetail(row) {
      this.$message.info(`查看${row.orderNo}的详情`);
    },

    handleSizeChange(val) {
      this.query.pageSize = val;
      this.handleQuery();
    },

    handleCurrentChange(val) {
      this.query.pageNum = val;
      this.handleQuery();
    },


  }
}
</script>

<style lang="scss" scoped>
.tab-content {
  padding: 0;
  height: 100%;
  background: #f5f7fa;

  .query-header {
    padding: 15px 24px;
    margin: 20px 20px 0 20px;
    background: #fff;
    border-radius: 4px 4px 0 0;
    border: 1px solid #e4e7ed;
    border-bottom: none;
    position: relative;

    .query-title {
      font-size: 14px;
      font-weight: 500;
      color: #303133;
      position: relative;
      padding-left: 12px;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        background: #409eff;
        border-radius: 2px;
      }
    }
  }

  .search-wrapper {
    padding: 20px 24px;
    margin: 0 20px;
    background: #fff;
    border: 1px solid #e4e7ed;
    border-top: none;

    ::v-deep .el-form-item {
      margin-bottom: 18px;
    }

    ::v-deep .el-form-item__label {
      font-weight: 400;
      color: #606266;
      font-size: 14px;
      line-height: 32px;
    }

    ::v-deep .el-input__inner {
      font-size: 13px;
      height: 32px;
      line-height: 32px;
    }

    ::v-deep .el-select .el-input__inner {
      font-size: 13px;
      height: 32px;
      line-height: 32px;
    }

    ::v-deep .el-date-editor .el-input__inner {
      font-size: 13px;
      height: 32px;
      line-height: 32px;
    }

    ::v-deep .el-button {
      font-size: 13px;
      height: 32px;
      padding: 8px 15px;
    }

    ::v-deep .el-row {
      margin-bottom: 0;
    }

    ::v-deep .el-row:last-child {
      margin-bottom: 0;
    }
  }

  .table-wrapper {
    margin: 20px 20px 20px 20px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #e4e7ed;

    .table-con {
      padding: 24px;
    }

    .flex-between {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .pagination-wrapper {
      padding: 20px 24px;
      text-align: right;
      border-top: 1px solid #e4e7ed;
    }
  }
}
</style>
