<!--<template>-->
<!--  <div class="app-container" style="width:1443px;margin:0 auto">-->
<!--    <el-breadcrumb separator="/" style="margin:20px 0">-->
<!--      <el-breadcrumb-item :to="{ path: '/portal/home' }" replace-->
<!--        >门户首页</el-breadcrumb-item-->
<!--      >-->
<!--      <el-breadcrumb-item-->
<!--        ><a :href="`/portal/home/<USER>/${$route.params.type}`"-->
<!--          >{{title}}</a-->
<!--        ></el-breadcrumb-item-->
<!--      >-->
<!--      <el-breadcrumb-item><a>详情</a></el-breadcrumb-item>-->
<!--    </el-breadcrumb>-->
<!--    <el-card class="d-card" shadow="never" v-loading="loading">-->
<!--      <div slot="header">-->
<!--        <h2 class="d-title">{{ data.noticeTitle||data.title}}</h2>-->
<!--      </div>-->
<!--      <p class="d-desc">-->
<!--        <span class="d-type">类型：{{ data.noticeTypeName }}</span>-->
<!--        <span>时间：{{ parseTime(data.createDate, "{y}-{m}-{d}") }}</span>-->
<!--      </p>-->
<!--      <div class="ql-container">-->
<!--        <div class="ql-snow ql-editor" v-html="data.noticeContent" />-->
<!--      </div>-->
<!--    </el-card>-->
<!--  </div>-->
<!--</template>-->

<!--<script>-->

<!--import AppLink from "@/components/Link";-->
<!--import { selectNoticeByNoticeId,termById } from "@/api/system/notice";-->
<!--import { findList } from "/src/api/system/programaType";-->
<!--import "quill/dist/quill.core.css";-->
<!--import "quill/dist/quill.snow.css";-->
<!--import "quill/dist/quill.bubble.css";-->

<!--export default {-->
<!--  name: "detail",-->
<!--  components: { AppLink },-->
<!--  data() {-->
<!--    return {-->
<!--      loading: false,-->
<!--      noticeId: undefined,-->
<!--      data: {},-->
<!--      title: ''-->
<!--    };-->
<!--  },-->
<!--  async created() {-->
<!--    let programaList = []-->
<!--    await findList({ tenantId: this.$store.getters.customParam.tenantId }).then((response) => {-->
<!--      programaList = response;-->
<!--    });-->
<!--    this.noticeId = this.$route.params.id;-->
<!--    let type = this.$route.params.type;-->
<!--    if(programaList.filter(item => item.programaTypeCode === type)) {-->
<!--      this.title = programaList.find(item => item.programaTypeCode === type).programaTypeName-->
<!--      this.getOne();-->
<!--    } else {-->
<!--      this.title = type-->
<!--      this.getEsOne();-->
<!--    }-->
<!--  },-->
<!--  methods: {-->
<!--    getOne() {-->
<!--      this.loading = true;-->
<!--      selectNoticeByNoticeId({-->
<!--        noticeId: this.noticeId,-->
<!--      }).then((r) => {-->
<!--        this.loading = false;-->
<!--        this.data = r.data;-->
<!--      });-->
<!--    },-->
<!--    getEsOne() {-->
<!--      this.loading = true;-->
<!--      termById({-->
<!--        id: this.noticeId,-->
<!--      }).then((r) => {-->
<!--        this.loading = false;-->
<!--        this.data = r.data.nameValuePairs;-->
<!--      });-->
<!--    },-->
<!--  },-->
<!--};-->
<!--</script>-->

<!--<style scoped lang="scss">-->
<!--.d-card {-->
<!--  border: 0px;-->
<!--  min-height: calc(100vh - 150px);-->
<!--  padding: 0px 50px;-->
<!--  .d-title {-->
<!--    text-align: center;-->
<!--  }-->
<!--  .d-desc {-->
<!--    text-align: right;-->
<!--    margin: 5px 10px;-->
<!--    .d-type {-->
<!--      padding: 0px 20px;-->
<!--    }-->
<!--  }-->
<!--}-->
<!--</style>-->
