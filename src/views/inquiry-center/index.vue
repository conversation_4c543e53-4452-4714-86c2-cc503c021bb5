<template>
  <div class="app-container" style="height: calc(100vh - 94px)">
    <split-pane
      :min-percent="12"
      :max-percent="30"
      :default-percent="15"
      split="vertical"
      v-on:resize="resize"
      ref="splitPane"
    >
      <template slot="paneL">
        <el-card
          class="inquiry-center-card"
          shadow="never"
          body-style=""
          v-if="extPercent <= 3"
        >
          <span  @click="closeLeft" class="inquiry-center-fold">
            <span
              class="el-icon-caret-right ext-text "
              style="margin-right: 4px"
            />
            点击可展开</span
          >
        </el-card>
        <el-card
          class="inquiry-center-card"
          shadow="never"
          body-style=""
          v-if="extPercent > 3"
        >
          <div slot="header" class="clearfix">
            <span>
              <span
                class="el-icon-caret-left ext-text inquiry-center-fold"
                style="margin-right: 4px"
                @click="closeLeft"
              />
              {{ leftTitle }}</span
            >
            <el-button
              style="float: right; padding: 3px 0"
              type="text"
              icon="el-icon-refresh"
              @click="getLeftData"
              :loading="treeLoading"
              >刷新</el-button
            >
          </div>
          <el-tree
            :data="list"
            :props="defaultProps"
            @node-click="handleNodeClick"
            :render-content="renderContent"
            node-key="permissionId"
            ref="asyncTree"
            :default-expanded-keys="expandedId"
          >
          </el-tree>
        </el-card>
      </template>
      <template slot="paneR">
        <el-card class="inquiry-center-card-iframe" shadow="never">
          <el-tabs
            v-model="activeTab"
            type="card"
            closable
            style="height: 100%"
            @edit="handleTabsEdit"
          >
            <el-tab-pane
              :key="item.id"
              v-for="item in tabs"
              :label="item.title"
              :name="item.id"
              style="height: 100%"
            >
              <iframe
                :key="item.id + 'iframe'"
                v-once
                :src="item.menuUrl"
                frameborder="0"
                class="inquiry-center-iframe"
                @load="loadUrl"
              ></iframe>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </template>
    </split-pane>
  </div>
</template>

<script>
import { getRouters } from "@/api/menu";
import { arrayToTree } from "@/utils";
import { mapState } from "vuex";
export default {
  name: "inquiryCenter",
  data() {
    return {
      list: [],
      defaultProps: {
        children: "children",
        label: "permissionName",
      },
      treeLoading: false,
      leftTitle: "数据加载中",
      icon: "",
      expandedId: [],
      percent: 15,
      extPercent: 15,
    };
  },

  computed: {
    ...mapState({
      tabs: (state) => state.inquiryCenter.tabs,
    }),
    activeTab: {
      get() {
        return this.$store.state.inquiryCenter.activeTab;
      },
      set(val) {
        this.$store.dispatch("inquiryCenter/setActiveTab", val);
      },
    },
  },
  created() {
    this.getLeftData();
  },
  methods: {
    handleNodeClick(data) {
      this.$store.dispatch("inquiryCenter/addTab", {
        title: data.permissionName,
        name: data.permissionId,
        id: data.permissionId,
        menuUrl: data.uri,
      });
    },
    handleTabsEdit(targetName, action) {
      if (this.tabs.length <= 1) {
        this.msgError("标签不允许删除完！");
        return;
      }
      this.$store.dispatch("inquiryCenter/delTab", {
        id: targetName,
      });
    },
    getLeftData() {
      this.treeLoading = true;
      getRouters({
        permissionScope: "portalQuery",
      }).then((res) => {
        const resList = res.data
        resList.forEach((element,index) => {
          if(element.permissionVisible === 'hide') {
            resList.splice(index, 1)
          }
        });
        const list = arrayToTree(resList, "children", "permissionId", "parentId");
        if (list && list.length > 0) {
          this.leftTitle = list[0].permissionName;
          this.list = list[0].children;
          this.expandedId = this.list.map((v) => v.permissionId);
          this.icon = list[0].icon;
        } else {
          this.leftTitle = "暂无数据";
        }
        if (this.list.length > 0) {
          if (this.list[0].children !== null) {
            this.$store.dispatch("inquiryCenter/addTab", {
              title: this.list[0].children[0].permissionName,
              name: this.list[0].children[0].permissionId,
              id: this.list[0].children[0].permissionId,
              menuUrl: this.list[0].children[0].uri,
            });
          }
        }
        this.treeLoading = false;
      });
    },
    renderContent(h, { node, data, store }) {
      return (
        <span>
          <span style="margin-right: 4px;" class="ext-text">
            {data.icon && <svg-icon icon-class={data.icon} />}
          </span>
          {node.label}
        </span>
      );
    },
    loadUrl(e) {
      console.log(e);
    },
    resize(e) {
      this.extPercent = e < 15 ? 15 : e;
      this.$refs.splitPane.percent = e < 15 ? 15 : e;
    },
    closeLeft() {
      if (this.$refs.splitPane.percent === 3) {
        this.$refs.splitPane.percent = this.percent;
        this.extPercent = this.percent;
      } else {
        this.percent = this.$refs.splitPane.percent;
        this.$refs.splitPane.percent = 3;
        this.extPercent = 3;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.inquiry-center-card {
  height: 100%;
}
.inquiry-center-fold {
  cursor: pointer;
}
.inquiry-center-card-iframe {
  height: 100%;
  ::v-deep .el-card__body {
    padding: 4px;
  }
  ::v-deep .el-card__body {
    height: 100%;
  }
  ::v-deep .el-tabs__content {
    height: 100%;
  }
  ::v-deep .el-tabs__header {
    margin: 0px;
  }
  .inquiry-center-iframe {
    width: 100%;
    height: 100%;
  }
}
</style>

