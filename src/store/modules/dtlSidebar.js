// 列表及详情页左侧sidebar显隐控制
const state = {
    showSidebar: false
}

const mutations = {
    showDtlSidebar (state) {
        state.showSidebar = true
    },
    hideDtlSidebar (state) {
        state.showSidebar = false
    }
}

const actions = {
    showDtlSidebar({commit}) {
        commit('showDtlSidebar')
    },
    hideDtlSidebar({commit}) {
        commit('hideDtlSidebar')
    },
}

export default {
    namespaced: true,
    state,
    mutations,
    actions
  }