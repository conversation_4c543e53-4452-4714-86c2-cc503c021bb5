<template>
  <div class="app-container">
    <el-card shadow="never">
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        :inline="true"
        @submit.native.prevent
      >
        <el-form-item label="租户" v-if="$store.getters.customParam.userType === 'admin'">
          <el-select
            v-model="queryParams.tenantId"
            placeholder="请输入租户名称搜索并选择"
            size="small"
            style="width: 240px"
            filterable
            remote
            :remote-method="getTenantList"
            :loading="getTenantLoading"
            @change="tenantChange"
          >
            <el-option
              v-for="item in tenantList"
              :key="item.tenantId"
              :label="item.tenantName"
              :value="item.tenantId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="角色名称" prop="roleName">
          <el-input
            v-model="queryParams.roleName"
            placeholder="请输入角色名称"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
          >搜索
          </el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置
          </el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:role:add']"
          >新增
          </el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table
        v-loading="loading"
        :data="roleList"
        row-key="roleId"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        default-expand-all
      >
        <el-table-column
          label="角色名称"
          prop="roleName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="roleSort"
          label="排序"
        />
        <el-table-column
          label="创建时间"
          align="left"
          prop="createDateStr"
        />
        <el-table-column
          label="角色描述"
          prop="description"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="是否分配角色"
          prop="description"
          :show-overflow-tooltip="true"
          align="center"
        >
          <template slot-scope="scope">
            <span>
               <el-tag :type="scope.row.distributeFlag === 'yes'?'success':''">
               {{ parseDistributeFlag(scope.row.distributeFlag) }}
            </el-tag>
              <!-- <span v-if="scope.row.distributeFlag">{{ parseDistributeFlag(scope.row.distributeFlag) }}</span> -->
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope" v-if="!scope.row.isOp">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:role:edit']"
              v-if="scope.row.tenantId === $store.getters.customParam.tenantId || $store.getters.customParam.userType === 'admin'"
            >修改
            </el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:role:remove']"
              v-if="scope.row.tenantId === $store.getters.customParam.tenantId || $store.getters.customParam.userType === 'admin'"
            >删除
            </el-button
            >
            <el-button
              v-if="(scope.row.tenantId === $store.getters.customParam.tenantId
                && $store.getters.customParam.userType === 'admin')
                && scope.row.distributeFlag === 'no'
                && scope.row.roleName !== '系统管理员'"
              size="mini"
              type="text"
              icon="el-icon-unlock"
              @click="handleDistribute(scope.row)"
              v-hasPermi="['system:role:distribute']"
            >分配
            </el-button>
            <el-button
              v-if="(scope.row.tenantId === $store.getters.customParam.tenantId
                && $store.getters.customParam.userType === 'admin')
                && scope.row.distributeFlag === 'yes'
                && scope.row.roleName !== '系统管理员'"
              size="mini"
              type="text"
              icon="el-icon-lock"
              @click="handleDistribute(scope.row)"
              v-hasPermi="['system:role:distribute']"
            >不分配
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改角色配置对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="open"
        width="500px"
        append-to-body
        v-dialogDrag
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="角色名称" prop="roleName">
            <el-input v-model="form.roleName" placeholder="请输入角色名称"/>
          </el-form-item>
          <el-form-item
            label="角色归属"
            prop="roleScope"
            placeholder="请选择角色归属"
          >
            <el-select v-model="form.roleScope">
              <el-option
                v-for="item in scopeOptions"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictValue"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="显示排序" prop="roleSort">
            <el-input-number
              v-model="form.roleSort"
              controls-position="right"
              :min="0"
            />
          </el-form-item>
          <el-form-item label="角色编码" prop="roleCode">
            <el-input
              v-model="form.roleCode"
              placeholder="请输入角色编码"/>

          </el-form-item>
          <el-form-item label="租户">
            <el-input
              v-model="form.tenantName"
              placeholder="租户"
              maxlength="50"
              disabled
            />
          </el-form-item>
          <el-form-item label="菜单权限">
            <el-checkbox
              v-model="menuExpand"
              @change="handleCheckedTreeExpand($event, 'menu')"
            >展开/折叠
            </el-checkbox
            >
            <el-checkbox
              v-model="menuNodeAll"
              @change="handleCheckedTreeNodeAll($event, 'menu')"
            >全选/全不选
            </el-checkbox
            >
            <el-checkbox
              v-model="menuCheckStrictly"
              @change="handleCheckedTreeConnect($event, 'menu')"
            >父子联动
            </el-checkbox
            >
            <el-tree
              class="tree-border"
              :data="permissionOptions"
              show-checkbox
              ref="menu"
              :check-strictly=true
              @check="hanleCheck"
              node-key="permissionId"
              empty-text="暂无数据"
              :props="defaultProps"
            ></el-tree>
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              v-model="form.description"
              type="textarea"
              placeholder="请输入内容"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="submitLoading"
          >确 定
          </el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import {
  findRoleListByScope,
  getRole,
  delRole,
  addRole,
  updateRole,
  changeRoleStatus,
  queryRoleMenu
} from "@/api/system/role";
import {list as tenantList} from "@/api/system/tenant";
import {treeselect as menuTreeselect} from "@/api/system/menu";
import {arrayToTree} from "@/utils";

export default {
  name: "Role",
  data() {
    return {
      menuCheckStrictly: true,
      // 遮罩层
      loading: true,
      submitLoading: false,
      getTenantLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      menuExpand: false,
      menuNodeAll: false,
      // 日期范围
      dateRange: [],
      // 状态数据字典
      statusOptions: [],
      // 菜单列表
      permissionOptions: [],
      tenantList: [],
      scopeOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        roleName: undefined,
        roleCode: undefined,
        roleStatus: undefined,
        tenantId: this.$store.getters.customParam.tenantId,
        tenantName: this.$store.getters.customParam.tenantName,
      },
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "permissionName"
      },
      // 表单校验
      rules: {
        roleName: [
          {required: true, message: "角色名称不能为空", trigger: "blur"}
        ],
        roleCode: [
          {required: true, message: "权限字符不能为空", trigger: "blur"}
        ],
        roleScope: [
          {required: true, message: "请选择角色归属", trigger: "blur"}
        ],
        roleSort: [
          {required: true, message: "角色排序不能为空", trigger: "blur"},
        ]
      }
    };
  },
  created() {
    this.getTenantList();
    this.getList();
    this.getDicts("sys_normal_disable").then(response => {
      this.statusOptions = response.data;
    });
  },
  methods: {
    getTenantList(tenantName) {
      this.getTenantLoading = true;
      let query = {}
      if (tenantName !== undefined && tenantName !== '') {
        query.tenantName = tenantName
        query.tenantId = undefined
      } else {
        query.tenantId = this.queryParams.tenantId
      }
      tenantList(query).then((response) => {
        this.tenantList = response.data;
      }).finally(() => {
        this.getTenantLoading = false;
      });
    },
    /** 查询角色列表 */
    getList() {
      this.loading = true;
      findRoleListByScope(
        this.addDateRange(this.queryParams, this.dateRange)
      ).then(r => {
        this.getDicts("scope").then(response => {
          this.scopeOptions = response.data;
          const newData = response.data.map(v => {
            v["children"] = r.data.filter(i => i.roleScope === v.dictValue);
            v["roleName"] = v.dictLabel;
            v["roleId"] = v.dictDataId;
            v["isOp"] = true;
            v["roleSort"] = v.dictSort;
            return v;
          });
          this.roleList = [];
          this.roleList = newData;
          this.loading = false;
        });
        this.roleList = r.data.records;
        this.loading = false;
      });
    },
    /** 查询菜单树结构 */
    getMenuTreeselect() {
      menuTreeselect(this.queryParams).then(response => {
        this.permissionOptions = arrayToTree(
          response.data,
          "children",
          "permissionId",
          "parentId"
        );
      });
    },
    // 所有菜单节点数据
    getMenuAllCheckedKeys() {
      // 目前被选中的菜单节点
      let checkedKeys = this.$refs.menu.getCheckedKeys();
      // 半选中的菜单节点
      let halfCheckedKeys = this.$refs.menu.getHalfCheckedKeys();
      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
      return checkedKeys;
    },
    /** 根据角色ID查询菜单树结构 */
    getRoleMenuTreeselect(roleId) {
      return queryRoleMenu(roleId).then(response => {
        return response;
      });
    },
    // 角色状态修改
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$confirm(
        '确认要"' + text + '""' + row.roleName + '"角色吗?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(function () {
          return changeRoleStatus(row.roleId, row.status);
        })
        .then(() => {
          this.msgSuccess(text + "成功");
        })
        .catch(function () {
          row.status = row.status === "0" ? "1" : "0";
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      if (this.$refs.menu !== undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      this.menuExpand = false;
      this.menuNodeAll = false;
      this.form = {
        roleId: undefined,
        roleName: undefined,
        roleCode: undefined,
        roleDesc: undefined,
        roleSort: 0,
        roleStatus: "valid",
        permissionIds: [],
        roleScope: undefined,
        tenantName: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 树权限（展开/折叠）
    handleCheckedTreeExpand(value, type) {
      let treeList = this.permissionOptions;
      for (let i = 0; i < treeList.length; i++) {
        this.$refs.menu.store.nodesMap[treeList[i].permissionId].expanded = value;
      }
    },
    // 树权限（全选/全不选）
    handleCheckedTreeNodeAll(value, type) {
      this.$refs.menu.setCheckedNodes(value ? this.permissionOptions : [])

    },
    // 树权限（父子联动）
    handleCheckedTreeConnect(value, type) {
      this.menuCheckStrictly = value ? true : false;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getMenuTreeselect();
      this.open = true;
      this.title = "添加角色";
      this.form.tenantName = this.queryParams.tenantName;
      this.form.tenantId = this.queryParams.tenantId;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getMenuTreeselect();
      const roleId = row.roleId;
      const roleMenu = this.getRoleMenuTreeselect(roleId);
      getRole(roleId).then(response => {
        this.form = response.data;
        this.open = true;
        this.$nextTick(() => {
          roleMenu.then(res => {
            let checkedKeys = res.data.permissions;
            this.$refs.menu.setCheckedKeys(checkedKeys.map(v => v.permissionId));
          });
        });
        this.title = "修改角色";
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.submitLoading = true;
          if (this.form.roleId !== undefined) {
            this.form.permissionIds = this.getMenuAllCheckedKeys();
            updateRole(this.form).then(response => {
              this.submitLoading = false;
              if (response.success) {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              } else {
                this.$message.error(response.message)
                return
              }
            });
          } else {
            this.form.permissionIds = this.getMenuAllCheckedKeys();
            addRole(this.form).then(response => {
              this.submitLoading = false;
              if (response.success) {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              } else {
                this.$message.error(response.message);
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const _this = this
      if (row.roleName === '默认角色' && row.roleScope === 'system') {
        this.msgError("默认角色不允许被删除！");
        return
      }
      this.$confirm(
        "是否确认删除角色名称为（" + row.roleName + "）的数据项?",
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(function () {
          delRole({roleId: row.roleId}).then(res => {
            if (res.success) {
              _this.msgSuccess("删除成功")
            } else {
              _this.msgError(res.message)
            }
            _this.getList();
          });
        });
    },
    handleDistribute(row) {
      const btnText = row.distributeFlag === "yes" ? '不分配' : '分配';
      this.$confirm(
        row.distributeFlag === "yes" ? '确认不分配' + row.roleName + '吗?' : '确认要分配' + row.roleName + '吗?',
        "警告",
        {
          confirmButtonText: btnText,
          cancelButtonText: "取消",
          type: "warning",
          beforeClose: (action, instance, done) => {
            if (action === "confirm") {
              instance.confirmButtonLoading = true;
              instance.confirmButtonText = btnText + "处理中...";
              updateRole({
                roleId: row.roleId,
                distributeFlag: row.distributeFlag === "yes" ? "no" : "yes",
              })
                .then((response) => {
                  done();
                  if (response.success) {
                    this.msgSuccess(btnText + "成功");
                    this.getList();
                  } else {
                    this.$message.error(response.message);
                  }
                  instance.confirmButtonLoading = false;
                })
                .catch((e) => {
                  done();
                  instance.confirmButtonLoading = false;
                });
            } else {
              done();
            }
          },
        }
      ).catch(function () {
      });
    },
    tenantChange(tenantId) {
      if (tenantId !== '') {
        this.queryParams.tenantName = this.tenantList.find(item => item.tenantId === tenantId).tenantName
      }
      this.handleQuery()
    },
    parseDistributeFlag(val) {
      if (val === 'yes') {
        return '已分配'
      } else return '未分配'
    },
    hanleCheck(data, node) {
      const _this = this
      // 获取当前节点是否被选中
      const isChecked = this.$refs.menu.getNode(data).checked
      // 如果当前节点被选中，则遍历上级节点和下级子节点并选中，如果当前节点取消选中，则遍历下级节点并取消选中
      if (isChecked) {
        // 判断是否有上级节点，如果有那么遍历设置上级节点选中
        data.parentId && setParentChecked(data.parentId)
        // 判断该节点是否有下级节点，如果有那么遍历设置下级节点为选中
        if (_this.menuCheckStrictly) {
          data.children && setChildreChecked(data.children, true)
        }
      } else {
        // 如果节点取消选中，则取消该节点下的子节点选中
        data.children && setChildreChecked(data.children, false)
      }
      function setParentChecked(parentId) {
        // 获取该id的父级node
        const parentNode = _this.$refs.menu.getNode(parentId)
        // 如果该id的父级node存在父级id则继续遍历
        parentNode && parentNode.data && parentNode.data.parentId && setParentChecked(parentNode.data.parentId)
        //  设置该id的节点为选中状态
        _this.$refs.menu.setChecked(parentId, true)
      }
      function setChildreChecked(node, isChecked) {
        node.forEach(item => {
          item.children && setChildreChecked(item.children, isChecked)
          _this.$refs.menu.setChecked(item.permissionId, isChecked)
        })
      }
    }
  }
};
</script>
