<template>
  <el-dialog
    :title="title"
    :visible.sync="openNetWorkDialog"
    width="80%"
    @close="handleClose">
      <div class="table-wrapper box-bg" >

        <div class="table-content">
          <el-table  :data="resultList" style="width: 100%" :row-class-name="tableRowStripeClassName">
            <el-table-column label="自然客户ID" align="center" prop="customId" />
            <el-table-column label="自然客户名称" align="left" prop="customName" min-width="110">
              <template slot-scope="scope">
                <span :title="scope.row.customName">{{ sliceText(scope.row.customName) }}</span>
              </template>
            </el-table-column>

            <el-table-column label="设备号码" align="center" prop="deviceNumber" />
            <el-table-column label="业务类型" align="left" prop="serviceKindName" />
            <el-table-column label="商机推送的业务类型" align="center" prop="propertyValue" />
            <el-table-column label="业务收入" align="center" prop="totalFee" />
            <el-table-column label="开户时间" align="center" prop="openDate" />

          </el-table>
          <div class="flex-end" style="margin-top: 0.14rem;">
            <el-pagination @current-change="handleCurrentChange"
                           :current-page.sync="queryParams.pageNum" :page-size="queryParams.pageSize" layout="total, prev, pager, next"
                           :total="total">
            </el-pagination>
          </div>
        </div>
      </div>
  </el-dialog>
</template>
<script>
import {resultError} from "@/utils/messageText";
import  { getPageList }  from '@/api/sw/targetMarketing/targetNetWork'
import UniTable from '@/components/UniTable/UniTable.vue'
export default {
  // import引入的组件需要注入到对象中才能使用
  components: { UniTable },
  name: 'detail',
  props: {
    openNetWorkDialog: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    paramInfo: {
      dataType: {
        type: String,
        default: ''
      },
      city: {
        type: String,
        default: ''
      },
      strategyId: {
        type: String,
        default: ''
      },
      contactor: {
        type: String,
        default: '靶向营销'
      },
      beginDate: {
        type: String,
        default: ''
      },
      endDate: {
        type: String,
        default: ''
      }
    },
  },
  data() {
    return {
      resultList: [],//商机或项目列表
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0
    }
  },
  watch: {
    paramInfo: {
      deep: true,
      handler(newValue,oldVal){
        this.queryParams.pageNum = 1;//当重新打开时设置第一页,不然数据多的如果上次翻到第大页,再点击条数少的打开会无数据
        console.info("info："+JSON.stringify(this.paramInfo))
        this.getDetailList()
      }
    },
  },
  created() {

  },
  mounted() {

  },
  // 监听属性类似于data概念
  computed: {},
  // 方法集合
  methods: {
    tableRowStripeClassName({ row, rowIndex }) {
      let rowStyle = "row-bg-high";
      if (rowIndex % 2 === 0) {
        rowStyle = "row-bg-normal";
      }
      return rowStyle;
    },
    getDetailList() {
      let superiorPolicyCode = this.paramInfo.strategyId;
      let type = this.paramInfo.dataType
      let city = this.paramInfo.city
      // let dataType = this.paramInfo.dataType;
      this.queryParams = {...this.paramInfo,...this.queryParams,superiorPolicyCode,type,city}
      let pageNum = this.queryParams.pageNum;
      this.resultList = [];
      this.total = 0
      getPageList(this.queryParams).then(response => {
        if (response.code === '1') {
          this.resultList = response.data.records;
          this.total = response.data.total;
          this.queryParams.pageNum = pageNum;
        }else{
          this.$message.error(resultError);
        }
      });
    },
    // 当前页码
    handleCurrentChange(page) {
      this.queryParams.pageNum = page;
      this.getDetailList();
    },
    sliceText(text){
      return text.length > 20 ? text.slice(0, 20) + '...' : text;
    },
    // 搜索
    handleQuery() {
      console.log(this.search, 'search')
    },
    handleClose() {
      this.$emit('update-open-network-dialog', false)
    },
  }
}
</script>
<style lang="scss" scoped>
.el-dialog__body {
  padding: 0;
}
::v-deep .el-dialog__body {
  padding: 0;
}
.header {
  font-size: 0.16667rem;

  padding: 0.16667rem;
  background-color: #fff;
  width: 100%;
  margin-top: 0.16667rem;
  border-radius: 0.04167rem;

  i {
    color: #3377ff;
  }
}


.m-con {
  margin: 0.08333rem;
  padding: 0.08333rem;
  background-color: #fff;

  .dict_search {
    width: 30%;
    padding-bottom: 0.08333rem;
  }
}

.table-content{
  padding: 0.1rem 0.2rem;
}

.action-button {
  padding: 0;
  font-size: 0.125rem;
  min-width: 0.125rem;
  background-color: transparent;
  border: none;
  cursor: pointer;
  color: #3377FF;
  font-family: PingFangSC-Regular;
  line-height: 0.18rem;
  font-weight: 400;
}
</style>
