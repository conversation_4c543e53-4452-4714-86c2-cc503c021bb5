<template>
    <div class="portal-body">
        <div class="analysis">
            <div class="search-wrapper box-bg">
                <el-form ref="form" :model="formQuery" label-width="1rem">
                    <el-row>
                        <el-col :span="7">
                            <el-form-item label="账期">
                                <el-col>
                                    <el-date-picker v-model="formQuery.month" type="month" placeholder="选择月">
                                    </el-date-picker>
                                </el-col>
                            </el-form-item>
                        </el-col>
                        <el-col :span="7">
                            <el-form-item label="所属行业">
                                <el-col>
                                    <el-select v-model="formQuery.industry" placeholder="请选择" clearable>
                                        <el-option v-for="item in industryList" :key="item.industryId"
                                            :label="item.industryName" :value="item.industryId">
                                        </el-option>
                                    </el-select>
                                </el-col>
                            </el-form-item>
                        </el-col>
                        <el-col :span="7">
                            <el-form-item label="业务大类">
                                <el-col>
                                    <el-select v-model="formQuery.category" placeholder="请选择" clearable>
                                        <el-option v-for="item in categoryList" :key="item.categoryId"
                                            :label="item.categoryName" :value="item.categoryId">
                                        </el-option>
                                    </el-select>
                                </el-col>
                            </el-form-item>
                        </el-col>
                        <el-col :span="7">
                            <el-form-item label="业务分类">
                                <el-col>
                                    <el-input v-model="formQuery.demand" clearable></el-input>
                                </el-col>
                            </el-form-item>
                        </el-col>
                        <el-col :span="7">
                            <el-form-item label="产品名称">
                                <el-col>
                                    <el-input v-model="formQuery.name" clearable></el-input>
                                </el-col>
                            </el-form-item>
                        </el-col>
                        <el-col :span="7">
                            <el-form-item label="是否打标">
                                <el-col>
                                    <el-select v-model="formQuery.marking" placeholder="请选择" clearable>
                                        <el-option v-for="item in markingList" :key="item.markingValue"
                                            :label="item.markingName" :value="item.markingValue">
                                        </el-option>
                                    </el-select>
                                </el-col>
                            </el-form-item>
                        </el-col>
                        <el-col :span="7">
                            <el-form-item label="区域支撑经理">
                                <el-col>
                                    <el-input v-model="formQuery.region" clearable></el-input>
                                </el-col>
                            </el-form-item>
                        </el-col>
                        <el-col :span="7">
                            <el-form-item label="地市支撑经理">
                                <el-col>
                                    <el-input v-model="formQuery.prefecture" clearable></el-input>
                                </el-col>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <div class="btn-group">
                        <el-button @click="resetForm">重置</el-button>
                        <el-button type="primary" @click="conditionQuery">查询</el-button>
                    </div>
                </el-form>
            </div>

            <div class="table-wrapper box-bg">
                <div class="flex-between" style="padding: 0 0.2rem;">
                    <div class="table-title">产品列表</div>
                    <div>
                        <el-button size="small" icon="el-icon-upload2">导出</el-button>
                        <el-button size="small" icon="el-icon-s-flag">批量打标</el-button>
                        <el-button size="small" icon="el-icon-s-promotion">推送</el-button>
                        <el-button type="primary" size="small" @click="goAdd()" icon="el-icon-plus">新增</el-button>
                    </div>
                </div>
                <el-divider></el-divider>
                <div class="table-con">
                    <el-table :data="displayedTableData" style="width: 100%">
                        <el-table-column type="selection">
                        </el-table-column>
                        <el-table-column prop="id" label="产品ID">
                        </el-table-column>
                        <el-table-column prop="name" label="产品名称">
                        </el-table-column>
                        <el-table-column prop="industry" label="所属行业">
                        </el-table-column>
                        <el-table-column prop="territory" label="细分领域">
                        </el-table-column>
                        <el-table-column prop="classification" label="业务分类">
                        </el-table-column>
                        <el-table-column prop="category" label="业务大类">
                        </el-table-column>
                        <el-table-column prop="region" label="区域支撑经理">
                        </el-table-column>
                        <el-table-column prop="prefecture" label="地市支撑经理">
                        </el-table-column>

                        <el-table-column label="操作" width="220px" align="center">
                            <template slot-scope="scope">
                                <div class="action-buttons flex-center">
                                    <button class="action-button" type="button" @click="goDetail(scope.row)">查看</button>
                                    <span class="separator"></span>
                                    <button class="action-button" type="button" @click="goUpdate(scope.row)">修改</button>
                                    <span class="separator"></span>
                                    <button class="action-button" type="button"
                                        @click="deleteProduct(scope.row)">删除</button>
                                    <span class="separator"></span>
                                    <button class="action-button" type="button"
                                        @click="showMakingDialog(scope.row)">打标</button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>

                    <div class="flex-end" style="margin-top: 0.14rem;">
                        <!-- <span class="demonstration"> 第 {{ (currentPage - 1) * pageSize + 1 }}-{{ Math.min(currentPage *pageSize,productData.length) }} 条/总共{{ productData.length }}条</span> -->
                        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                            :current-page.sync="currentPage1" :page-size="100" layout="total, prev, pager, next"
                            :total="1000">
                        </el-pagination>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>

<style scoped lang="less"></style>
<style>
.el-message {
    font-size: 0.2rem;
}
</style>
<script>
import {deleteCancel, deleteSuccess} from "@/utils/messageText";

export default {
    name: 'index',
    data() {
        return {
            formQuery: {
                industry: '',
                category: '',
                demand: '',
                name: '',
                marking: '',
                region: '',
                prefecture: '',
                month: ''
            },
            productData: [{
                id: '001',
                name: '数字员工',
                industry: '工业企业',
                territory: '化工',
                classification: '智能问答',
                category: '创新业务',
                region: 'li',
                prefecture: 'dddd'
            }, {
                id: '002',
                name: '能耗管控平台',
                industry: '工业企业',
                territory: '装备制造',
                classification: '能耗管理',
                category: '创新业务',
                region: 'li',
                prefecture: 'dddd'
            }, {
                id: '004',
                name: '需求管理平台',
                industry: '工业企业',
                territory: '生物医药',
                classification: '需求管理',
                category: '创新业务',
                region: 'li',
                prefecture: 'dddd'
            }, {
                id: '005',
                name: '数控平台',
                industry: '工业企业',
                territory: '钢铁冶金',
                classification: '数据中台',
                category: '创新业务',
                region: 'li',
                prefecture: 'dddd'
            }],
            currentPage: 1,
            pageSize: 2,
            industryList: [{
                industryId: 1,
                industryName: "工业企业"
            }, {
                industryId: 2,
                industryName: "农业企业"
            }],
            categoryList: [{
                "categoryId": 1,
                "categoryName": "创新业务"
            }, {
                "categoryId": 2,
                "categoryName": "核心业务"
            }],
            markingList: [{
                "markingValue": 1,
                "markingName": "是"
            }, {
                "markingValue": 0,
                "markingName": "否"
            }],
            dialogVisible: false,
            dialogList: ''
        }
    },
    computed: {
        // 分页数据
        displayedTableData() {
            const startIndex = (this.currentPage - 1) * this.pageSize;
            const endIndex = startIndex + this.pageSize;
            return this.productData.slice(startIndex, endIndex);
        }
    },
    methods: {
        // 当前页码
        handleCurrentChange(page) {
            this.currentPage = page;
        },
        // 条件查询
        conditionQuery() {
            console.log(this.formQuery)
        },
        // 重置表单数据
        resetForm() {
            this.formQuery = {
                industry: '', // 省分行业
                category: '', // 业务需求分类
                demand: '', // 业务场景
                name: '', // 产品名称
                marking: '' // 是否达标
            };
        },
        //详情页
        goDetail() {
            this.$router.push("/sw/productDetail");
        },
        //弹窗
        handleClose(done) {
            this.$confirm('确认关闭？')
                .then(_ => {
                    done();
                    this.dialogList = ''
                })
                .catch(_ => { });
        },
        //打标
        showMakingDialog(item) {
            console.log('每一行的数据：', item)
            this.dialogVisible = true;
            this.dialogList = item
        },
        //提交打标信息
        submitMaking() {
            this.dialogVisible = false;
            console.log('打标信息：', this.dialogList)
        },
        //返回清空数据
        clearDialog() {
            this.dialogVisible = false;
            this.dialogList = ''
        },
        //新增产品
        goAdd() {
            this.$router.push("/sw/productAdd");
        },
        //删除产品
        deleteProduct(item) {
            console.log("要删除的数据信息：", item)
            this.$confirm('此操作将永久删除该产品, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message({
                    type: 'success',
                    message: deleteSuccess
                });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: deleteCancel
                });
            });
        },
        //修改
        goUpdate(item) {
            this.$router.push({
                path: "/sw/productAdd",
                query: {
                    productId: item.id
                }
            });
        }
    }
}
</script>
