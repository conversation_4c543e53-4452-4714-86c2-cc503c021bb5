<template>
  <section :class="hidennSidebar ? 'app-main-no-menu' : 'app-main'">
    <transition name="fade-transform" mode="out-in">
      <router-view />
    </transition>
  </section>
</template>

<script>
import { mapState } from "vuex";
export default {
  name: "AppMain",
  computed: {
    ...mapState({
      hidennSidebar: (state) => state.app.hidennSidebar,
    }),
  },
};
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: 100vh;
  width: 100%;
  position: relative;
  overflow: hidden;
  background-color: #f5f9fa;
}
.app-main::-webkit-scrollbar{
  display: none;
}
.app-main-no-menu {
  min-height: 100vh;
  width: 90%;
  position: relative;
  overflow: hidden;
  background-color: #f5f9fa;
  margin: auto;
}
.fixed-header + .app-main {
  padding-top: 140px;
}
.fixed-header + .app-main-no-menu {
  padding-top: 50px;
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
// .el-popup-parent--hidden {
//   .fixed-header {
//     padding-right: 15px;
//   }
// }
</style>
