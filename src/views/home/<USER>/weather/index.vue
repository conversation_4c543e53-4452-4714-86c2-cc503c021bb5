<template>
  <el-card
    :class="this.bgColor === true ? 'weather-card' : 'unshow-weather-card'"
    v-loading="Loading"
    shadow="never"
  >
    <div slot="header" v-if="show">
      <span class="card-title">
        <span>
          <i class="icon iconfont"></i>天气预报
        </span>
      </span>
    </div>
    <div class="hot home-card-body">
      <div
        id="he-plugin-standard"
        :class="
          list.icon === '100'
            ? 'he-plugin-standard'
            : '' || list.icon === '101'
            ? 'd101'
            : '' || list.icon === '104'
            ? 'd104'
            : '' || list.icon === '300'
            ? 'd300'
            : '' || list.icon === '303'
            ? 'd303'
            : '' || list.icon === '305'
            ? 'd305'
            : '' || list.icon === '306'
            ? 'd306'
            : '' || list.icon === '404'
            ? 'd404'
            : '' || list.icon === '500'
            ? 'd500'
            : '' || list.icon === '502'
            ? 'd502'
            : '' || list.icon === '150'
            ? 'd150'
            : ''
        "
      >
        <div style="margin: 10px 10px 0px 20px; color: white; font-size: 18px">{{ location }}</div>
        <div class="he-plugin-standard-content">
          <div class="leftBox">
            <div style="display: flex;justify-content: space-around;">
              <div class="imgs">
                <i class="qi-100"></i>
                <img
                  v-if="list.icon === '100'"
                  src="@/assets/weather/100-fill.svg"
                  width="50"
                  height="50"
                  class="sun"
                  onload="SVGInject(this)"
                />
                <img
                  v-if="list.icon === '101'"
                  src="@/assets/weather/101-fill.svg"
                  width="50"
                  height="50"
                  class="cloudy"
                  onload="SVGInject(this)"
                />
                <img
                  v-if="list.icon === '104'"
                  src="@/assets/weather/104-fill.svg"
                  width="50"
                  height="50"
                  class="cloudyDay"
                  onload="SVGInject(this)"
                />
                <img
                  v-if="list.icon === '150'"
                  src="@/assets/weather/150-fill.svg"
                  width="50"
                  height="50"
                  class="night"
                  onload="SVGInject(this)"
                />
                <img
                  v-if="list.icon === '300'"
                  src="@/assets/weather/300-fill.svg"
                  width="50"
                  height="50"
                  class="rains"
                  onload="SVGInject(this)"
                />
                <img
                  v-if="list.icon === '303'"
                  src="@/assets/weather/303.svg"
                  width="50"
                  height="50"
                  class="lighting"
                  onload="SVGInject(this)"
                />
                <img
                  v-if="list.icon === '305'"
                  src="@/assets/weather/305.svg"
                  width="50"
                  height="50"
                  class="littleRain"
                  onload="SVGInject(this)"
                />
                <!-- 中雨 -->
                <img
                  v-if="list.icon === '306'"
                  src="@/assets/weather/306.svg"
                  width="50"
                  height="50"
                  class="Moderate-Rain"
                  onload="SVGInject(this)"
                />
                <img
                  v-if="list.icon === '404'"
                  src="@/assets/weather/404.svg"
                  width="50"
                  height="50"
                  class="snow"
                  onload="SVGInject(this)"
                />
                <img
                  v-if="list.icon === '500'"
                  src="@/assets/weather/500-fill.svg"
                  width="50"
                  height="50"
                  class="fog"
                  onload="SVGInject(this)"
                />
                <img
                  v-if="list.icon === '502'"
                  src="@/assets/weather/502.svg"
                  width="50"
                  height="50"
                  class="haze"
                  onload="SVGInject(this)"
                />
              </div>
              <div class="weather-temperature">
                <div class="rain">体感温度</div>
                <div class="level">{{ list.feelsLike }}°</div>
              </div>
            </div>
            <div>
              <div class="future-rain">风向：{{ list.windDir }}</div>
            </div>
          </div>
          <div class="rightBox">
            <div class="today-weather">
              <div style="margin-left: 3px; color: white">今天</div>
              <div class="today-imgs">
                <img
                  v-if="list.icon === '100'"
                  src="@/assets/weather/100-fill.svg"
                  width="50"
                  height="50"
                  class="right-sun"
                  onload="SVGInject(this)"
                />
                <img
                  v-if="list.icon === '101'"
                  src="@/assets/weather/101-fill.svg"
                  width="50"
                  height="50"
                  class="right-cloudy"
                  onload="SVGInject(this)"
                />
                <img
                  v-if="list.icon === '104'"
                  src="@/assets/weather/104-fill.svg"
                  width="50"
                  height="50"
                  class="right-cloudyDay"
                  onload="SVGInject(this)"
                />
                <img
                  v-if="list.icon === '150'"
                  src="@/assets/weather/150-fill.svg"
                  width="50"
                  height="50"
                  class="eveving"
                  onload="SVGInject(this)"
                />
                <img
                  v-if="list.icon === '300'"
                  src="@/assets/weather/300-fill.svg"
                  width="50"
                  height="50"
                  class="right-rain"
                  onload="SVGInject(this)"
                />
                <img
                  v-if="list.icon === '303'"
                  src="@/assets/weather/303.svg"
                  width="50"
                  height="50"
                  class="right-lighting"
                  onload="SVGInject(this)"
                />
                <img
                  v-if="list.icon === '305'"
                  src="@/assets/weather/305.svg"
                  width="50"
                  height="50"
                  class="right-littleRain"
                  onload="SVGInject(this)"
                />
                <img
                  v-if="list.icon === '306'"
                  src="@/assets/weather/306.svg"
                  width="50"
                  height="50"
                  class="right-Moderate-Rain"
                  onload="SVGInject(this)"
                />
                <img
                  v-if="list.icon === '404'"
                  src="@/assets/weather/404.svg"
                  width="50"
                  height="50"
                  class="right-snow"
                  onload="SVGInject(this)"
                />
                <img
                  v-if="list.icon === '500'"
                  src="@/assets/weather/500-fill.svg"
                  width="50"
                  height="50"
                  class="right-fog"
                  onload="SVGInject(this)"
                />
                <img
                  v-if="list.icon === '502'"
                  src="@/assets/weather/502.svg"
                  width="50"
                  height="50"
                  class="right-haze"
                  onload="SVGInject(this)"
                />
              </div>
              <div style="font-size: 16px; margin-bottom: 10px">
                <span
                  style="margin: 0; padding: 0; color: white; margin-left: 10px"
                >{{ list.temp }}°</span>
                <span
                  style="
                    margin: 0;
                    padding: 0;
                    margin-left: 10px;
                    color: #ddc307;
                  "
                >{{ list.text }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script>
import { getLocationId } from "@/api/weather/index";
export default {
  props: ["item"],
  name: "weather",
  data() {
    return {
      show: "",
      Loading: false,
      location: "济南",
      key: "5b539d64e94e4c04a01c2d95b9bbbc41",
      list: [],
      icons: "301",
      bgColor: "",
    };
  },
  mounted() {
    this.getLocation();
  },
  created() {
    this.show = this.item.showTitle;
    this.bgColor = this.item.showBgColor;
  },
  watch: {
    "item.showTitle": function (val) {
      this.show = val;
    },
    "item.showBgColor": function (val) {
      this.bgColor = val;
    },
  },
  methods: {
    getLocation() {
      getLocationId().then((res) => {
        this.list = res.data;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.weather-card {
  height: 100%;
  background: #fff;
  .home-card-body {
    padding-top: 20px;
  }
  #he-plugin-standard {
    // width: 450px;
    height: 100%;
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    // overflow: hidden;
    .leftBox {
      float: left;
      width: 55%;
      height: 100%;
      border-right: 1px solid white;
    }
    .imgs {
      width: 50px;
      height: 50px;
      margin: 15px 10px auto 15px;
      img {
        width: 100%;
      }
    }
    .weather-temperature {
      color: white;
      margin-right: 20px;
    }
    .weather-temperature p {
      margin: 0;
      padding: 0;
    }
    .rain {
      width: 80px;
      height: 25px;
      color: #ff9d1e;
      text-align: center;
      border-radius: 10px;
      line-height: 25px;
      margin: 15px 0 0 25px;
    }
    .level {
      margin: 2px 0 0 70px;
      color: #e59c65;
      font-size: 20px;
    }
    .future-rain {
      margin: 12px 0 0 20px;
      color: white;
    }
    .rightBox {
      float: left;
      width: 40%;
      padding-left: 10px;
    }
    .today-imgs {
      width: 40px;
      height: 40px;
      margin: 10px 0 10px 100px;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .he-plugin-standard {
    background-image: url("../../../../assets/weather/100d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d101 {
    background-image: url("../../../../assets/weather/101d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d104 {
    background-image: url("../../../../assets/weather/104d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d300 {
    background-image: url("../../../../assets/weather/300d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d303 {
    background-image: url("../../../../assets/weather/303d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d305 {
    background-image: url("../../../../assets/weather/300d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d306 {
    background-image: url("../../../../assets/weather/306d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d404 {
    background-image: url("../../../../assets/weather/101d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d500 {
    background-image: url("../../../../assets/weather/500d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d502 {
    background-image: url("../../../../assets/weather/502d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d150 {
    background-image: url("../../../../assets/weather/101d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .sun,
  .night,
  .lighting,
  .right-sun,
  .eveving,
  .right-lighting {
    fill: #edaf27;
  }
  .cloudy,
  .cloudyDay,
  .rains,
  .littleRain,
  .snow,
  .fog,
  .haze,
  .Moderate-Rain,
  .right-cloudy,
  .right-cloudyDay,
  .right-rain,
  .right-littleRain,
  .right-snow,
  .right-fog,
  .right-Moderate-Rain,
  .right-haze {
    fill: #e0f1ff;
    margin-left: 20px;
  }
}
.unshow-weather-card {
  height: 100%;
  border: 0px;
  background: #fff;
  .home-card-body {
    padding-top: 20px;
  }
  #he-plugin-standard {
    // width: 450px;
    height: 100%;
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    // overflow: hidden;
    .leftBox {
      float: left;
      width: 55%;
      height: 100%;
      border-right: 1px solid white;
    }
    .imgs {
      width: 50px;
      height: 50px;
      margin: 15px 10px auto 15px;
      img {
        width: 100%;
      }
    }
    .weather-temperature {
      color: white;
      margin-right: 20px;
    }
    .weather-temperature p {
      margin: 0;
      padding: 0;
    }
    .rain {
      width: 80px;
      height: 25px;
      color: #ff9d1e;
      text-align: center;
      border-radius: 10px;
      line-height: 25px;
      margin: 15px 0 0 25px;
    }
    .level {
      margin: 2px 0 0 70px;
      color: #e59c65;
      font-size: 20px;
    }
    .future-rain {
      margin: 12px 0 0 20px;
      color: white;
    }
    .rightBox {
      float: left;
      width: 40%;
      padding-left: 10px;
    }
    .today-imgs {
      width: 40px;
      height: 40px;
      margin: 10px 0 10px 100px;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .he-plugin-standard {
    background-image: url("../../../../assets/weather/100d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d101 {
    background-image: url("../../../../assets/weather/101d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d104 {
    background-image: url("../../../../assets/weather/104d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d300 {
    background-image: url("../../../../assets/weather/300d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d303 {
    background-image: url("../../../../assets/weather/303d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d305 {
    background-image: url("../../../../assets/weather/300d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d306 {
    background-image: url("../../../../assets/weather/306d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d404 {
    background-image: url("../../../../assets/weather/101d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d500 {
    background-image: url("../../../../assets/weather/500d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d502 {
    background-image: url("../../../../assets/weather/502d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .d150 {
    background-image: url("../../../../assets/weather/101d.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: 100%;
    border-radius: 0px;
    overflow: hidden;
  }
  .sun,
  .night,
  .lighting,
  .right-sun,
  .eveving,
  .right-lighting {
    fill: #edaf27;
  }
  .cloudy,
  .cloudyDay,
  .rains,
  .littleRain,
  .snow,
  .fog,
  .haze,
  .Moderate-Rain,
  .right-cloudy,
  .right-cloudyDay,
  .right-rain,
  .right-littleRain,
  .right-snow,
  .right-fog,
  .right-Moderate-Rain,
  .right-haze {
    fill: #e0f1ff;
    margin-left: 20px;
  }
}
</style>
