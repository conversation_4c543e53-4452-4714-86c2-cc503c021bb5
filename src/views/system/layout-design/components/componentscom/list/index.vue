<template>
  <div
    class="list"
    :style="{ backgroundImage: 'url(' + datas.config.bgImg + ')' }"
  >
    <uni-section title="数据列表" type="line">
      <uni-list :border="datas.config.border">
        <uni-list-item title="列表文字" />
        <uni-list-item :border="datas.config.border" title="列表文字" note="列表描述信息" rightText="右侧文字" />
        <uni-list-item title="列表左侧带略缩图" note="列表描述信息" showArrow
                       :thumb="logo"
                       thumb-size="sm" rightText="小图" />
      </uni-list>
    </uni-section>
    <!-- 删除组件 -->
    <slot name="deles" />
  </div>
</template>

<script>
import uniSection from "@/components/UniUI/uni-section"
import uniList from "@/components/UniUI/uni-list/uni-list";
import uniListItem from "@/components/UniUI/uni-list/uni-list-item";
export default {
  name: 'list',
  components: {
    uniList,
    uniSection,
    uniListItem
  },
  props: {
    datas: Object,
  },
  created(){
    console.log(this.datas,'--------list')
  },
  data() {
    return {
      extraIcon: {
        color: '#4cd964',
        size: '22',
        type: 'gear-filled'
      },
      // logo: require("@/assets/logo/logo.jpg")
      logo:null
    }
  }
}
</script>

<style scoped lang="less">
.list {
  position: relative;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  /* 默认导航 */
  .defaultNavigation {
    // overflow-x: scroll;
    justify-content: space-evenly;
    &::-webkit-scrollbar {
      height: 1px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #155bd4;
    }
    /deep/.el-collapse-item__header,
    /deep/.el-collapse-item__wrap {
      border-bottom: 0 !important;
    }
    /* 导航 */
    .navigationList {
      display: flex;
      flex-direction: column;
      align-items: center;
      img {
        margin-top: 5px;
        width: 45px;
        height: 45px;
      }
      p {
        font-size: 12px;
        margin-top: 5px;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: center;
        box-sizing: border-box;
      }
    }
  }
}
.uni-padding-wrap {
  padding: 0 30px;
}
.content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 70px;
  text-align: center;
}

.content-text {
  font-size: 14px;
  color: #666;
}
</style>
