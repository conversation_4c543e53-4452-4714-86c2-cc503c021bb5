<template>
  <div :class="{ 'has-logo': showLogo }">
    <el-scrollbar wrap-class="scrollbar-wrapper potal-sidebar">
      <el-menu mode="vertical">
        <sidebar-item
          v-for="(route, index) in permission_routes"
          :key="route.path + index"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import Logo from "./Logo";
import SidebarItem from "./SidebarItem";
import variables from "@/assets/styles/variables.scss";

export default {
  components: { SidebarItem, Logo },
  computed: {
    ...mapState(["settings"]),
    ...mapGetters(["permission_routes", "sidebar"]),
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      // if set pathpath, the sidebar will highlight the path you set
      if (path) {
        return path;
      }
      return path;
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo;
    },
    variables() {
      return variables;
    },
  },
};
</script>
<style lang="scss" scoped>
 .potal-sidebar {
  .el-menu {
    background-color: #3e97ea00;
  }
  .el-menu-item {
    background: #fff;
  }
  .el-menu-item:hover,
  .el-menu-item:focus {
    outline: none;
    // background-color: #0e73d4;
    background: #fff;
  }
}
</style>
