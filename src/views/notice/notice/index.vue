<template>
  <div class="app-container">
    <el-card shadow="never">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item
          v-if="$store.getters.customParam.userType === 'admin'"
          label="租户"
        >
          <el-select
            v-model="queryParams.tenantId"
            style="width: 200px"
            size="small"
            filterable
            remote
            :remote-method="getTenantList"
            :loading="getTenantLoading"
            @change="tenantChange(queryParams.tenantId)"
          >
            <el-option
              v-for="item in tenantList"
              :key="item.tenantId"
              :label="item.tenantName"
              :value="item.tenantId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="公告标题" prop="noticeTitle">
          <el-input
            v-model="queryParams.noticeTitle"
            placeholder="请输入公告标题"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="类型" prop="noticeType">
          <el-select
            v-model="queryParams.noticeType"
            placeholder="公告类型"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in typeOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索
          </el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置
          </el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            v-hasPermi="['system:notice:add']"
            @click="handleAdd"
            >新增
          </el-button>
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="noticeList">
        <el-table-column label="序号" type="index" width="50" />
        <el-table-column
          label="公告标题"
          align="left"
          prop="noticeTitle"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <a style="color: #3a7eb9" @click="detail(scope.row)">{{
              scope.row.noticeTitle
            }}</a>
          </template>
        </el-table-column>
        <el-table-column
          label="公告类型"
          align="center"
          prop="noticeTypeName"
          width="100"
        >
          <template slot-scope="scope">
            <el-tag effect="plain" :type="selectDictRemark(typeOptions,scope.row.noticeType)">
              {{ scope.row.noticeTypeName }}
            </el-tag>
          </template>
        </el-table-column>

		<el-table-column
          label="通知类型"
          align="center"
          prop="notificationTypeName"
          width="80"
        >
          <template slot-scope="scope">
            <el-tag
              effect="plain"
              :type="
               selectDictRemark(noticeNotificationOptions,scope.row.notificationType)
              "
            >
              {{  scope.row.notificationTypeName }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- :formatter="statusFormat" -->
        <el-table-column
          label="创建者"
          align="center"
          prop="createByName"
          width="100"
        />
        <el-table-column
          label="创建时间"
          align="center"
          prop="createDate"
          width="200"
        >
          <template slot-scope="scope">
            <span>{{
              parseTime(scope.row.createDate, "{y}-{m}-{d} {h}:{m}:{s}")
            }}</span>
          </template>
        </el-table-column>

		<el-table-column
          label="状态"
          align="center"
          prop="noticeStatusName"
          width="100"
        >
          <template slot-scope="scope">
            <el-tag :type="changeStatus(scope.row)">
              {{ scope.row.noticeStatusName }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          label="是否置顶"
          align="center"
          prop="isTop"
          width="80"
        >
          <template slot-scope="scope">
            <el-tag :type="scope.row.isTop === 0 ? 'info' : 'success'">
              {{ scope.row.isTop == "0" ? "否" : "是" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="紧急程度"
          align="center"
          prop="priorityName"
          width="80"
        >
          <template slot-scope="scope">
            <el-tag  :type="selectDictRemark(noticePriorityOptions,scope.row.priorityValue)">
              {{ scope.row.priorityName}}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-position"
              @click="
                updateStatus(
                  scope.row,
                  scope.row.noticeStatus != 'notice_status_release'
                )
              "
              :loading="fbLoadId === scope.row.noticeId"
              >{{
                scope.row.noticeStatus != "notice_status_release"
                  ? "发布"
                  : "取消发布"
              }}
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-upload2"
              @click="updateTop(scope.row)"
              :loading="topLoadId === scope.row.noticeId"
              >{{ scope.row.isTop == 0 ? "置顶" : "取消置顶" }}
            </el-button>
            <el-button
              v-if="scope.row.noticeStatus != 'notice_status_release'"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              :loading="loadId === scope.row.noticeId"
              v-hasPermi="['system:notice:edit']"
              >修改
            </el-button>
            <el-button
              v-if="scope.row.noticeStatus != 'notice_status_release'"
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              :loading="delLoadId === scope.row.noticeId"
              v-hasPermi="['system:notice:remove']"
              >删除
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              :loading="detailLoadId === scope.row.noticeId"
              @click="detail(scope.row)"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改公告对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="open"
        width="1000px"
        append-to-body
        v-dialogDrag
      >
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="140px"
          :disabled="formDisabled"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="公告标题" prop="noticeTitle">
                <el-input
                  v-model="form.noticeTitle"
                  placeholder="请输入公告标题"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="公告类型" prop="noticeType">
                <el-select v-model="form.noticeType" placeholder="请选择">
                  <el-option
                    v-for="dict in typeOptions"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="紧急程度" prop="priorityValue">
                <el-select v-model="form.priorityValue" placeholder="请选择">
                  <el-option
                    v-for="dict in noticePriorityOptions"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="通知类型" prop="notificationType">
                <el-select v-model="form.notificationType" placeholder="请选择">
                  <el-option
                    v-for="dict in noticeNotificationOptions"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="租户">
                <el-input
                  v-model="form.tenantName"
                  placeholder="租户"
                  maxlength="50"
                  disabled
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="是否全员可看"
                prop="isAllShow"
              >
                <el-switch
                  v-model="form.isAllShow"
                  active-text="配置权限"
                  inactive-text="全员可看"
                  :active-value="0"
                  :inactive-value="1"
                  @change="setAuth"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="form.isAllShow === 0">
            <el-col :span="12">
              <el-form-item prop="visibleRange">
                <span slot="label">
                  <el-tooltip content="当前用户所属租户下的组织" placement="top">
                    <i class="el-icon-question" />
                  </el-tooltip>
                  当前租户组织
                </span>
                <treeselect
                  v-model="form.orgList"
                  :options="allTree"
                  :normalizer="normalizer"
                  :show-count="true"
                  :flat="true"
                  placeholder="选择当前租户组织"
                  :multiple="true"
                  :default-expand-level="1"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="visibleRange">
                <span slot="label">
                  <el-tooltip content="选择的管辖租户下的所有组织" placement="top">
                    <i class="el-icon-question" />
                  </el-tooltip>
                  管辖租户
                </span>
                <el-select v-model="form.jurisdictionTenantIds" multiple collapse-tags placeholder="请选择管辖租户">
                  <el-option
                    v-for="dict in jurisdictionTenantList"
                    :key="dict.tenantId"
                    :label="dict.tenantName"
                    :value="dict.tenantId"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="公告封面" prop="coverUrl">
              <el-upload
                ref="coverUpload"
                class="upload-demo"
                action=""
                :http-request="uploadImage"
                :on-preview="handlePreview"
                :on-remove="handleRemove"
                :before-remove="beforeRemove"
                :on-exceed="handleExceed"
                :on-success="handleSueccss"
                :limit="1"
                :file-list="imageList"
              >
                <el-button size="small" type="primary">点击上传</el-button>
                <!-- <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div> -->
              </el-upload>
              <el-dialog :visible.sync="previewDialogVisible" append-to-body>
                <img width="100%" :src="previewImageUrl" />
              </el-dialog>
            </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="内容">
                <editor
                  v-model="form.noticeContent"
                  :min-height="192"
                  :read-only.sync="formDisabled"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button
            type="primary"
            @click="submitForm"
            :loading="saveLoading"
            :disabled="formDisabled"
            >确 定
          </el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import {
  listNotice,
  selectNoticeByNoticeId,
  delNotice,
  addNotice,
  updateNotice,
  updateStatusById,
  updateTopByNoticeId,
} from "@/api/system/notice";
import { treeAll } from "@/api/system/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import Editor from "@/components/Editor";
import { uploadSingle } from "@/api/tool/upload";
import { list as tenantList } from "@/api/system/tenant";
import { getJurisdictionTenantList } from "@/api/system/tenantJurisdiction";

export default {
  name: "Notice",
  components: {
    Editor,
    Treeselect,
  },
  data() {
    const visibleRangeValidate = (rule, value, callback) => {
      if (this.form.isAllShow === 0) {
        // 租户组织的数据长度
        const orgListLength = this.form.orgList.length
        // 管辖租户组织的数据长度
        const jurisdictionLength = this.form.jurisdictionTenantIds.length
        if (orgListLength === 0 && jurisdictionLength === 0) {
          callback(new Error("请选择当前租户组织或管辖租户"));
        } else if (orgListLength === 0 || jurisdictionLength !== 0) {
          callback();
        } else {
          callback();
        }
      } else {
        callback();
      }
    }
    return {
      // 遮罩层
      loading: true,
      getTenantLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      noticeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 类型数据字典
      statusOptions: [],
      // 状态数据字典
      typeOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        noticeTitle: undefined,
        createBy: undefined,
        status: undefined,
        showType: 1,
        programaType: "system_notice",
        tenantId: this.$store.getters.customParam.tenantId,
        tenantName: this.$store.getters.customParam.tenantName,
      },
      // 表单参数
      form: {
        isAllShow: 1,
        noticeContentText: "",
        notificationType: "general",
      },
      // 表单校验
      rules: {
        noticeTitle: [
          { required: true, message: "公告标题不能为空", trigger: "blur" },
        ],
        noticeType: [
          { required: true, message: "公告类型不能为空", trigger: "change" },
        ],
        visibleRange: [
          { required: true, validator: visibleRangeValidate, trigger: "blur" },
        ],
        priorityValue:[
          { required: true, message: "紧急程度不能为空", trigger: "change" },
        ],
        notificationType:[
          { required: true, message: "通知类型不能为空", trigger: "change" },
        ]
      },
      loadId: undefined,
      delLoadId: undefined,
      detailLoadId: undefined,
      saveLoading: false,
      fbLoadId: undefined,
      topLoadId: undefined,
      noticePriorityOptions: [],
      noticeNotificationOptions: [],
      allTree: [],
      previewDialogVisible: false,
      previewImageUrl: "",
      imageList: [],
      defaultOrgId: this.$store.getters.orgId,
      formDisabled: false,
      tenantList: [],
      jurisdictionTenantList: [],
    };
  },
  created() {
    this.getList();
    this.getTreeAll();
    this.getDicts("notice_priority_list").then((response) => {
      this.noticePriorityOptions = response.data;
    });
    this.getDicts("notice_notification_list").then((response) => {
      this.noticeNotificationOptions = response.data;
    });
    this.getDicts("notice_type_list").then((response) => {
      this.typeOptions = response.data;
    });
    this.getTenantList();
    this.getJurisdictionTenantList();
  },
  methods: {
    // 状态标签显示
    changeStatus(row) {
      if (row.noticeStatus === "notice_status_release") {
        return "";
      } else if (row.noticeStatus === "notice_status_draft") {
        return "info";
      }
    },
    /** 查询公告列表 */
    getList() {
      this.loading = true;
      listNotice(this.queryParams).then((response) => {
        this.noticeList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getTreeAll() {
      treeAll({
        orgId: this.defaultOrgId,
        tenantId: this.queryParams.tenantId,
      }).then((r) => {
        this.allTree = r.data;
      });
    },
    // 公告状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
    // 公告状态字典翻译
    typeFormat(row, column) {
      return this.selectDictLabel(this.typeOptions, row.noticeType);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        noticeId: undefined,
        noticeTitle: undefined,
        noticeType: undefined,
        noticeContent: undefined,
        status: "0",
        noticeContentText: undefined,
        notificationType: "general",
        isAllShow: 1,
        tenantName: undefined,
        orgList: [],
        jurisdictionTenantIds: [],
      };
      this.resetForm("form");
      this.$refs.coverUpload && this.$refs.coverUpload.clearFiles();
      this.imageList = [];
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.formDisabled = false;
      this.reset();
      this.form.notificationType = "general";
      this.form = { ...this.form };
      this.open = true;
      this.title = "添加公告";
      this.form.tenantName = this.queryParams.tenantName;
      this.form.tenantId = this.queryParams.tenantId;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.formDisabled = false;
      this.reset();
      const noticeId = row.noticeId;
      this.loadId = noticeId;
      selectNoticeByNoticeId({ noticeId }).then((response) => {
        this.loadId = undefined;
        if (response.data) {
          this.form = response.data;
          this.open = true;
          this.title = "修改公告";
          this.form.coverUrl &&
            this.imageList.push({ url: this.form.coverUrl });
        } else {
          this.$message.error("数据异常！");
        }
      });
    },
    /** 详情按钮操作 */
    detail(row) {
      this.reset();
      const noticeId = row.noticeId;
      this.detailLoadId = noticeId;
      selectNoticeByNoticeId({ noticeId }).then((response) => {
        this.formDisabled = true;
        this.detailLoadId = undefined;
        if (response.data) {
          this.form = response.data;
          this.open = true;
          this.title = "查看公告";
          this.form.coverUrl &&
            this.imageList.push({ url: this.form.coverUrl });
        } else {
          this.$message.error("数据异常！");
        }
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.saveLoading = true;
          if (this.form.noticeId !== undefined) {
            if (this.form.noticeContent) {
              this.form.noticeContentText = this.form.noticeContent.replace(
                /<[^<>]+>/g,
                ""
              );
            }
            updateNotice(this.form).then((response) => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
              this.saveLoading = false;
            });
          } else {
            //提取富文本框内的纯文本  并赋值给空字符串
            if (this.form.noticeContent) {
              this.form.noticeContentText = this.form.noticeContent.replace(
                /<.*?>/g,
                ""
              );
            }
            addNotice({
              ...this.form,
              programaType: "system_notice",
              noticeStatus: "notice_status_draft",
            }).then((response) => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
              this.saveLoading = false;
            });
          }
        }
      });
    },
    updateStatus(row, flag) {
      const noticeId = row.noticeId;
      this.fbLoadId = noticeId;
      this.$confirm(
        '是否确认发布公告标题为"' + row.noticeTitle + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return updateStatusById({
            noticeId,
            noticeStatus: flag
              ? "notice_status_release"
              : "notice_status_draft",
          });
        })
        .then(() => {
          this.fbLoadId = undefined;
          this.getList();
          this.msgSuccess("操作成功");
        })
        .catch(() => {
          this.fbLoadId = undefined;
        });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const noticeId = row.noticeId;
      this.delLoadId = noticeId;
      this.$confirm(
        '是否确认删除公告标题为"' + row.noticeTitle + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delNotice({ noticeId });
        })
        .then(() => {
          this.delLoadId = undefined;
          this.getList();
          this.msgSuccess("删除成功");
        })
        .catch(() => {
          this.delLoadId = undefined;
        });
    },
    updateTop(row) {
      this.topLoadId = row.noticeId;
      updateTopByNoticeId({ ...row, isTop: row.isTop == 0 ? "1" : "0" })
        .then((response) => {
          this.msgSuccess("操作成功");
          this.open = false;
          this.getList();
          this.topLoadId = undefined;
        })
        .catch(() => {
          this.topLoadId = undefined;
          this.$message.error("操作失败!");
        });
    },
    normalizer(node) {
      if (
        node.children == null ||
        node.children === "null" ||
        node.children.length === 0
      ) {
        delete node.children;
      }
      return {
        id: node.orgId,
        label: node.orgName || "权限已删除",
        children: node.children,
      };
    },
    uploadImage(content) {
      let formData = new FormData();
      //content.file 	文件file对象
      formData.append("multipartFile", content.file);
      formData.append("appCode", this.appCode);
      uploadSingle(formData).then((res) => {
        this.form.coverUrl = res.data.url;
        this.imageList.push({ url: this.form.coverUrl });
      });
    },
    handleRemove(file, fileList) {
      this.form.coverUrl = "";
      this.imageList = [];
    },
    handlePreview(file) {
      this.previewImageUrl = this.form.coverUrl;
      this.previewDialogVisible = true;
    },
    handleExceed(files, fileList) {
      this.$message.warning(`只能上传一张图片,请先移除前一张`);
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除？`);
    },
    handleSueccss(response, file, fileList) {
      console.log("res", response);
    },
    getTenantList(tenantName) {
      this.getTenantLoading = true;
      let query = {}
      if (tenantName !== undefined && tenantName !== '') {
        query.tenantName = tenantName
        query.tenantId = undefined
      } else {
        query.tenantId = this.queryParams.tenantId
      }
      tenantList(query).then((response) => {
        this.tenantList = response.data;
      }).finally(() => {
        this.getTenantLoading = false;
      });
    },
    tenantChange(tenantId) {
      if (tenantId !== "") {
        this.queryParams.tenantName = this.tenantList.find(
          (item) => item.tenantId === tenantId
        ).tenantName;
      }
      this.handleQuery();
    },
    setAuth() {
      this.getTreeAll();
    },
    getJurisdictionTenantList() {
      getJurisdictionTenantList({}).then((response) => {
        this.jurisdictionTenantList = response.data;
      });
    }
  },
};
</script>
