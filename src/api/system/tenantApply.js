import request from "@/utils/request";

// 查询分页
export function page(params) {
  return request({
    url: "/user/tenantApply",
    method: "get",
    params: params
  });
}

// 根据ID查询详细
export function getById(id) {
  return request({
    url: "/user/tenantApply/" + id,
    method: "get"
  });
}

// 修改用户
export function update(data) {
  return request({
    url: "/user/tenantApply",
    method: "put",
    data: data
  });
}

// 删除
export function del(id) {
  return request({
    url: "/user/tenantApply/" + id,
    method: "delete"
  });
}

// 审核用户
export function audit(data) {
  return request({
    url: "/user/tenantApply/audit",
    method: "post",
    data: data
  });
}
