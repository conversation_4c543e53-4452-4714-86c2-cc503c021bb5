<template>
  <div class="iframe-content">
    <iframe v-if="url" :src="url" frameborder="0" v-once :key="url" class="iframe"></iframe>
  </div>
</template>

<script>
import { getInfo } from "/src/api/login";
import ssoCrypto from "/src/utils/ssoCrypto";
import { getToken } from "/src/utils/auth";
export default {
  name: "iframe-content",
  data() {
    return {
      url: undefined,
    };
  },
  mounted() {},
  created() {
    if (this.$route.meta.permissionFrame === 'Y') {
      getInfo({ token: getToken() }).then((res) => {
        let urls = this.$route.meta.external.split("?");
        this.url = `${urls[0]}?${urls.length > 1 ? urls[1] + "&" : ""}mmy=${ssoCrypto(`${res.loginName}:${res.tenantId}`)}`;
      }).catch((error) => {
        this.$message({
          message: "获取用户信息失败",
          center: true,
        });
      });
    } else {
      this.url = this.$route.meta.external;
    }
    const _this = this;
    window.addEventListener('message', function(messageEvent){
      _this.logout();
    })
  },
  computed: {},
  methods: {
    async logout() {
      this.$confirm("确定注销并退出系统吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$store.dispatch("LogOut").then(() => {
          var tenantName = this.$store.getters.customParam.tenantLoginName;
          this.$router.push({
            path: `/login`,
            query: { redirect: this.$route.fullPath, tenantName: tenantName },
          });
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.iframe {
  width: 100%;
  min-height: calc(100vh - 50px);
}
</style>
