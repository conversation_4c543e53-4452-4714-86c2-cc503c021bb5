<template>
  <el-card :class="this.bgColor === true ?'tool-card':'unshow-tool-card'" v-loading="toolsLoading" shadow="never" >
    <div slot="header" v-if="show">
      <span class="card-title" >
        <div class="title">
          <span > <i class="icon iconfont icon-gongju"></i>小工具 </span>
        </div>
        <router-link to="/portal/more"
        ><el-button class="exec" type="text"
        >更多<i class="el-icon-d-arrow-right"></i></el-button
        ></router-link>
      </span>
    </div>
    <el-empty v-if="tools.length <= 0 && !toolsLoading" :image-size="100"/>
    <div v-if="tools.length > 0" class="tool home-card-body">
      <div
        v-for="item in tools"
        :key="item.id"
        :style="`background-color: ${item.remark}20;`"
        class="tool-item"
        @click="toApp(item.url)"
      >
        <svg-icon
          slot="prefix"
          :icon-class="item.logoTemp"
          class="el-input__icon"
          :style="`height: 30px;width: 30px; color: ${item.remark}`"
        />
        <span>{{ item.name }}</span>
      </div>
      <!--      <div style="background-color: #9fa2a520" class="tool-item">-->
      <!--        <i class="icon iconfont icon-xinzeng" style="color: #9fa2a5"></i>-->
      <!--      </div>-->
    </div>
  </el-card>
</template>

<script>
import {list as tools, selectTenantApps} from "@/api/apps/app";
// import {getToken} from "@/utils/auth";

export default {
  props:['item'],
  name: "tool",
  data() {
    return {
      tools: [],
      toolsLoading: false,
      show:"",
      bgColor:"",
    };
  },
  created() {
    this.getTools();
    this.show = this.item.showTitle;
    this.bgColor = this.item.showBgColor;
  },
  watch:{
    'item.showTitle' :function(val){
      this.show = val
    },
    'item.showBgColor' :function(val) {
      this.bgColor = val
    }
  },
  methods: {
    getTools() {
      this.toolsLoading = true;
      selectTenantApps("1").then((res) => {
        if (res.data.length > 0) {
          this.tools = res.data[0].apps;
          this.toolsLoading = false;
        } else {
          this.toolsLoading = false;
        }
      });
    },
    toApp(url) {
      const usrs = url.split("?");
      console.log(`${usrs[0]}?${usrs.length > 1 ? usrs[1] + "&" : ""}`);
      window.open(
        `${usrs[0]}?${usrs.length > 1 ? usrs[1] + "&" : ""}` //token=${getToken()}
      );
    },
  },
};
</script>

<style scoped lang="scss">
.tool-card {
  height: 100%;
  background: #fff;

  ::v-deep .el-card__body {
    // padding: 0px 20px 35px 20px;
  }
  .home-card-body {
    padding-top: 20px;
  }
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }

    .exec {
      padding: 3px 0;
    }
  }

  .tool {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-around;
    overflow-y: auto;
    height: 100%;

    .tool-item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin: 10px 0 0 0;
      border-radius: 5px;
      width: 70px;
      height: 70px;
      font-size: 14px;
      font-weight: bold;
      cursor: pointer;
    }
  }
}
.unshow-tool-card {
  border: 0px;
  height: 100%;
  background: #fff;

  ::v-deep .el-card__body {
    // padding: 0px 20px 35px 20px;
  }
  .home-card-body {
    padding-top: 20px;
  }
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }

    .exec {
      padding: 3px 0;
    }
  }

  .tool {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-around;
    overflow-y: auto;
    height: 100%;

    .tool-item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin: 10px 0 0 0;
      border-radius: 5px;
      width: 70px;
      height: 70px;
      font-size: 14px;
      font-weight: bold;
      cursor: pointer;
    }
  }
}
</style>
