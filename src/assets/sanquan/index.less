.x-app-container {
  width: 100vw;
  height: 100%;
  background: url(../../assets/images/sw/bg-new.png) no-repeat top;
  background-size: 100%;
  background-color: #F1F8FF;
  // min-width: 1440px;
  overflow: scroll;

  @media (min-width: 1400px) {
    .wrapper {
      width: 94%;
      // min-height: 100%;
      margin: 0 auto;
    }
  }

  @media (max-width: 1366px) {
    .wrapper {
      width: 94%;
      // min-height: 100%;
      margin: 0 auto;
    }
  }

  .wrapper {
    // width: 12rem;
    // width: 75%;
    // height: 800px;
    // max-width: 1440px;
    // min-width: 1200px;
    // margin: 0 auto;
  }
}

.myInnerDialog {
  .el-dialog__header {
    height: 54px !important;
    line-height: 54px !important;
    .el-dialog__title{
      margin-top: 10px !important;
      position: absolute;
      top: 8px;
      left: 10px;
      line-height: 20px;
    }
  }
}

.portal-body {
  font-size: 0.125rem;
  width: 100%;
  height: 100%;
  border-radius: 10px;

  .box-bg {

  }

  .search-wrapper {
    position: relative;
    padding: 24px 24px 0 0;

    .el-form-item {
      margin-bottom: 0.2rem;
    }

    .el-form-item__label {
      font-family: PingFangSC-Regular;
      font-size: 0.135rem;
      color: #373D41;
      letter-spacing: 0;
      // text-align: right;
      // line-height: 0.3rem;
      font-weight: 400;
    }

    .el-select,
    .el-input {
      width: 2rem;
      height: 0.27rem;
    }

    .el-input__inner {
      height: 0.27rem;
      line-height: 0.2rem;
      font-size: 0.135rem !important;
      font-family: PingFangSC-Regular !important;
    }

    .btn-group {
      position: absolute;
      top: 24px;
      right: 0.2rem;
    }

  }

  .table-wrapper {
    padding: 0.13rem 0;

    .table-title {
      &::before {
        display: inline-block;
        content: '';
        margin-right: 0.07rem;
        width: 5px;
        height: 24px;
        background: #3377FF;
        border-top-right-radius: 15px;
        border-bottom-right-radius: 15px;
        vertical-align: middle;
      }

      font-family: PingFangSC-Regular;
      // font-size: 0.125rem;
      font-size: 16px;
      color: #181818;
      letter-spacing: 0;
      // line-height: 0.2rem;
      font-weight: 600;
    }

    .el-divider--horizontal {
      margin: 0.1rem 0;
    }

    .table-con {

    }

    .action-buttons {
      .action-button {
        padding: 0;
        font-size: 0.125rem;
        min-width: 0.125rem;
        background-color: transparent;
        border: none;
        cursor: pointer;
        color: #3377FF;
        font-family: PingFangSC-Regular;
        line-height: 0.18rem;
        font-weight: 400;

      }

      .separator {
        display: inline-block;
        margin: 0 0.06rem;
        position: relative;
        width: 1px;
        height: 0.1rem;
        background-color: #D9D9D9;
      }
    }
  }

  .menus-body {
    padding: 0.09rem;
    width: 100%;

    .link-user-info {
      text-align: center;
      vertical-align: middle;
      height: 0.4rem;
      line-height: 0.4rem;
      background: #ffffff;
      border: 1px #f5f5f5 solid;
      color: #0f9af2;
      font-size: 0.125rem;
    }

    .menus {
      list-style: none;
      margin: 0.1rem 0 0 0;
      padding: 0.09rem;
      background: #ffffff;

      .menus-item {
        list-style: none;

        .title {
          background: rgb(232, 231, 231);
          height: 0.28rem;
          line-height: 0.26rem;
          padding: 0 0.09rem;
          border-bottom: 0.015rem solid rgb(139, 142, 148);
          border-radius: 0.02rem 0.02rem 0 0;

          .text {
            background: rgb(232, 231, 231);
            border-bottom: 0.020rem solid rgb(59, 116, 240);
            display: inline-block;
          }
        }

        ul {
          list-style: none;
          margin: 0;
          padding: 0 0.2rem;
          background: #f5f5f5;

          li {
            list-style: none;
            // height: 0.23rem;
            // line-height: 0.23rem;
            width: 1.25rem;

            .content {
              width: 1.25rem;
              height: 0.45rem;
              line-height: 0.45rem;
              margin: 0.08rem 0 0.08rem 0;
              padding-left: 0.1rem;
              border: 0.01rem solid #0f9af2;
              border-radius: 0.04rem;
              background-color: #dcebf4;
              display: flex;

              .icon-star {
                color: #1791dc;
                margin-right: 0.08rem;
                font-size: 0.135rem;
              }

            }

          }

          li:hover {
            color: #0f9af2;
          }
        }
      }
    }
  }

  //潜在机会列表
  .opportunity-list {
    padding: 0.09rem;
    width: 100%;
    height: 100%;

    .base-info {
      display: flex;
      justify-content: space-between;
      margin-right: 1rem;
      font-size: 0.125rem;
      font-weight: 400;
      height: 0.35rem;
      line-height: 0.35rem;
    }

    .link-user-info {
      text-align: center;
      vertical-align: middle;
      height: 0.4rem;
      line-height: 0.4rem;
      background: #ffffff;
      border: 1px #f5f5f5 solid;
      color: #0f9af2;
      font-size: 0.125rem;
    }

    .list-query {
      margin-top: 0.25rem;
      padding-top: 0.09rem;
      min-height: 0.4rem;
      line-height: 0.4rem;
      background: #ffffff;
      border: 1px #f5f5f5 solid;
      color: #0f9af2;

      .a-row .el-form-item__label {
        // font-size: 0.125rem !important;
      }

      .icon {
        /* margin-left: 0.15rem;*/
        width: 0.04rem;
        height: 0.24rem;
        background: #3377FF;
        border-radius: 2px;
      }

      .title {
        font-family: PingFangSC-Regular;
        padding-left: 0.075rem;
        font-size: 0.133rem;
        color: #181818;
        letter-spacing: 0;
        line-height: 0.24rem;
        font-weight: 600;
      }
    }

    .list-table {
      margin-top: 0.25rem;
      padding-top: 0.1rem;
      min-height: 1.5rem;
      background: #ffffff;
      border: 1px #f5f5f5 solid;
      color: #0f9af2;
      font-size: 0.125rem;

      .icon {
        margin-left: 0.15rem;
        width: 0.04rem;
        height: 0.24rem;
        background: #3377FF;
        border-radius: 2px;
      }

      .title {
        font-family: PingFangSC-Regular;
        padding-left: 0.075rem;
        font-size: 0.133rem;
        color: #181818;
        letter-spacing: 0;
        line-height: 0.24rem;
        font-weight: 600;
      }

      .list-table-page {
        margin-top: 0.09rem;
      }
    }
  }

  //潜在机会详情
  .opportunity-details {
    padding: 0.09rem;
    width: 100%;
    height: 100%;

    .detail-top {
      width: 100%;
      height: 1rem;
      // background: #FFFFFF;
      display: flex;
      // background-image: url('../images/sw/strategy/lanbg.png');
      background-image: url("~@/assets/images/sw/strategy/lanbg.png");
      background-repeat: no-repeat;
      background-position: center center;
      background-size: cover;

      .detail-type {
        height: 100%;
        background: #0e73d4;
        width: 6rem;
        color: #FFFFFF;
        line-height: 1rem;
        font-size: 0.3rem;
        text-align: center;
      }

      .detail-info {
        width: 100%;
        padding: 0.1rem 0.3rem;

        .company_name {
          color: #0e73d4;
          line-height: 0.25rem;
          font-size: 0.135rem;
        }

        .user-info {
          line-height: 0.18rem;
        }
      }

      .detail-pie {
        width: 6rem;
        height: 1rem;
      }
    }

    .push-produce {
      margin-top: 0.1rem;
      width: 100%;
      background: #FFFFFF;
      min-height: 3rem;
    }
  }

  //潜在机会反馈
  .opportunity-feedback {
    padding: 0.09rem;
    width: 100%;
    height: 100%;

    .opportunity-feedback-top {
      width: 100%;
      height: 1rem;
      background: #FFFFFF;
      display: flex;
      padding: 0.09rem;

      .left-company-info {
        width: 5rem;
        height: 100%;

        .company-name {
          font-size: 0.3rem;
          color: #0e73d4;
        }

        .company-user {
          font-size: 0.135rem;
          color: #0e73d4;
          line-height: 0.2rem;
          height: 0.2rem;
          padding: 0.1rem 0 0 0;
        }
      }

      .right-company-info {
        width: 100%;
        height: 100%;

        .company-name {
          height: 0.392rem;
        }

        .company-time {
          width: 100%;
          text-align: right;
          font-size: 0.135rem;
          color: #0e73d4;
          line-height: 0.2rem;
          height: 0.2rem;
          padding: 0.1rem 0 0 5rem;
        }
      }
    }

    .opportunity-feedback-content {
      width: 100%;
      margin-top: 0.1rem;
      min-height: 3.4rem;
      background: #FFFFFF;
      padding: 0.09rem;

      .common-opportunities {
        margin-top: 0.1rem;
      }

      .opportunities-title {
        font-size: 0.22rem;
        height: 0.3rem;
        line-height: 0.3rem;
        margin-right: 0.05rem;
      }

      .invalid-opportunities {
        ul {
          list-style: none;
          padding: 0;
          margin-left: 0.4rem;
        }

        ul li {
          list-style: none;
          height: 0.35rem;
          line-height: 0.3rem;
          display: flex;

          .div_radio {
            width: 1.5rem;
          }

          .li_options {
            padding: 0 0.05rem;

            .el-input {
              padding: 0 0.05rem;
            }
          }

          .el-radio-button {
            margin: 0 0.05rem;
            border: 2px solid #f5f5f5;
          }

          .el-radio-button:first-child {
            border-radius: 0;
          }

          .el-radio-button:last-child {
            border-radius: 0;
          }
        }
      }
    }

    .opportunity-feedback-btn {
      margin-top: 0.1rem;
      text-align: center;
    }
  }

  //产品管理列表
  .product-list {
    padding: 0.09rem;
    width: 100%;
    height: 100%;

    .link-user-info {
      text-align: center;
      vertical-align: middle;
      height: 0.4rem;
      line-height: 0.4rem;
      background: #ffffff;
      border: 1px #f5f5f5 solid;
      color: #0f9af2;
      font-size: 0.125rem;
    }

    .list-query {
      margin-top: 0.25rem;
      padding-top: 0.09rem;
      min-height: 0.4rem;
      line-height: 0.4rem;
      background: #ffffff;
      border: 1px #f5f5f5 solid;
      color: #0f9af2;

      .a-row .el-form-item__label {
        font-size: 0.125rem !important;
      }
    }

    .list-table {
      margin-top: 0.25rem;
      padding-top: 0.1rem;
      min-height: 1.5rem;
      background: #ffffff;
      border: 1px #f5f5f5 solid;
      color: #0f9af2;
      font-size: 0.125rem;

      .list-button {
        padding-left: 0.2rem;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #F0F0F0;


        .el-icon-marking {
          background: url('../images/sw/product/marking.png') center no-repeat;
          font-size: 0.125rem;
          background-size: cover;
          margin-right: 0.05rem;
        }

        .el-icon-marking:before {
          content: "打标";
          font-size: 0.06rem;
          visibility: hidden;
        }
      }

      .list-table-page {
        margin-top: 0.09rem;

        .action-buttons {

          .action-button {
            padding: 0;
            font-size: 0.125rem;
            min-width: 0.125rem;
            background-color: transparent;
            border: none;
            cursor: pointer;
            color: #3377FF;

          }

          .separator {
            margin: 0 0.04rem;
            position: relative;
          }

          .separator::before {
            content: "";
            position: absolute;
            top: 2;
            bottom: 0;
            left: 0;
            width: 0.012rem;
            height: 0.13rem;
            background-color: #D9D9D9;
          }
        }

        .block {
          display: flex;
          align-items: center;
          float: right;

          .demonstration {
            margin-right: 10px;
            color: #000000;
          }

          .product-pagination {
            float: right;
          }
        }
      }
    }
  }

  //产品详情
  .product-detail {
    // padding: 0.09rem;
    width: 100%;
    height: 100%;

    .link-user-info {
      text-align: center;
      vertical-align: middle;
      height: 0.4rem;
      line-height: 0.4rem;
      background: #ffffff;
      border: 1px #f5f5f5 solid;
      color: #0f9af2;
      font-size: 0.125rem;
    }

    .pd-title {
      margin-top: 0.1rem;
      border: 0.025rem solid #ffffff;
      border-radius: 0.01rem;
      padding: 0.1rem 0;

      .pd-name {
        margin: 0 0 0.1rem 0;
        vertical-align: middle;
        font-size: 0.17rem;
        font-weight: 600;
      }

      .pd-type {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 0.8rem;
        height: 0.23rem;
        border: 0.02rem solid #3377FF;
        border-radius: 0.04rem;
        background-color: #3377FF;
        color: #fff;
        margin: 0 0 0.1rem 0.1rem;
        font-size: 0.125rem;
      }

      .pd-jump {
        display: flex;
        justify-content: center;
        align-items: center;
        color: red;
        cursor: pointer;
        align-items: center;
        font-size: 0.125rem;
        margin: 0 0 0 0.5rem;
        padding-bottom: 0.1rem;
      }

      .pd-unit {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 0.8rem;
        height: 0.23rem;
        border: 0.02rem solid #3377FF;
        border-radius: 0.04rem;
        background-color: #EAF1FF;
        color: #3377FF;
        margin: 0 0 0.1rem 0;
        font-size: 0.125rem;
      }

      .pd-text {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 0.23rem;
        margin: 0 0 0.1rem 0.2rem;
      }

      .pd-uploadtime {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 0.8rem;
        height: 0.23rem;
        border: 0.02rem solid #FF9F18;
        border-radius: 0.04rem;
        background-color: #FDF2EA;
        color: #FF9F18;
        margin: 0 0 0.1rem 0.5rem;
        font-size: 0.125rem;
      }

      .pd-desc {
        font-family: PingFangSC-Regular;
        font-size: 0.135rem;
        color: #73777A;
        font-weight: 400;
        line-height: 1.8;
      }

    }

    .content {
      margin-top: 0.25rem;
      background: #ffffff;

      .pd-headline {
        font-weight: 600;

        .pd-headline-before {
          display: flex;

          &::before {
            display: inline-block;
            content: '';
            margin-right: 0.07rem;
            width: 4px;
            height: 0.2rem;
            background: #3377FF;
            border-radius: 2px;
            vertical-align: middle;
          }
        }
      }

      .pd-middle {
        display: flex;
        // justify-content: space-between;
        margin-right: 1rem;
        font-size: 0.135rem;
        font-weight: 400;
        height: 0.35rem;
        line-height: 0.35rem;
      }

      .list-bottom {
        text-align: center;
        vertical-align: middle;
        height: 0.4rem;
        line-height: 0.4rem;
        background: #ffffff;
        border: 1px #f5f5f5 solid;
        color: #0f9af2;
        font-size: 0.125rem;
        margin-top: 0.2rem;
      }

      .pd-attachment {
        // display: flex;
        // justify-content: space-between;
        // margin-right: 1rem;
        font-size: 0.125rem;
        font-weight: 400;
        height: 0.35rem;
        line-height: 0.35rem;
        margin-top: 10px;

        .attachment {
          display: flex;
          float: left;
          margin-right: 10px;

          .attachments-container {
            display: flex;
            flex-wrap: wrap;
            width: 11rem;

            .attachment-column {
              flex: 0 0 auto;
              width: 3.6rem;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;

              .attachment-name {
                width: 2.5rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
          }


          .attachment-icon {
            width: 0.15rem;
            height: 0.16rem;
            line-height: 0.15rem;
            margin-right: 0.05rem;
            vertical-align: middle;
          }

          .download-icon {
            width: 0.15rem;
            height: 0.16rem;
            margin-left: 0.06rem;
            vertical-align: middle;
          }
        }

        .attachment:last-child {
          margin-right: 0;
        }
      }
    }
  }

  //产品新增
  .product-add {
    padding: 0.09rem;
    width: 100%;
    height: 100%;

    .link-user-info {
      text-align: center;
      vertical-align: middle;
      height: 0.4rem;
      line-height: 0.4rem;
      background: #ffffff;
      border: 1px #f5f5f5 solid;
      color: #0f9af2;
      font-size: 0.125rem;
    }

    .list-query {
      padding: 0.25rem;
      margin-top: 0.25rem;
      padding-top: 0.09rem;
      min-height: 0.4rem;
      line-height: 0.4rem;
      background: #ffffff;
      border: 1px #f5f5f5 solid;
      color: #0f9af2;
    }

    .choose-button {
      margin-left: 0.1rem;
      height: 0.36rem;
      width: 0.6rem;
      background-color: #e1dfdf;
      color: #333;
      border: none;
    }

    .list-bottom {
      text-align: center;
      vertical-align: middle;
      height: 0.4rem;
      line-height: 0.4rem;
      background: #ffffff;
      border: 1px #f5f5f5 solid;
      color: #0f9af2;
      font-size: 0.125rem;
      margin-top: 0.2rem;
    }

  }


  //工单反馈页面
  .feedback-list {
    width: 100%;
    height: 100%;

    .list-query {
      margin-top: 0.25rem;
      padding-top: 0.09rem;
      min-height: 0.4rem;
      line-height: 0.4rem;
      background: #ffffff;
      border: 0px #f5f5f5 solid;
      color: #0f9af2;

      .a-row .el-form-item__label {
        font-size: 0.125rem !important;
      }
    }

    .list-table {
      margin-top: 0.25rem;
      padding-top: 0.1rem;
      min-height: 1.5rem;
      background: #ffffff;
      border: 1px #f5f5f5 solid;
      color: #0f9af2;
      font-size: 0.125rem;

      .list-button {
        padding-left: 0.2rem;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #F0F0F0;


        .el-icon-marking {
          background: url('../images/sw/product/marking.png') center no-repeat;
          font-size: 0.125rem;
          background-size: cover;
          margin-right: 0.05rem;
        }

        .el-icon-marking:before {
          content: "打标";
          font-size: 0.06rem;
          visibility: hidden;
        }
      }

      .list-table-page {
        margin-top: 0.09rem;

        .action-buttons {

          .action-button {
            padding: 0;
            font-size: 0.125rem;
            min-width: 0.125rem;
            background-color: transparent;
            border: none;
            cursor: pointer;
            color: #3377FF;

          }

          .separator {
            margin: 0 0.04rem;
            position: relative;
          }

          .separator::before {
            content: "";
            position: absolute;
            top: 2;
            bottom: 0;
            left: 0;
            width: 0.012rem;
            height: 0.13rem;
            background-color: #D9D9D9;
          }
        }

        .block {
          display: flex;
          align-items: center;
          float: right;

          .demonstration {
            margin-right: 10px;
            color: #000000;
          }

          .product-pagination {
            float: right;
          }
        }
      }
    }
  }

  // 整体转化率分析
  .analysis {
    color: #000000;
  }
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-around {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.flex-start {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.flex-end {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}


.opt-btn {
  min-width: 0.3rem !important;
}


.el-table {
  .el-table__cell {
    padding: 16px 0 !important;
    height: 23px;
    line-height: 23px;
    overflow: hidden;
    font-weight: normal;
  }

  .cell {
    overflow: hidden;
    // height: 23px;
    line-height: 23px;
  }

  .el-table__header-wrapper {
    background: #f5f8ff !important;

    thead {
      th {
        background: #f5f8ff !important;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.8);
        letter-spacing: 0;
        // font-weight: 400;

        .el-checkbox {
          margin-left: 4px !important;
        }
      }
    }
  }

  .el-table__body-wrapper {
    .row-bg-high {
      opacity: 1;
      background: #f5f8ff !important;

      td {
        background: #f5f8ff !important;
      }
    }
  }
}

  .el-menu--popup{
    min-width: 120px !important;
  }

  .el-submenu .el-menu-item{
    min-width: 120px !important;
  }

  .el-submenu__icon-arrow{
    right: 10px !important;
  }

