<template>
  <div class="app-container">
    <div class="screen">
      <span>筛选系统</span>
      <el-select v-model="form.categoryName" clearable placeholder="请选择">
        <el-option
          v-for="item in titleList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
    </div>

    <el-empty v-if="list.length <= 0 && !loading" :image-size="200" style="margin-top: 50px" />

    <div v-if="list.length > 0">
      <el-card
        v-loading="loading"
        element-loading-text="加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
        shadow="never"
        v-for="item in list"
        :key="item.id"
        class="app-card"
        v-if="!form.categoryName ? list : form.categoryName === item.categoryName"
      >
        <div slot="header">
          <span class="card-title">
            <span>
              <i class="icon iconfont icon-shuxian"></i>
              {{ item.categoryName }}
            </span>
          </span>
        </div>
        <div class="apps">
          <div
            v-for="citem in item.apps"
            :key="citem.id"
            class="app-item"
            @click="toApp(citem.url)"
          >
            <img class="app-item-logoTemp" :src="citem.logoTemp" />
            <span>{{ citem.name }}</span>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { selectRoleApps } from "@/api/apps/app";
import { getInfo } from "@/api/login";
import ssoCrypto from "@/utils/ssoCrypto";
import { getToken } from "@/utils/auth";

export default {
  name: "apps",
  data() {
    return {
      list: [],
      loading: true,
      titleList: [],
      form: {
        categoryName: "",
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 获取应用列表
    getList() {
      this.loading = true;
      selectRoleApps("2").then((res) => {
        this.list = res.data;
        this.list.forEach((item) => {
          this.titleList.push({ value: item.categoryName });
        });
        this.loading = false;
      });
    },
    // 单点登录跳转
    toApp(url) {
      getInfo({ token: getToken() })
        .then((res) => {
          const urls = url.split("?");
          window.open(
            `${urls[0]}?${urls.length > 1 ? urls[1] + "&" : ""}mmy=${ssoCrypto(
              `${res.loginName}:${res.tenantId}`
            )}`
          );
        })
        .catch((error) => {
          this.$message({
            message: "获取用户信息失败",
            center: true,
          });
        });
    },
  },
};
</script>

<style scoped lang="scss">
.app-container {
  max-width: 1443px;
  margin: 0 auto;
}
.screen {
  padding: 10px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  span {
    margin-right: 10px;
    font-size: 14px;
  }
}
.app-card {
  margin: 0px 0px 10px 0px;
  background-color: #00000000;
  border: 0px solid #e6ebf5;

  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > span {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .icon {
      font-size: 22px;
      color: #419eee;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }

    .exec {
      padding: 3px 0;
    }
  }
  .apps {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;

    .app-item {
      padding: 40px;
      flex-basis: 30%;
      background-color: #ffffff;
      margin: 15px;
      border-radius: 10px;
      display: flex;
      flex-direction: row;
      align-items: center;

      > span {
        font-weight: bold;
        margin-left: 20px;
      }

      .app-item-logoTemp {
        width: 50px;
        height: 50px;
      }

      &:hover {
        box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
      }

      cursor: pointer;
    }
  }
}
</style>
