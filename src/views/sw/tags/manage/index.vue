<template>
  <div class="portal-body">
    <div class="analysis">
      <div class="search-wrapper box-bg">
        <el-form
          :model="queryParams"
          @submit.native.prevent
          ref="queryForm"
          label-width="1rem"
          label-position="left"
        >
          <el-row>
            <el-col :span="7">
              <el-form-item label="标签名称" prop="tagName">
                <el-input
                  v-model="queryParams.tagName"
                  placeholder="请输入"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <div class="tab-total">
                <div class="tab-title">
                  <i class="el-icon-collection-tag"></i>
                  标签总数：<span class="tab-total-num">{{ info.totalCount }}</span>个
                </div>
              </div>
            </el-col>
            <el-col :span="7">
              <div class="tab-total">
                <div class="tab-title">近一个月新增：<span class="tab-total-num">{{ info.monthCount }}</span>个
                </div>
              </div>
            </el-col>
          </el-row>
          <div class="btn-group" style="right: 0;">
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
          </div>
        </el-form>
      </div>
      <div class="container" style="min-height: 700px;">
        <div class="con-left table-wrapper box-bg">
          <div class="flex-between">
            <div class="table-title">标签分类</div>
          </div>
          <div class="tab-tree">
            <el-tree
              :data="treeData"
              v-loading="treeLoading"
              :expand-on-click-node="false"
              @current-change="handleNodeClick"
            >
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <i class="el-icon-collection-tag tab-icon"></i>
              <span>{{ node.label }}</span>
            </span>
            </el-tree>
          </div>
        </div>
        <div class="con-main table-wrapper box-bg">
          <div class="flex-between" >
            <div class="table-title">标签列表</div>
            <div>
              <el-button type="primary"
                 size="mini"
                 @click="handleEdit"
                 :disabled="showAddBtn">新增
              </el-button>
            </div>
          </div>
          <el-divider />
          <div class="table-con">
            <uni-table
              action="/sanquan/tag/findPage"
              :columns="tableColumns"
              ref="uniTable"
              :params="queryParams"
            >
              <template v-slot:tagName="scope">
                <div style="cursor: pointer;color: #3377ff" @click="handleView(scope.row)">
                  {{ scope.row.tagName }}
                </div>
              </template>
              <template v-slot:opt="scope">
                <div class="action-buttons flex-center">
                  <el-button
                    size="mini"
                    class="action-button"
                    type="button"
                    @click="handleEdit(scope.row)"
                  >修改
                  </el-button>
                  <el-button
                    size="mini"
                    class="action-button"
                    type="button"
                    @click="handleDel(scope.row)"
                  >删除
                  </el-button>
                </div>
              </template>
            </uni-table>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>
<script>
import { getAllTree } from '@/api/sw/tags/labelClassify'
import { findPageList, tagStatistics, tagDel } from '@/api/sw/tags/tagManage'
import UniTable from '@/components/UniTable/UniTable.vue'

export default {
  // import引入的组件需要注入到对象中才能使用
  components: { UniTable },
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 5
      },
      // 标签分类
      treeData: [],
      // 表格数据
      tableData: [],
      // 传递列表数据
      columnData: {},
      // 表格loading
      tableLoading: false,
      // tree
      treeLoading: false,
      // 表格总数
      total: 0,
      //当前页
      currentPage: 1,
      // 标签id
      tagId: undefined,
      info: {},
      showAddBtn: true,
      tag: {},
      tableColumns:[{
        label:"标签名称",
        props:"tagName",
        slot: true
      },{
        props:'tagCode',
        label:'标签编码'
      },{
        label:"标签联系人",
        align:"center",
        props:"tagUser"
      },{
        label:"标签联系人电话",
        align:"center",
        props:"tagUserPhone"
      },{
        label:"创建时间",
        align:"center",
        props:"createDate"
      },{
        label:"业务口径",
        align:"center",
        props:"businessCaliber"
      },{
        label:"操作",
        align:"center",
        props:"opt"
        ,slot:true
      }],
    }
  },
  // 初始化
  created() {
    this.treeLoading = true
    getAllTree().then((res) => {
      this.treeData = res.data
      this.treeLoading = false
    })
    this.getList()
  },
  // 监听属性类似于data概念
  computed: {},
  // 方法集合
  methods: {
    /** 查询列表 */
    getList() {
      // 统计
      tagStatistics({}).then((res) => {
        this.info = res.data
        console.info("eeeeeeeeeeeeeeeeeeeeeeeee"+JSON.stringify(this.info))
      })

      let obj = {
        tagTypeId: this.tagId,
        ...this.queryParams
      }
      this.$refs.uniTable.loadData(obj)
      // 分页
      // findPageList(obj).then((res) => {
      //   this.tableData = res.data.records
      //   this.total = res.data.total
      // })
    },
    // 点击查询右侧数据
    handleNodeClick(node, data) {
      console.log('点击标签分类了:', node, 'node')
      this.tag = node
      const { id } = node
      this.tagId = id
      this.queryParams.tagTypeId = id
      this.getList()
      this.showAddBtn = false
    },
    // 排序
    onSort(type, val) {
      this.queryParams.sortByName = type
      this.queryParams.nameSort = val
      console.log(this.queryParams, '排序')
      // this.getList();
    },
    // 修改
    handleEdit(row) {
      this.$router.push({
        path: '/sw/tagEdit',
        query: {
          id: row.id,
          tag_type_id: this.tag.id,
          tag_type_name: this.tag['label']
        }
      })
      // if (row.id) {
      //   this.$router.push({
      //     path: '/sw/tagEdit',
      //     query: {
      //       id: row.id
      //     }
      //   })
      // } else {
      //   this.$router.push('/sw/tagEdit')
      // }
      this.openTabDialog = true
    },
    // 删除
    handleDel(row) {
      this.$confirm('此操作将删除该数据项，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        tagDel(row.id).then((res) => {
          this.$message.success('删除成功')
          this.getList()
        })
      })
    },
    // 详情
    handleView(row) {
      this.$router.push({
        name: 'tagsDetails',
        params: {
          id: row.id
        }
      })
      // console.log(222);
      // this.title = "字典详情";
      // this.columnData = row;
      // this.openTabDialog = true;
    },
    // 当前页码
    handleCurrentChange(page) {
      this.currentPage = page
      this.getList()
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.uniTable.resetPage()
      this.resetForm("queryForm");
      this.handleQuery();
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  display: flex;

  .con-left {
    width: 25%;
    margin-right: 0.16667rem;
    .flex-between {
      display: block;
      padding: 0 0.16667rem 0.08333rem 0;
      border-bottom: 0.00833rem solid;
      border-bottom: 0.00833rem solid #f1f1f1;
    }
    .tab-icon {
      padding-right: 0.08333rem;
    }
  }
  .con-main {
    flex: 1;
  }
}
</style>
