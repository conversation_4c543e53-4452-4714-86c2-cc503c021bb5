<template>
  <el-card
    :class="this.bgColor === true ? 'notice-card' : 'unshow-notice-card'"
    v-loading="businessLoading"
    shadow="never"
  >
    <div slot="header" v-if="show">
      <span class="card-title">
        <div class="title">
          <div><i class="icon iconfont icon-yewudongtai"></i>新闻动态</div>
        </div>
        <div class="moreButtom">
          <el-button
            class="exec"
            type="text"
            style="font-size: 12px"
            @click="moreExt('business_news')"
            >更多<i class="el-icon-d-arrow-right"></i
          ></el-button>
        </div>
      </span>
    </div>
    <div class="home-card-body">
      <a v-for="item in business" :key="item.id" class="notice"
         :href="`/portal/home/<USER>/${item.programaType}/${item.noticeId}`" target="view_window">
        <div class="notice-title">
          「<span style="color: red">{{ item.noticeTypeName }}</span
          >」{{ item.noticeTitle }}
        </div>
        <div class="notice-time">
          {{ parseTime(item.createDate, "{y}-{m}-{d}") }}
        </div>
      </a>
      <el-empty
        style="padding: 20px 0"
        v-if="business.length <= 0"
        :image-size="50"
        description="暂无新闻动态"
        ><el-link
          icon="el-icon-refresh"
          @click="getBusiness"
          :underline="false"
          type="primary"
          >刷新</el-link
        ></el-empty
      >
    </div>
  </el-card>
</template>

<script>
import { selectTopPage } from "@/api/system/notice";
export default {
  props: ["item"],
  name: "business",
  data() {
    return {
      business: [],
      businessLoading: false,
      show: "",
      bgColor:"",
    };
  },
  created() {
    this.getBusiness();
    this.show = this.item.showTitle;
    this.bgColor = this.item.showBgColor;
  },
  methods: {
    moreExt(type) {
      window.open(`/portal/home/<USER>/${type}`);
    },
    getBusiness() {
      this.businessLoading = true;
      selectTopPage({
        programaType: "business_news",
        queryNum:10,
      }).then((res) => {
        this.businessLoading = false;
        this.business = res.data.records;
      });
    },
  },
  watch: {
    "item.showTitle": function (val) {
      this.show = val;
    },
    "item.showBgColor" :function(val) {
      this.bgColor = val
    }
  },
};
</script>

<style scoped lang="scss">
.notice-card {
  background: #fff;
  height: 100%;
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 0;
    }
  }
  .notice {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    padding: 4px 0;
    font-size: 14px;
    .notice-title {
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: nowrap;
      flex-basis: 70%;
    }
    .notice-time {
      flex-basis: 20%;
      text-align: right;
    }
  }
}

.unshow-notice-card {
  border: 0px;
  height: 100%;
  background: #fff;
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 0;
    }
  }
  .notice {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    padding: 4px 0;
    font-size: 14px;
    .notice-title {
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: nowrap;
      flex-basis: 70%;
    }
    .notice-time {
      flex-basis: 20%;
      text-align: right;
    }
  }
}



.moreButtom {
  display: flex;
  justify-content: flex-end;
}
.home-card-body {
  padding-top: 20px;
}
</style>
