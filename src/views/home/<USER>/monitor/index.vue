<template>
  <el-card
    :class="this.bgColor === true ? 'monitor-card' :'unshow-monitor-card'"
    v-loading="monitor"
    shadow="never"
  >
    <div slot="header" v-if="showTitle">
      <div class="card-title">
        <div class="title">
          <span>
            <i class="icon iconfont icon-gonggao"></i>云监控-环形图
          </span>
        </div>
        <div class="top">
          <el-button icon="el-icon-d-arrow-right" class="el-button" circle @click="cancel"></el-button>
          <transition v-if="show">
            <el-select
              v-model="item.keyValue"
              placeholder="请选择监控类型"
              @change="useChart()"
              style="margin-left:20px"
            >
              <el-option
                v-for="item in options"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictValue"
              ></el-option>
            </el-select>
          </transition>
        </div>
      </div>
    </div>
    <div class="home-body" id="home-body">
      <div class="chart" id="chart">
        <div ref="charts" id="charts"></div>
      </div>
    </div>
  </el-card>
</template>

<script>
import * as echarts from "echarts";
export default {
  name: "monitor",
  props: {
    item: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      showTitle: "",
      monitor: false,
      show: true,
      bgColor: "",
      options: [],
      laber: "",
      timer: "",
    };
  },
  mounted() {
    this.useChart();
  },
  created() {
    this.showTitle = this.item.showTitle;
    this.bgColor = this.item.showBgColor;
    this.getDicts("ringData").then((response) => {
      this.options = response.data;
    });
  },
  beforeDestroy() {
    // clearInterval(this.timer);
  },
  watch: {
    "item.keyValue": function (newVal, oldVal) {
      if (newVal && newVal !== "") {
        this.useChart();
      } else {
        this.msgInfo("请选择监控类型");
      }
    },
    "item.showTitle": function (val) {
      this.showTitle = val;
    },
    "item.showBgColor": function (val) {
      this.bgColor = val;
    },
  },
  methods: {
    cancel() {
      this.show = !this.show;
    },
    setUseChart() {
      this.item.keyValue = this.options.find(
        (ele) => ele.dictValue === this.item.keyValue
      ).dictValue;
      that.$emit("setUseChart", this.item);
    },
    useChart() {
      var chartDom = this.$refs.charts;
      var myChart = echarts.init(chartDom);
      var option;

      option = {
        tooltip: {
          formatter: "{a} <br/>{b} : {c}%",
        },
        series: [
          {
            name: "Pressure",
            type: "gauge",
            detail: {
              formatter: "{value}",
            },
            data: [
              {
                value: 0.0,
                name: "加载数据中..",
              },
            ],
          },
        ],
      };

      myChart.resize({
        width: 550,
        height: 380,
      });

      myChart.setOption(option);

      var that = this;
      if (that.item.keyValue !== undefined) {
        // that.timer = setInterval(() => {
        //   setTimeout(function () {
        that.$store
          .dispatch("monitor/getMonitordata", {
            monitorKey: that.item.keyValue,
            type: "percent",
            limitNum: 1,
          })
          .then((res) => {
            if (res !== []) {
              res.forEach((element) => {
                that.num = element.split("}")[1];
                that.num = parseFloat(that.num);

                that.laber = that.selectDictLabels(
                  that.options,
                  that.item.keyValue
                );
                var option;

                option = {
                  tooltip: {
                    formatter: "{a} <br/>{b} : {c}%",
                  },
                  series: [
                    {
                      name: "Pressure",
                      type: "gauge",
                      detail: {
                        formatter: "{value}",
                      },
                      data: [
                        {
                          value: that.num.toFixed(2),
                          name: that.laber,
                        },
                      ],
                    },
                  ],
                };

                myChart.setOption(option);

                //参数container为图表盒子节点.charts为图表节点
              });
            }
          });
        //   }, 0);
        // }, 5000);
      }

      // utilization(this.usefulVal).then((res) => {
      //   res
      //     .trim()
      //     .split("\n")
      //     .forEach((v, i) => {
      //       if (v.indexOf(this.usefulVal.key) !== -1) {
      //         v.split("\n").forEach((item,index) =>{
      //           console.log(item[2])
      //           if(item.indexof("#") === -1) {
      //             console.log(item)
      //           }
      //         })
      //       }
      //     });
      //   this.item.name = this.name;
      //   this.item.key = this.key
      //   this.$emit("setData", this.item);
      // });
    },
  },
};
</script>

<style lang="scss" scoped>
.monitor-card {
  width: 100%;
  height: 100%;
  background: #fff;
  .card-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .home-body {
    width: 100%;
    height: 300px;
    overflow: hidden;
    .chart {
      width: 100%;
      height: 100%;
      #charts {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }

  #titleTop {
    position: absolute;
    top: 50%;
    right: 50%;
    transform: translate(50%, -50%);
    margin: 0 auto;
    font-size: 16px;
    text-align: center;
  }

  .veRing {
    margin: 0 auto;
  }
  .top {
    display: flex;
    margin-left: 20px;
    overflow: hidden;
  }
  .el-button {
    display: inline-block;
    height: 40px;
    width: 40px;
  }
  .pieChart {
    width: 100%;
    height: 100%;
    .demo-form-inlin {
      width: 100%;
    }
  }
  #pieCharts {
    width: 100%;
    height: 180px;
    margin-top: 5px;
  }
  .demo-form-inline {
    width: 85%;
    margin: 0 auto;
    height: 40px;
  }
  .el-form-item__label {
    color: black;
    font-size: 12px;
  }
  .Box-enter-active,
  .Box-leave-active {
    transition: all 2s;
  }
  .Box-enter,
  .Box-leave-to {
    width: 0px;
    height: 0px;
  }
}
.unshow-monitor-card {
  height: 100%;
  background: #fff;
  border: 0px;
  .card-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .home-body {
    height: 300px;
    overflow: hidden;
    .chart {
      width: 100%;
      height: 100%;
      #charts {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
  #titleTop {
    position: absolute;
    top: 50%;
    right: 50%;
    transform: translate(50%, -50%);
    margin: 0 auto;
    font-size: 16px;
    text-align: center;
  }

  .veRing {
    margin: 0 auto;
  }
  .centerTitle {
  }
  .top {
    display: flex;
    margin-left: 20px;
    overflow: hidden;
  }
  .el-button {
    display: inline-block;
    height: 40px;
    width: 40px;
  }
  .pieChart {
    width: 100%;
    height: 100%;
    .demo-form-inlin {
      width: 100%;
    }
  }
  #pieCharts {
    width: 100%;
    height: 180px;
    margin-top: 5px;
  }
  .demo-form-inline {
    width: 85%;
    margin: 0 auto;
    height: 40px;
  }
  .el-form-item__label {
    color: black;
    font-size: 12px;
  }
  .Box-enter-active,
  .Box-leave-active {
    transition: all 2s;
  }
  .Box-enter,
  .Box-leave-to {
    width: 0px;
    height: 0px;
  }
}
</style>
