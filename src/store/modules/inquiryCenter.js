const state = {
  tabs: [],
  activeTab: 'inquiry-center'
}

const mutations = {
  ADD: (state, view) => {
    if (state.tabs.findIndex(v => v.id === view.id) > -1) {
      state.activeTab = view.id
    } else {
      state.tabs.push(view)
      state.activeTab = view.id
    }
  },
  DEL: (state, view) => {
    const index = state.tabs.findIndex(v => v.id === view.id)
    if (index > -1 && state.tabs.length > 1) {
      state.tabs.splice(index, 1)
      state.activeTab = state.tabs[index - 1].id
    }
  },
  setActiveTab: (state, id) => {
    state.activeTab = id
  },
}

const actions = {
  addTab({ commit }, view) {
    commit("ADD", view);
  },
  delTab({ commit }, view) {
    commit("DEL", view);
  },
  setActiveTab({ commit }, id) {
    commit("setActiveTab", id);
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
