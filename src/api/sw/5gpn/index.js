import request from '@/utils/request'

// 专线受理专网相关接口
export function getZxslCityList() {
  return request({
    url: '/sanquan/5gpn/zxsl/findCityList',
    method: 'get',
  })
}

export function queryZxslList(params) {
  return request({
    url: '/sanquan/5gpn/zxsl/queryList',
    method: 'get',
    params
  })
}

export function exportZxslList(params) {
  return request({
    url: '/sanquan/5gpn/zxsl/exportList',
    method: 'get',
    params
  })
}

export function getZxslDetail(id) {
  return request({
    url: '/sanquan/5gpn/zxsl/detail',
    method: 'get',
    params: { id }
  })
}

// 投资申请专网相关接口
export function getTzsqCityList() {
  return request({
    url: '/sanquan/5gpn/tzsq/findCityList',
    method: 'get',
  })
}

export function queryTzsqList(params) {
  return request({
    url: '/sanquan/5gpn/tzsq/queryList',
    method: 'get',
    params
  })
}

export function exportTzsqList(params) {
  return request({
    url: '/sanquan/5gpn/tzsq/exportList',
    method: 'get',
    params
  })
}

export function getTzsqDetail(id) {
  return request({
    url: '/sanquan/5gpn/tzsq/detail',
    method: 'get',
    params: { id }
  })
}

// 网络-基站专网相关接口
export function getWljsCityList() {
  return request({
    url: '/sanquan/5gpn/wljs/findCityList',
    method: 'get',
  })
}

export function queryWljsList(params) {
  return request({
    url: '/sanquan/5gpn/wljs/queryList',
    method: 'get',
    params
  })
}

export function exportWljsList(params) {
  return request({
    url: '/sanquan/5gpn/wljs/exportList',
    method: 'get',
    params
  })
}

export function getWljsDetail(id) {
  return request({
    url: '/sanquan/5gpn/wljs/detail',
    method: 'get',
    params: { id }
  })
}

// 网络-核心/边缘专网相关接口
export function getWlhxCityList() {
  return request({
    url: '/sanquan/5gpn/wlhx/findCityList',
    method: 'get',
  })
}

export function queryWlhxList(params) {
  return request({
    url: '/sanquan/5gpn/wlhx/queryList',
    method: 'get',
    params
  })
}

export function exportWlhxList(params) {
  return request({
    url: '/sanquan/5gpn/wlhx/exportList',
    method: 'get',
    params
  })
}

export function getWlhxDetail(id) {
  return request({
    url: '/sanquan/5gpn/wlhx/detail',
    method: 'get',
    params: { id }
  })
}
