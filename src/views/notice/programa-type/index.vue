<template>
  <div class="app-container">
    <el-card shadow="never">
      <!-- 查询条件表单 -->
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        :inline="true"
      >
        <el-form-item
          v-if="$store.getters.customParam.userType === 'admin'"
          label="租户"
        >
          <el-select
            v-model="queryParams.tenantId"
            style="width: 200px"
            size="small"
            filterable
            remote
            :remote-method="getTenantList"
            :loading="getTenantLoading"
            @change="tenantChange(queryParams.tenantId)"
          >
            <el-option
              v-for="item in tenantList"
              :key="item.tenantId"
              :label="item.tenantName"
              :value="item.tenantId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="栏目类型编码" prop="programaTypeCode">
          <el-input
            clearable
            placeholder="请输入栏目类型编码"
            size="small"
            style="width: 240px"
            v-model="queryParams.programaTypeCode"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="栏目类型名称" prop="programaTypeName">
          <el-input
            clearable
            placeholder="请输入栏目类型名称"
            size="small"
            style="width: 240px"
            v-model="queryParams.programaTypeName"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <!-- 搜索扩展工具栏 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            >新增</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <!-- 列表内容 -->
      <el-table :loading="loading" :data="dataList">
        <el-table-column
          label="栏目类型编码"
          align="left"
          prop="programaTypeCode"
          width="200"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="栏目类型名称"
          align="left"
          prop="programaTypeName"
          width="200"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="租户名称"
          align="left"
          prop="tenantName"
          width="200"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              :loading="
                reloadId === scope.row.programaTypeId && reloadType === 'update'
              "
              >编辑
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              :loading="
                reloadId === scope.row.programaTypeId && reloadType === 'delete'
              "
              >删除
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-s-promotion"
              @click="handleSync(scope.row)"
              :loading="
                reloadId === scope.row.programaTypeId && reloadType === 'sync'
              "
              >同步至其他租户
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 新增或编辑对话框 -->
      <el-dialog
        append-to-body
        width="1000px"
        :title="title"
        :visible.sync="open"
        :close-on-press-escape="false"
        @close="cancel"
        v-dialogDrag
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="150px">
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="栏目类型编码" prop="programaTypeCode">
                <el-input
                  v-model="form.programaTypeCode"
                  placeholder="请输入栏目类型编码"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="栏目类型名称" prop="programaTypeName">
                <el-input
                  v-model="form.programaTypeName"
                  placeholder="请输入栏目类型名称"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="submitForm" type="primary" :loading="saveLoading"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import {
  add,
  del,
  update,
  findOne,
  findPageList,
  syncData,
} from "/src/api/system/programaType";
import { list as tenantList } from "/src/api/system/tenant";

export default {
  name: "NoticeType",
  data() {
    return {
      // 列表遮罩层
      loading: true,
      // 提交遮罩层
      saveLoading: false,
      getTenantLoading: false,
      // 数据行锁ID
      reloadId: undefined,
      // 数据行锁操作类型
      reloadType: undefined,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 参数表格数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示新增或编辑对话框
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenantId: this.$store.getters.customParam.tenantId,
        programaTypeCode: undefined,
        programaTypeName: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        programaTypeCode: [
          { required: true, message: "栏目类型编码不能为空", trigger: "blur" },
          { max: 64, message: "长度需要小于 64 个字符", trigger: "blur" },
        ],
        programaTypeName: [
          { required: true, message: "栏目类型名称不能为空", trigger: "blur" },
          { max: 128, message: "长度需要小于 128 个字符", trigger: "blur" },
        ],
      },
      // 租户列表
      tenantList: [],
    };
  },
  created() {
    this.getList();
    this.getTenantList();
  },
  methods: {
    // 数据行上锁
    setLoad(id, type) {
      this.reloadId = id;
      this.reloadType = type;
    },
    // 数据行锁重置
    resetLoad() {
      this.reloadId = undefined;
      this.reloadType = undefined;
    },
    // 重置按钮事件
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 取消按钮事件
    cancel() {
      this.resetForm("form");
      this.resetLoad();
      this.open = false;
    },
    // ┏ ━ ━ ━ ━   ━ ━ ━ ━ CRUD基础操作方法 ━ ━ ━ ━   ━ ━ ━ ━ ┓
    // 新增按钮事件
    handleAdd() {
      this.resetForm("form");
      this.open = true;
      this.title = "新增";
      this.reloadType = "add";
    },
    // 提交按钮事件
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.saveLoading = true;
          if (this.reloadType === "add") {
            add({ tenantId: this.queryParams.tenantId, ...this.form }).then(
              (response) => {
                this.submitResponse(response);
              }
            );
          } else if (this.reloadType === "update") {
            update(this.form).then((response) => {
              this.submitResponse(response);
            });
          }
        }
      });
    },
    // 提交响应处理
    submitResponse(response) {
      if (!response.success) {
        this.$message.error(response.message);
      } else {
        this.$message.success("保存成功");
        this.open = false;
        this.getList();
      }
      this.saveLoading = false;
      this.resetLoad();
    },
    // 删除按钮事件
    handleDelete(row) {
      this.setLoad(row.programaTypeId, "delete");
      this.$confirm("是否确认删除这条数据?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return del({ programaTypeId: row.programaTypeId });
        })
        .then(() => {
          this.resetLoad();
          this.getList();
          this.$message.success("删除成功");
        })
        .catch(() => {
          this.resetLoad();
        });
    },
    // 编辑按钮事件
    handleUpdate(row) {
      this.form = JSON.parse(JSON.stringify(row));
      this.open = true;
      this.title = "编辑";
      this.reloadType = "update";
    },
    // 搜索按钮事件
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 详情查询
    getOne(row) {
      // this.reset();
      // this.reloadId = row.programaTypeId;
      // this.reloadType = "select";
      // getById(programaTypeId).then((response) => {
      //   if (response.data) {
      //     this.form = response.data;
      //     this.open = true;
      //     this.title = "修改租户";
      //   } else {
      //     this.$message.error("数据异常！");
      //   }
      // });
    },
    // 列表查询
    getList() {
      this.loading = true;
      findPageList({ ...this.queryParams }).then((response) => {
        this.dataList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // ┗ ━ ━ ━ ━   ━ ━ ━ ━ CRUD基础操作方法 ━ ━ ━ ━   ━ ━ ━ ━ ┛
    // 获取租户列表
    getTenantList(tenantName) {
      this.getTenantLoading = true;
      let query = {}
      if (tenantName !== undefined && tenantName !== '') {
        query.tenantName = tenantName
        query.tenantId = undefined
      } else {
        query.tenantId = this.queryParams.tenantId
      }
      tenantList(query).then((response) => {
        this.tenantList = response.data;
      }).finally(() => {
        this.getTenantLoading = false;
      });
    },
    // 租户列表修改事件
    tenantChange(tenantId) {
      if (tenantId !== "") {
        this.queryParams.tenantName = this.tenantList.find(
          (item) => item.tenantId === tenantId
        ).tenantName;
      }
      this.handleQuery();
    },
    // 同步至其他租户按钮事件
    handleSync(row) {
      let _this = this;
      this.$confirm("是否确认同步这条数据至其他所有租户?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          _this.setLoad(row.programaTypeId, "sync");
          syncData({ programaTypeId: row.programaTypeId })
            .then((res) => {
              if (res.success) {
                _this.getList();
                _this.$message.success(res.message);
              } else {
                _this.$message.error(res.message);
              }
              _this.resetLoad();
            })
            .catch((err) => {
              _this.resetLoad();
              _this.$message.error("同步异常,请稍后再试");
            });
        })
        .catch((err) => {
          // 点击取消按钮
        });
    },
  },
};
</script>
