<template>
  <div class="captiontext" :style="{ background: datas.config.backColor }">
    <div
      style="padding: 6px 0"
      :style="{
        'border-bottom': datas.config.borderBott
          ? '1px solid #F9F9F9'
          : '1px solid #fff',
      }"
    >
      <!-- 标题 -->
      <h2
        :style="{
          'font-size': datas.config.wordSize + 'px',
          'font-weight': datas.config.wordWeight,
          color: datas.config.wordColor,
          'text-align': datas.config.positions,
          height: datas.config.wordHeight + 'px',
          'line-height': datas.config.wordHeight + 'px',
          'padding-right': !(datas.config.positions !== 'center' && datas.config.more.show)
            ? '0'
            : '60px',
        }"
        v-if="datas.config.name"
      >
        {{ datas.config.name }}
      </h2>

      <!-- 描述文字 -->
      <p
        :style="{
          'font-size': datas.config.descriptionSize + 'px',
          'font-weight': datas.config.descriptionWeight,
          color: datas.config.descriptionColor,
          'text-align': datas.config.positions,
        }"
        style="margin-top: 8px"
        v-if="datas.config.description"
      >
        {{ datas.config.description }}
      </p>

      <!-- 更多 -->
      <p
        class="more"
        v-show="datas.config.more.show"
        :class="datas.config.positions !== 'center' ? 'lef' : ''"
        :style="{
          color: datas.config.more.type === 0 ? '#38f' : '',
          top: (datas.config.wordHeight - 6) / 2 + 'px',
        }"
      >
        {{ datas.config.more.type === 2 ? '' : datas.config.more.text }}
        <span> {{ datas.config.more.type === 0 ? '' : '>' }}</span>
      </p>
    </div>

    <!-- 删除组件 -->
    <slot name="deles" />
  </div>
</template>

<script>
export default {
  name: 'captiontext',
  props: {
    datas: Object,
  },
}
</script>

<style scoped lang="less">
.captiontext {
  border: 2px solid #fff;
  box-sizing: border-box;
  width: 100%;
  padding: 0 14px;
  min-height: 20px;
  position: relative;

  h2,
  p {
    word-wrap: break-word;
    min-height: 10px;
  }

  /* 更多 */
  .more {
    font-size: 10px;
    color: #969799;
    text-align: center;
    &.lef {
      position: absolute;
      right: 15px;
      top: 12px;
    }
  }
}
</style>
