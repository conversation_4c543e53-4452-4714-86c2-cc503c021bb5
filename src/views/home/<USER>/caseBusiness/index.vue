<template>
  <el-card class="notice-card" v-loading="businessLoading" shadow="never">
    <div slot="header">
      <span class="card-title">
        <div class="title">
          <div>业务动态</div>
          <el-button type="text" style="font-size: 16px" @click="moreExt('business_news')">MORE</el-button>
        </div>
      </span>
    </div>
    <div class="home-card-body">
      <div class="items" v-for="item in business" :key="item.id">
        <div class="items-img">
          <img :src="item.coverUrl" alt="图片加载中..." />
          <!-- <img src="../../../../assets/images/home/<USER>" alt=""> -->
        </div>
        <div class="items-title">{{item.noticeTitle}}</div>
        <div class="items-content">{{item.noticeContentText}}</div>
        <div class="items-button" @click="detail(item)">MORE</div>
      </div>
      <el-empty
        style="margin:0 auto"
        v-if="business.length <= 0"
        :image-size="50"
        description="暂无新闻动态"
      >
        <el-link icon="el-icon-refresh" @click="getBusiness" :underline="false" type="primary">刷新</el-link>
      </el-empty>
    </div>
  </el-card>
</template>

<script>
import { selectTop5Page } from "@/api/system/notice";
export default {
  name: "caseBusiness",
  data() {
    return {
      business: [],
      businessLoading: false,
    };
  },
  created() {
    this.getBusiness();
  },
  methods: {
    moreExt(type) {
      window.open(`/portal/home/<USER>/${type}`);
    },
    getBusiness() {
      this.businessLoading = true;
      selectTop5Page({
        programaType: "business_news",
      }).then((res) => {
        this.businessLoading = false;
        this.business = res.data.records;
        this.business = this.business.slice(0, 3);
      });
    },
    detail(item) {
      window.open(`/portal/home/<USER>/${item.programaType}/${item.noticeId}`);
    },
  },
  watch: {},
};
</script>

<style scoped lang="scss">
.notice-card {
  border: 0px;
  width: 100%;
  background: #fff;
  height: 100%;
  ::v-deep .el-card__header {
    padding: 0;
    height: 20px;
    border: 0px;
    margin-bottom: 28px;
  }
  ::v-deep .el-card__body {
    padding: 0;
  }
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      width: 100%;
      font-size: 24px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #646464;
      display: flex;
      justify-content: space-between;
    }
  }
  .home-card-body {
    width: 100%;
    height: 470px;
    display: flex;
    justify-content: space-between;
    .items {
      width: 30%;
      height: 470px;
      .items-img {
        width: 100%;
        height: 250px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .items-title {
        width: 100%;
        margin: 20px 0;
        font-size: 18px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        // color: #666666;
        color: black;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .items-content {
        width: 100%;
        margin: 20px 0;
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #999999;
        line-height: 28px;
        word-break: break-all;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
      }
      .items-button {
        width: 105px;
        height: 39px;
        border: 1px solid #d5d5d5;
        text-align: center;
        line-height: 39px;
        cursor: pointer;
      }
    }
  }
}

// .moreButtom {
//   display: flex;
//   justify-content: flex-end;
// }
</style>
