<template>
  <div class="app-container">
    <el-card shadow="never">
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        :inline="true"
      >
        <el-form-item
          v-if="$store.getters.customParam.userType === 'admin'"
          label="租户"
        >
          <el-select
            v-model="queryParams.tenantId"
            style="width: 200px"
            filterable
            remote
            :remote-method="getTenantList"
            :loading="getTenantLoading"
            @change="tenantChange"
            size="small"
          >
            <el-option
              v-for="item in tenantList"
              :key="item.tenantId"
              :label="item.tenantName"
              :value="item.tenantId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="用户名称" prop="staffName">
          <el-input
            v-model="queryParams.staffName"
            placeholder="请输入用户名称"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
          >搜索
          </el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置
          </el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="loading" :data="onlineList">
        <el-table-column label="序号" type="index" width="50" align="center">
        </el-table-column>
        <el-table-column
          label="租户"
          prop="customParam.tenantName"
          :show-overflow-tooltip="true"
          align="center"
        />
        <el-table-column
          label="用户ID"
          prop="userid"
          :show-overflow-tooltip="true"
          align="center"
          width="285px"
        />
        <el-table-column
          label="所在组织"
          prop="currentOrgName"
          :show-overflow-tooltip="true"
          align="center"
        />
        <el-table-column
          label="令牌"
          prop="token"
          :show-overflow-tooltip="true"
          align="center"
          width="180"
        />

        <el-table-column
          label="用户姓名"
          prop="staffName"
          :show-overflow-tooltip="true"
          align="center"
          width="180"
        />
        <el-table-column
          label="过期时间"
          prop="expiration"
          :show-overflow-tooltip="true"
          align="center"
          width="180"
        />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="forceLogout(scope.row)"
              v-hasPermi="['system:role:edit']"
              v-if="scope.row.tenantId === $store.getters.customParam.tenantId || $store.getters.customParam.userType === 'admin'"
            >强退
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!--      <pagination-->
      <!--        v-show="total > 0"-->
      <!--        :total="total"-->
      <!--        :page.sync="queryParams.pageNum"-->
      <!--        :limit.sync="queryParams.pageSize"-->
      <!--        @pagination="getList"-->
      <!--      />-->
    </el-card>
  </div>
</template>

<script>
import {onlineUsers, forceLogout} from "@/api/system/online";
import {list as tenantList} from "@/api/system/tenant";

export default {
  name: "index",
  data() {
    return {
      // 遮罩层
      loading: true,
      getTenantLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        staffName: '',
        tenantId: this.$store.getters.customParam.tenantId,
      },
      onlineList: [],
      tenantList: [],
    }
  },
  created() {
    this.getOnlineList();
    this.getTenantList();
  },
  methods: {
    /** 查询系统日志 */
    getOnlineList() {
      this.loading = true;
      onlineUsers().then(res => {
        this.onlineList = res.data
        this.loading = false;
      })
    },
    forceLogout(row) {
      this.$confirm('此操作将退出('+row.staffName+'),是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        forceLogout(row.token).then(res => {
          this.$message.success("强退成功！")
          this.getOnlineList();
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    getTenantList(tenantName) {
      this.getTenantLoading = true;
      let query = {}
      if (tenantName !== undefined && tenantName !== '') {
        query.tenantName = tenantName
        query.tenantId = undefined
      } else {
        query.tenantId = this.queryParams.tenantId
      }
      tenantList(query).then((response) => {
        this.tenantList = response.data;
      }).finally(() => {
        this.getTenantLoading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getOnlineList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    tenantChange() {
      this.queryParams.pageNum = 1;
      this.getOnlineList();
    },
  }
}
</script>

<style scoped>

</style>
