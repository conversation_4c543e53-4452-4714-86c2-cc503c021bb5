<template>
  <div class="app-container">
    <el-card shadow="never">
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        :inline="true"
      >
        <el-form-item label="租户名称" prop="tenantName">
          <el-input
            v-model="queryParams.tenantName"
            placeholder="请输入租户名称"
            clearable
            size="small"
            style="width: 240px; margin-right: 20px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="租户登录名" prop="tenantLoginName">
          <el-input
            v-model="queryParams.tenantLoginName"
            placeholder="请输入租户登录名"
            clearable
            size="small"
            style="width: 240px; margin-right: 20px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="租户管理员" prop="displayName">
          <el-input
            v-model="queryParams.displayName"
            placeholder="请输入租户管理员"
            clearable
            size="small"
            style="width: 240px; margin-right: 20px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="有效日期" prop="effectiveDate">
          <el-date-picker
            v-model="dateRange"
            size="small"
            style="width: 240px; margin-right: 20px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="租户状态">
          <el-select
            v-model="queryParams.tenantStatus"
            placeholder="租户状态"
            clearable
            size="small"
            style="width: 240px; margin-right: 20px"
          >
            <el-option
              v-for="dict in effectiveStatusOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            >新增</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="dataList">
        <el-table-column
          label="租户名称"
          align="left"
          prop="tenantName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="租户登录名"
          align="left"
          prop="tenantLoginName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="租户管理员"
          align="left"
          prop="displayName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="租户域名"
          align="left"
          prop="tenantDomain"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="租户前台位置"
          align="left"
          prop="tenantHtmlPath"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="业务ID"
          align="left"
          prop="businessId"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="最大用户数"
          align="left"
          prop="maxStaff"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="有效截止时间"
          align="left"
          prop="effectiveDate"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="租户状态"
          align="left"
          prop="tenantStatus"
          :formatter="effectiveStatusFormat"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <el-tag effect="plain" :type="change(scope.row)">
              {{
                selectDictLabel(effectiveStatusOptions, scope.row.tenantStatus)
              }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="260"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              :loading="
                reloadId === scope.row.tenantId && reloadType === 'edit'
              "
              :disabled="scope.row.tenantLoginName === 'system'"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              :loading="
                reloadId === scope.row.tenantId && reloadType === 'remove'
              "
              :disabled="scope.row.tenantLoginName === 'system'"
              >删除</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-circle-check"
              @click="openAddRole(scope.row)"
              v-hasPermi="['system:role:menu']"
              >角色赋权</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-setting"
              @click="openJurisdiction(scope.row)"
              :disabled="$store.getters.customParam.userType === 'user'"
              >管辖维护</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改参数配置对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="open"
        width="1000px"
        append-to-body
        :close-on-press-escape="false"
        @close="cancel"
        v-dialogDrag
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="150px">
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="租户名称" prop="tenantName">
                <el-input
                  v-model="form.tenantName"
                  placeholder="请输入租户名称"
                  maxlength="64"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="租户登录名" prop="tenantLoginName">
                <el-input
                  v-model="form.tenantLoginName"
                  placeholder="请输入租户登录名"
                  maxlength="64"
                  :disabled="reloadType === 'edit'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="租户管理员登录名" prop="displayName">
                <el-input
                  v-model="form.displayName"
                  placeholder="请输入登录名称"
                  maxlength="64"
                  :disabled="reloadType === 'edit'"
                />
              </el-form-item>
            </el-col>
            <el-col v-if="reloadType === 'add'" :span="12">
              <el-form-item label="租户管理员姓名" prop="staffName">
                <el-input
                  v-model="form.staffName"
                  placeholder="请输入租户管理员姓名"
                  maxlength="64"
                />
              </el-form-item>
            </el-col>
            <el-col v-if="reloadType === 'add'" :span="12">
              <el-form-item label="租户管理员手机号码" prop="cellphone">
                <el-input
                  v-model="form.cellphone"
                  placeholder="请输入租户管理员手机号码"
                  maxlength="64"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="域名">
                <el-input
                  v-model="form.tenantDomain"
                  placeholder="请输入域名"
                  maxlength="64"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="前台位置">
                <el-input
                  v-model="form.tenantHtmlPath"
                  placeholder="请输入前台位置"
                  maxlength="11"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="业务ID">
                <el-input
                  v-model="form.businessId"
                  placeholder="请输入业务ID"
                  maxlength="50"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="最大用户数" prop="maxStaff">
                <el-input-number
                  v-model="form.maxStaff"
                  controls-position="right"
                  :min="0"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="有效截止时间" prop="effectiveDate">
                <el-date-picker
                  v-model="form.effectiveDate"
                  size="small"
                  style="width: 325px"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <div v-if="reloadType === 'add'">
              <el-col :span="12">
                <el-form-item label="组织名称" prop="orgName">
                  <el-input
                    v-model="form.orgName"
                    placeholder="请输入组织名称"
                    maxlength="50"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="组织全称">
                  <el-input
                    v-model="form.fullName"
                    placeholder="请输入组织全称"
                    maxlength="50"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="组织编码" prop="code">
                  <el-input
                    v-model="form.code"
                    placeholder="请输入组织编码"
                    maxlength="50"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="组织展示顺序">
                  <el-input-number
                    v-model="form.orgSort"
                    controls-position="right"
                    :min="0"
                  />
                </el-form-item>
              </el-col>
            </div>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="saveLoading"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>

      <!-- 管辖 -->
      <el-dialog
        title="管辖维护"
        :visible.sync="jurisdictionOpen"
        width="1300px"
        height="900px"
        append-to-body
      >
        <template>
          <div
            class="app-container"
            style="height: calc(100vh - 300px); width: 100%"
          >
            <el-card class="dep-card" shadow="never">
              <el-form
                :model="queryJurisdictionParams"
                ref="queryJurisdictionForm"
                :inline="true"
                label-width="68px"
              >
                <el-form-item label="用户名称" prop="tenantName">
                  <el-input
                    v-model="queryJurisdictionParams.tenantName"
                    placeholder="请输入用户名称"
                    clearable
                    size="small"
                    style="width: 240px"
                    @keyup.enter.native="handleTenantQuery"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    icon="el-icon-search"
                    size="mini"
                    @click="handleTenantQuery"
                    >搜索
                  </el-button>
                  <el-button
                    icon="el-icon-refresh"
                    size="mini"
                    @click="resetTenantQuery"
                    >重置</el-button
                  >
                </el-form-item>
              </el-form>

              <el-table
                ref="multipleJurisdictionTable"
                v-loading="jurisdictionLoading"
                :data="tenantDataList"
                :row-key="rowKey"
                @selection-change="handleTenantSelectionChange"
              >
                <el-table-column
                  type="selection"
                  :reserve-selection="true"
                  align="center"
                  width="100"
                />
                <el-table-column
                  label="租户名称"
                  align="left"
                  prop="tenantName"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="租户登录名"
                  align="left"
                  prop="tenantLoginName"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="租户管理员"
                  align="left"
                  prop="displayName"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="租户域名"
                  align="left"
                  prop="tenantDomain"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="租户前台位置"
                  align="left"
                  prop="tenantHtmlPath"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="最大用户数"
                  align="left"
                  prop="maxStaff"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="有效截止时间"
                  align="left"
                  prop="effectiveDate"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="租户状态"
                  align="left"
                  prop="tenantStatus"
                  :formatter="effectiveStatusFormat"
                  :show-overflow-tooltip="true"
                >
                  <template slot-scope="scope">
                    <el-tag :type="change(scope.row)">
                      {{
                        selectDictLabel(
                          effectiveStatusOptions,
                          scope.row.tenantStatus
                        )
                      }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
              <pagination
                v-show="tenantTotal > 0"
                :total="tenantTotal"
                :page.sync="queryJurisdictionParams.pageNum"
                :limit.sync="queryJurisdictionParams.pageSize"
                @pagination="getTenantList"
              />
            </el-card>
          </div>
        </template>
        <div slot="footer" class="dialog-footer">
          <el-button
            type="primary"
            @click="jurisdictionSubmitForm"
            :loading="jurisdictionSubmitLoading"
            >确 定</el-button
          >
          <el-button @click="jurisdictionCancel">取 消</el-button>
        </div>
      </el-dialog>
    </el-card>
    <transfer-table
      ref="transferTable"
      modelName="角色赋权"
      @loadData="loadData"
      :listRole="listRole"
      :listProp="{
        key: 'roleName',
        name: '角色名称',
      }"
      :selectedProp="{
        key: 'roleName',
        name: '角色名称',
      }"
      @add="addTenantRole"
      @remove="delTenantRole"
    />
  </div>
</template>

<script>
import {
  page,
  getById,
  del,
  add,
  update,
  delTenantRole,
  updateTenantRole,
  getHadRolesAndNoRoles,
} from "@/api/system/tenant";
import TransferTable from "@/components/TransferTable";
import { findRoleListByScope } from "@/api/system/role";
import {
  addJurisdictionTenant,
  getJurisdictionTenantIds,
} from "@/api/system/tenantJurisdiction";

export default {
  name: "Tenant",
  components: { TransferTable },
  data() {
    return {
      // 遮罩层
      loading: true,
      jurisdictionLoading: false,
      jurisdictionSubmitLoading: false,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      tenantTotal: 0,
      // 参数表格数据
      dataList: [],
      tenantDataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      jurisdictionOpen: false,
      // 是否有效数据字典
      effectiveStatusOptions: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenantName: undefined,
        tenantLoginName: undefined,
        displayName: undefined,
        effectiveDate: undefined,
        tenantStatus: undefined,
      },
      queryJurisdictionParams: {
        pageNum: 1,
        pageSize: 8,
        tenantName: undefined,
        excludeTenantId: undefined,
        tenantStatus: "valid",
      },
      tenantId: this.$store.getters.customParam.tenantId,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        tenantName: [
          { required: true, message: "租户名称不能为空", trigger: "blur" },
        ],
        tenantLoginName: [
          { required: true, message: "租户登录名不能为空", trigger: "blur" },
        ],
        staffName: [
          {
            required: true,
            message: "租户管理员姓名不能为空",
            trigger: "blur",
          },
        ],
        maxStaff: [
          { required: true, message: "最大用户数不能为空", trigger: "blur" },
        ],
        effectiveDate: [
          { required: true, message: "有效截止时间不能为空", trigger: "blur" },
        ],
        displayName: [
          {
            required: true,
            message: "租户管理员登录名不能为空",
            trigger: "blur",
          },
        ],
        orgName: [
          { required: true, message: "组织名称不能为空", trigger: "blur" },
        ],
        kind: [{ required: true, message: "请选择组织类型", trigger: "blur" }],
        code: [
          { required: true, message: "组织编码不能为空", trigger: "blur" },
        ],
        cellphone: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
      },
      reloadId: undefined,
      reloadType: undefined,
      saveLoading: false,
      defaultOrgId: this.$store.getters.orgId,
      columns: [
        { key: 0, label: `用户编号`, visible: false },
        { key: 1, label: `登录名称`, visible: true },
        { key: 2, label: `用户名称`, visible: true },
        { key: 3, label: `组织`, visible: true },
        { key: 4, label: `手机号码`, visible: true },
        { key: 5, label: `邮箱`, visible: true },
      ],
      loadTenantId: undefined,
      listRole: [],
      // 租户已管理的管辖租户数据
      currentJurisdictionTenantIds: [],
      // 租户已选中但是未保存的管辖租户数据
      selectedJurisdictionTenantIds: [],
      selectedTenant: {},
    };
  },
  created() {
    this.getList();
    this.getDicts("tenant_status").then((response) => {
      this.effectiveStatusOptions = response.data;
    });
  },
  methods: {
    rowKey(row) {
      return row.tenantId;
    },
    change(row) {
      if (row.tenantStatus === "valid") {
        return "";
      } else if (row.tenantStatus === "invalid") {
        return "danger";
      }
    },
    /** 查询参数列表 */
    getList() {
      this.loading = true;
      page({
        ...this.addDateRange({}, this.dateRange).params,
        ...this.queryParams,
      }).then((response) => {
        this.dataList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 查询参数列表 */
    getTenantList() {
      this.jurisdictionLoading = true;
      page(this.queryJurisdictionParams).then((response) => {
        this.tenantDataList = response.data.records;
        this.$nextTick((_) => {
          if (this.currentJurisdictionTenantIds.length === 0) {
            this.$refs.multipleJurisdictionTable.clearSelection();
          }
          this.tenantDataList.forEach((i) => {
            if (this.currentJurisdictionTenantIds.indexOf(i.tenantId) > -1) {
              this.$refs.multipleJurisdictionTable.toggleRowSelection(i, true); // 设置默认选中
            } else {
              this.$refs.multipleJurisdictionTable.toggleRowSelection(i, false);
            }
          });
        });
        this.tenantTotal = response.data.total;
        this.jurisdictionLoading = false;
      });
    },
    // 参数系统内置字典翻译
    effectiveStatusFormat(row, column) {
      return this.selectDictLabel(
        this.effectiveStatusOptions,
        row.tenantStatus
      );
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reloadId = undefined;
      this.reloadType = undefined;
      this.reset();
    },
    jurisdictionCancel() {
      this.jurisdictionOpen = false;
      this.resetForm("queryJurisdictionForm");
    },
    // 表单重置
    reset() {
      this.form = {
        tenantName: undefined,
        tenantLoginName: undefined,
        displayName: undefined,
        tenantAdminId: undefined,
        tenantDomain: undefined,
        tenantHtmlPath: undefined,
        businessId: undefined,
        maxStaff: undefined,
        effectiveDate: undefined,
        tenantStatus: undefined,
      };
      this.resetForm("queryForm");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleTenantQuery() {
      this.queryJurisdictionParams.pageNum = 1;
      this.getTenantList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.queryParams.tenantStatus = undefined;
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 重置按钮操作 */
    resetTenantQuery() {
      this.resetForm("queryJurisdictionForm");
      this.handleTenantQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加租户";
      // this.form.tenantStatus = 'valid';
      this.reloadType = "add";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const tenantId = row.tenantId;
      this.reloadId = tenantId;
      this.reloadType = "edit";
      getById(tenantId).then((response) => {
        if (response.data) {
          this.form = response.data;
          this.open = true;
          this.title = "修改租户";
        } else {
          this.$message.error("数据异常！");
        }
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.saveLoading = true;
          if (this.form.tenantId !== undefined) {
            update(this.form).then((response) => {
              if (!response.success) {
                this.$message.error(response.message);
                this.saveLoading = false;
              } else {
                this.msgSuccess("修改成功");
                this.open = false;
                this.saveLoading = false;
                this.getList();
              }
            });
          } else {
            add(this.form).then((response) => {
              if (!response.success) {
                this.$message.error(response.message);
                this.saveLoading = false;
              } else {
                this.msgSuccess("新增成功");
                this.open = false;
                this.saveLoading = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const tenantId = row.tenantId;
      this.reloadId = tenantId;
      this.reloadType = "remove";
      this.$confirm(
        '是否确认删除租户名称为"' + row.tenantName + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return del(tenantId);
        })
        .then(() => {
          this.reloadId = undefined;
          this.reloadType = undefined;
          this.getList();
          this.msgSuccess("删除成功");
        })
        .catch(() => {
          this.reloadId = undefined;
          this.tenantId = undefined;
        });
    },
    openAddRole(row) {
      this.loadTenantId = row.tenantId;
      this.$refs.transferTable.open();
    },
    addTenantRole(row) {
      updateTenantRole(this.loadTenantId, row.roleId).then((r) => {
        this.$refs.transferTable.reload();
      });
    },
    delTenantRole(row) {
      delTenantRole(this.loadTenantId, row.roleId).then((r) => {
        this.$refs.transferTable.reload();
      });
    },
    loadData() {
      findRoleListByScope({ tenantId: this.tenantId }).then((r) => {
        this.getDicts("scope").then((response) => {
          const newData = response.data.map((v) => {
            v["children"] = r.data.filter((i) => i.roleScope === v.dictValue);
            v["roleName"] = v.dictLabel;
            v["roleId"] = v.dictDataId;
            v["isOp"] = true;
            return v;
          });
          this.$refs.transferTable.setData(newData);
        });
      });
      getHadRolesAndNoRoles({ tenantId: this.loadTenantId }).then((r) => {
        this.$refs.transferTable.setRightData({ data: r.data.havelist });
      });
    },
    async openJurisdiction(row) {
      this.selectedTenant = row;
      this.queryJurisdictionParams.excludeTenantId = row.tenantId
      // 查询当前租户管辖的所有租户
      this.currentJurisdictionTenantIds = [];
      this.selectedJurisdictionTenantIds = [];
      await getJurisdictionTenantIds({ manageTenantId: row.tenantId }).then(
        (response) => {
          this.currentJurisdictionTenantIds = response.data;
          this.selectedJurisdictionTenantIds = response.data;
        }
      );
      // 查询所有租户
      await this.resetTenantQuery();
      this.jurisdictionOpen = true;
    },
    handleTenantSelectionChange(val) {
      let tenantIds = val.map(function (value, index, array) {
        return value.tenantId;
      });
      this.selectedJurisdictionTenantIds = tenantIds;
    },
    jurisdictionSubmitForm() {
      this.jurisdictionSubmitLoading = true;
      let managedTenantIds = Array.from(
        new Set([...this.selectedJurisdictionTenantIds])
      );
      addJurisdictionTenant({
        managedTenantIds: managedTenantIds,
        manageTenantId: this.selectedTenant.tenantId,
      }).then((response) => {
        if (response.success) {
          this.msgSuccess("添加管辖租户成功！");
          this.jurisdictionOpen = false;
          this.jurisdictionSubmitLoading = false;
          this.resetQuery();
        } else {
          this.$message.error("添加管辖租户失败！");
        }
      });
    },
  },
};
</script>
