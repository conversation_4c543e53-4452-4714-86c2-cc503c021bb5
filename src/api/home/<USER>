import request from "@/utils/request";

// 通多id查询
export function getById(id) {
  return request({
    url: "/gh/tu-policy/getById/"+id,
    method: "get",
  });
}

// 查询
//首页政策法规列表
export function getAll(p) {
  return request({
    url: "/gh/tu-policy/list",
    method: "get",
    params:p
  });
}

// 分页查询
export function getByPage(p) {
  return request({
    url: "/gh/tu-policy/page",
    method: "get",
    params:p
  });
}

// 新增公告
export function addLaw(data) {
  return request({
    url: "/gh/tu-policy/save",
    method: "post",
    data: data
  });
}

//首页请求
//书香中国
// export function selectSFByDataAndTitle(data) {
//   return request({
//     url: "/dingding/scholarly/selectSFByDataAndTitle",
//     method: "post",
//     data: data
//   });
// }

//首页请求
//健步走echart图
// export function getEchartData(data) {
//   return request({
//     url: "/dingding/statistics/fronPageChart",
//     method: "post",
//     data: data
//   });
// }


//政策法规删除
export function delLaw(id) {
  return request({
    url: "/gh/tu-policy/deleteById/"+id,
    method: "get",
  });
}

