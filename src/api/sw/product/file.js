import request from '@/utils/request'

// 文件上传
export function fileUpload(data) {
    return request({
      url: '/sanquan/files/upload',
      method: 'post',
      ContentType:'multipart/form-data;',
      data,
    })
}

// 文件下载
export function fileDown(id) {
    return request({
      url: '/sanquan/files/down/' + id,
      method: 'get'
    })
}

// 文件（文件流）下载
export function fileDownload(id) {
  return request({
    url: '/sanquan/files/download/' + id,
    method: 'get',
    responseType: 'blob'
  })
}

// 文件删除
export function fileDelete(id) {
    return request({
      url: '/sanquan/files/delete/' + id,
      method: 'get'
    })
}

