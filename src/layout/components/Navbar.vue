<template>
  <div class="navbar">
    <hamburger
      id="hamburger-container"
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />

    <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!topNav" />
    <top-nav id="topmenu-container" class="topmenu-container" v-if="topNav" />
    <div class="right-menu">
      <!-- <a :href="'/portal/home'" id="header-index" class="el-icon-s-home">门户首页1</a> -->
      <template v-if="device !== 'mobile'">
        <screenfull id="screenfull" class="right-menu-item hover-effect" />

        <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>
      </template>

      <el-dropdown class="avatar-container right-menu-item hover-effect" placement="bottom-end">
        <div class="avatar-wrapper">
          <span class="el-dropdown-link user-info">
            {{ userInfo.staffName
            }}
            <i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-tooltip class="item" effect="dark" :content="userInfo.orgName" placement="left">
            <div class="Organization">{{ userInfo.orgName }}</div>
          </el-tooltip>
        </div>

        <el-dropdown-menu slot="dropdown">
          <router-link to="/system/user/profile">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link>
          <el-dropdown-item divided @click.native="logout">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <!-- <div class="right-menu">
      <a  :href="'/'" id="header-index" class="el-icon-s-home">门户首页</a>
      <template v-if="device !== 'mobile'">

        <screenfull id="screenfull" class="right-menu-item hover-effect" />

        <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>
      </template>

      <el-dropdown class="avatar-container right-menu-item hover-effect">


       <div class="avatar-wrapper" style="margin-top:10px">
          <span class="el-dropdown-link user-info">
            个人中心<i class="el-icon-arrow-down el-icon--right"></i>
          </span>

        </div>
        <el-dropdown-menu slot="dropdown">
          <router-link to="/system/user/profile">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link>
          <el-dropdown-item @click.native="setting = true">
            <span>布局设置</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>-->
  </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import Breadcrumb from "@/components/Breadcrumb";
import TopNav from "@/components/TopNav";
import Hamburger from "@/components/Hamburger";
import Screenfull from "@/components/Screenfull";
import SizeSelect from "@/components/SizeSelect";
import Search from "@/components/HeaderSearch";
import { configData } from "@/api/login";

export default {
  components: {
    Breadcrumb,
    TopNav,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search,
  },
  created() {
    this.getTitle();
  },
  computed: {
    ...mapGetters(["sidebar", "avatar", "device"]),
    ...mapState({
      userInfo: (state) => state.user.userInfo,
    }),
    setting: {
      get() {
        return this.$store.state.settings.showSettings;
      },
      set(val) {
        this.$store.dispatch("settings/changeSetting", {
          key: "showSettings",
          value: val,
        });
      },
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav;
      },
    },
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
    async logout() {
      this.$confirm("确定注销并退出系统吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$store.dispatch("LogOut").then(() => {
          var tenantName = this.$store.getters.customParam.tenantLoginName;
          this.$router.push({
            path: `/login`,
            query: { redirect: this.$route.fullPath, tenantName: tenantName },
          });
        });
      });
    },
    getTitle() {
      configData({
        configCodes: ["tenant_title"],
        clientType: "1",
        tenantId: this.$store.getters.customParam.tenantId,
      }).then((r) => {
        if (r.success === true) {
          if (r.data[0]) {
            this.systemTitle = r.data[0].configValue;
            document.title = this.systemTitle;
          }
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    position: relative;

    &:focus {
      outline: none;
    }

    #header-index {
      position: absolute;
      left: -90px;
      font-size: 16px;
      top: 18px;
      color: #5a5e66;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 10px;

      .avatar-wrapper {
        position: relative;
        display: flex;
        align-items: center;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
          margin-right: 10px;
          margin-top: 5px;
        }

        .user-info {
          font-size: 14px;
          font-weight: bold;
          margin-top: -10px;
          color: #858282;
          width: 97px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
        .Organization {
          position: absolute;
          font-size: 12px;
          top: 10px;
          text-align: left;
          font-weight: 800;
          z-index: 100;
          width: 80px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          color: #858282;
        }
      }
    }
  }
}
</style>
