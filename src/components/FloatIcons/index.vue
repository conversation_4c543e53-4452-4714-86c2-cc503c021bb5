<template>
  <div
    ref="icons"
    class="icons-container"
    :style="{ left: left + 'px', top: top + 'px' }"
    @mouseenter="mouseover()"
    @mouseleave="mouseLeave()"
  >
    <slot></slot>
  </div>
</template>
<script>
export default {
  name: "FloatIcons",
  props: {
    // 滚动id
    scroller: {
      type: String,
      default: "",
    },
    // 初始位置距离底部的距离
    bottom: {
      type: Number,
      default: 160,
    },
  },
  data() {
    return {
      clientWidth: 0,
      clientHeight: 0,
      itemWidth: 0,
      itemHeight: 0,
      left: null,
      top: null,
    };
  },
  computed: {
    // 滚动对象，默认空返回window
    scrollContainer() {
      if (this.scroller === "") {
        return window;
      } else {
        return document.getElementById(this.scroller);
      }
    },
  },
  created() {
    // 屏幕宽度
    this.clientWidth = document.documentElement.clientWidth;
    // 屏幕高度
    this.clientHeight = document.documentElement.clientHeight;
  },
  mounted() {
    this.$nextTick(() => {
      this.scrollContainer.addEventListener("scroll", this.handleScrollStart);
      console.log(this.scrollContainer);
      // 获取宽度
      this.itemWidth = this.$refs.icons.offsetWidth;
      this.itemHeight = this.$refs.icons.offsetHeight;
      // 设置位置
      this.left = this.clientWidth - this.itemWidth + this.getItemWidth();
      this.top = this.clientHeight - this.itemWidth - this.bottom;
    });
  },
  methods: {
    mouseover(d) {
      this.left -= this.getItemWidth();
    },
    mouseLeave(d) {
      this.left += this.getItemWidth();
    },
    getItemWidth() {
      return this.$refs.icons.offsetWidth / 2;
    },
    handleScrollStart() {
      console.log("handleScrollStart1");
      this.currentTop =
        document.documentElement.scrollTop || document.body.scrollTop;
      if (this.left > this.clientWidth / 2) {
        this.left = this.clientWidth - this.itemWidth / 2;
      } else {
        this.left = -this.itemWidth / 2;
      }
    },
  },
  beforeDestroy() {
    this.scrollContainer.removeEventListener("scroll", this.handleScrollStart);
  },
};
</script>

<style lang="scss" scoped>
.icons-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: fixed;
  background: rgb(255, 255, 255);
  box-shadow: 0px 2px 10px 0px rgba(198, 198, 198, 0.5);
  // border-radius: 50%;
  z-index: 1000;
  transition: all 0.3s;
  cursor: pointer;
}
</style>