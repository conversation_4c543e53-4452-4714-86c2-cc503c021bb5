<template>
  <el-card
    :class="this.bgColor === true ?'app-card':'unshow-app-card'"
    shadow="never"
    style="width:100%;height:100%"
  >
    <div slot="header">
      <span class="card-title">
        <div class="title">应用中心</div>
      </span>
    </div>
    <div class="app-container">
      <el-empty v-if="list.length <= 0 && !loading" :image-size="200" style="margin-top: 50px" />
      <div class="screen">
        <span>筛选系统</span>
        <el-select v-model="form.categoryName" clearable placeholder="请选择">
          <el-option
            v-for="item in titleList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>

      <el-card
        v-if="list.length > 0 && !form.categoryName?list:form.categoryName === item.categoryName"
        v-loading="loading"
        element-loading-text="加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
        shadow="never"
        v-for="item in list"
        :key="item.id"
        class="app-card"
      >
        <div slot="header">
          <span class="card-title">
            <span>
              <i class="icon iconfont icon-shuxian"></i>
              {{ item.categoryName }}
            </span>
          </span>
        </div>
        <div class="apps">
          <div
            v-for="citem in item.apps"
            :key="citem.id"
            class="app-item"
            @click="toApp(citem.url)"
          >
            <img class="app-item-logoTemp" :src="citem.logoTemp" />
            <span>{{ citem.name }}</span>
          </div>
        </div>
      </el-card>
    </div>
  </el-card>
</template>

<script>
// import { list, selectTenantApps } from "@/api/apps/app";
import { selectRoleApps } from "@/api/apps/app";
import { getInfo } from "@/api/login";
import ssoCrypto from "@/utils/ssoCrypto";
import { getToken } from "@/utils/auth";
export default {
  name: "appCenter",
  props: {
    item: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      list: [],
      loading: true,
      titleList: [],
      form: {
        categoryName: "",
      },
      bgColor: "",
      showTitle: "",
    };
  },
  created() {
    this.showTitle = this.item.showTitle;
    this.bgColor = this.item.showBgColor;
    this.getList();
  },
  watch: {
    "item.showTitle": function (val) {
      this.showTitle = val;
    },
    "item.showBgColor": function (val) {
      this.bgColor = val;
    },
  },
  methods: {
    getList() {
      this.loading = true;
      selectRoleApps("2").then((res) => {
        this.list = res.data;
        this.list.forEach((item) => {
          this.titleList.push({ value: item.categoryName });
        });
        this.loading = false;
      });
    },
    // 跳转   支持单点登录
    toApp(url) {
      getInfo({ token: getToken() })
        .then((res) => {
          const usrs = url.split("?");
          console.log(`未加密：${res.loginName}:${res.tenantId}`);
          console.log(ssoCrypto(`${res.loginName}:${res.tenantId}`));
          window.open(
            `${usrs[0]}?${usrs.length > 1 ? usrs[1] + "&" : ""}mmy=${ssoCrypto(
              `${res.loginName}:${res.tenantId}`
            )}`
          );
        })
        .catch((error) => {
          console.log(error);
          this.$message({
            message: "居中的文字",
            center: true,
          });
        });
    },
  },
};
</script>

<style scoped lang="scss">
.app-card {
  margin: 0px 0px 10px 0px;
  background-color: #fff;

  .app-container {
    max-width: 1443px;
    margin: 0 auto;
  }
  .screen {
    padding: 10px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    span {
      margin-right: 10px;
      font-size: 14px;
    }
  }

  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      font-size: 24px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #646464;
    }
    .tab {
      display: flex;
      align-items: center;
      .tab-items {
        height: 32px;
        font-size: 14px;
        line-height: 32px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #646464;
        text-align: center;
        cursor: pointer;
        border: 0px;
        margin-left: 20px;
        color: #ffffff;
        background: #4d85e2;
      }
      .tab-item {
        height: 32px;
        font-size: 14px;
        line-height: 32px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #646464;
        text-align: center;
        cursor: pointer;
        background: #fff;
        border: 0px;
        margin-left: 20px;
      }
      .tab-item:focus {
        height: 32px;
        font-size: 14px;
        line-height: 32px;
        text-align: center;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        background: #4d85e2;
      }
    }
  }
  .apps {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;

    .app-item {
      padding: 40px;
      flex-basis: 30%;
      background-color: #ffffff;
      margin: 15px;
      border-radius: 10px;
      display: flex;
      flex-direction: row;
      align-items: center;

      > span {
        font-weight: bold;
        margin-left: 20px;
      }

      .app-item-logoTemp {
        width: 50px;
        height: 50px;
      }

      &:hover {
        box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
      }

      cursor: pointer;
    }
  }
}
.unshow-app-card {
  margin: 0px 0px 10px 0px;
  background-color: #fff;
  border: 0px;

  .app-container {
    max-width: 1443px;
    margin: 0 auto;
  }
  .screen {
    padding: 10px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    span {
      margin-right: 10px;
      font-size: 14px;
    }
  }

  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      font-size: 24px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #646464;
    }
    .tab {
      display: flex;
      align-items: center;
      .tab-items {
        height: 32px;
        font-size: 14px;
        line-height: 32px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #646464;
        text-align: center;
        cursor: pointer;
        border: 0px;
        margin-left: 20px;
        color: #ffffff;
        background: #4d85e2;
      }
      .tab-item {
        height: 32px;
        font-size: 14px;
        line-height: 32px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #646464;
        text-align: center;
        cursor: pointer;
        background: #fff;
        border: 0px;
        margin-left: 20px;
      }
      .tab-item:focus {
        height: 32px;
        font-size: 14px;
        line-height: 32px;
        text-align: center;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        background: #4d85e2;
      }
    }
  }
  .apps {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;

    .app-item {
      padding: 40px;
      flex-basis: 30%;
      background-color: #ffffff;
      margin: 15px;
      border-radius: 10px;
      display: flex;
      flex-direction: row;
      align-items: center;

      > span {
        font-weight: bold;
        margin-left: 20px;
      }

      .app-item-logoTemp {
        width: 50px;
        height: 50px;
      }

      &:hover {
        box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
      }

      cursor: pointer;
    }
  }
}
</style>

