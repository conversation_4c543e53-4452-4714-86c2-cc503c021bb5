<template>
  <div class="app-container" style="height: calc(100vh - 120px)">
    <split-pane
      v-on:resize="resize"
      :min-percent="15"
      :max-percent="30"
      :default-percent="15"
      split="vertical"
    >
      <template slot="paneL">
        <el-card class="dep-card" shadow="never" v-loading="treeLoading">
          <div slot="header" class="clearfix">
            <span>用户组</span>
            <div style="float: right; padding: 3px">
              <el-button
                v-if="selectNode && selectNode.orgType !== 'group'"
                style="padding: 0; margin-left: 3px"
                type="text"
                icon="el-icon-plus"
                @click="handleGroupAdd"
                >新增</el-button
              >
              <el-button
                v-if="selectNode && selectNode.orgType === 'group'"
                style="padding: 0px; margin-left: 3px"
                type="text"
                icon="el-icon-edit"
                @click="handleGroupEdit"
                >编辑</el-button
              >
              <el-button
                v-if="selectNode && selectNode.orgType === 'group'"
                style="padding: 0px; margin-left: 3px"
                type="text"
                icon="el-icon-delete"
                @click="handleGroupDelete"
                >删除</el-button
              >

              <el-button
                v-if="selectNode"
                style="padding: 0px; margin-left: 3px"
                type="text"
                icon="el-icon-refresh"
                @click="reloadTree"
                >刷新</el-button
              >
            </div>
            <el-form style="margin-top: 40px;margin-bottom: -20px;" v-if="$store.getters.customParam.userType === 'admin'">
              <el-form-item label="租户：">
                <el-select
                  v-model="queryParams.tenantId"
                  style="width: 100px;"
                  filterable
                  remote
                  :remote-method="getTenantList"
                  :loading="getTenantLoading"
                  @change="tenantChange"
                >
                  <el-option
                      v-for="item in tenantList"
                      :key="item.tenantId"
                      :label="item.tenantName"
                      :value="item.tenantId"
                  />
                </el-select>
              </el-form-item>
            </el-form>
          </div>
          <el-tree
            :props="lazyTreeProps"
            :load="loadNode"
            lazy
            :expand-on-click-node="false"
            :default-expanded-keys="[defaultOrgId]"
            ref="asyncTree"
            @node-click="handleNodeClick"
            node-key="orgId"
            highlight-current
          >
          </el-tree>
        </el-card>
      </template>
      <template slot="paneR">
        <el-card class="dep-card" shadow="never" v-loading="treeLoading">
          <div slot="header" class="clearfix">
            <span>{{ selectNodeName }}</span>
            <el-button
              style="float: right; padding: 3px 0"
              type="text"
              icon="el-icon-refresh"
              @click="allUser"
              >全部人员</el-button
            >
          </div>
          <el-form
            :model="queryParams"
            ref="queryForm"
            :inline="true"
            v-show="showSearch"
            label-width="68px"
          >
            <el-form-item label="用户名称" prop="staffName">
              <el-input
                v-model="queryParams.staffName"
                placeholder="请输入用户名称"
                clearable
                size="small"
                style="width: 240px"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="手机号" prop="cellphone">
              <el-input
                v-model="queryParams.cellphone"
                placeholder="请输入手机号"
                clearable
                size="small"
                style="width: 240px"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="queryParams.email"
                placeholder="请输入邮箱"
                clearable
                size="small"
                style="width: 240px"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
                >重置</el-button
              >
            </el-form-item>
          </el-form>

          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                :disabled="
                  !(selectNode && selectNode.orgType === 'group')
                "
                type="primary"
                plain
                icon="el-icon-user"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['system:user-group:add']"
                >新增用户</el-button
              >
            </el-col>
            <right-toolbar
              :showSearch.sync="showSearch"
              @queryTable="getList"
              :columns="columns"
            ></right-toolbar>
          </el-row>

          <el-table v-loading="loading" :data="userList">
            <el-table-column
              label="用户编号"
              align="left"
              key="staffId"
              prop="staffId"
              v-if="columns[0].visible"
            />
            <el-table-column
              label="登录名称"
              align="left"
              key="loginName"
              prop="loginName"
              v-if="columns[1].visible"
            />
            <el-table-column
              label="用户名称"
              align="left"
              key="staffName"
              prop="staffName"
              v-if="columns[2].visible"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="工作组名称"
              align="left"
              key="orgName"
              prop="orgName"
              v-if="columns[3].visible"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="手机号码"
              align="left"
              key="cellphone"
              prop="cellphone"
              v-if="columns[4].visible"
            />
            <el-table-column
              label="邮箱"
              align="left"
              key="email"
              prop="email"
              v-if="columns[5].visible"
            />
            <el-table-column
              label="操作"
              align="center"
              width="160"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                  v-if="scope.row.staffId !== 1"
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['system:user:remove']"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-card>
      </template>
    </split-pane>

    <!-- 添加或修改用户 -->
    <el-drawer
      ref="orgUserFormDialog"
      :title="`当前用户组：${selectNode && selectNode.orgName}`"
      :visible.sync="open"
      size="70%"
      @close="cancel"
    >
      <div class="drawer-content">
        <split-pane
          v-on:resize="resize"
          :min-percent="20"
          :max-percent="30"
          :default-percent="20"
          split="vertical"
        >
          <template slot="paneL">
            <el-card class="drawer-card" shadow="never" v-loading="treeLoading">
              <div slot="header" class="clearfix">
                <span>组织</span>

                <el-button
                  v-if="orgUserFormDialog.selectNode"
                  style="float: right; padding: 3px 0"
                  type="text"
                  icon="el-icon-refresh"
                  @click="reloadTree"
                  >刷新</el-button
                >
              </div>
              <el-tree
                :props="lazyTreeProps"
                :load="loadNodeOrg"
                lazy
                :expand-on-click-node="false"
                :default-expanded-keys="[defaultOrgId]"
                ref="asyncTreeAddUser"
                @node-click="handleNodeClickAddUser"
                node-key="orgId"
                highlight-current
              >
              </el-tree>
            </el-card>
          </template>
          <template slot="paneR">
            <el-card class="drawer-card" shadow="never" v-loading="treeLoading">
              <div slot="header" class="clearfix">
                <span>{{ orgUserFormDialog.selectNodeName }}</span>
              </div>
              <el-row :gutter="10" class="mb8">
                <right-toolbar
                  :showSearch.sync="orgUserFormDialog.showSearch"
                  @queryTable="getList"
                  :columns="columns2"
                ></right-toolbar>
              </el-row>
              <el-table
                v-loading="orgUserFormDialog.loading"
                :data="orgUserFormDialog.userList"
                @selection-change="handleSelectionChangeAddUser"
                max-height="650"
                size="mini"
              >
                <el-table-column type="selection" width="50" align="center" />
                <el-table-column
                  label="用户编号"
                  align="left"
                  key="staffId"
                  prop="staffId"
                  v-if="columns2[0].visible"
                />
                <el-table-column
                  label="登录名称"
                  align="left"
                  key="loginName"
                  prop="loginName"
                  v-if="columns2[1].visible"
                />
                <el-table-column
                  label="用户名称"
                  align="left"
                  key="staffName"
                  prop="staffName"
                  v-if="columns2[2].visible"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="组织名称"
                  align="left"
                  key="orgName"
                  prop="orgName"
                  v-if="columns2[3].visible"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="手机号码"
                  align="left"
                  key="cellphone"
                  prop="cellphone"
                  v-if="columns2[4].visible"
                />
                <el-table-column
                  label="邮箱"
                  align="left"
                  key="email"
                  prop="email"
                  v-if="columns2[5].visible"
                />
              </el-table>
              <pagination
                v-show="orgUserFormDialog.total > 0"
                :total="orgUserFormDialog.total"
                :page.sync="orgUserFormDialog.queryParams.pageNum"
                :limit.sync="orgUserFormDialog.queryParams.pageSize"
                @pagination="getList"
              />
            </el-card>
          </template>
        </split-pane>
        <div class="demo-drawer__footer">
          <el-button
            type="primary"
            @click="submitSelectUserForm"
            :loading="selectSaveLoading"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </div>
    </el-drawer>
    <!-- 添加或修改部门对话框 -->
    <el-dialog
      :title="orgFormDialog.title"
      :visible.sync="orgFormDialog.open"
      width="600px"
      append-to-body
      @close="cancel"
      v-dialogDrag
    >
      <el-form
        ref="orgForm"
        :model="orgForm"
        :rules="orgFormRules"
        label-width="80px"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="归属组织" prop="parentName">
              <el-input
                v-model="orgForm.parentName"
                placeholder="请选择归属组织"
                disabled
              >
                <template
                  slot="append"
                  v-if="orgFormDialog.title === '编辑用户组'"
                  ><el-link @click="$refs.treeSelect.open()"
                    >选择</el-link
                  ></template
                >
              </el-input>
            </el-form-item>
            <dept-select
              ref="treeSelect"
              name=""
              value=""
              @selected="selected"
            />
            <el-form-item hidden>
              <el-input v-model="orgForm.parentId"> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户组" prop="orgName">
              <el-input v-model="orgForm.orgName" placeholder="请输入用户组名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="orgSort">
              <el-input-number
                v-model="orgForm.orgSort"
                controls-position="right"
                :min="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="租户">
              <el-input
                  v-model="orgForm.tenantName"
                  placeholder="租户"
                  maxlength="50"
                  disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="菜单权限">
              <el-checkbox
                v-model="menuExpand"
                @change="handleCheckedTreeExpand($event, 'menu')"
                >展开/折叠</el-checkbox
              >
              <el-checkbox
                v-model="menuNodeAll"
                @change="handleCheckedTreeNodeAll($event, 'menu')"
                >全选/全不选</el-checkbox
              >
              <el-checkbox
                v-model="menuCheckStrictly"
                @change="handleCheckedTreeConnect($event, 'menu')"
                >父子联动</el-checkbox
              >
              <el-tree
                class="tree-border"
                :data="permissionOptions"
                show-checkbox
                ref="menu"
                node-key="permissionId"
                :check-strictly="!menuCheckStrictly"
                empty-text="加载中，请稍后"
                :props="defaultMenuProps"
                :default-checked-keys="orgFormDialog.checkedMenuIds"
              ></el-tree>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitOrgForm"
          :loading="saveGroupLoading"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listUser, delUserForGroup, addUserForGroup } from "@/api/system/user";
import { getToken,getAuthorKey} from "@/utils/auth";
import { treeselect, addDept, getDept, updateDept, delDept } from "@/api/system/dept";
import Treeselect from "@riophae/vue-treeselect";
import { treeselect as menuTreeselect, getOrgMenu } from "@/api/system/menu";
import { list as tenantList } from "@/api/system/tenant";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { arrayToTree } from "@/utils";
import DeptSelect from "@/components/DeptSelect";
export default {
  name: "UserGroup",
  components: { Treeselect, DeptSelect },
  data() {
    let menuIdsChecked = (rule, value, callback) => {
      if (this.orgForm.permissionIds && this.orgForm.permissionIds.length) {
        callback();
      } else {
        return callback(new Error("请选择菜单"));
      }
    };
    return {
      selectSaveLoading: false,
      saveGroupLoading: false,
      getTenantLoading: false,
      // 遮罩层
      loading: true,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 用户表格数据
      userListAddUser: null,
      tenantList: [],
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: [],
      deptOptionsAll: [],
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,
      // 默认密码
      initPassword: undefined,
      // 日期范围
      dateRange: [],
      // 状态数据字典
      statusOptions: [],
      // 性别状态字典
      sexOptions: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // 菜单列表
      permissionOptions: [],
      // 表单参数
      form: {},
      orgFormDialog: {
        title: "",
        open: false,
        checkedMenuIds: [],
      },
      //为添加组织 所用form
      orgForm: {
        orgType: "group",
      },
      //为组织添加用户 所用form
      orgUserForm: {},
      orgUserFormDialog: {
        showSearch: false,
        selectNodeName: "全部人员",
        selectNode: undefined,
        total: 0,
        loading: true,
        treeLoading: true,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          staffOrgType: "F",
          orgId: this.$store.getters.orgId,
          tenantId: this.$store.getters.customParam.tenantId,
          tenantName: this.$store.getters.customParam.tenantName,
        },
        userList: [],
      },
      defaultProps: {
        children: "children",
        label: "name",
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { [getAuthorKey()]: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData",
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        staffName: undefined,
        email: undefined,
        cellphone: undefined,
        status: undefined,
        deptId: undefined,
        orgType: 'group',
        orgId: this.$store.getters.orgId,
        tenantId: this.$store.getters.customParam.tenantId,
        tenantName: this.$store.getters.customParam.tenantName,
      },

      // 列信息
      columns: [
        { key: 0, label: `用户编号`, visible: false },
        { key: 1, label: `登陆名称`, visible: false },
        { key: 2, label: `用户名称`, visible: true },
        { key: 3, label: `工作组名称`, visible: true },
        { key: 4, label: `手机号码`, visible: true },
        { key: 5, label: `邮箱`, visible: true },
      ],
      columns2: [
        { key: 0, label: `用户编号`, visible: false },
        { key: 1, label: `登陆名称`, visible: false },
        { key: 2, label: `用户名称`, visible: true },
        { key: 3, label: `组织名称`, visible: true },
        { key: 4, label: `手机号码`, visible: true },
        { key: 5, label: `邮箱`, visible: true },
      ],
      // 表单校验
      orgFormRules: {
        parentName: [
          { required: true, message: "上级组织不能为空", trigger: "blur" },
        ],
        orgName: [
          { required: true, message: "组织名称不能为空", trigger: "blur" },
        ],
        orgSort: [
          { required: true, message: "显示排序不能为空", trigger: "blur" },
        ],
      },
      lazyTreeProps: {
        children: "children",
        label: "orgName",
        isLeaf: "leaf",
      },
      treeLoading: false,
      selectNodeName: "全部人员",
      selectNode: undefined,
      menuExpand: false,
      menuNodeAll: false,
      menuCheckStrictly: true,
      defaultMenuProps: {
        children: "children",
        label: "permissionName",
      },
      isAddUser: false,
      selectUserIds: [],
      defaultOrgId: this.$store.getters.orgId,
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {
    this.getTenantList();
    this.getList();
  },
  methods: {
    getTenantList(tenantName) {
      this.getTenantLoading = true;
      let query = {}
      if (tenantName !== undefined && tenantName !== '') {
        query.tenantName = tenantName
        query.tenantId = undefined
      } else {
        query.tenantId = this.queryParams.tenantId
      }
      tenantList(query).then((response) => {
        this.tenantList = response.data;
      }).finally(() => {
        this.getTenantLoading = false;
      });
    },
    // 所有菜单节点数据
    getMenuAllCheckedKeys() {
      // 目前被选中的菜单节点
      let checkedKeys = this.$refs.menu.getCheckedKeys();
      // 半选中的菜单节点
      let halfCheckedKeys = this.$refs.menu.getHalfCheckedKeys();
      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
      return checkedKeys;
    },
    /** 查询菜单树结构 */
    getMenuTreeselect() {
      menuTreeselect(this.queryParams).then((response) => {
        this.permissionOptions = arrayToTree(
          response.data,
          "children",
          "permissionId",
          "parentId"
        );
      });
    },
    // 树权限（展开/折叠）
    handleCheckedTreeExpand(value, type) {
      if (type === "menu") {
        let treeList = this.permissionOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.menu.store.nodesMap[treeList[i].permissionId].expanded = value;
        }
      } else if (type === "dept") {
        let treeList = this.deptOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.dept.store.nodesMap[treeList[i].id].expanded = value;
        }
      }
    },
    // 树权限（全选/全不选）
    handleCheckedTreeNodeAll(value, type) {
      if (type === "menu") {
        this.$refs.menu.setCheckedNodes(value ? this.permissionOptions : []);
      } else if (type === "dept") {
        this.$refs.dept.setCheckedNodes(value ? this.deptOptions : []);
      }
    },
    // 树权限（父子联动）
    handleCheckedTreeConnect(value, type) {
      if (type === "menu") {
        this.form.menuCheckStrictly = value ? true : false;
      } else if (type === "dept") {
        this.form.deptCheckStrictly = value ? true : false;
      }
    },
    /** 新增用户组按钮操作 */
    handleGroupAdd() {
      this.reset();
      if (this.selectNode) {
        this.getMenuTreeselect();
        this.orgFormDialog.open = true;
        this.orgFormDialog.title = "添加用户组";
        this.orgForm.parentId = this.selectNode.orgId;
        this.orgForm.parentName = this.selectNode.orgName;
        this.orgForm.tenantName = this.queryParams.tenantName;
        this.orgForm.tenantId = this.queryParams.tenantId;
      } else {
        this.$message.error("请选择上级组织");
      }
    },
    /** 编辑用户组按钮操作 */
    handleGroupEdit() {
      this.reset();
      if (this.selectNode) {
        this.getMenuTreeselect();
        this.orgFormDialog.open = true;
        this.orgFormDialog.title = "编辑用户组";
        getDept(this.selectNode.orgId).then((res) => {
          this.orgForm = res.data
        });
        this.orgFormDialog.checkedMenuIds = [];
        this.menuCheckStrictly = false;
        getOrgMenu({ orgId: this.selectNode.orgId }).then((res) => {
          this.orgFormDialog.checkedMenuIds = res.data.permissions.map(v => v.permissionId);
          this.orgForm.permissionIds = this.getMenuAllCheckedKeys();
        });
      } else {
        this.$message.error("请选择上级组织");
      }
    },
    handleGroupDelete(row) {
      const orgName = this.selectNode.orgName;
      const orgId = this.selectNode.orgId
      this.$confirm('是否确认删除用户组名称为"' + orgName + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delDept({orgId: orgId});
        })
        .then((res) => {
          if(res.success === false) {
            this.$message.error(res.message)
          }else {
            this.msgSuccess("删除成功");
            this.reloadTree()
          }
          // 更新父节点
          this.selectNode = this.$refs.asyncTree.getNode(this.selectNode.orgId)
          this.reloadTree();
          this.queryParams.orgId = this.selectNode.orgId
          this.getList();
        })
        .catch((err) => {
          // this.msgError("取消删除");
        });
    },
    /** 查询用户列表 */
    getList() {
      if (this.isAddUser) {
        this.orgUserFormDialog.loading = true;
        listUser(
          this.addDateRange(this.orgUserFormDialog.queryParams, this.dateRange)
        ).then((response) => {
          this.orgUserFormDialog.userList = response.data.records;
          this.orgUserFormDialog.total = response.data.total;
          this.orgUserFormDialog.loading = false;
        });
      } else {
        this.loading = true;
        listUser({
          ...this.addDateRange(this.queryParams, this.dateRange),
        }).then((response) => {
          this.userList = response.data.records;
          this.total = response.data.total;
          this.loading = false;
        });
      }
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      treeselect({
        orgId: this.defaultOrgId,
        type: "current",
        tenantId: this.queryParams.tenantId
      }).then((response) => {
        this.deptOptions = response.data;
      });
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.orgId = data.orgId;
      this.selectNode = data;
      this.selectNodeName = data.orgName;
      this.isAddUser = false;
      this.orgForm.parentId = data.parentId
      this.getList();
    },
    //添加用户 节点单击事件
    handleNodeClickAddUser(data) {
      this.orgUserFormDialog.queryParams.orgId = data.orgId;
      this.orgUserFormDialog.selectNode = data;
      this.orgUserFormDialog.selectNodeName = data.orgName;
      this.getList();
    },
    allUser() {
      const node = this.$refs.asyncTree.root;
      node.loaded = false;
      node.expand();
      this.queryParams.orgId = "";
      this.selectNodeName = "全部人员";
      this.selectNode = undefined;
      this.getList();
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.orgFormDialog.open = false;
      this.orgFormDialog.checkedMenuIds = [];
      this.isAddUser = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.orgForm = {
        orgType: "group",
        orgName: undefined,
        orgId: undefined,
        parentName: undefined,
        parentId: undefined,
        orgSort: 0
      };
      this.orgUserForm = {};
      this.resetForm("orgForm");
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 添加用户时 多选框选中数据
    handleSelectionChangeAddUser(selection) {
      this.selectUserIds = selection.map((item) => item.staffId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.isAddUser = true;
      this.orgUserForm.orgId = this.queryParams.orgId
        ? this.queryParams.orgId
        : this.defaultOrgId;
      this.open = true;
      this.getTreeselect();
      this.getList();
    },
    /** 提交按钮 */
    submitOrgForm: function () {
      this.orgForm.permissionIds = this.getMenuAllCheckedKeys();
      this.$refs["orgForm"].validate((valid) => {
        if (valid) {
          this.saveGroupLoading = true;
          if (this.orgFormDialog.title === "编辑用户组") {
            updateDept(this.orgForm)
              .then((response) => {
                this.saveGroupLoading = false;
                if (response.success) {
                  this.msgSuccess("修改成功");
                  this.orgFormDialog.open = false;
                  this.selectNode.orgName = this.orgForm.orgName;
                } else {
                  this.$message.error("修改失败");
                }
              })
              .catch((err) => {
                this.saveGroupLoading = false;
              });
          } else {
            addDept(this.orgForm)
              .then((response) => {
                this.saveGroupLoading = false;
                if (response.success) {
                  this.msgSuccess("新增成功");
                  this.orgFormDialog.open = false;
                  this.reloadTree();
                } else {
                  this.$message.error("新增失败");
                }
              })
              .catch((err) => {
                this.saveGroupLoading = false;
              });
          }
        }
      });
    },
    /** 用户添加提交按钮 */
    submitSelectUserForm: function () {
      if (this.selectUserIds.length <= 0) {
        this.$message("请选择一个用户进行添加");
      } else {
        this.selectSaveLoading = true;
        addUserForGroup({
          staffIds: this.selectUserIds,
          ...this.selectNode,
        })
          .then((response) => {
            this.selectSaveLoading = false;
            if (response.success) {
              this.msgSuccess("新增成功");
              this.open = false;
              this.isAddUser = false;
              this.getList();
            } else {
              this.$message.error("新增失败");
            }
          })
          .catch((err) => {
            this.selectSaveLoading = false;
          });
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const staffOrgId = row.staffOrgId;
      this.$confirm(
        '是否确认删除用户名称为"' + row.staffName + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delUserForGroup({ staffOrgId: staffOrgId });
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    loadNode(node, resolve) {
      if (!node.data) {
        this.treeLoading = true;
      }
      treeselect({
        orgId: node.data ? node.data.orgId : this.defaultOrgId,
        queryType: node.data ? "down" : "current",
        orgType: 'all',
        tenantId: this.queryParams.tenantId
      }).then((response) => {
        resolve(response.data);
        this.treeLoading = false;
      });
    },
    loadNodeOrg(node, resolve) {
      if (!node.data) {
        this.treeLoading = true;
      }
      treeselect({
        orgId: node.data ? node.data.orgId : this.defaultOrgId,
        queryType: node.data ? "down" : "current",
        tenantId: this.queryParams.tenantId
      }).then((response) => {
        resolve(response.data);
        this.treeLoading = false;
      });
    },
    reloadTree() {
      if (this.selectNode && !this.isAddUser) {
        const node = this.$refs.asyncTree.getNode(this.selectNode);
        node.childNodes = [];
        node.loaded = false;
        node.expand();
      } else if  (this.selectNode && this.isAddUser)  {
        const node = this.$refs.asyncTreeAddUser.getNode(
          this.orgUserFormDialog.selectNode
        );
        node.childNodes = [];
        node.loaded = false;
        node.expand();
      } else {
        this.$refs.asyncTree.root.loaded = false;
        this.$refs.asyncTree.root.expand();
      }
      // 主动调用展开节点方法，重新查询该节点下的所有子节点
    },
    resize() {
      console.log("resize");
    },
    // 组织选择的回调
    selected(data) {
      this.orgForm.parentId = data.orgId;
      this.orgForm.parentName = data.orgName;
      this.$refs.treeSelect.close();
    },
    tenantChange(tenantId) {
      if (tenantId !== '') {
        this.orgUserFormDialog.queryParams.tenantId = tenantId
        this.queryParams.tenantName = this.tenantList.find(item => item.tenantId === tenantId).tenantName
      }
      this.selectNode = undefined
      this.queryParams.orgId = undefined
      this.$refs.asyncTree.root.loaded = false;
      this.$refs.asyncTree.root.expand();
      this.handleQuery()
    }
  },
};
</script>
<style lang="scss" scoped>
.dep-card {
  min-height: calc(100vh - 120px);
}
.drawer-card {
  border: 0px;
}
.drawer-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.demo-drawer__footer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}
</style>
