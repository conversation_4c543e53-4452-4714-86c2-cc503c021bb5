<template>
  <div class="app-container">
    <el-button type="primary" @click="addDialog">新增</el-button>
    <el-table :data="datasourceListData" stripe style="width: 100%">
      <el-table-column type="index" width="50"></el-table-column>
      <el-table-column prop="driverName" label="驱动" width="200"></el-table-column>
      <el-table-column prop="host" label="主机" width="150"></el-table-column>
      <el-table-column prop="port" label="端口" width="100"></el-table-column>
      <el-table-column prop="databaseName" label="数据库" width="150"></el-table-column>
      <el-table-column prop="username" label="用户名" width="150"></el-table-column>
      <el-table-column prop="suffix" label="参数"></el-table-column>
      <el-table-column label="操作" width="150">
        <template slot-scope="scope">
          <el-button @click="updateDialog(scope.row)" type="text" size="small">修改</el-button>
          <el-button @click="delDatasource(scope.row)" type="text" size="small">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog :title="dialogTitle" :visible.sync="dialogFormVisible" ref="dialog" v-dialogDrag>
      <el-form
        ref="datasourceForm"
        :model="datasourceForm"
        :rules="datasourceFormRules"
        label-width="70px"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="驱动" prop="driverName">
              <el-input v-model="datasourceForm.driverName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="库名" prop="databaseName">
              <el-input v-model="datasourceForm.databaseName"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="主机" prop="host">
              <el-input v-model="datasourceForm.host"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="端口" prop="port">
              <el-input v-model="datasourceForm.port"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户" prop="username">
              <el-input v-model="datasourceForm.username"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input v-model="datasourceForm.password"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="参数" prop="suffix">
              <el-input v-model="datasourceForm.suffix"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="testConnection()">测试</el-button>
        <el-button type="primary" @click="addOrUpdateDatasource('datasourceForm')">确 定</el-button>
        <el-button @click="dialogFormVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  add,
  deleteDatasource,
  findPage,
  testConn,
  update,
} from "@/api/apps/datasource";

export default {
  data() {
    return {
      datasourceListData: [],
      dialogFormVisible: false,
      dialogTitle: "新增",
      datasourceForm: {
        driverName: "",
        databaseName: "",
        host: "",
        port: "",
        username: "",
        password: "",
        suffix: "",
      },
      datasourceFormRules: {
        driverName: [
          { required: true, message: "请输入驱动名称", trigger: "blur" },
        ],
        databaseName: [
          { required: true, message: "请输入数据库名称", trigger: "blur" },
        ],
        host: [{ required: true, message: "请输入主机地址", trigger: "blur" }],
        port: [{ required: true, message: "请输入端口号", trigger: "blur" }],
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
      },
      queryParams: {
        tenantId: this.$store.getters.customParam.tenantId,
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  created() {
    this.getDatasourceList();
  },
  methods: {
    getDatasourceList() {
      findPage(this.queryParams).then((res) => {
        this.datasourceListData = res.data.records;
      });
    },
    addDialog() {
      this.dialogTitle = "新增";
      this.dialogFormVisible = true;
      this.resetForm();
    },
    addOrUpdateDatasource(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.dialogTitle === "新增") {
            add(this.datasourceForm).then((res) => {
              this.$message.success("添加成功！");
              this.dialogFormVisible = false;
              this.getDatasourceList();
            });
          } else {
            update(this.datasourceForm).then((res) => {
              this.$message.success("修改成功！");
              this.dialogFormVisible = false;
              this.getDatasourceList();
            });
          }
        } else {
          this.$message.success("请完成表单填写");
          return false;
        }
      });
    },
    testConnection() {
      testConn(this.datasourceForm).then((res) => {
        this.$message.success(res.data);
      });
    },
    updateDialog(row) {
      this.resetForm();
      this.dialogTitle = "修改";
      this.dialogFormVisible = true;
      this.datasourceForm = row;
    },
    delDatasource(row) {
      console.log(row);
      this.$confirm(
        "此操作将永久删除" + row.databaseName + ", 是否继续?",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          deleteDatasource(row.id).then((res) => {
            this.$message.success("删除成功");
            this.getDatasourceList();
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    resetForm() {
      this.datasourceForm = {};
    },
  },
};
</script>

<style scoped>
</style>

