<template>
    <div class="portal-body">
        <div class="analysis">
            <div class="flex-start back" @click="goBack()">
                <img src="../../../assets/images/sw/back.png" alt="" style="width: 14px;height: 14px;">
                <p>返回</p>
            </div>

            <div class="search-wrapper box-bg">
                <el-form ref="form" :model="formQuery" label-width="1rem">
                    <el-row>
                        <el-col :span="7">
                            <el-form-item label="账期">
                                <el-col>
                                    <el-date-picker v-model="formQuery.monthId" type="month" placeholder="选择月"
                                        format="yyyyMM" value-format="yyyyMM">
                                    </el-date-picker>
                                </el-col>
                            </el-form-item>
                        </el-col>
                        <!-- <el-col :span="7" v-show="type == 0">
                            <el-form-item label="策略名称">
                                <el-col>
                                    <el-input v-model="formQuery.name" clearable></el-input>
                                </el-col>
                            </el-form-item>
                        </el-col> -->
                        <el-col :span="7" v-show="type == 3">
                            <el-form-item label="地市">
                                <el-col>
                                    <!-- <el-input v-model="formQuery.city" clearable></el-input> -->
                                    <el-select v-model="formQuery.city" placeholder="请选择">
                                        <el-option v-for="item in citylist" :key="item.value" :label="item.label"
                                            :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-col>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <div class="btn-group">
                        <el-button @click="resetForm">重置</el-button>
                        <el-button type="primary" @click="getData">查询</el-button>
                    </div>
                </el-form>
            </div>

            <div class="table-wrapper box-bg">
                <div class="flex-between" style="padding: 0 0.2rem;">
                    <div class="table-title">{{ title }}</div>
                </div>
                <el-divider></el-divider>
                <div class="table-con">
                    <el-table :data="tableData" style="width: 100%">
                        <!-- <el-table-column type="selection"></el-table-column> -->
                        <template v-for="item in tableColumns">
                            <el-table-column v-if="item.hasDtl" :prop="item.value" :label="item.name" :align="'center'">
                                <template slot-scope="scope">
                                    <span @click="goDtl(scope.row)" class="pointer">{{ scope.row[item.value] }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column v-else :prop="item.value" :label="item.name" :align="'center'">
                            </el-table-column>
                        </template>
                        <!-- <el-table-column v-for="item in tableColumns" :key="item.id" :prop="item.value"
                            :label="item.name">
                        </el-table-column> -->
                    </el-table>

                    <div class="flex-end" style="margin-top: 0.14rem;" v-show="dtype===3">
                      <el-pagination @current-change="handleCurrentChange"
                                     :current-page.sync="formQuery.pageNum" :page-size="formQuery.pageSize" layout="total, prev, pager, next"
                                     :total="total">
                      </el-pagination>
                        <!-- <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                            :current-page.sync="currentPage" :page-size="100" layout="total, prev, pager, next"
                            :total="1000">
                        </el-pagination> -->
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>

<style scoped lang="less">
p {
    margin: 0;
}

ul,
li {
    list-style: none;
}

/deep/ .el-input .el-input__icon{
  line-height: 0.25rem;
}
.analysis {
    padding: 20px 0;

    .back {
        margin-top: 10px;
        cursor: pointer;
    }

    .search-wrapper {
        margin-top: 10px;
    }
}

.pointer {
    cursor: pointer;
}
</style>

<script>
import { strategyDownOne, potentialOpportunityDownOne, orderDownOne, businessOpportunityDownOne ,businessOpportunityDownOnePage} from '@/api/sw/analysis/analysis'
export default {
    name: 'index',
    data() {
        return {
            citylist: [
                {
                    label: '全省',
                    value: ''
                }, {
                    label: '济南市',
                    value: '济南'
                }, {
                    label: '青岛市',
                    value: '青岛'
                }, {
                    label: '潍坊市',
                    value: '潍坊'
                }, {
                    label: '淄博市',
                    value: '淄博'
                }, {
                    label: '枣庄市',
                    value: '枣庄'
                }, {
                    label: '东营市',
                    value: '东营'
                }, {
                    label: '烟台市',
                    value: '烟台'
                }, {
                    label: '济宁市',
                    value: '济宁'
                }, {
                    label: '泰安市',
                    value: '泰安'
                }, {
                    label: '威海市',
                    value: '威海'
                }, {
                    label: '日照市',
                    value: '日照'
                }, {
                    label: '临沂市',
                    value: '临沂'
                }, {
                    label: '德州市',
                    value: '德州'
                }, {
                    label: '聊城市',
                    value: '聊城'
                }, {
                    label: '滨州市',
                    value: '滨州'
                }, {
                    label: '菏泽市',
                    value: '菏泽'
                },
            ],
            formQuery: {
                pageNum: 1,
                pageSize: 10,
                monthId: '202404',
                industry: '',
                // name: '',
                city: ''
            },
            total : 0,
            currentPage: 1,
            tableColumns: [],
            tableData: [],
            cltableColumns: [
                {
                    name: '策略id',
                    value: 'id',
                }, {
                    name: '策略名称',
                    value: 'strategyName',
                }, {
                    name: '策略执行次数',
                    value: 'resultExecNum',
                }, {
                    name: '生成潜在机会数',
                    value: 'potentialOpportunityNum',
                }, {
                    name: '工单数',
                    value: 'orderNum',
                }, {
                    name: '商机数',
                    value: 'businessOpportunityNum',
                }, {
                    name: '商机转化率',
                    value: 'rate',
                },
            ],
            cltableData: [],
            optableColumns: [
                {
                    name: '地市',
                    value: 'cityName',
                }, {
                    name: '生成潜在机会数',
                    value: 'potentialOpportunityNum',
                }, {
                    name: '工单数',
                    value: 'orderNum',
                }, {
                    name: '商机数',
                    value: 'businessOpportunityNum',
                }, {
                    name: '商机转化率',
                    value: 'rate',
                },
            ],
            optableData: [],
            gdtableColumns: [
                {
                    name: '地市',
                    value: 'cityName',
                }, {
                    name: '工单数',
                    value: 'orderNum',
                }, {
                    name: '已查看',
                    value: 'readNum',
                }, {
                    name: '执行中',
                    value: 'exectingNum',
                    hasDtl: true,
                }, {
                    name: '已关单',
                    value: 'closeOrderNum',
                }, {
                    name: '平均执行天数',
                    value: 'avgdays',
                },
            ],
            gdtableData: [],
            sjtableColumns: [
                {
                    name: '地市',
                    value: 'cityName',
                }, {
                    name: '客户名称',
                    value: 'customerName',
                }, {
                    name: '客户经理',
                    value: 'customerManager',
                }, {
                    name: '商机提交时间',
                    value: 'feedbackDate',
                }
            ],
            sjtableData: [],
            dtitle: '',
            dindustry: '',
            dtype: ''
        }
    },
    props: ['title', 'industry', 'type'],
    computed: {
        dindustry(newv,oldv){
            this.formQuery.industry = newv
        }
    },
    mounted() {
        this.dtitle = this.title
        this.dindustry = this.industry
        this.dtype = this.type
        this.formQuery.industry = this.industry
        this.handleTable(this.dtype)
        this.getData()
    },
    activated() {
        if ((this.type && this.type != this.dtype) || (this.industry && this.industry != this.dindustry)) {
            this.dtype = this.type?this.type:this.dtype
            this.dindustry = this.industry?this.industry:this.dindustry
            this.formQuery.industry = this.dindustry
            this.handleTable(this.dtype)
            this.getData()
        }
        console.log('keep alive analysis detail')
    },
    deactivated() {
        console.log('deactivated analysis detail')
    },
    beforeDestroy() {
        console.log('beforeDestroy analysis detail')
    },
    destroyed() {
        console.log('destroyed analysis detail')
    },
    methods: {
        getData() {
            switch (this.dtype) {
                case 0: this.getStrategyDownOne(); break;
                case 1: this.getPotentialOpportunityDownOne(); break;
                case 2: this.getOrderDownOne(); break;
                case 3: this.getBusinessOpportunityDownOne(); break;
                default: break;
            }
        },
        getStrategyDownOne() {
            strategyDownOne(this.formQuery).then(res => {
                if (res.success) {
                    for(let i=0,len=res.data.length;i<len;i++){
                        res.data[i].rate += '%'
                    }
                    this.tableData = res.data
                }
            })
        },
        getPotentialOpportunityDownOne() {
            potentialOpportunityDownOne(this.formQuery).then(res => {
                if (res.success) {
                    for(let i=0,len=res.data.length;i<len;i++){
                        res.data[i].rate += '%'
                    }
                    this.tableData = res.data
                }
            })
        },
        getOrderDownOne() {
            orderDownOne(this.formQuery).then(res => {
                if (res.success) {
                    this.tableData = res.data
                }
            })
        },
        getBusinessOpportunityDownOne() {
            businessOpportunityDownOnePage(this.formQuery).then(res => {
                if (res.success) {
                  // this.resultList = response.data.records;
                  this.total = res.data.total;
                    for(let i=0,len=res.data.records.length;i<len;i++){
                        // let d = res.records.data[i].feedbackDate;
                        // if(d){
                        //     let t = new Date(d);
                        //     res.records.data[i].feedbackDate = this.$moment(t).format("YYYY-MM-DD hh:mm:ss")
                        // }
                    }
                    this.tableData = res.data.records
                }
            })
        },
        //详情页
        go() {
            if (this.$route.params.industry) {
                this.$router.push("/sw/analysisOverviewAll");
            }

        },
        //返回
        goBack() {
            this.$router.push({
                name: "analysisOverview",
                params: { industry: this.dindustry }
            });
        },
        goDtl(row) {
            this.$router.push({
                name: "analysisDetailSec",
                params: {
                    title: this.dtitle,
                    industry: this.dindustry,
                    type: this.dtype
                }
            });
        },
        // 重置表单数据
        resetForm() {
            this.formQuery = {
                monthId: '',
                industry: this.dindustry
            };
        },
        conditionQuery() {

        },
        // 当前页码
        handleCurrentChange(page) {
          this.formQuery.pageNum = page;
          this.handleTable(this.dtype)
          this.getData()
        },
        handleSizeChange() {

        },
        handleTable(t) {
            switch (t) {
                case 0: this.tableColumns = this.cltableColumns;
                    // this.tableData = this.cltableData;
                    break;
                case 1: this.tableColumns = this.optableColumns;
                    // this.tableData = this.optableData;
                    break;
                case 2: this.tableColumns = this.gdtableColumns;
                    // this.tableData = this.gdtableData;
                    break;
                case 3: this.tableColumns = this.sjtableColumns;
                    // this.tableData = this.sjtableData;
                    break;
            }
        },

    }
}
</script>
