import request from "@/utils/request";

/**
 * 分页查询
 * @param {Object} params 传入参数
 * @returns
 */
export function findPageList(params) {
  return request({
    url: "/sanquan/tag/findPage",
    method: "get",
    params
  });
}
/**
 * 详情
 * @param {Number} data 传入id
 * @returns
 */
export function tagFindOne(id) {
  return request({
    url: "/sanquan/tag/findOne",
    method: "get",
    params: {
      id
    }
  });
}

export function findFormOne(id) {
  return request({
    url: "/sanquan/tag/findFormOne",
    method: "get",
    params: {
      id
    }
  });
}
/**
 * 统计
 * @param {Object} data 传入参数
 * @returns
 */
export function tagStatistics(data) {
  return request({
    url: "/sanquan/tag/statistics",
    method: "post",
    data
  });
}
/**
 * 保存
 * @param {Object} data 传入参数
 * @returns
 */
export function tagSave(data) {
  return request({
    url: "/sanquan/tag/save",
    method: "post",
    data
  });
}
export function testDb(data) {
  return request({
    url: "/sanquan/tag/testDbLink",
    method: "post",
    data
  });
}

export function checkExist(data) {
  return request({
    url: "/sanquan/tag/checkExist",
    method: "get",
    params:data
  });
}
/**
 * 删除
 * @param {Number} id 传入
 * @returns
 */
export function tagDel(id) {
  return request({
    url: "/sanquan/tag/delete",
    method: "post",
    params: {
      id
    }
  });
}
