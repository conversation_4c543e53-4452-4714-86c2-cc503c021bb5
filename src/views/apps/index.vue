<template>
  <div class="app-container"></div>
</template>

<script>
import { list } from "@/api/apps/app";
export default {
  name: "apps",
  data() {
    return {
      list: [],
      queryParams: {},
      loading: false
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      list(this.queryParams).then(response => {
        this.loading = false;
      });
    }
  }
};
</script>

<style scoped lang="scss"></style>
