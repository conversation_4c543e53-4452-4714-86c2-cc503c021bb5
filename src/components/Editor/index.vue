<!--<template>-->
<!--  <div>-->
<!--    <el-button style="margin-bottom: 10px;" @click="templateSelection">模板选择</el-button>-->
<!--    <el-upload-->
<!--      class="uploadFile"-->
<!--      :action="uploadUrl"-->
<!--      :on-success="handleUploadFileSuccess"-->
<!--      :on-error="handleUploadFileError"-->
<!--      name="multipartFile"-->
<!--      :show-file-list="false"-->
<!--      :headers="headers"-->
<!--      style="display: none"-->
<!--    ></el-upload>-->
<!--    <el-upload-->
<!--      class="uploadPicture"-->
<!--      :action="uploadUrl"-->
<!--      :on-success="handleUploadPictureSuccess"-->
<!--      :on-error="handleUploadPictureError"-->
<!--      name="multipartFile"-->
<!--      accept=".jpg, .jpeg, .png, .gif"-->
<!--      :show-file-list="false"-->
<!--      :headers="headers"-->
<!--      style="display: none"-->
<!--    ></el-upload>-->
<!--    <div class="editor" ref="editor" :style="styles"></div>-->
<!--    <template-selection :template-dialog.sync="templateDialog" @innerStockBlot="innerStockBlot" />-->
<!--  </div>-->
<!--</template>-->

<!--<script>-->
<!--import Quill from "quill";-->
<!--import "quill/dist/quill.core.css";-->
<!--import "quill/dist/quill.snow.css";-->
<!--import "quill/dist/quill.bubble.css";-->
<!--import { getToken,getAuthorKey } from "@/utils/auth";-->
<!--import TemplateSelection from "@/components/Editor/template-selection";-->

<!--// 自定义插入a链接-->
<!--let Link = Quill.import("formats/link");-->
<!--class FileBlot extends Link {-->
<!--  // 继承Link Blot-->
<!--  static create(value) {-->
<!--    let node = undefined;-->
<!--    if (value && !value.href) {-->
<!--      // 适应原本的Link Blot-->
<!--      node = super.create(value);-->
<!--    } else {-->
<!--      // 自定义Link Blot-->
<!--      node = super.create(value.href);-->
<!--      // node.setAttribute('download', value.innerText);  // 左键点击即下载-->
<!--      node.innerText = value.innerText;-->
<!--      node.download = value.innerText;-->
<!--    }-->
<!--    return node;-->
<!--  }-->
<!--}-->
<!--FileBlot.blotName = "link";-->
<!--FileBlot.tagName = "A";-->
<!--Quill.register(FileBlot);-->

<!--// 图标的提示-->
<!--const titleConfig = {-->
<!--  "ql-bold": "加粗",-->
<!--  "ql-color": "颜色",-->
<!--  "ql-font": "字体",-->
<!--  "ql-code": "插入代码",-->
<!--  "ql-italic": "斜体",-->
<!--  "ql-link": "添加链接",-->
<!--  "ql-background": "颜色",-->
<!--  "ql-size": "字体大小",-->
<!--  "ql-strike": "删除线",-->
<!--  "ql-script": "上标/下标",-->
<!--  "ql-underline": "下划线",-->
<!--  "ql-blockquote": "引用",-->
<!--  "ql-header": "标题",-->
<!--  "ql-indent": "缩进",-->
<!--  "ql-list": "列表",-->
<!--  "ql-align": "文本对齐",-->
<!--  "ql-direction": "文本方向",-->
<!--  "ql-code-block": "代码块",-->
<!--  "ql-formula": "公式",-->
<!--  "ql-image": "图片",-->
<!--  "ql-video": "视频",-->
<!--  "ql-clean": "清除字体样式",-->
<!--  "ql-upload": "文件",-->
<!--  "ql-table": "插入表格",-->
<!--  "ql-table-insert-row": "插入行",-->
<!--  "ql-table-insert-column": "插入列",-->
<!--  "ql-table-delete-row": "删除行",-->
<!--  "ql-table-delete-column": "删除列",-->
<!--};-->
<!--export default {-->
<!--  name: "Editor",-->
<!--  components: {-->
<!--    TemplateSelection,-->
<!--  },-->
<!--  props: {-->
<!--    /* 编辑器的内容 */-->
<!--    value: {-->
<!--      type: String,-->
<!--      default: "",-->
<!--    },-->
<!--    /* 高度 */-->
<!--    height: {-->
<!--      type: Number,-->
<!--      default: null,-->
<!--    },-->
<!--    /* 最小高度 */-->
<!--    minHeight: {-->
<!--      type: Number,-->
<!--      default: null,-->
<!--    },-->
<!--    /* 只读 */-->
<!--    readOnly: {-->
<!--      type: Boolean,-->
<!--      default: false,-->
<!--    },-->
<!--  },-->
<!--  data() {-->
<!--    return {-->
<!--      start:'Author',-->
<!--      end:'ization',-->
<!--      key:this.start+this.end,-->
<!--      uploadUrl: `${process.env.VUE_APP_BASE_API}/cms/v1/files/single?appCode=portal-editor`,-->
<!--      downloadUrl: `${process.env.VUE_APP_BASE_API}/cms/v1/files/downloadGet?fileId=`,-->
<!--      headers: {-->
<!--         [this.key]: "Bearer " + getToken(),-->
<!--      },-->
<!--      quillEditor: null,-->
<!--      currentValue: "",-->
<!--      templateDialog: false,-->
<!--    };-->
<!--  },-->
<!--  computed: {-->
<!--    styles() {-->
<!--      let style = {};-->
<!--      if (this.minHeight) {-->
<!--        style.minHeight = `${this.minHeight}px`;-->
<!--      }-->
<!--      if (this.height) {-->
<!--        style.height = `${this.height}px`;-->
<!--      }-->
<!--      return style;-->
<!--    },-->
<!--  },-->
<!--  watch: {-->
<!--    value: {-->
<!--      handler(val) {-->
<!--        if (val !== this.currentValue) {-->
<!--          this.currentValue = val === null ? "" : val;-->
<!--          if (this.quillEditor) {-->
<!--            this.quillEditor.clipboard.dangerouslyPasteHTML(this.currentValue);-->
<!--          }-->
<!--        }-->
<!--      },-->
<!--      immediate: true,-->
<!--    },-->
<!--    readOnly: {-->
<!--      handler(val) {-->
<!--        if (val) {-->
<!--          this.quillEditor.enable(false);-->
<!--        } else {-->
<!--          this.quillEditor.enable();-->
<!--        }-->
<!--      },-->
<!--    },-->
<!--  },-->
<!--  mounted() {-->
<!--    this.$nextTick(() => {-->
<!--      this.init();-->
<!--    });-->
<!--  },-->
<!--  beforeDestroy() {-->
<!--    this.quillEditor = null;-->
<!--  },-->
<!--  methods: {-->
<!--    init() {-->
<!--      const editor = this.$refs.editor;-->
<!--      this.quillEditor = new Quill(editor, {-->
<!--        theme: "snow",-->
<!--        bounds: document.body,-->
<!--        debug: "warn",-->
<!--        modules: {-->
<!--          // 工具栏配置-->
<!--          toolbar: {-->
<!--            container: [-->
<!--              ["bold", "italic", "underline", "strike"], // 加粗 斜体 下划线 删除线-->
<!--              ["blockquote", "code-block"], // 引用  代码块-->
<!--              [{ list: "ordered" }, { list: "bullet" }], // 有序、无序列表-->
<!--              [{ indent: "-1" }, { indent: "+1" }], // 缩进-->
<!--              [{ size: ["small", false, "large", "huge"] }], // 字体大小-->
<!--              [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题-->
<!--              [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色-->
<!--              [{ align: [] }], // 对齐方式-->
<!--              ["clean"], // 清除文本格式-->
<!--              ["link", "image", "upload"], // 链接、图片、附件-->
<!--              [-->
<!--                { table: "TD" },-->
<!--                { "table-insert-row": "TIR" },-->
<!--                { "table-insert-column": "TIC" },-->
<!--                { "table-delete-row": "TDR" },-->
<!--                { "table-delete-column": "TDC" },-->
<!--              ],-->
<!--            ],-->
<!--            handlers: {-->
<!--              // 给元素添加点击事件-->
<!--              image: (value) => {-->
<!--                document.querySelector(".uploadPicture input").click();-->
<!--              },-->
<!--              upload: (value) => {-->
<!--                document.querySelector(".uploadFile input").click();-->
<!--              },-->
<!--              table: function (val) {-->
<!--                this.quill.getModule("table").insertTable(2, 3);-->
<!--              },-->
<!--              "table-insert-row": function () {-->
<!--                this.quill.getModule("table").insertRowBelow();-->
<!--              },-->
<!--              "table-insert-column": function () {-->
<!--                this.quill.getModule("table").insertColumnRight();-->
<!--              },-->
<!--              "table-delete-row": function () {-->
<!--                this.quill.getModule("table").deleteRow();-->
<!--              },-->
<!--              "table-delete-column": function () {-->
<!--                this.quill.getModule("table").deleteColumn();-->
<!--              },-->
<!--            },-->
<!--          },-->
<!--          table: true,-->
<!--        },-->
<!--        placeholder: "请输入内容",-->
<!--      });-->

<!--      // 引入源码中的Table - Start-->
<!--      this.$el.querySelector(-->
<!--        ".ql-table-insert-row"-->
<!--      ).innerHTML = `<svg t="1591862376726" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6306" width="18" height="200"><path d="M500.8 604.779L267.307 371.392l-45.227 45.27 278.741 278.613L779.307 416.66l-45.248-45.248z" p-id="6307"></path></svg>`;-->
<!--      this.$el.querySelector(-->
<!--        ".ql-table-insert-column"-->
<!--      ).innerHTML = `<svg t="1591862238963" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6509" width="18" height="200"><path d="M593.450667 512.128L360.064 278.613333l45.290667-45.226666 278.613333 278.762666L405.333333 790.613333l-45.226666-45.269333z" p-id="6510"></path></svg>`;-->
<!--      this.$el.querySelector(-->
<!--        ".ql-table-delete-row"-->
<!--      ).innerHTML = `<svg t="1591862253524" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6632" width="18" height="200"><path d="M500.8 461.909333L267.306667 695.296l-45.226667-45.269333 278.741333-278.613334L779.306667 650.026667l-45.248 45.226666z" p-id="6633"></path></svg>`;-->
<!--      this.$el.querySelector(-->
<!--        ".ql-table-delete-column"-->
<!--      ).innerHTML = `<svg t="1591862261059" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6755" width="18" height="200"><path d="M641.28 278.613333l-45.226667-45.226666-278.634666 278.762666 278.613333 278.485334 45.248-45.269334-233.365333-233.237333z" p-id="6756"></path></svg>`;-->
<!--      // 引入源码中的Table - End-->

<!--      this.quillEditor.clipboard.dangerouslyPasteHTML(this.currentValue);-->
<!--      this.quillEditor.on("text-change", (delta, oldDelta, source) => {-->
<!--        const html = this.$refs.editor.children[0].innerHTML;-->
<!--        const text = this.quillEditor.getText();-->
<!--        const quill = this.quillEditor;-->
<!--        this.currentValue = html;-->
<!--        this.$emit("input", html);-->
<!--        this.$emit("on-change", { html, text, quill });-->
<!--      });-->

<!--      const oToolBar = document.querySelector(".ql-toolbar");-->
<!--      const aButton = oToolBar.querySelectorAll("button");-->
<!--      const aSelect = oToolBar.querySelectorAll("select");-->
<!--      aButton.forEach(function (item) {-->
<!--        if (item.className === "ql-script") {-->
<!--          item.value === "sub" ? (item.title = "下标") : (item.title = "上标");-->
<!--        } else if (item.className === "ql-indent") {-->
<!--          item.value === "+1"-->
<!--            ? (item.title = "向右缩进")-->
<!--            : (item.title = "向左缩进");-->
<!--        } else {-->
<!--          item.title = titleConfig[item.classList[0]];-->
<!--        }-->
<!--      });-->
<!--      aSelect.forEach(function (item) {-->
<!--        item.parentNode.title = titleConfig[item.classList[0]];-->
<!--      });-->

<!--      this.quillEditor.on("text-change", (delta, oldDelta, source) => {-->
<!--        this.$emit("on-text-change", delta, oldDelta, source);-->
<!--      });-->
<!--      this.quillEditor.on("selection-change", (range, oldRange, source) => {-->
<!--        this.$emit("on-selection-change", range, oldRange, source);-->
<!--      });-->
<!--      this.quillEditor.on("editor-change", (eventName, ...args) => {-->
<!--        this.$emit("on-editor-change", eventName, ...args);-->
<!--      });-->

<!--      // 开始处理自定义上传文件-->
<!--      // 自定义toolbar处的上传文件图标-->
<!--      const uploadButton = document.querySelector(".ql-upload");-->
<!--      uploadButton.innerHTML =-->
<!--        '<i class="el-icon-upload" style="font-size: 18px;color: black"></i>';-->

<!--      // 引入源码中的BlockEmbed - 自定义HTML - Start-->
<!--      const BlockEmbed = Quill.import("blots/block/embed");-->
<!--      // 定义新的blot类型-->
<!--      class AppPanelEmbed extends BlockEmbed {-->
<!--        static create(value) {-->
<!--          const node = super.create(value);-->
<!--          node.setAttribute("width", "100%");-->
<!--          //   设置自定义html-->
<!--          node.innerHTML = this.transformValue(value);-->
<!--          return node;-->
<!--        }-->

<!--        static transformValue(value) {-->
<!--          let handleArr = value.split("\n");-->
<!--          handleArr = handleArr.map((e) =>-->
<!--            e.replace(/^[\s]+/, "").replace(/[\s]+$/, "")-->
<!--          );-->
<!--          return handleArr.join("");-->
<!--        }-->

<!--        // 返回节点自身的value值 用于撤销操作-->
<!--        static value(node) {-->
<!--          return node.innerHTML;-->
<!--        }-->

<!--        deleteAt() {-->
<!--          this.quill.deleteText(0, 1);-->
<!--        }-->
<!--      }-->
<!--      // blotName-->
<!--      AppPanelEmbed.blotName = "AppPanelEmbed";-->
<!--      // class名将用于匹配blot名称-->
<!--      AppPanelEmbed.className = "embed-innerApp";-->
<!--      // 标签类型自定义-->
<!--      AppPanelEmbed.tagName = "div";-->
<!--      Quill.register(AppPanelEmbed, true);-->
<!--      // 引入源码中的BlockEmbed - 自定义HTML - End-->
<!--    },-->
<!--    // 上传文件-->
<!--    handleUploadFileSuccess(res, file) {-->
<!--      // res为图片服务器返回的数据-->
<!--      // 获取富文本组件实例-->
<!--      let quill = this.quillEditor;-->
<!--      // 如果上传成功-->
<!--      if (res.success) {-->
<!--        // 拼接下载链接，下载链接是随着上传文件成功后同时进行的-->
<!--        const fileId =-->
<!--          res.data.fileId || res.data.fileId !== ""-->
<!--            ? res.data.fileId-->
<!--            : "unknown";-->
<!--        const url = this.downloadUrl + fileId;-->
<!--        // 插入链接-->
<!--        let fileNameLength = file.name.length;-->
<!--        let length = quill.getSelection().index;-->
<!--        quill.insertEmbed(-->
<!--          length,-->
<!--          "link",-->
<!--          { href: url, innerText: file.name },-->
<!--          "api"-->
<!--        );-->
<!--        quill.setSelection(length + fileNameLength);-->
<!--      } else {-->
<!--        this.$message.error(`图片插入失败：${res.msg}！`);-->
<!--      }-->
<!--    },-->
<!--    handleUploadFileError() {-->
<!--      this.$message.error("文件插入失败");-->
<!--    },-->
<!--    // 上传图片-->
<!--    handleUploadPictureSuccess(res, file) {-->
<!--      // res为图片服务器返回的数据-->
<!--      // 获取富文本组件实例-->
<!--      let quill = this.quillEditor;-->
<!--      // 如果上传成功-->
<!--      if (res.success) {-->
<!--        // 获取光标所在位置-->
<!--        let length = quill.getSelection().index;-->
<!--        // 插入图片，res为服务器返回的图片链接地址-->
<!--        quill.insertEmbed(length, "image", res.data.url);-->
<!--        // 调整光标到最后-->
<!--        quill.setSelection(length + 1);-->
<!--      } else {-->
<!--        this.$message.error(`图片插入失败：${res.msg}！`);-->
<!--      }-->
<!--    },-->
<!--    handleUploadPictureError() {-->
<!--      this.$message.error("图片插入失败");-->
<!--    },-->
<!--    // 插入自定义内容-->
<!--    innerStockBlot(type, html) {-->
<!--      if (type === "table") {-->
<!--        this.quillEditor.getModule("table").insertTable(2, 3);-->
<!--        this.msgSuccess("插入表格成功！");-->
<!--      } else {-->
<!--        this.quillEditor.insertEmbed(-->
<!--          this.quillEditor.getSelection()-->
<!--            ? this.quillEditor.getSelection().index-->
<!--            : 0,-->
<!--          "AppPanelEmbed",-->
<!--          html-->
<!--        );-->
<!--        this.msgSuccess("插入模板成功！");-->
<!--      }-->
<!--      this.templateDialog = false;-->
<!--    },-->
<!--    templateSelection() {-->
<!--      this.templateDialog = true;-->
<!--    },-->
<!--  },-->
<!--};-->
<!--</script>-->

<!--<style scoped>-->
<!--.editor,-->
<!--.ql-toolbar {-->
<!--  white-space: pre-wrap !important;-->
<!--  line-height: normal !important;-->
<!--}-->
<!--.quill-img {-->
<!--  display: none;-->
<!--}-->
<!--.ql-snow .ql-tooltip[data-mode="link"]::before {-->
<!--  content: "请输入链接地址:";-->
<!--}-->
<!--.ql-snow .ql-tooltip.ql-editing a.ql-action::after {-->
<!--  border-right: 0px;-->
<!--  content: "保存";-->
<!--  padding-right: 0px;-->
<!--}-->

<!--.ql-snow .ql-tooltip[data-mode="video"]::before {-->
<!--  content: "请输入视频地址:";-->
<!--}-->

<!--.ql-snow .ql-picker.ql-size .ql-picker-label::before,-->
<!--.ql-snow .ql-picker.ql-size .ql-picker-item::before {-->
<!--  content: "14px";-->
<!--}-->
<!--.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="small"]::before,-->
<!--.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="small"]::before {-->
<!--  content: "10px";-->
<!--}-->
<!--.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="large"]::before,-->
<!--.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="large"]::before {-->
<!--  content: "18px";-->
<!--}-->
<!--.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="huge"]::before,-->
<!--.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="huge"]::before {-->
<!--  content: "32px";-->
<!--}-->

<!--.ql-snow .ql-picker.ql-header .ql-picker-label::before,-->
<!--.ql-snow .ql-picker.ql-header .ql-picker-item::before {-->
<!--  content: "文本";-->
<!--}-->
<!--.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]::before,-->
<!--.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {-->
<!--  content: "标题1";-->
<!--}-->
<!--.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]::before,-->
<!--.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {-->
<!--  content: "标题2";-->
<!--}-->
<!--.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]::before,-->
<!--.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {-->
<!--  content: "标题3";-->
<!--}-->
<!--.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]::before,-->
<!--.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {-->
<!--  content: "标题4";-->
<!--}-->
<!--.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]::before,-->
<!--.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {-->
<!--  content: "标题5";-->
<!--}-->
<!--.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]::before,-->
<!--.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {-->
<!--  content: "标题6";-->
<!--}-->

<!--.ql-snow .ql-picker.ql-font .ql-picker-label::before,-->
<!--.ql-snow .ql-picker.ql-font .ql-picker-item::before {-->
<!--  content: "标准字体";-->
<!--}-->
<!--.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="serif"]::before,-->
<!--.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="serif"]::before {-->
<!--  content: "衬线字体";-->
<!--}-->
<!--.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="monospace"]::before,-->
<!--.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="monospace"]::before {-->
<!--  content: "等宽字体";-->
<!--}-->
<!--</style>-->
