import request from "@/utils/request";

// 查询字典数据列表
export function listData(query) {
  return request({
    url: `/user/dict/data`,
    method: "get",
    params: query
  });
}

// 查询字典数据详细
export function getData(dictCode) {
  return request({
    url: "/user/dict/data/" + dictCode,
    method: "get"
  });
}

// 根据字典类型查询字典数据信息
export function getDicts(dictCode) {
  return request({
    url: "/user/dict/data/list/" + dictCode,
    method: "get"
  });
}

// 新增字典数据
export function addData(data) {
  return request({
    url: "/user/dict/data",
    method: "post",
    data: data
  });
}

// 修改字典数据
export function updateData(data) {
  return request({
    url: "/user/dict/data/edit",
    method: "post",
    data: data
  });
}

// 删除字典数据
export function delData(data) {
  return request({
    url: "/user/dict/data/delete",
    method: "post",
    data: data
  });
}

// 导出字典数据
export function exportData(query) {
  return request({
    url: "/system/dict/data/export",
    method: "get",
    params: query
  });
}
