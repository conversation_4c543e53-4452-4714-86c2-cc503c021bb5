import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'
import SsoLayout from '@/layout/sso'

/**
 * Note: 路由配置项
 *
 * hidden: true                   // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true               // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect           // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'             // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
    noCache: true                // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'               // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'             // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false            // 如果设置为false，则不会在breadcrumb面包屑中显示
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/ssoRedirect',
    component: SsoLayout,
    hidden: true
  },
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [{
      path: '/redirect/:path(.*)',
      component: resolve => require(['@/views/redirect'], resolve)
    }]
  },
  {
    path: '/',
    redirect: '/sw/'
  },
  {
    path: '/login',
    component: resolve => require(['@/views/login'], resolve)
    // component: resolve => require(['@/views/loginsso'], resolve)
  },
  {
    path: '/loginsso',
    component: resolve => require(['@/views/loginsso'], resolve)
  },
  {
    path: '/401',
    component: resolve => require(['@/views/error/401'], resolve),
    hidden: true
  },
  {
    path: '/sw',
    component: resolve => require(['@/views/sw/index'], resolve),
    hidden: true,
    name:'swIndex',
    meta : {
      noCache: false,
      title: '首页',
      icon: '',
      breadcrumb: false,
    },
    children: [
      {
        // 菜单
        path: '/sw/',
        component: resolve => require(['@/views/sw/sso'], resolve),
        hidden: true,
        name:'swIndexA',
        meta : {
          noCache: false,
          title: '转化评价',
          icon: '',
          breadcrumb: false,
        }
      },
      {
        // 菜单
        path: '/sw/menus',
        component: resolve => require(['@/views/sw/platform/menus/index.vue'], resolve),
        hidden: true,
        name:'menus',
        meta : {
          noCache: false,
          title: '菜单管理',
          icon: '',
          breadcrumb: false,
        }
      },

      {
        // 菜单
        path: '/sw/roles',
        component: resolve => require(['@/views/sw/platform/roles/index.vue'], resolve),
        hidden: true,
        name:'roles',
        meta : {
          noCache: false,
          title: '角色管理',
          icon: '',
          breadcrumb: false,
        }
      },
      {
        // 菜单
        path: '/sw/users',
        component: resolve => require(['@/views/sw/platform/users/index.vue'], resolve),
        hidden: true,
        name:'users',
        meta : {
          noCache: false,
          title: '用户管理',
          icon: '',
          breadcrumb: false,
        }
      },
      {
        // 5G专网端到端流程进度
        path: '/sw/5g/progress',
        component: resolve => require(['@/views/sw/5g/progress.vue'], resolve),
        hidden: true,
        name: 'Private5GProgress',
        meta: {
          noCache: false,
          title: '5G专网端到端流程进度',
          icon: '',
          breadcrumb: false
        }
      },
      {
        path: '/sw/5g/progressDetail',
        component: () => import('@/views/sw/5g/progressDetail'),
        name: 'Private5GProgressDetail',
        meta: { title: '5G专网端到端流程进度详情', icon: 'el-icon-document' },
        hidden: true
      },
      {
        // 字典管理列表
        path: '/sw/tagsDictManage',
        component: resolve => require(['@/views/sw/tags/dictManage'], resolve),
        hidden: true,
        name:'dictManage',
        meta : {
          noCache: false,
          title: '字典管理',
          icon: '',
          breadcrumb: false,
        }
      },
    ],
  },
  {
    path: '/404',
    component: resolve => require(['@/views/error/404'], resolve),
    hidden: true
  },
  {
    path: '/system',
    component: Layout,
    hidden: true,
    children: [
      {
        // 后台 个人中心
        path: 'user/profile',
        component: resolve => require(['@/views/system/user/profile'], resolve),
        hidden: true
      }
    ]
  }
]

// 路由重复
//获取原型对象上的push函数
const originalPush = Router.prototype.push
//修改原型对象中的push方法
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}

export default new Router({
  mode: 'hash', // 去掉url中的#
  scrollBehavior: () => ({
    y: 0
  }),
  routes: constantRoutes
})
