<template>
  <div class="app-container">
    <el-card shadow="never">
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        :inline="true"
      >
        <el-form-item label="租户" v-if="$store.getters.customParam.userType === 'admin'">
          <el-select
              v-model="queryParams.tenantId"
              placeholder="请选择租户"
              size="small"
              style="width: 240px"
              filterable
              remote
              :remote-method="getTenantList"
              :loading="getTenantLoading"
              @change="tenantChange"
          >
            <el-option
                v-for="item in tenantList"
                :key="item.tenantId"
                :label="item.tenantName"
                :value="item.tenantId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="文档库名称" prop="documentLibName">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入文档库名称"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
          >
            搜索
          </el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            >新增
          </el-button>
        </el-col>
      </el-row>

      <el-table
        @row-click="clickRow"
        v-loading="loading"
        :data="documentLibList"
      >
        <el-table-column
            label="文档库名称"
            align="left"
            prop="name"
            :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <a style="color:#3a7eb9;" @click="detail(scope.row)">{{ scope.row.name }}</a>
          </template>
        </el-table-column>
        <el-table-column
          label="页面数量"
          prop="docNum"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="显示顺序"
          prop="sort"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="创建人"
          prop="createUserName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="创建时间"
          align="left"
          prop="createDate"
          width="300"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createDate) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="300"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope" v-if="!scope.row.isOp">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-user"
              @click="handleMemberManager(scope.row)"
            >
              {{
                currentLoginUser.userInfo.staffId === scope.row.createBy
                  ? "成员管理"
                  : "查看成员"
              }}
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-if="currentLoginUser.userInfo.staffId === scope.row.createBy"
              >修改
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-if="currentLoginUser.userInfo.staffId === scope.row.createBy"
              >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="docLibTotal > 0"
        :total="docLibTotal"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="initDocumentLibs"
      />

      <!--   文档库成员管理 - 选择用户   -->
      <el-dialog
        title="成员管理"
        @open="show"
        :visible.sync="openMemberManager"
        width="1300px"
        height="800px"
        :before-close="handleClose"
        append-to-body
        v-dialogDrag
      >
        <template>
          <div
            class="app-container"
            style="height: calc(100vh - 300px); width: 100%"
          >
            <split-pane
              :min-percent="15"
              :max-percent="30"
              :default-percent="25"
              split="vertical"
            >
              <template slot="paneL">
                <el-card
                  class="dep-card"
                  shadow="never"
                  v-loading="treeLoading"
                >
                  <el-tree
                    :props="lazyTreeProps"
                    :load="loadNode"
                    lazy
                    :expand-on-click-node="false"
                    :default-expanded-keys="[defaultOrgId]"
                    ref="asyncTree"
                    @node-click="handleNodeClick"
                    node-key="orgId"
                    highlight-current
                  >
                  </el-tree>
                </el-card>
              </template>
              <template slot="paneR">
                <el-card
                  class="dep-card"
                  shadow="never"
                  v-loading="treeLoading"
                >
                  <div slot="header" class="clearfix">
                    <span>{{ selectNodeName }}</span>
                    <el-button
                      style="float: right; padding: 3px 0"
                      type="text"
                      icon="el-icon-refresh"
                      @click="allUser"
                      >全部人员
                    </el-button>
                  </div>
                  <el-form
                    :model="userQueryParams"
                    ref="queryForm"
                    :inline="true"
                    v-show="showSearch"
                    label-width="68px"
                  >
                    <el-form-item label="用户名称" prop="staffName">
                      <el-input
                        v-model="userQueryParams.staffName"
                        placeholder="请输入用户名称"
                        clearable
                        size="small"
                        style="width: 240px"
                        @keyup.enter.native="handleQuery"
                      />
                    </el-form-item>
                    <el-form-item label="邮箱" prop="email">
                      <el-input
                        v-model="userQueryParams.email"
                        placeholder="请输入邮箱"
                        clearable
                        size="small"
                        style="width: 240px"
                        @keyup.enter.native="handleQuery"
                      />
                    </el-form-item>
                    <el-form-item>
                      <el-button
                        type="primary"
                        icon="el-icon-search"
                        size="mini"
                        @click="handleQuery"
                        >搜索
                      </el-button>
                      <el-button
                        icon="el-icon-refresh"
                        size="mini"
                        @click="resetQuery"
                        >重置</el-button
                      >
                    </el-form-item>
                  </el-form>

                  <el-table
                    ref="multipleTable"
                    v-loading="loading"
                    :data="userList"
                    @selection-change="handleSelectionChange"
                  >
                    <el-table-column
                      type="selection"
                      align="center"
                      width="100"
                    />
                    <el-table-column
                      label="用户编号"
                      align="left"
                      key="staffId"
                      prop="staffId"
                      v-if="columns[0].visible"
                    />
                    <el-table-column
                      label="登录名称"
                      align="left"
                      key="loginName"
                      prop="loginName"
                      v-if="columns[1].visible"
                    />
                    <el-table-column
                      label="用户名称"
                      align="left"
                      key="staffName"
                      prop="staffName"
                      v-if="columns[2].visible"
                      :show-overflow-tooltip="true"
                    />
                    <el-table-column
                      label="工作组名称"
                      align="left"
                      key="orgName"
                      prop="orgName"
                      v-if="columns[4].visible"
                      :show-overflow-tooltip="true"
                    />
                    <el-table-column
                      label="手机号码"
                      align="left"
                      key="cellphone"
                      prop="cellphone"
                      v-if="columns[5].visible"
                    />
                    <el-table-column
                      label="邮箱"
                      align="left"
                      key="email"
                      prop="email"
                      v-if="columns[6].visible"
                    />
                  </el-table>
                  <pagination
                    v-show="memberSelectionTotal > 0"
                    :total="memberSelectionTotal"
                    :page.sync="userQueryParams.pageNum"
                    :limit.sync="userQueryParams.pageSize"
                    @pagination="getList"
                  />
                </el-card>
              </template>
            </split-pane>
          </div>
        </template>

        <div slot="footer" class="dialog-footer">
          <el-button
            type="primary"
            @click="memberSubmitForm"
            :loading="submitLoading"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>

      <!--   文档库成员管理 - 列表页   -->
      <el-dialog
        title="文档成员管理"
        :visible.sync="openMemberList"
        append-to-body
        v-dialogDrag
      >
        <el-card class="dep-card" shadow="never" v-loading="treeLoading">
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="addMemberHandler"
                v-if="currentLoginUser.userInfo.staffId === currentLib.createBy"
                >添加成员
              </el-button>
              <el-button
                type="danger"
                plain
                icon="el-icon-delete"
                size="mini"
                @click="delMemberHandler"
                :disabled="rowChecked"
                v-if="currentLoginUser.userInfo.staffId === currentLib.createBy"
              >
                删除成员
              </el-button>
            </el-col>
          </el-row>

          <el-table
            v-loading="loading"
            :data="memberList"
            empty-text="该文档暂无成员"
            @selection-change="memberHandleSelectionChange"
          >
            <el-table-column type="selection" width="100" align="center" />
            <el-table-column
              label="用户编号"
              align="left"
              key="staffId"
              prop="staffId"
              v-if="columns[0].visible"
            />
            <el-table-column
              label="登录名称"
              align="left"
              key="loginName"
              prop="loginName"
              v-if="columns[1].visible"
            />
            <el-table-column
              label="用户名称"
              align="left"
              key="staffName"
              prop="staffName"
              v-if="columns[2].visible"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="手机号码"
              align="left"
              key="cellphone"
              prop="cellphone"
              v-if="columns[5].visible"
            />
            <el-table-column
              label="邮箱"
              align="left"
              key="email"
              prop="email"
              v-if="columns[6].visible"
            />
            <el-table-column
              label="操作"
              align="center"
              class-name="small-padding fixed-width"
              v-if="currentLoginUser.userInfo.staffId === currentLib.createBy"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  icon="el-icon-delete"
                  @click="delMemberHandler(scope.row)"
                  type="text"
                  v-if="scope.row.staffId !== currentLib.createBy"
                  >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="memberManagerTotal > 0"
            :total="memberManagerTotal"
            :page.sync="memberQueryParams.pageNum"
            :limit.sync="memberQueryParams.pageSize"
            @pagination="getMember"
          />
        </el-card>
      </el-dialog>

      <!-- 添加或修改角色配置对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="open"
        width="500px"
        append-to-body
        v-dialogDrag
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-form-item label="文档库名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入文档库名称" />
          </el-form-item>
          <el-form-item label="文档库描述" label-width="100px" prop="description">
            <el-input
              v-model="form.description"
              type="textarea"
              placeholder="请输入内容"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="租户">
            <el-input
                v-model="form.tenantName"
                placeholder="租户"
                maxlength="50"
                disabled
            />
          </el-form-item>
          <el-form-item label="排序" prop="docLibSort">
            <el-input-number
              v-model="docLibSort"
              controls-position="right"
              :min="1"
              @blur="handChange()"
            />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="submitLoading"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import {
  addDocumentLib,
  addDocumentLibMember,
  deleteDocumentLib,
  deleteDocumentLibMember,
  getDocumentLibMember,
  getDocumentList,
  getMemberPage,
  isView,
  updateDocumentLib,
} from "@/api/apps/documentLib";
import { treeselect } from "@/api/system/dept";
import { listUser } from "@/api/system/user";
import { list as tenantList } from '@/api/system/tenant'

export default {
  name: "DocumentLib",
  data() {
    return {
      // 遮罩层
      loading: true,
      submitLoading: false,
      getTenantLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      memberManagerTotal: 0,
      memberSelectionTotal: 0,
      docLibTotal: 0,
      // 文档库列表
      documentLibList: [],
      tenantList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openMemberManager: false,
      openMemberList: false,
      menuExpand: false,
      menuNodeAll: false,
      // 日期范围
      dateRange: [],
      // 菜单列表
      menuOptions: [],
      scopeOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        tenantId: this.$store.getters.customParam.tenantId,
        tenantName: this.$store.getters.customParam.tenantName,
      },
      // 表单参数
      form: {},
      docLibSort:'',
      // 表单校验
      rules: {
        name: [
          { required: true, message: "文档库名称不能为空", trigger: "blur" },
          {
            min:1,
            max:40,
            message: "请输入1到40个字符",
            trigger: "blur",
          },
        ],
        description: [
          { required: true, message: '文档描述不能为空', trigger: 'blur' },
          { min:1, max:60, message:"请输入1到60个字符", trigger:"blur" }
        ]
      },

      //成员管理相关属性
      treeLoading: false,
      lazyTreeProps: {
        children: "children",
        label: "orgName",
        isLeaf: "leaf",
      },
      defaultOrgId: this.$store.getters.orgId,
      selectNodeName: "全部人员",
      // 查询参数
      userQueryParams: {
        pageNum: 1,
        pageSize: 10,
        staffName: undefined,
        email: undefined,
        status: undefined,
        orgId: this.$store.getters.orgId,
        staffOrgType: "F",
        tenantId: this.$store.getters.customParam.tenantId,
        tenantName: this.$store.getters.customParam.tenantName,
      },
      memberQueryParams: {
        pageNum: 1,
        pageSize: 10,
        docLibId: undefined,
        staffName: undefined,
        email: undefined,
        status: undefined,
        orgId: this.$store.getters.orgId,
        staffOrgType: "F",
      },
      // 用户表格数据
      userList: null,
      //文档库成员表格
      memberList: null,
      // 列信息
      columns: [
        { key: 0, label: `用户编号`, visible: false },
        { key: 1, label: `登陆名称`, visible: false },
        { key: 2, label: `用户名称`, visible: true },
        { key: 3, label: `用户昵称`, visible: true },
        { key: 4, label: `工作组名称`, visible: true },
        { key: 5, label: `手机号码`, visible: true },
        { key: 6, label: `邮箱`, visible: true },
        { key: 7, label: `创建时间`, visible: true },
      ],
      multipleSelection: [],
      memberMultipleSelection: [],
      currentDocumentLibId: "",
      currentMember: [],
      rowChecked: true,
      currentLib: {},
    };
  },
  computed: {
    currentLoginUser() {
      return this.$store.state.user;
    },
  },
  created() {
    this.getTenantList();
    this.initDocumentLibs();
    this.userQueryParams.orgId = this.$store.getters.orgId;
    this.getList();
  },
  methods: {
    getTenantList(tenantName) {
      this.getTenantLoading = true;
      let query = {}
      if (tenantName !== undefined && tenantName !== '') {
        query.tenantName = tenantName
        query.tenantId = undefined
      } else {
        query.tenantId = this.queryParams.tenantId
      }
      tenantList(query).then((response) => {
        this.tenantList = response.data;
      }).finally(() => {
        this.getTenantLoading = false;
      });
    },
    handChange() {
      if(this.docLibSort.toString().length >= 11) {
        this.$message.error('请输入1到11以内的数字')
      }
    },
    // 获取el-dialog中的table
    show() {
      this.$nextTick((_) => {
        if (this.currentMember.length === 0) {
          this.$refs.multipleTable.clearSelection();
        }
        this.userList.forEach((i) => {
          if (this.currentMember.indexOf(i.staffId) > -1) {
            this.$refs.multipleTable.toggleRowSelection(i, true); // 设置默认选中
          } else {
            this.$refs.multipleTable.toggleRowSelection(i, false);
          }
        });
      });
    },
    //查询文档库列表
    initDocumentLibs() {
      this.loading = true;
      getDocumentList(this.queryParams).then((response) => {
        this.documentLibList = response.data.records;
        this.docLibTotal = response.data.total;
        this.loading = false;
      });
    },
    /** 查询角色列表 */
    getList() {
      const _this = this
      listUser({ ...this.addDateRange(this.userQueryParams) }).then(
        (response) => {
          this.userList = response.data.records;
          if (this.currentMember.length > 0) {
            this.$nextTick((_) => {
              if (this.currentMember.length === 0) {
                this.$refs.multipleTable.clearSelection();
              }
              this.userList.forEach((i) => {
                if (this.currentMember.indexOf(i.staffId) > -1) {
                  this.$refs.multipleTable.toggleRowSelection(i, true); // 设置默认选中
                } else {
                  this.$refs.multipleTable.toggleRowSelection(i, false);
                }
              });
            });
          }
          this.memberSelectionTotal = response.data.total;
          this.loading = false;
        }
      );
    },
    /*查询文档库成员列表*/
    getMember() {
      getMemberPage(this.memberQueryParams).then((res) => {
        this.memberList = res.data.records;
        if (this.memberList !== null) {
          this.currentMember = this.memberList.map((v) => v.staffId);
          this.multipleSelection = this.memberList.map((v) => v.staffId);
        }
        this.memberManagerTotal = res.data.total;
        this.loading = false;
      });
    },
    addMemberHandler() {
      this.openMemberManager = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.openMemberManager = false;
      this.openMemberList = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        name: undefined,
        description: undefined,
      };
      this.resetForm("form");
      this.docLibSort = "";
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
      this.initDocumentLibs();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.name = "";
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加文档库";
      this.form.tenantName = this.queryParams.tenantName;
      this.form.tenantId = this.queryParams.tenantId;
    },
    handleMemberManager(row) {
      this.currentLib = row;
      this.currentDocumentLibId = row.id;

      this.openMemberList = true;
      this.memberQueryParams.docLibId = row.id;
      this.getMember();

      getDocumentLibMember({ documentLibId: row.id }).then((response) => {
        this.currentMember = response.data;
        this.multipleSelection = response.data;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.title = "修改文档库";
      this.reset();
      this.open = true;
      this.form.id = row.id;
      this.form.name = row.name;
      this.form.description = row.description;
      this.docLibSort = row.sort;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm("是否确认删除名称为（" + row.name + "）的文档库?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return deleteDocumentLib(row.id);
        })
        .then(() => {
          this.$message({
            message: "删除成功！",
            type: "success",
          });
        })
        .catch((err) => {
          // this.$message.error("删除失败！");
        })
        .finally(() => {
          this.queryParams.pageNum = 1;
          this.initDocumentLibs();
        });
    },
    memberSubmitForm() {
      let userIds = Array.from(new Set([...this.multipleSelection, ...this.currentMember]));
      addDocumentLibMember({
        userIds: userIds,
        documentLibId: this.currentDocumentLibId,
      }).then((response) => {
        if (response.success) {
          this.$message({
            message: "添加成员成功！",
            type: "success",
          });
          this.openMemberManager = false;
          this.getMember();
        } else {
          this.$message.error("添加成员失败！");
        }
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.submitLoading = true;
          this.form.sort = this.docLibSort;
          if (this.form.id !== undefined) {
            updateDocumentLib(this.form).then((response) => {
              this.submitLoading = false;
              if (response.success) {
                this.msgSuccess("修改成功");
                this.open = false;
                this.initDocumentLibs();
              } else {
                this.$message.error(response.message);
              }
            });
          } else {
            addDocumentLib(this.form).then((response) => {
              this.submitLoading = false;
              if (response.success) {
                this.msgSuccess("新增成功");
                this.open = false;
                this.initDocumentLibs();
                this.reset();
              } else {
                this.$message.error(response.message);
              }
            });
          }
        }
      });
    },
    clickRow(row, column, event) {
      if (column.label !== "操作") {
        isView({ id: row.id }).then((res) => {
          if (res.data) {
            this.$router.push({
              name: "documentDetail",
              params: {
                documentLibId: row.id,
                documentLibName: row.name,
              },
            });
          } else {
            return;
          }
        });
      }
    },
    detail(row) {
      isView({ id: row.id }).then((res) => {
        if (res.data) {
          this.$router.push({
            name: "documentDetail",
            params: {
              documentLibId: row.id,
              documentLibName: row.name,
            },
          });
        } else {
          return
        }
      });
    },
    /*成员管理相关方法*/
    loadNode(node, resolve) {
      if (!node.data) {
        this.treeLoading = true;
      }
      treeselect({
        orgId: node.data ? node.data.orgId : this.defaultOrgId,
        queryType: node.data ? "down" : "current",
        tenantId: this.queryParams.tenantId
      }).then((response) => {
        resolve(response.data);
        this.treeLoading = false;
      });
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.userQueryParams.orgId = data.orgId;
      this.selectNode = data;
      this.selectNodeName = data.orgName;
      this.isAddUser = false;
      this.getList();
    },
    allUser() {
      const node = this.$refs.asyncTree.root;
      node.loaded = false;
      node.expand();
      this.userQueryParams.orgId = "";
      this.selectNodeName = "全部人员";
      this.selectNode = undefined;
      this.getList();
    },
    handleSelectionChange(val) {
      let userIds = val.map(function (value, index, array) {
        return value.staffId;
      });
      this.multipleSelection = userIds;
    },
    memberHandleSelectionChange(val) {
      this.memberMultipleSelection = val.map(function (value, index, array) {
        return value.staffId;
      });
      this.rowChecked = !val.length;
    },
    handleClose(done) {
      done();
    },
    delMemberHandler(row) {
      const _this = this;
      const userIds = row.staffId ? [ row.staffId ] : _this.memberMultipleSelection;
      if (userIds.includes(this.currentLib.createBy)) {
        this.$message.error(
          `不允许删除该文档库创建者：'${this.currentLib.createUserName}'，请重新选择`
        );
        return;
      }
      this.$confirm(
        "是否确认删除当前选中的共" + userIds.length + "个该文档内成员?",
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return deleteDocumentLibMember({
            userIds: userIds,
            documentLibId: _this.currentDocumentLibId,
          });
        })
        .then(() => {
          this.$message({
            message: "删除成功！",
            type: "success",
          });
        })
        .catch((err) => {
          // this.$message.error("删除失败！")
        })
        .finally(() => {
          this.memberQueryParams.docLibId = _this.currentDocumentLibId;
          this.getMember();
        });
    },
    tenantChange(tenantId) {
      if (tenantId !== '') {
        this.queryParams.tenantName = this.tenantList.find(item => item.tenantId === tenantId).tenantName
        this.userQueryParams.tenantId = tenantId
      }
      this.handleQuery();
    },
  },
};
</script>
<style lang="scss" scoped>
.dep-card {
  width: 100%;
}
</style>
