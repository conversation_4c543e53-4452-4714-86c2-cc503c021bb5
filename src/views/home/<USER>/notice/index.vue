<template>
  <el-card
    :class="this.bgColor === true ?'notice-card':'unshow-notice-card'"
    v-loading="noticeLoading"
    shadow="never"
  >
    <div slot="header" v-if="show">
      <span class="card-title">
        <div class="title">
          <span> <i class="icon iconfont icon-gonggao"></i>通知公告 </span>
        </div>
        <div class="moreButtom">
          <el-button
            class="exec"
            type="text"
            style="font-size: 12px"
            @click="moreExt('system_notice')"
            >更多<i class="el-icon-d-arrow-right"></i
          ></el-button>
        </div>
      </span>
    </div>
    <div class="home-card-body">
      <a class="notice" v-for="item in notice" :key="item.noticeId"
         :href="`/portal/home/<USER>/${item.programaType}/${item.noticeId}`" target="view_window">
        <div class="notice-title">
          「<span style="color: red">{{ item.noticeTypeName }}</span
          >」{{ item.noticeTitle }}
        </div>
        <div class="notice-time">
          {{ parseTime(item.createDate, "{y}-{m}-{d}") }}
        </div>
      </a>
      <el-empty
        style="padding: 20px 0"
        v-if="notice.length <= 0"
        :image-size="30"
        description="暂无业务动态"
      >
        <el-link
          icon="el-icon-refresh"
          @click="getNotice"
          :underline="false"
          type="primary"
          >刷新</el-link
        ></el-empty
      >
    </div>
  </el-card>
</template>

<script>
import { selectTopPage } from "@/api/system/notice";
export default {
  props: ["item"],
  name: "notice",
  data() {
    return {
      show: "",
      notice: [],
      noticeLoading: false,
      bgColor:"",
    };
  },
  created() {
    this.getNotice();
    this.show = this.item.showTitle;
    this.bgColor = this.item.showBgColor;
  },
  watch:{
    'item.showTitle' :function (val) {
      this.show = val
    },
    'item.showBgColor' :function(val) {
      this.bgColor = val
    }
  },
  methods: {
    moreExt(type) {
      window.open(`/portal/home/<USER>/${type}`);
    },
    getNotice() {
      this.noticeLoading = true;
      selectTopPage({
        programaType: "system_notice",
      }).then((res) => {
        this.notice = res.data.records;
        this.noticeLoading = false;
      });
    },
  },
};
</script>

<style scoped lang="scss">
.home-card-body {
  padding-top: 20px;
}
.notice-card {
  height: 100%;
  background: #fff;
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 0;
    }
  }
  .notice {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    padding: 4px 0;
    font-size: 14px;
    .notice-title {
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: nowrap;
      flex-basis: 70%;
    }
    .notice-time {
      flex-basis: 20%;
      text-align: right;
    }
  }
}
.unshow-notice-card {
  height: 100%;
  border: 0px;
  background: #fff;
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 0;
    }
  }
  .notice {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    padding: 4px 0;
    font-size: 14px;
    .notice-title {
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: nowrap;
      flex-basis: 70%;
    }
    .notice-time {
      flex-basis: 20%;
      text-align: right;
    }
  }
}
.moreButtom {
  display: flex;
  justify-content: flex-end;
}
</style>
