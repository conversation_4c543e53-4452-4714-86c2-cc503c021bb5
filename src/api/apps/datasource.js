import request from '@/utils/request'

export function findPage(data) {
  return request({
    url: '/generator/datasource/findPage',
    method: 'post',
    data
  })
}

export function findList() {
  return request({
    url: '/generator/datasource/findList',
    method: 'get'
  })
}

export function testConn(data) {
  return request({
    url: '/generator/datasource/testConnection',
    method: 'post',
    data
  })
}

export function add(data) {
  return request({
    url: '/generator/datasource/add',
    method: 'post',
    data
  })
}

export function update(data) {
  return request({
    url: '/generator/datasource/update',
    method: 'post',
    data
  })
}

export function deleteDatasource(id) {
  return request({
    url: '/generator/datasource/delete',
    method: 'get',
    params: {
      id
    }
  })
}
