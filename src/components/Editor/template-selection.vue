<!--<template>-->
<!--  <div>-->
<!--    <el-dialog-->
<!--      append-to-body-->
<!--      width="60%"-->
<!--      :visible.sync="dialogVisible"-->
<!--      :close-on-click-moda="false"-->
<!--      :close-on-press-escape="false"-->
<!--      title="请选择模板"-->
<!--      @close="cancel"-->
<!--    >-->
<!--      <el-row :gutter="20">-->
<!--        <el-col :span="8" v-for="(i, index) in templateList" :key="index">-->
<!--          <el-card shadow="hover" style="margin-top: 20px;">-->
<!--            <img :src="i.thumbnail" class="image">-->
<!--            <div style="padding: 14px;">-->
<!--              <span>{{ i.name }}</span>-->
<!--              <div class="bottom clearfix">-->
<!--                <el-button type="text" class="button" @click="innerStock(i.type)">选择</el-button>-->
<!--                <el-button type="text" class="button" @click="templatePreview(i)" style="margin-right: 10px;">预览</el-button>-->
<!--              </div>-->
<!--            </div>-->
<!--          </el-card>-->
<!--        </el-col>-->
<!--      </el-row>-->
<!--    </el-dialog>-->
<!--    &lt;!&ndash; 文件预览弹出层 &ndash;&gt;-->
<!--    <el-dialog-->
<!--      append-to-body-->
<!--      title=""-->
<!--      :visible.sync="isEnlargeImage"-->
<!--      top="8%"-->
<!--      width="60%"-->
<!--    >-->
<!--      <div style="text-align: center">-->
<!--        <img :src="templateObj.image" width="50%" height="50%" @click="isEnlargeImage = false"><br>-->
<!--        <img v-if="templateObj.type === 'table'" :src="templateObj.imageDescription" width="50%" height="50%"-->
<!--             @click="isEnlargeImage = false">-->
<!--        <div>{{ templateObj.name }}</div>-->
<!--      </div>-->
<!--    </el-dialog>-->
<!--  </div>-->
<!--</template>-->

<!--<script>-->
<!--export default {-->
<!--  name: "TemplateSelection",-->
<!--  props: {-->
<!--    templateDialog: {-->
<!--      type: Boolean,-->
<!--      default: false,-->
<!--    }-->
<!--  },-->
<!--  watch: {-->
<!--    templateDialog(val) {-->
<!--      this.dialogVisible = val;-->
<!--    }-->
<!--  },-->
<!--  data() {-->
<!--    return {-->
<!--      dialogVisible: false,-->
<!--      isEnlargeImage: false,-->
<!--      templateList: [-->
<!--        {-->
<!--          name: '会议纪要',-->
<!--          type: 'meeting',-->
<!--          htm: '<h1>会议纪要</h1>\n' +-->
<!--              '<h2>一、会议信息</h2>\n' +-->
<!--              '<h3>会议主题：</h3>\n' +-->
<!--              '<h3>会议时间：</h3>\n' +-->
<!--              '<h3>会议地点：</h3>\n' +-->
<!--              '<h3>参会人员：</h3>\n' +-->
<!--              '<h3>相关人员：</h3>\n' +-->
<!--              '<h2>二、会议议题</h2>\n' +-->
<!--              '<h2>三、会议结论</h2>\n' +-->
<!--              '<h2>四、后续跟进</h2>',-->
<!--          thumbnail: require("@/assets/images/editor/thumbnail/meeting.png"),-->
<!--          image: require("@/assets/images/editor/image/meeting.png"),-->
<!--        },-->
<!--        {-->
<!--          name: '工作周报',-->
<!--          type: 'weekly',-->
<!--          htm: '<h1>工作周报</h1>\n' +-->
<!--            '<p>以下是某某组周报内容（XX月XX日-XX月XX日）</p>\n' +-->
<!--            '<p></p>\n' +-->
<!--            '<h2>本周主要进展</h2>\n' +-->
<!--            '<p>1、</p>\n' +-->
<!--            '<p>2、</p>\n' +-->
<!--            '<p>3、</p>\n' +-->
<!--            '<h2>详细工作内容</h2>\n' +-->
<!--            '<h3>【模块名称】</h3>\n' +-->
<!--            '<p>1、</p>\n' +-->
<!--            '<p>2、</p>\n' +-->
<!--            '<p>3、</p>\n' +-->
<!--            '<h3>【模块名称】</h3>\n' +-->
<!--            '<p>1、</p>\n' +-->
<!--            '<p>2、</p>\n' +-->
<!--            '<p>3、</p>',-->
<!--          thumbnail: require("@/assets/images/editor/thumbnail/weekly.png"),-->
<!--          image: require("@/assets/images/editor/image/weekly.png"),-->
<!--        },-->
<!--        {-->
<!--          name: '工作总结',-->
<!--          type: 'workSummary',-->
<!--          htm: '<h1>工作总结</h1>\n' +-->
<!--            '<p>XX月XX日</p>\n' +-->
<!--            '<p></p>\n' +-->
<!--            '<h2>做得好的事项</h2>\n' +-->
<!--            '<p>1、</p>\n' +-->
<!--            '<p>2、</p>\n' +-->
<!--            '<p>3、</p>\n' +-->
<!--            '<h2>待提升的事项</h2>\n' +-->
<!--            '<p>1、</p>\n' +-->
<!--            '<p>2、</p>\n' +-->
<!--            '<p>3、</p>\n' +-->
<!--            '<h2>改进点及落地措施</h2>\n' +-->
<!--            '<p>1、</p>\n' +-->
<!--            '<p>2、</p>\n' +-->
<!--            '<p>3、</p>',-->
<!--          thumbnail: require("@/assets/images/editor/thumbnail/workSummary.png"),-->
<!--          image: require("@/assets/images/editor/image/workSummary.png"),-->
<!--        },-->
<!--        {-->
<!--          name: '活动方案',-->
<!--          type: 'activitiesProgramme',-->
<!--          htm: '<h1>活动方案</h1>\n' +-->
<!--            '<h2>活动主题</h2>\n' +-->
<!--            '<p>● XX公司庆功/团建活动</p>\n' +-->
<!--            '<h2>活动时间：</h2>\n' +-->
<!--            '<h3>● 2022年01月01日</h3>\n' +-->
<!--            '<h2>参与部门：</h2>\n' +-->
<!--            '<p>● 全体员工/XX部门员工</p>\n' +-->
<!--            '<h2>活动地址：</h2>\n' +-->
<!--            '<p>● 大会议室/XX酒店2号厅</p>\n' +-->
<!--            '<h2>活动目的：</h2>\n' +-->
<!--            '<p>● 1、</p>\n' +-->
<!--            '<p>● 2、</p>\n' +-->
<!--            '<p>● 3、</p>\n' +-->
<!--            '<h2>活动流程：</h2>\n' +-->
<!--            '<p>● 1、</p>\n' +-->
<!--            '<p>● 2、</p>\n' +-->
<!--            '<p>● 3、</p>\n' +-->
<!--            '<h2>活动所需物料：</h2>\n' +-->
<!--            '<p>● 1、</p>\n' +-->
<!--            '<p>● 2、</p>\n' +-->
<!--            '<p>● 3、</p>',-->
<!--          thumbnail: require("@/assets/images/editor/thumbnail/activitiesProgramme.png"),-->
<!--          image: require("@/assets/images/editor/image/activitiesProgramme.png"),-->
<!--        },-->
<!--        {-->
<!--          name: '工作清单',-->
<!--          type: 'workList',-->
<!--          htm: '<h1>工作清单</h1>\n' +-->
<!--            '<p>XX月XX日</p>\n' +-->
<!--            '<p></p>\n' +-->
<!--            '<h2>1.紧急且重要</h2>\n' +-->
<!--            '<p>1、</p>\n' +-->
<!--            '<p>2、</p>\n' +-->
<!--            '<p>3、</p>\n' +-->
<!--            '<h2>2.紧急但不重要</h2>\n' +-->
<!--            '<p>1、</p>\n' +-->
<!--            '<p>2、</p>\n' +-->
<!--            '<p>3、</p>\n' +-->
<!--            '<h2>3.不紧急但重要</h2>\n' +-->
<!--            '<p>1、</p>\n' +-->
<!--            '<p>2、</p>\n' +-->
<!--            '<p>3、</p>\n' +-->
<!--            '<h2>4.不紧急不重要</h2>\n' +-->
<!--            '<p>1、</p>\n' +-->
<!--            '<p>2、</p>\n' +-->
<!--            '<p>3、</p>',-->
<!--          thumbnail: require("@/assets/images/editor/thumbnail/workList.png"),-->
<!--          image: require("@/assets/images/editor/image/workList.png"),-->
<!--        },-->
<!--        {-->
<!--          name: '表格(需在编辑器区域点击鼠标光标)',-->
<!--          type: 'table',-->
<!--          htm: '<table border=“1”cells cellspacing="0"  height="300px" style="text-align: center;center">\n' +-->
<!--            '  <tr>\n' +-->
<!--            '    <td style="width:50px">表格</td>\n' +-->
<!--            '    <td style="width:80px"></td>\n' +-->
<!--            '    <td style="width:50px">表格</td>\n' +-->
<!--            '    <td style="width:80px"></td>\n' +-->
<!--            '  </tr>\n' +-->
<!--            '  <tr>\n' +-->
<!--            '    <td >表格</td>\n' +-->
<!--            '    <td ></td>\n' +-->
<!--            '    <td >表格</td>\n' +-->
<!--            '    <td ></td>\n' +-->
<!--            '  </tr>\n' +-->
<!--            '  <tr>\n' +-->
<!--            '    <td >表格</td>\n' +-->
<!--            '    <td ></td>\n' +-->
<!--            '    <td >表格</td>\n' +-->
<!--            '    <td ></td>\n' +-->
<!--            '  </tr>\n' +-->
<!--            '</table>',-->
<!--          thumbnail: require("@/assets/images/editor/thumbnail/table.png"),-->
<!--          image: require("@/assets/images/editor/image/table.png"),-->
<!--          imageDescription: require("@/assets/images/editor/image/table_operate.png"),-->
<!--        },-->
<!--      ],-->
<!--      templateObj: {},-->
<!--    };-->
<!--  },-->
<!--  methods: {-->
<!--    cancel() {-->
<!--      this.$emit("update:templateDialog", false);-->
<!--    },-->
<!--    innerStock(type) {-->
<!--      this.$emit("innerStockBlot", type, this.templateList.find(item => item.type === type).htm)-->
<!--    },-->
<!--    templatePreview(i) {-->
<!--      this.isEnlargeImage = true-->
<!--      this.templateObj = i-->
<!--    },-->
<!--  }-->
<!--}-->
<!--</script>-->

<!--<style scoped>-->
<!--  .bottom {-->
<!--    margin-top: 13px;-->
<!--    line-height: 12px;-->
<!--  }-->

<!--  .button {-->
<!--    padding: 0;-->
<!--    float: right;-->
<!--  }-->

<!--  .image {-->
<!--    width: 100%;-->
<!--    display: block;-->
<!--  }-->

<!--  .clearfix:before,-->
<!--  .clearfix:after {-->
<!--    display: table;-->
<!--    content: "";-->
<!--  }-->

<!--  .clearfix:after {-->
<!--    clear: both-->
<!--  }-->
<!--</style>-->
