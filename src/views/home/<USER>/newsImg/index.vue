<template>
  <el-card
    :class="this.bgColor === true ?'news-card':'unshow-news-card'"
    v-loading="Loading"
    shadow="never"
  >
    <div slot="header" v-if="show">
      <span class="card-title">
        <span>
          <i class="icon iconfont"></i>新闻
        </span>
      </span>
    </div>
    <div class="home-card-body">
      <div class="news-content">
        <el-carousel height="300px" indicator-position="outside" :autoplay="false">
          <el-carousel-item v-model="oneList" v-if="oneList">
            <div class="first-img">
              <div class="left-img" v-if="oneList[0]">
                <a
                  :href="
                    'https://www.163.com/dy/article/' +
                    oneList[0].docid +
                    '.html'
                  "
                  target="_blank"
                >
                  <img v-if="oneList[0]" :src="oneList[0].imgsrc" alt />
                </a>
                <div class="title">{{ oneList[0].title }}</div>
              </div>
              <div class="right-img">
                <div class="top" v-if="oneList[1]">
                  <a
                    :href="
                      'https://www.163.com/dy/article/' +
                      oneList[1].docid +
                      '.html'
                    "
                    target="_blank"
                  >
                    <img v-if="oneList[1]" :src="oneList && oneList[1].imgsrc" alt />
                  </a>
                  <div class="title">{{ oneList[1].title }}</div>
                </div>
                <div class="bottom" v-if="oneList[2]">
                  <a
                    :href="
                      'https://www.163.com/dy/article/' +
                      oneList[2].docid +
                      '.html'
                    "
                    target="_blank"
                  >
                    <img v-if="oneList[2]" :src="oneList && oneList[2].imgsrc" alt />
                  </a>
                  <div class="title">{{ oneList[2].title }}</div>
                </div>
              </div>
            </div>
          </el-carousel-item>
          <el-carousel-item v-model="twoList" v-if="twoList">
            <div class="first-img">
              <div class="left-img" v-if="twoList[0]">
                <a
                  :href="
                    'https://www.163.com/dy/article/' +
                    twoList[0].docid +
                    '.html'
                  "
                  target="_blank"
                >
                  <img :src="twoList[0].imgsrc" alt />
                </a>
                <div class="title">{{ twoList[0].title }}</div>
              </div>
              <div class="right-img">
                <div class="top" v-if="twoList[1]">
                  <a
                    :href="
                      'https://www.163.com/dy/article/' +
                      twoList[1].docid +
                      '.html'
                    "
                    target="_blank"
                  >
                    <img :src="twoList[1].imgsrc" alt />
                  </a>
                  <div class="title">{{ twoList[1].title }}</div>
                </div>
                <div class="bottom" v-if="twoList[2]">
                  <a
                    :href="
                      'https://www.163.com/dy/article/' +
                      twoList[2].docid +
                      '.html'
                    "
                    target="_blank"
                  >
                    <img :src="twoList[2].imgsrc" alt />
                  </a>
                  <div class="title">{{ twoList[2].title }}</div>
                </div>
              </div>
            </div>
          </el-carousel-item>
          <el-carousel-item v-model="threeList" v-if="threeList">
            <div class="first-img">
              <div class="left-img" v-if="threeList[0]">
                <a
                  :href="
                    'https://www.163.com/dy/article/' +
                    threeList[0].docid +
                    '.html'
                  "
                  target="_blank"
                >
                  <img :src="threeList[0].imgsrc" alt />
                </a>
                <div class="title">{{ threeList[0].title }}</div>
              </div>
              <div class="right-img">
                <div class="top" v-if="threeList[1]">
                  <a
                    :href="
                      'https://www.163.com/dy/article/' +
                      threeList[1].docid +
                      '.html'
                    "
                    target="_blank"
                  >
                    <img :src="threeList[1].imgsrc" alt />
                  </a>
                  <div class="title">{{ threeList[1].title }}</div>
                </div>
                <div class="bottom" v-if="threeList[2]">
                  <a
                    :href="
                      'https://www.163.com/dy/article/' +
                      threeList[2].docid +
                      '.html'
                    "
                    target="_blank"
                  >
                    <img :src="threeList[2].imgsrc" alt />
                  </a>
                  <div class="title">{{ threeList[2].title }}</div>
                </div>
              </div>
            </div>
          </el-carousel-item>
          <el-carousel-item v-model="fourList" v-if="fourList">
            <div class="first-img">
              <div class="left-img" v-if="fourList[0]">
                <a
                  :href="
                    'https://www.163.com/dy/article/' +
                    fourList[0].docid +
                    '.html'
                  "
                  target="_blank"
                >
                  <img :src="fourList[0].imgsrc" alt />
                </a>
                <div class="title">{{ fourList[0].title }}</div>
              </div>
              <div class="right-img">
                <div class="top" v-if="fourList[1]">
                  <a
                    :href="
                      'https://www.163.com/dy/article/' +
                      fourList[1].docid +
                      '.html'
                    "
                    target="_blank"
                  >
                    <img :src="fourList[1].imgsrc" alt />
                  </a>
                  <div class="title">{{ fourList[1].title }}</div>
                </div>
                <div class="bottom" v-if="fourList[2]">
                  <a
                    :href="
                      'https://www.163.com/dy/article/' +
                      fourList[2].docid +
                      '.html'
                    "
                    target="_blank"
                  >
                    <img :src="fourList[2].imgsrc" alt />
                  </a>
                  <div class="title">{{ fourList[2].title }}</div>
                </div>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
    </div>
  </el-card>
</template>
<script>
import { getDate } from "@/api/news/index";
export default {
  name: "newsImg",
  props: ["item"],
  data() {
    return {
      show: "",
      Loading: false,
      oneList: [],
      twoList: [],
      threeList: [],
      fourList: [],
      list: [],
      bgColor: "",
    };
  },
  created() {
    this.getList();
    this.show = this.item.showTitle;
    this.bgColor = this.item.showBgColor;
  },
  mounted() {
    this.getList();
  },
  watch: {
    "item.showTitle": function (val) {
      this.show = val;
    },
    "item.showBgColor": function (val) {
      this.bgColor = val;
    },
  },
  methods: {
    getList() {
      getDate().then((res) => {
        this.list = res.data.T1348647853363;
        var newList = {};
        this.list = this.list.reduce((item, next) => {
          newList[next.postid]
            ? ""
            : (newList[next.postid] = true && item.push(next));
          return item;
        }, []);
        this.list.forEach((item, index) => {
          if (index == 0 || index == 1 || index == 2) {
            this.oneList.push(item);
          }
          if (index == 3 || index == 4 || index == 5) {
            this.twoList.push(item);
          }
          if (index == 6 || index == 7 || index == 8) {
            this.threeList.push(item);
          }
          if (index == 9 || index == 10 || index == 11) {
            this.fourList.push(item);
          }
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.home-card-body {
  padding-top: 20px;
}
.news-card {
  height: 100%;
  background: #fff;
  .news-content {
    width: 100%;
    position: relative;
  }
  .left {
    width: 35px;
    background: black;
    position: absolute;
    opacity: 0.5;
    top: 130px;
    z-index: 99;
    left: 0;
  }
  .left:hover {
    background: red;
    opacity: 0.5;
  }
  .el-carousel__item h3 {
    color: #475669;
    font-size: 14px;
    opacity: 0.75;
    height: 100%;
    margin: 0;
  }

  .el-carousel__item:nth-child(2n) {
    background-color: white;
  }

  .el-carousel__item:nth-child(2n + 1) {
    background-color: white;
  }

  .el-carousel__indicators--outside button {
    height: 10px;
    background: gray;
    // margin-bottom: 20px;
    border-radius: 10px;
  }
  .el-carousel__indicators--outside button:hover {
    height: 10px;
    background: red;
    // margin-bottom: 20px;
  }
  .first-img {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    .left-img {
      width: 68%;
      height: 100%;
      position: relative;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        cursor: pointer;
        transition: all 0.6s;
      }
      img:hover {
        transform: scale(1.1);
      }
      .title {
        width: 90%;
        position: absolute;
        bottom: 0;
        z-index: 99;
        color: white;
        font-size: 14px;
        margin: 0px 0px 8px 15px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        cursor: pointer;
      }
    }
    .right-img {
      width: 31%;
      height: 100%;
      background: white;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      overflow: hidden;
      .top {
        width: 100%;
        height: 49%;
        background: bisque;
        position: relative;
        overflow: hidden;
        img {
          width: 100%;
          height: 100%;
          cursor: pointer;
          transition: all 0.6s;
        }
        img:hover {
          transform: scale(1.1);
        }
        .title {
          position: absolute;
          bottom: 0;
          z-index: 99;
          color: white;
          font-size: 14px;
          margin: 0px 0px 5px 10px;
          width: 90%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      .bottom {
        width: 100%;
        height: 49%;
        overflow: hidden;
        position: relative;
        img {
          width: 100%;
          height: 100%;
          cursor: pointer;
          transition: all 0.6s;
        }
        img:hover {
          transform: scale(1.1);
        }
        .title {
          position: absolute;
          bottom: 0;
          z-index: 99;
          color: white;
          font-size: 14px;
          margin: 0px 0px 5px 10px;
          width: 90%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
.unshow-news-card {
  border: 0px;
  height: 100%;
  background: #fff;
  .news-content {
    width: 100%;
    position: relative;
  }
  .left {
    width: 35px;
    background: black;
    position: absolute;
    opacity: 0.5;
    top: 130px;
    z-index: 99;
    left: 0;
  }
  .left:hover {
    background: red;
    opacity: 0.5;
  }
  .el-carousel__item h3 {
    color: #475669;
    font-size: 14px;
    opacity: 0.75;
    height: 100%;
    margin: 0;
  }

  .el-carousel__item:nth-child(2n) {
    background-color: white;
  }

  .el-carousel__item:nth-child(2n + 1) {
    background-color: white;
  }

  .el-carousel__indicators--outside button {
    height: 10px;
    background: gray;
    // margin-bottom: 20px;
    border-radius: 10px;
  }
  .el-carousel__indicators--outside button:hover {
    height: 10px;
    background: red;
    // margin-bottom: 20px;
  }
  .first-img {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    .left-img {
      width: 68%;
      height: 100%;
      position: relative;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        cursor: pointer;
        transition: all 0.6s;
      }
      img:hover {
        transform: scale(1.1);
      }
      .title {
        width: 90%;
        position: absolute;
        bottom: 0;
        z-index: 99;
        color: white;
        font-size: 14px;
        margin: 0px 0px 8px 15px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        cursor: pointer;
      }
    }
    .right-img {
      width: 31%;
      height: 100%;
      background: white;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      overflow: hidden;
      .top {
        width: 100%;
        height: 49%;
        background: bisque;
        position: relative;
        overflow: hidden;
        img {
          width: 100%;
          height: 100%;
          cursor: pointer;
          transition: all 0.6s;
        }
        img:hover {
          transform: scale(1.1);
        }
        .title {
          position: absolute;
          bottom: 0;
          z-index: 99;
          color: white;
          font-size: 14px;
          margin: 0px 0px 5px 10px;
          width: 90%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      .bottom {
        width: 100%;
        height: 49%;
        overflow: hidden;
        position: relative;
        img {
          width: 100%;
          height: 100%;
          cursor: pointer;
          transition: all 0.6s;
        }
        img:hover {
          transform: scale(1.1);
        }
        .title {
          position: absolute;
          bottom: 0;
          z-index: 99;
          color: white;
          font-size: 14px;
          margin: 0px 0px 5px 10px;
          width: 90%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
</style>
