<template>
  <div class="app-container">
    <el-empty v-if="list[0].apps.length <= 0 && !loading" :image-size="200" style="margin-top: 50px" />
    <el-card
      v-if="list[0].apps.length > 0"
      v-loading="loading"
      class="app-card"
      shadow="never"
    >
      <div slot="header">
        <span class="card-title">
          <span>小工具展示 </span>
          <span>
            <el-button class="exec" type="text" @click="refresh"
            >刷新<i class="el-icon-refresh"></i
            ></el-button>
          </span>
        </span>
      </div>
      <div class="tool" v-if="list[0].apps.length > 0">
        <div
          v-for="item in list[0].apps"
          :key="item.id"
          class="tool-icon"
          :style="`background-color: ${item.remark}20;`"
          @click="toApp(item.url)"
        >
          <svg-icon
            slot="prefix"
            :icon-class="item.logoTemp"
            class="el-input__icon"
            :style="`height: 30px;width: 30px; color: ${item.remark}`"
          />
          <span>{{ item.name }} </span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import {
  list,
  listAll,
  addCenter,
  deleteCenter,
  updCenter, selectTenantApps
} from "@/api/apps/app";
import IconSelect from "@/components/IconSelect";

export default {
  name: "apps",
  components: {IconSelect},
  data() {
    return {
      list: [
        {
          apps: []
        }
      ],
      loading: true,
      toolsMar: false,
      allloading: false,
      listAll: [
        {
          apps: []
        }
      ],
      // 以下下为应用的
      centerVisble: false,
      centerloading: false,
      centerTitle: "新增应用",
      centerForm: {
        sort: 1,
        logoTemp: ""
      },
      centerRules: {
        name: [
          {required: true, message: "请输入名称", trigger: "blur"},
          {min: 1, max: 64, message: "最大输入64个字符", trigger: "blur"}
        ],
        url: [
          {required: true, message: "请输入访问地址", trigger: "blur"},
          {min: 1, max: 255, message: "最大输入255个字符", trigger: "blur"}
        ],
        remark: [
          {min: 1, max: 255, message: "最大输入255个字符", trigger: "blur"}
        ]
      },
      imageUrl: ""
    };
  },
  created() {
    this.refresh();
  },
  methods: {
    toApp(url) {
      const usrs = url.split("?");
      console.log(`${usrs[0]}?${usrs.length > 1 ? usrs[1] + "&" : ""}`)
      window.open(
        `${usrs[0]}?${usrs.length > 1 ? usrs[1] + "&" : ""}`//token=${getToken()}
      );
    },
    refresh() {
      this.getList();
      this.all();
    },
    getList() {
      this.loading = true;
      selectTenantApps("1").then(res => {
        if (res.data.length > 0) {
          this.list = res.data;
          this.loading = false;
        } else {
          this.loading = false;
        }
      });
    },
    all() {
      this.allloading = true;
      listAll(1).then(res => {
        this.listAll = res.data;
        this.allloading = false;
      });
    },
    // 以下下为应用的
    openCenter(row) {
      this.centerVisble = true;
      this.centerloading = false;
      this.centerTitle = "新增应用";
      this.centerForm = {
        sort: 1,
        categoryId: row.id
      };
    },
    selected(name) {
      this.centerForm = {
        ...this.centerForm,
        logoTemp: name
      };
    },
    closeCenter() {
      this.centerVisble = false;
      this.centerloading = false;
      this.centerTitle = "新增应用";
      this.imageUrl = "";
      this.centerForm = {
        sort: 1
      };
    },
    saveApp() {
      this.$refs["centerForm"].validate(valid => {
        if (valid) {
          this.centerloading = true;
          const centerForm = this.centerForm;
          addCenter(this.centerForm).then(r => {
            this.centerloading = false;
            if (r.success) {
              this.$message({
                message: "添加成功！",
                type: "success"
              });
              this.closeCenter();
              this.all(centerForm.categoryId);
              this.getList(centerForm.categoryId);
            } else {
              this.$message.error("添加失败！");
            }
          });
        } else {
          return false;
        }
      });
    },
    editCenter(row) {
      this.centerForm = {
        ...row
      };
      this.imageUrl = row.logoTemp;
      this.centerVisble = true;
      this.centerloading = false;
      this.centerTitle = "修改应用";
    },
    updApp() {
      this.$refs["centerForm"].validate(valid => {
        if (valid) {
          this.centerloading = true;
          const centerForm = this.centerForm;
          updCenter(this.centerForm).then(r => {
            this.centerloading = false;
            if (r.success) {
              this.$message({
                message: "修改成功！",
                type: "success"
              });
              this.closeCenter();
              this.all(centerForm.categoryId);
              this.getList(centerForm.categoryId);
            } else {
              this.$message.error("修改失败！");
            }
          });
        } else {
          return false;
        }
      });
    },
    delCenter(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "删除",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
        beforeClose: (action, instance, done) => {
          if (action === "confirm") {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = "删除中...";
            deleteCenter(row.id).then(r => {
              instance.confirmButtonLoading = false;
              if (r.success) {
                this.$message({
                  message: "删除成功！",
                  type: "success"
                });
                this.all(row.categoryId);
                this.getList(row.categoryId);
                done();
              } else {
                this.$message.error("删除失败！");
                done();
              }
            });
          } else {
            done();
          }
        }
      }).catch(() => {
        this.$message({
          type: "info",
          message: "已取消删除"
        });
      });
    },
    handleAppStatusChange(value) {
      updCenter({
        id: value.id,
        status: value.status ? false : true
      }).then(res => {
        if (res.success) {
          this.all(value.categoryId);
          this.getList(value.categoryId);
        } else {
          this.$message.error("修改失败！");
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
.app-card {
  margin: 0px 0px 10px 0px;
  min-height: calc(100vh - 200px);
  border: 0px solid #e6ebf5;

  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > span {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .icon {
      font-size: 22px;
      color: #419eee;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }

    .exec {
      padding: 3px 0;
    }
  }

  .tool {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }

  .app-empty {
    background-color: #f5f9fa;
  }
}

.box-card {
  height: 100%;
  overflow: auto;
}

.body {
  margin: 40px;

  .foot {
    text-align: center;
    margin-top: 50px;
  }
}

.tool-icon {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 15px 15px 0 0;
  border-radius: 5px;
  font-size: 14px;
  width: 75px;
  height: 75px;
  font-weight: bold;

  .img {
    font-size: 28px;
  }

  cursor: pointer;
}
</style>

<style scoped>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
