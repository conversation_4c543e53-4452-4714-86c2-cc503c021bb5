<template>
  <div class="app-container">
    <el-card shadow="never">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item
          label="租户"
          v-if="$store.getters.customParam.userType === 'admin'"
        >
          <el-select
            v-model="queryParams.tenantId"
            placeholder="请选择租户"
            size="small"
            style="width: 200px"
            @change="tenantChange"
          >
            <el-option
              v-for="item in tenantList"
              :key="item.tenantId"
              :label="item.tenantName"
              :value="item.tenantId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="菜单名称" prop="permissionName">
          <el-input
            v-model="queryParams.menuName"
            placeholder="请输入菜单名称"
            clearable
            style="width: 200px"
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="permissionStatus">
          <el-select
            v-model="queryParams.menuStatus"
            placeholder="菜单状态"
            clearable
            style="width: 200px"
            size="small"
          >
            <el-option
              v-for="dict in permissionStatusOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="菜单归属" prop="permissionScope">
          <el-select
            v-model="queryParams.permissionScope"
            placeholder="菜单归属"
            clearable
            size="small"
            style="width: 200px"
          >
            <el-option
              v-for="dict in permissionScopeOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <el-row :gutter="5" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            >新增</el-button
          >
          <el-button
            type="danger"
            plain
            size="mini"
            icon="el-icon-s-promotion"
            @click="onPreviewEip()"
            >门户预览</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>
      <!-- 列表主体 -->
      <el-table
        v-loading="loading"
        :data="permissionList"
        row-key="menuId"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        height="500"
      >
        <el-table-column
          prop="menuName"
          label="菜单名称"
          :show-overflow-tooltip="true"
          width="200"
        >
          <template slot-scope="scope">
            <a @click="copyToClipboard(scope.row.menuName)">{{
              scope.row.menuName
            }}</a>
          </template>
        </el-table-column>

        <el-table-column prop="icon" label="图标" align="center" width="100">
          <template slot-scope="scope">
            <svg-icon :icon-class="scope.row.menuIcon" />
          </template>
        </el-table-column>
        <el-table-column
          prop="menuSort"
          label="排序"
          sortable
          sort-by="menuSort"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            <a @click="move(scope.row, 'up')"><i class="el-icon-sort-up" /></a>
            {{ scope.row.menuSort }}
            <a @click="move(scope.row, 'down')"
              ><i class="el-icon-sort-down"
            /></a>
            <!-- {{ scope.row.menuSort }} -->
          </template>
        </el-table-column>

        <el-table-column
          prop="menuDescription"
          label="描述"
          :show-overflow-tooltip="true"
          width="200"
        ></el-table-column>
        <!-- <el-table-column prop="menuContent" label="菜单内容" :show-overflow-tooltip="true" width="200"></el-table-column> -->
        <el-table-column
          prop="createDate"
          label="创建时间"
          :show-overflow-tooltip="true"
          width="200"
        ></el-table-column>
        <el-table-column
          prop="createBy"
          label="创建人"
          :show-overflow-tooltip="true"
          width="200"
        ></el-table-column>
        <el-table-column
          prop="menuShowType"
          label="菜单类型"
          :show-overflow-tooltip="true"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            <el-tag
              effect="plain"
              :type="
                selectDictRemark(permissionTypeOptions, scope.row.menuShowType)
              "
            >
              {{
                selectDictLabel(permissionTypeOptions, scope.row.menuShowType)
              }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="menuStatus"
          label="状态"
          :show-overflow-tooltip="true"
          width="120"
          align="center"
        >
          <template slot-scope="scope">
            <el-tag
              :type="
                selectDictLabel(
                  permissionStatusOptions,
                  scope.row.menuStatus
                ) === '正常'
                  ? ''
                  : 'danger'
              "
            >
              {{
                selectDictLabel(permissionStatusOptions, scope.row.menuStatus)
              }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          min-width="250"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-plus"
              @click="handleAdd(scope.row)"
              >新增</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-s-order"
              @click="designPage(scope.row)"
              >设计</el-button
            ><el-button
              size="mini"
              type="text"
              icon="el-icon-s-promotion"
              @click="onPreview(scope.row)"
              >单页预览</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加或修改菜单对话框 -->
    <el-dialog
      :title="title"
      custom-class="submitDialog"
      :visible.sync="open"
      width="700px"
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="租户">
              <el-input
                v-model="form.tenantName"
                placeholder="租户"
                maxlength="50"
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="上级菜单" prop="menuParentId">
              <treeselect
                v-model="form.menuParentId"
                :options="menuOptions"
                :normalizer="normalizer"
                :show-count="true"
                @select="changeSelect"
                placeholder="选择上级菜单"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="菜单类型" prop="menuShowType">
              <el-col :span="2">
                <el-tooltip
                  class="tooltipItem"
                  effect="dark"
                  placement="bottom-start"
                >
                  <div slot="content">
                    【目录】<span style="color: red">用于归纳菜单</span>
                    仅在此功能中展示,不作为菜单加载;<br />
                    【链接】<span style="color: red">用于常规菜单导航配置</span>
                    用于外部链接跳转;<br />
                  </div>
                  <i class="el-icon-question" />
                </el-tooltip>
              </el-col>
              <el-col :span="7">
                <el-radio-group v-model="form.menuShowType">
                  <el-radio
                    v-for="dict in permissionTypeOptions"
                    :key="dict.dictValue"
                    :label="dict.dictValue"
                    style="margin-top: 10px"
                    >{{ dict.dictLabel }}</el-radio
                  >
                  <el-radio
                    v-for="dict in mobileJumpModeOptions"
                    :key="dict.dictValue"
                    :label="dict.dictValue"
                    v-if="isMobileJump === 'Y'"
                    style="margin-top: 10px"
                    >{{ dict.dictLabel }}</el-radio
                  >
                </el-radio-group>
              </el-col>

              <el-col :span="12">
                <el-form-item
                  v-if="form.menuShowType === 'link'"
                  label="是否外链"
                >
                  <el-col :span="5">
                    <el-tooltip
                      class="tooltipItem"
                      effect="dark"
                      content="当前连接是否为外部链接（不同域名下的链接为外部链接）"
                      placement="bottom-end"
                    >
                      <i class="el-icon-question" />
                    </el-tooltip>
                  </el-col>
                  <el-col :span="19">
                    <el-radio-group v-model="form.menuFrame">
                      <el-radio label="1">是</el-radio>
                      <el-radio label="0">否</el-radio>
                    </el-radio-group>
                  </el-col>
                </el-form-item>
              </el-col>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="菜单名称" prop="menuName">
              <el-input v-model="form.menuName" placeholder="请输入菜单名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              v-if="form.permissionType !== 'operation'"
              label="菜单图标"
            >
              <el-popover
                placement="bottom-start"
                width="460"
                trigger="click"
                @show="$refs['iconSelect'].reset()"
              >
                <IconSelect ref="iconSelect" @selected="selected" />
                <el-input
                  slot="reference"
                  v-model="form.menuIcon"
                  placeholder="点击选择图标"
                  readonly
                >
                  <svg-icon
                    v-if="form.menuIcon"
                    slot="prefix"
                    :icon-class="form.menuIcon"
                    class="el-input__icon"
                    style="height: 32px; width: 16px"
                  />
                  <i
                    v-else
                    slot="prefix"
                    class="el-icon-search el-input__icon"
                  />
                </el-input>
              </el-popover>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="menuSort">
              <el-input-number
                v-model="form.menuSort"
                controls-position="right"
                de="1"
                :min="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="menuStatus">
              <el-radio-group v-model="form.menuStatus">
                <el-radio
                  v-for="item in permissionStatusOptions"
                  :key="item.dictValue"
                  :label="item.dictValue"
                >
                  {{ item.dictLabel }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20">
            <el-form-item
              v-if="form.menuFrame === '1'"
              label="跳转路径"
              prop="menuUri"
            >
              <el-col :span="22">
                <el-input v-model="form.menuUri" placeholder="跳转路径" />
              </el-col>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20">
            <el-form-item label="描述" prop="menuDescription">
              <el-col :span="22">
                <el-input v-model="form.menuDescription" placeholder="描述" />
              </el-col>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20">
            <el-form-item label="菜单内容" prop="menuContent">
              <el-col :span="22">
                <el-input
                  type="textarea"
                  :autosize="{ minRows: 8 }"
                  maxlength="9999999999999"
                  v-model="form.menuContent"
                  placeholder='仅支持json对象格式，示例：{"title": "门户首页"}'
                />
                <!-- <json-viewer :value="form.menuContent" v-model="form.menuContent" :expand-depth=5 copyable></json-viewer> -->
              </el-col>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <DesignPage
      ref="designPage"
      @handleQuery="handleQuery"
      @closeDesign="closeDesign"
      v-if="designVisible"
    />
  </div>
</template>

<script>
// import JsonViewer from 'vue-json-viewer'
// Vue.use(JsonViewer)
import { getPortalDetails } from "@/api/system/portalManagement";
import DesignPage from "./designPage.vue";
import {
  getPortalMenu,
  addMainCategory,
  addTableMenu,
  editTableMenu,
  editTableMenuUpdate,
  deleteTableMenu,
} from "@/api/system/portalManagement";
import { list as tenantList } from "@/api/system/tenant";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import IconSelect from "@/components/IconSelect";

export default {
  name: "Menu",
  components: {
    Treeselect,
    IconSelect,
    DesignPage,
    // JsonViewer
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 菜单表格树数据
      permissionList: [],
      tenantList: [],
      // 菜单树选项
      menuOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 菜单类型
      permissionTypeOptions: [],
      // 菜单归属
      permissionScopeOptions: [],
      // 是否显示
      menuVisibleOptions: [],
      // 菜单状态
      permissionStatusOptions: [],
      // 终端类型
      permissionTerminal: [],
      // 查询参数
      queryParams: {
        eipId: undefined,
        menuName: undefined,
        menuStatus: undefined,
        tenantId: this.$store.getters.customParam.tenantId,
        tenantName: this.$store.getters.customParam.tenantName,
        pageNum: 1,
        pageSize: 99999,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        menuName: [
          {
            required: true,
            message: "菜单名称不能为空",
            trigger: "blur",
          },
        ],
        menuSort: [
          {
            required: true,
            message: "菜单顺序不能为空",
            trigger: "blur",
          },
          {
            pattern: /^\d+$/,
            message: "只可以输入非负整数",
            trigger: "blur",
          },
        ],
        // menuUri: [{ required: true, message: "跳转地址不能为空", trigger: "blur" }],
        menuStatus: [
          {
            required: true,
            message: "请选择状态",
            trigger: "blur",
          },
        ],
        menuParentId: [
          {
            required: true,
            message: "请选择父级节点",
            trigger: "blur",
          },
        ],
      },
      isMobileJump: "N",
      mobileJumpModeOptions: [],
      eipId: "",
      designVisible: false,
      portalDetails: {},
    };
  },
  created() {
    const dictId = this.$route.params && this.$route.params.eipId;
    this.queryParams.eipId = dictId;
    this.eipId = dictId;
    // console.log(this.queryParams.eipId)
    this.getTenantList();
    this.getList();
    this.getPortalDetail();
    this.getDicts("menu_show_type").then((response) => {
      this.permissionTypeOptions = response.data;
    });
    this.getDicts("scope").then((response) => {
      this.permissionScopeOptions = response.data;
    });
    this.getDicts("menu_visible").then((response) => {
      this.menuVisibleOptions = response.data;
    });
    this.getDicts("menu_status").then((response) => {
      this.permissionStatusOptions = response.data;
    });
    this.getDicts("mobile_jump_mode").then((response) => {
      this.mobileJumpModeOptions = response.data;
    });
    this.getDicts("menu_terminal").then((response) => {
      this.permissionTerminal = response.data;
    });
  },
  methods: {
    getTenantList() {
      tenantList().then((response) => {
        this.tenantList = response.data;
      });
    },
    // 选择图标
    selected(icon) {
      this.form = {
        ...this.form,
        menuIcon: icon,
      };
    },
    // 菜单列表排序
    sortByKey(array, key) {
      return array.sort(function (a, b) {
        let x = a[key];
        let y = b[key];
        return x < y ? -1 : x > y ? 1 : 0; //相当于  x-y
      });
    },
    /** 查询菜单列表 */
    getList() {
      this.loading = true;
      getPortalMenu(this.queryParams).then((response) => {
        let sortTable = response.data.records;
        let sort = this.sortByKey(sortTable, "menuSort");
        this.permissionList = this.handlePortalTree(sort, "menuId");
        this.loading = false;
      });
    },

    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.menuId,
        label: node.menuName,
        children: node.children,
      };
    },
    /** 查询菜单下拉树结构 */
    getTreeselect() {
      getPortalMenu(this.queryParams).then((response) => {
        this.menuOptions = [];
        const menu = {
          menuId: 0,
          menuName: "主类目",
          children: [],
        };
        menu.children = this.handlePortalTree(response.data.records, "menuId");
        this.menuOptions.push(menu);
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        menuId: undefined,
        menuParentId: 0,
        menuName: undefined,
        icon: undefined,
        menuShowType: "catalog",
        menuSort: 1,
        menuStatus: "valid",
        menuFrame: "0",
        menuUri: undefined,
        tenantName: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      this.getTreeselect();
      if (row != null && row.menuId) {
        this.form.menuParentId = row.menuId;
        if (row.menuId !== 0) {
          this.form.permissionScope = row.permissionScope;
        }
      } else {
        this.form.menuParentId = 0;
      }
      this.form.eipId = this.eipId;
      this.form.tenantName = this.queryParams.tenantName;
      this.form.tenantId = this.queryParams.tenantId;
      this.isMobileJump = "N";
      this.open = true;
      this.title = "添加菜单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getTreeselect();
      editTableMenu(row.menuId).then((response) => {
        this.form = response.data;
        this.form.menuFrame = String(response.data.menuFrame);
        this.open = true;
        this.title = "修改菜单";
      });
    },

    // json数据格式校验
    isJSON(str) {
      if (typeof str == "string") {
        try {
          var obj = JSON.parse(str);
          if (typeof obj == "object" && obj) {
            return true;
          } else {
            // console.log(typeof obj)
            return false;
          }
        } catch (e) {
          console.log("error：" + str + "!!!" + e);
          return false;
        }
      }
      console.log("It is not a string!");
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.menuId !== undefined) {
            if (
              this.form.menuContent !== undefined &&
              this.form.menuContent !== ""
            ) {
              if (this.isJSON(this.form.menuContent)) {
                editTableMenuUpdate(this.form).then((response) => {
                  if (!response.success) {
                    this.$message.error(response.message);
                  } else {
                    this.msgSuccess("修改成功");
                    this.open = false;
                    this.getList();
                  }
                });
              } else {
                this.$message.error("菜单内容数据格式不正确，请参考示例");
              }
            } else {
              editTableMenuUpdate(this.form).then((response) => {
                if (!response.success) {
                  this.$message.error(response.message);
                } else {
                  this.msgSuccess("修改成功");
                  this.open = false;
                  this.getList();
                }
              });
            }
          } else {
            if (
              this.form.menuContent !== undefined &&
              this.form.menuContent !== ""
            ) {
              if (this.isJSON(this.form.menuContent)) {
                addTableMenu(this.form).then((response) => {
                  if (response.success) {
                    this.msgSuccess("新增成功");
                    this.open = false;
                    this.getList();
                  } else {
                    this.$message.error(response.msg);
                  }
                });
              } else {
                this.$message.error("菜单内容数据格式不正确，请参考示例");
              }
            } else {
              addTableMenu(this.form).then((response) => {
                if (response.success) {
                  this.msgSuccess("新增成功");
                  this.open = false;
                  this.getList();
                } else {
                  this.$message.error(response.msg);
                }
              });
            }
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const _this = this;
      this.$confirm(
        '是否确认删除名称为"' + row.menuName + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(function () {
        deleteTableMenu({
          menuId: row.menuId,
        }).then((res) => {
          if (res.success) {
            _this.msgSuccess("删除成功");
          } else {
            _this.msgError(res.message);
          }
          _this.getList();
        });
      });
    },
    changeSelect(e) {
      if (e.parentId !== 0) {
        this.form.permissionScope = e.permissionScope;
      }
    },
    tenantChange(tenantId) {
      if (tenantId !== "") {
        this.queryParams.tenantName = this.tenantList.find(
          (item) => item.tenantId === tenantId
        ).tenantName;
      }
      this.handleQuery();
    },
    move(row, type) {
      if (type === "up") {
        if (row.menuSort <= 0) {
          this.$message.error("序号不能小于0!");
          return;
        } else {
          row.menuSort -= 1;
        }
      } else if (type === "down") {
        row.menuSort += 1;
      }
      editTableMenuUpdate(row).then((response) => {
        if (response.success) {
          this.$message.success((type === "up" ? "上" : "下") + "移成功");
          this.getList();
        } else {
          this.$message.error(response.message);
        }
      });
    },
    designPage(row) {
      if (this.portalDetails.eipCode) {
        this.designVisible = true;
        this.$nextTick(() => {
          this.$refs.designPage.open(row, this.portalDetails);
        });
      }
    },
    getPortalDetail() {
      getPortalDetails(this.$route.params.eipId).then((res) => {
        this.portalDetails = res.data;
      });
    },
    closeDesign() {
      this.designVisible = false;
    },
    onPreview(item) {
      window.open(`/landing/${this.portalDetails.eipCode}/${item.menuId}`);
    },
    onPreviewEip(){
      window.open(`/landing/${this.portalDetails.eipCode}`);
    }
  },
};
</script>

<style scoped lang="scss">
.tooltipItem {
  line-height: 40px;
  margin-right: 0px;
}

::v-deep .el-table__row:not([class*="el-table__row--level-"]) {
  td:first-child {
    padding-left: 24px !important;
  }
}

::v-deep .el-table__row:not([class*="el-table__row--level-"]) {
  background-color: #ffffff;
}

::v-deep .el-table__row--level-0 {
  background-color: #ffffff;
}

::v-deep .el-table__row--level-1 {
  background-color: #f7f7f7;
}

::v-deep .el-table__row--level-2 {
  background-color: #efefef;
}

::v-deep .el-table__row--level-3 {
  background-color: #e7e7e7;
}

::v-deep .el-table__row--level-4 {
  background-color: #dfdfdf;
}
</style>
<style lang="scss">
.submitDialog .el-dialog__body {
  height: 600px;
  overflow: auto;
}
</style>
