// import { indexData } from "@/api/poster/index.js";

const state = {
  sidebar: {
    opened: localStorage.getItem('sidebarStatus') ? !!+localStorage.getItem('sidebarStatus') : true,
    withoutAnimation: false
  },
  device: 'desktop',
  size: localStorage.getItem('size') || 'medium',
  hidennSidebar: false,
  pageSetting: false,
  sceenRate:0

}

const mutations = {
  TOGGLE_SIDEBAR: state => {
    state.sidebar.opened = !state.sidebar.opened
    state.sidebar.withoutAnimation = false
    if (state.sidebar.opened) {
      localStorage.setItem('sidebarStatus',1)
    } else {
      localStorage.setItem('sidebarStatus',0)
    }
  },
  CLOSE_SIDEBAR: (state, withoutAnimation) => {
    localStorage.setItem('sidebarStatus',0)
    state.sidebar.opened = false
    state.sidebar.withoutAnimation = withoutAnimation
  },
  TOGGLE_DEVICE: (state, device) => {
    state.device = device
  },
  SET_SIZE: (state, size) => {
    state.size = size
    localStorage.setItem('size',size)
  },
  HIDENN_SIDEBAR: (state, hidennSidebar) => {
    state.hidennSidebar = hidennSidebar
  },
  pageSetting: (state, pageSetting) => {
    state.pageSetting = pageSetting
  },
}

const actions = {
  toggleSideBar({
    commit
  }) {
    commit('TOGGLE_SIDEBAR')
  },
  closeSideBar({
    commit
  }, {
    withoutAnimation
  }) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  },
  toggleDevice({
    commit
  }, device) {
    commit('TOGGLE_DEVICE', device)
  },
  setSize({
    commit
  }, size) {
    commit('SET_SIZE', size)
  },
  setHidennSidebar({
    commit
  }, hidennSidebar) {
    commit('HIDENN_SIDEBAR', hidennSidebar)
  },
  changePageSetting({
    commit, state
  }) {
    commit('pageSetting', !state.pageSetting)
  },
}
export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
