<template>
  <el-card
    :class="
      this.bgColor === true
        ? 'notice-buisness-card'
        : 'unshow-notice-buisness-card'
    "
    v-loading="noticeLoading"
    shadow="never"
  >
    <div slot="header">
      <span class="card-title">
        <el-menu
          :default-active="activeIndex"
          class="el-menu-demo"
          mode="horizontal"
          @select="handleSelect"
          style="width: 100%"
          active-text-color="#3399FF"
          text-color="#111"
        >
          <div class="title-font" v-if="show">
            <span>
              <i class="icon iconfont icon-yewudongtai"></i>重点指标
            </span>
          </div>
          <el-menu-item index="air" style="margin-left: 20px">
            大气环境质量</el-menu-item
          >
          <el-menu-item index="water">水环境质量</el-menu-item>
          <el-menu-item index="environment">生态环境质量</el-menu-item>
        </el-menu>
      </span>
    </div>
    <div class="notice-buisness-body-air" v-show="air">
      <div class="body-left">
        <div class="body-left-top">
          <span>全年考核</span>
          <span style="font-size: 16px">(2020)</span>
        </div>
        <div class="time">(更新于2021-06-25 16时)</div>
        <!-- 国控 -->
        <div class="chart">
          <div class="chart-top">
            <span>已完成目标</span>
            <span> 累计优良 396天</span>
          </div>
          <div class="box"></div>
          <div class="chart-bottom">
            <span>考核剩余天数：189</span>
            <span> 目标 396天</span>
          </div>
        </div>
        <div class="city">
          <div style="width: 100%">嘉祥县</div>
          <div style="width: 100%">
            <span>昨天嘉祥县空气质量排名</span>
            <span style="color: red">162</span>
            <span>名</span>
          </div>
        </div>
        <div class="city">
          <div style="width: 100%">微山县</div>
          <div style="width: 100%">
            <span>昨天微山县空气质量排名</span>
            <span style="color: red">152</span>
            <span>名</span>
          </div>
        </div>
        <div class="city">
          <div style="width: 100%">汶上县</div>
          <div style="width: 100%">
            <span>昨天汶上县空气质量排名</span>
            <span style="color: red">110</span>
            <span>名</span>
          </div>
        </div>
      </div>
      <div class="body-right">
        <el-row :gutter="20">
          <el-col :span="12" :offset="6" class="echarts-item" >
            <div id="pieChartData" style="height:100%;width:800px"></div>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="notice-buisness-body-water" v-show="water">
      <div id="main"></div>
    </div>
    <div class="notice-buisness-body-environment" v-show="environment">
      <div id="environment-main"></div>
    </div>
  </el-card>
</template>

<script>
import { selectTop5Page } from "@/api/system/notice";
export default {
  name: "chart",
  props: ["item"],
  data() {
    return {
      bgColor: "",
      show: "",
      notice: [],
      noticeLoading: false,
      activeIndex: "air",
      air: true,
      water: false,
      environment: false,
    };
  },
  created() {
    this.getNotice(this.activeIndex);
    this.show = this.item.showTitle;
    this.bgColor = this.item.showBgColor;
  },
  mounted() {
    this.drawLine();
    this.environmentLine();
    this.pieChart();
  },
  watch: {
    "item.showTitle": function (val) {
      this.show = val;
    },
    "item.showBgColor": function (val) {
      this.bgColor = val;
    },
  },
  methods: {
    //生态环境质量柱状图
    environmentLine() {
      var app = {};
      let myChart = this.$echarts.init(
        document.getElementById("environment-main")
      );
      var option;

      const posList = [
        "left",
        "right",
        "top",
        "bottom",
        "inside",
        "insideTop",
        "insideLeft",
        "insideRight",
        "insideBottom",
        "insideTopLeft",
        "insideTopRight",
        "insideBottomLeft",
        "insideBottomRight",
      ];
      app.configParameters = {
        rotate: {
          min: -90,
          max: 90,
        },
        align: {
          options: {
            left: "left",
            center: "center",
            right: "right",
          },
        },
        verticalAlign: {
          options: {
            top: "top",
            middle: "middle",
            bottom: "bottom",
          },
        },
        position: {
          options: posList.reduce(function (map, pos) {
            map[pos] = pos;
            return map;
          }, {}),
        },
        distance: {
          min: 0,
          max: 100,
        },
      };
      app.config = {
        rotate: 90,
        align: "left",
        verticalAlign: "middle",
        position: "insideBottom",
        distance: 15,
        onChange: function () {
          const labelOption = {
            rotate: app.config.rotate,
            align: app.config.align,
            verticalAlign: app.config.verticalAlign,
            position: app.config.position,
            distance: app.config.distance,
          };
          myChart.setOption({
            series: [
              {
                label: labelOption,
              },
              {
                label: labelOption,
              },
              {
                label: labelOption,
              },
              {
                label: labelOption,
              },
            ],
          });
        },
      };
      const labelOption = {
        show: true,
        position: app.config.position,
        distance: app.config.distance,
        align: app.config.align,
        verticalAlign: app.config.verticalAlign,
        rotate: app.config.rotate,
        formatter: "{c}  {name|{a}}",
        fontSize: 16,
        rich: {
          name: {},
        },
      };
      option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          data: ["森林覆盖率", "水源丰度", "城市绿化率", "生物丰度"],
        },
        toolbox: {
          show: true,
          orient: "vertical",
          left: "right",
          top: "center",
          feature: {
            mark: { show: true },
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ["line", "bar", "stack"] },
            restore: { show: true },
            saveAsImage: { show: true },
          },
        },
        xAxis: [
          {
            type: "category",
            axisTick: { show: false },
            data: ["2012", "2013", "2014", "2015", "2016"],
          },
        ],
        yAxis: [
          {
            type: "value",
          },
        ],
        series: [
          {
            name: "森林覆盖率",
            type: "bar",
            barGap: 0,
            label: labelOption,
            emphasis: {
              focus: "series",
            },
            data: [100, 99.5, 99.8, 99, 100],
          },
          {
            name: "水源丰度",
            type: "bar",
            label: labelOption,
            emphasis: {
              focus: "series",
            },
            data: [100, 99, 100, 98, 99],
          },
          {
            name: "城市绿化率",
            type: "bar",
            label: labelOption,
            emphasis: {
              focus: "series",
            },
            data: [110, 120.5, 107, 110.6, 130],
          },
          {
            name: "生物丰度",
            type: "bar",
            label: labelOption,
            emphasis: {
              focus: "series",
            },
            data: [99.8, 100.2, 101, 106.8, 116.94],
          },
        ],
      };
      myChart.resize({
        width: 800,
        height: 350,
      });
      option && myChart.setOption(option);
    },
    //折线图
    drawLine() {
      let myChart = this.$echarts.init(document.getElementById("main"));
      myChart.setOption({
        title: {
          text: "",
        },
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: ["高锰酸盐", "氨氮", "总磷", "硫化物"],
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
        },
        yAxis: {
          type: "value",
          name: "mg/L",
          data: [0, 1, 2, 3, 4],
        },
        series: [
          {
            name: "高锰酸盐",
            type: "line",
            smooth: true,
            data: [0.52, 0.81, 1.05, 1.14, 0.52, 0.335, 0.127],
          },
          {
            name: "氨氮",
            type: "line",
            smooth: true,
            data: [0.785, 0.128, 0.473, 1.045, 0.747, 1.04, 1.21],
          },
          {
            name: "总磷",
            type: "line",
            smooth: true,
            data: [0.775, 0.138, 0.063, 0.885, 0.317, 0.727, 0.471],
          },
          {
            name: "硫化物",
            type: "line",
            smooth: true,
            data: [0.185, 0.321, 0.723, 0.645, 0.347, 0.454, 0.142],
          },
        ],
      });
      myChart.resize({
        width: 800,
        height: 350,
      });
      window.onresize = function () {
        myChart.resize();
      };
    },
    // 饼状图
    pieChart() {
      let myChart = this.$echarts.init(document.getElementById("pieChartData"));
      myChart.setOption({
        tooltip: {
          trigger: "item",
        },
        legend: {
          orient: "vertical",
          left: "left",
        },
        series: [
          {
            name: 'Access From',
            type: "pie",
            radius: "50%",
            data: [
              { value: 84, name: "重度污染" },
              { value: 18, name: "严重污染" },
              { value: 69, name: "中度污染" },
              { value: 31.7, name: "轻度污染" },
              { value: 422, name: "良" },
              { value: 90, name: "优" },
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      });
      myChart.resize({
        width: 500,
        height: 400,
      });
    },
    moreExt() {
      window.open(`/portal/home/<USER>/${this.activeIndex}`);
    },
    getNotice(activeIndex) {
      this.noticeLoading = true;
      selectTop5Page({
        programaType: activeIndex,
      }).then((res) => {
        this.notice = res.data.records;
        this.noticeLoading = false;
      });
    },
    handleSelect(e) {
      if (e == "air") {
        this.air = true;
        this.water = false;
        this.environment = false;
        this.pieChart();
      } else if (e == "water") {
        this.water = true;
        this.air = false;
        this.environment = false;
        this.drawLine();
      } else if (e == "environment") {
        this.environment = true;
        this.air = false;
        this.water = false;
        this.environmentLine();
      }
      this.getNotice(e);
    },
  },
};
</script>

<style lang="scss" scoped>
.notice-buisness-card {
  background: #fff;
  height: 100%;
  ::v-deep .el-card__header {
    padding: 0px;
    border-bottom: 0px solid #ebeef5;
  }
  ::v-deep .el-menu--horizontal > .el-menu-item {
    height: 58px;
    margin: 0px 10px 0px 10px;
  }
  ::v-deep .el-card__body {
    padding: 10px 20px 20px 20px;
  }
  .card-title {
    display: flex;
    .el-menu {
      display: flex;
      justify-content: flex-end;
    }
    .el-menu-demo {
      display: flex;
      justify-content: flex-end;
      margin-right: 20px;
    }
    .title-font {
      z-index: 99;
      position: absolute;
      top: 20px;
      left: 20px;
      font-size: 14px;
      .icon-yewudongtai {
        color: #419eee;
      }
    }
    .icon {
      font-size: 18px;
      margin-right: 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 20px;
      border-bottom: 1px solid #ebeef5;
      border-radius: 0px;
    }
  }
  .notice-buisness-body-air {
    width: 100%;
    display: flex;
    justify-content: space-between;
    height: 100%;
    .body-left {
      width: 40%;
      // border-right: 1px solid rgb(179, 167, 167);
      .body-left-top {
        padding-left: 20px;
        display: flex;
        height: 58px;
        box-sizing: content-box;
        overflow: hidden;
        line-height: 58px;
        font-size: 20px;
        .botton {
          width: 40px;
          height: 28px;
          margin: 15px 0px 0px 20px;
          padding: 0;
        }
        // .buttom {
        //   width: 30px;
        //   height: 28px;
        //   background: #fff;
        //   border: 1px solid rgb(163, 163, 224);
        //   margin: 15px 0px 0px 15px;
        //   color: #111;
        // }
      }
      .time {
        width: 90%;
        text-align: right;
        overflow: hidden;
      }
      .chart {
        margin-top: 15px;
        width: 90%;
        height: 80px;
        overflow: hidden;
        .chart-top {
          width: 100%;
        }
        .box {
          width: 100%;
          height: 10px;
          background: linear-gradient(to right, #7f06a8, #5c565e);
          margin: 5px 0px 5px 0px;
          overflow: hidden;
        }
      }
      .city {
        width: 95%;
        height: 50px;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        border: 1px solid rgb(113, 113, 145);
        margin-top: 5px;
        p {
          margin: 0px;
        }
      }
    }
    .body-right {
      width: 60%;
    }
    .notice-buisness {
      width: 100%;
      display: flex;
      justify-content: space-between;
      flex-direction: row;
      font-size: 14px;
      padding: 4px 0;
      .notice-title {
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        white-space: nowrap;
        flex-basis: 70%;
      }
      .notice-time {
        flex-basis: 20%;
        text-align: right;
      }
    }
  }
}
.unshow-notice-buisness-card {
  background: #f5f9fa;
  height: 100%;
  border: 0px;
  ::v-deep .el-card__header {
    padding: 0px;
    border-bottom: 0px solid #ebeef5;
  }
  ::v-deep .el-menu--horizontal > .el-menu-item {
    height: 58px;
    margin: 0px 10px 0px 10px;
  }
  ::v-deep .el-card__body {
    padding: 10px 20px 20px 20px;
  }
  .card-title {
    display: flex;
    .el-menu {
      display: flex;
      justify-content: flex-end;
    }
    .el-menu-demo {
      display: flex;
      justify-content: flex-end;
      margin-right: 20px;
      background: #f5f9fa;
    }
    .title-font {
      z-index: 99;
      position: absolute;
      top: 20px;
      left: 20px;
      font-size: 14px;
      .icon-yewudongtai {
        color: #419eee;
      }
    }
    .icon {
      font-size: 18px;
      margin-right: 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 20px;
      border-bottom: 1px solid #ebeef5;
      border-radius: 0px;
    }
  }
  .notice-buisness-body-air {
    width: 100%;
    display: flex;
    justify-content: space-between;
    height: 100%;
    .body-left {
      width: 40%;
      // border-right: 1px solid rgb(179, 167, 167);
      .body-left-top {
        padding-left: 20px;
        display: flex;
        height: 58px;
        box-sizing: content-box;
        overflow: hidden;
        line-height: 58px;
        font-size: 20px;
        .botton {
          width: 40px;
          height: 28px;
          margin: 15px 0px 0px 20px;
          padding: 0;
        }
        // .buttom {
        //   width: 30px;
        //   height: 28px;
        //   background: #fff;
        //   border: 1px solid rgb(163, 163, 224);
        //   margin: 15px 0px 0px 15px;
        //   color: #111;
        // }
      }
      .time {
        width: 90%;
        text-align: right;
        overflow: hidden;
      }
      .chart {
        margin-top: 15px;
        width: 90%;
        height: 80px;
        overflow: hidden;
        .chart-top {
          width: 100%;
        }
        .box {
          width: 100%;
          height: 10px;
          background: linear-gradient(to right, #7f06a8, #5c565e);
          margin: 5px 0px 5px 0px;
          overflow: hidden;
        }
      }
      .city {
        width: 95%;
        height: 50px;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        border: 1px solid rgb(113, 113, 145);
        margin-top: 5px;
        p {
          margin: 0px;
        }
      }
    }
    .body-right {
      width: 60%;
    }
    .notice-buisness {
      width: 100%;
      display: flex;
      justify-content: space-between;
      flex-direction: row;
      font-size: 14px;
      padding: 4px 0;
      .notice-title {
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        white-space: nowrap;
        flex-basis: 70%;
      }
      .notice-time {
        flex-basis: 20%;
        text-align: right;
      }
    }
  }
}
.notice-buisness-body-water {
  width: 100%;
  #main {
    height: 300px;
  }
}
.notice-buisness-body-environment {
  width: 100%;
  height: 100vh;
}
</style>
