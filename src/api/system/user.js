import request from "@/utils/request";

// 查询用户列表
export function listUser(params) {
  return request({
    url: "/user/staffOrgs",
    method: "get",
    params: params
  });
}

// 查询用户详细
export function getUser(staffId) {
  return request({
    url: "/user/staffs/getUser/"+staffId,
    method: "get"
  });
}

// 新增用户
export function addPortaluser(data) {
  return request({
    url: "/user/staffs",
    method: "post",
    data: data
  });
}

export function addUserForGroup(data) {
  return request({
    url: "/user/staffOrgs",
    method: "post",
    data: data
  });
}

export function delUserForGroup(data) {
  return request({
    url: "/user/staffOrgs/delete",
    method: "post",
    data: data
  });
}

export function addStaffOrgAndRole(data) {
  return request({
    url: "/user/staffOrgs/addStaffOrgAndRole",
    method: "post",
    data: data
  });
}

// 修改用户
export function updateUser(data) {
  data.sex="male"
  return request({
    url: "/user/staffs/edit",
    method: "post",
    data: data
  });
}

// 删除用户
export function delUser(data) {
  return request({
    url: "/user/staffs/delete",
    method: "post",
    data: data
  });
}

// 查询用户所拥有的角色
export function findUserRoles(staffOrgId) {
  return request({
    url: "/user/staffRole/staffOrgs/staffRoles/" + staffOrgId,
    method: "get"
  });
}

// 用户密码重置
export function resetUserPwd(staffId, newPassWord) {
  const data = {
    staffId: staffId,
    newPassWord: newPassWord
  };
  return request({
    url: "/user/staffs/reset",
    method: "post",
    data: data
  });
}

// 用户状态修改
export function changeUserStatus(userId, status) {
  const data = {
    userId,
    status
  };
  return request({
    url: "/system/user/changeStatus",
    method: "put",
    data: data
  });
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: "/system/user/profile",
    method: "get"
  });
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: "/user/staffs/edit",
    method: "post",
    data: data
  });
}

// 用户密码重置
export function updateUserPwd(data) {
  return request({
    url: "/user/staffs/change",
    method: "post",
    data: data
  });
}


// 用户头像上传
export function uploadAvatar(data) {
  return request({
    url: "/system/user/profile/avatar",
    method: "post",
    data: data
  });
}

// 下载用户导入模板
export function importTemplate() {
  return request({
    url: "/system/user/importTemplate",
    method: "get"
  });
}

export function addUserRoles(staffOrgId, roleId) {
  return request({
    url: "/user/staffRole/staffOrgs/" + staffOrgId + "/" + roleId,
    method: "post"
  });
}
export function delUserRoleInfo(staffOrgId, roleId) {
  return request({
    url: "/user/staffRole/delStaffRole/" + staffOrgId + "/" + roleId,
    method: "post"
  });
}


export function getInfo(params) {
  return request({
    url: "/auth/oauth/check_token",
    method: "post",
    params
  });
}
export function enableOrDisablePortaluser(data) {
  return request({
    url: "/user/staffs/edit",
    method: "post",
    data: data
  });
}

// 获取短信验证码
export function getVerificationCode(data) {
  return request({
    url: "/user/staffs/getVerificationCode",
    method: "post",
    data,
    headers: {
      isToken: false
    }
  });
}

// 修改密码
export function forgottenPassword(data) {
  return request({
    url: "/user/staffs/forgottenPassword",
    method: "post",
    data
  });
}

