<!--<template>-->
<!--  <div class="ckeditor-box" :style="{'width':width,'height':height}">-->
<!--    <div ref="editorDom" :id="editorId" v-html="compValue" :style="{'width':width,'height':height}"></div>-->
<!--  </div>-->
<!--</template>-->

<!--<script>-->
<!--import ClassicEditor from 'vue-ckeditor5-auto/build/ckeditor'-->
<!--import axios from 'axios'-->
<!--import {getAuthorKey } from '@/utils/auth'-->
<!--export default {-->
<!--  name: 'VueEditor',-->
<!--  props: {-->
<!--    width: {-->
<!--      type: String, default: 'calc(100% - 20px)'-->
<!--    },-->
<!--    height: {-->
<!--      type: String, default: 'calc(100% - 20px)'-->
<!--    },-->
<!--    value: {-->
<!--      type: String, default: ''-->
<!--    },-->
<!--    headerToken: {-->
<!--      type: String, default: ''-->
<!--    },-->
<!--    fileAction: {-->
<!--      type: String, default: `${process.env.VUE_APP_BASE_API}/cms/v1/files/single?appCode=portal-editor`-->
<!--    },-->
<!--    fileName: {-->
<!--      type: String, default: 'file'-->
<!--    },-->
<!--    toolBar: {-->
<!--      type: Array, default: function() {-->
<!--        return []-->
<!--      }-->
<!--    }-->
<!--  },-->
<!--  data() {-->
<!--    return {-->
<!--      compValue: '',-->
<!--      editorConfig: {-->
<!--        fontSize: {-->
<!--          options: [12, 14, 16, 18, 20, 22, 24]-->
<!--        },-->
<!--        fontFamily: {-->
<!--          options: [-->
<!--            // '微软雅黑',-->
<!--            // '等线',-->
<!--            '宋体',-->
<!--            '仿宋',-->
<!--            '黑体',-->
<!--            '楷体'-->
<!--            // '华文彩云'-->
<!--          ]-->
<!--        }-->
<!--      },-->
<!--      editorComp: null,-->
<!--      editorId:'eid_'-->
<!--    }-->
<!--  },-->
<!--  created() {-->
<!--    this.editorId += parseInt(Math.random()*10000)-->
<!--  },-->
<!--  mounted() {-->
<!--    this.compValue = this.value-->
<!--    this.initEditor(null)-->
<!--  },-->
<!--  computed: {-->
<!--    valueChange() {-->
<!--      return this.height-->
<!--    },-->
<!--    valueContext() {-->
<!--      return this.value-->
<!--    }-->
<!--  },-->
<!--  watch: {-->
<!--    valueContext(newValue) {-->
<!--      if(!(this.compValue===newValue)){-->
<!--        this.compValue=newValue;-->
<!--        this.editorComp.setData(newValue)-->
<!--      }-->
<!--    }-->
<!--  },-->
<!--  methods: {-->
<!--    initEditor(newValue) {-->
<!--     this.compValue=this.value-->
<!--      let _this = this-->
<!--      if (this.toolBar.length > 0) {-->
<!--        this.editorConfig.toolbar.items = this.toolBar-->
<!--      }-->

<!--      ClassicEditor.create(document.getElementById(this.editorId), this.editorConfig)-->
<!--        .then((editor) => {-->
<!--          _this.editorComp = editor-->
<!--          _this.editorComp.plugins.get('FileRepository').createUploadAdapter = (loader) => {-->
<!--            return new UpdateFile(loader, _this.fileAction, _this.headerToken, _this.fileName)-->
<!--          }-->
<!--          _this.editorComp.model.document.on('change:data', function() {-->
<!--            _this.compValue= editor.getData()-->
<!--            _this.$emit('input', _this.compValue)-->
<!--          })-->
<!--        })-->
<!--        .catch((error) => {-->
<!--          console.error(error)-->
<!--        })-->
<!--    }-->
<!--  }-->
<!--}-->

<!--class UpdateFile {-->
<!--  loader = null-->
<!--  actionUri = null-->
<!--  headerToken = null-->
<!--  fileName = null-->

<!--  constructor(loader, updloadAction, headerToken, fileName) {-->
<!--    // Save Loader instance to update upload progress.-->
<!--    this.loader = loader-->
<!--    this.actionUri = updloadAction-->
<!--    this.headerToken = headerToken-->
<!--    this.fileName = fileName-->
<!--  }-->

<!--  async upload() {-->
<!--    const data = new FormData()-->
<!--    console.log(this.fileName, await this.loader.file)-->
<!--    data.append(this.fileName, await this.loader.file)-->
<!--    return new Promise((resolve, reject) => {-->
<!--      axios({-->
<!--        url: this.actionUri,-->
<!--        method: 'post',-->
<!--        data,-->
<!--        headers: {-->
<!--          [getAuthorKey()]: 'Bearer ' + this.headerToken  // 此处为你定义的token 值(Bearer token 接口认证方式)-->
<!--        },-->
<!--        withCredentials: true-->
<!--      }).then(res => {-->
<!--        console.log(res)-->
<!--        let resData = res.data.data-->
<!--        resData.default = resData.url-->
<!--        resolve(resData)-->
<!--      }).catch(error => {-->
<!--        reject(error)-->
<!--      })-->
<!--    })-->
<!--  }-->
<!--}-->
<!--</script>-->

<!--<style lang="scss" scoped>-->
<!--.ckeditor-box {-->
<!--  padding: 5px;-->
<!--  min-height: 550px;-->

<!--  ::v-deep .ck.ck-editor {-->
<!--    position: relative;-->
<!--    height: 100%;-->
<!--  }-->

<!--  ::v-deep .ck-editor {-->
<!--    width: 100%;-->
<!--    height: 100%;-->
<!--    min-height: 550px;-->

<!--    .ck-editor__main {-->
<!--      width: 100%;-->
<!--      height: calc(100% - 40px);-->
<!--      min-height: 510px;-->

<!--      .ck-content {-->
<!--        height: 100%;-->
<!--        min-height: 510px;-->
<!--      }-->
<!--    }-->
<!--  }-->
<!--}-->
<!--</style>-->
