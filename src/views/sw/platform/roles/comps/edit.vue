<template>
  <div>
    <!-- 添加或修改标签弹框 -->
    <el-dialog
      :title="title"
      :visible.sync="openTabDialog"
      @open="onOpen"
      @close="onCancel"
      width="1000px"
      height="600px"
    >
      <template slot="title">
        <span class="dialog_title">{{ title }}</span>
      </template>
      <el-row style="height:500px;overflow: hidden">
        <el-col :span="16">
          <el-form ref="myForm" :model="formData" :rules="rules" label-width="100px">
            <el-form-item label="角色名称" prop="roleName">
              <el-input v-model="formData.roleName" placeholder="请输入角色名称"/>
            </el-form-item>
            <el-form-item label="角色编码" prop="roleCode">
              <el-input v-model="formData.roleCode" placeholder="请输入角色编码"/>
            </el-form-item>
            <el-form-item label="数据范围" prop="dataScope">
              <el-select v-model="formData['dataScope']"
                         style="width: 100%"
                         placeholder="请选择"
              >
                <el-option label="全部范围" value="all"></el-option>
                <el-option label="本地市" value="own_city"></el-option>
                <el-option label="本区县" value="own_county"></el-option>
                <el-option label="本行业" value="own_industry"></el-option>
                <el-option label="本营服" value="own_grid"></el-option>
<!--                <el-option label="本线索" value="own_xiansuo"></el-option>-->
<!--                <el-option label="本标讯" value="own_biaoxun"></el-option>-->
                <el-option label="细分行业" value="own_detail_industry"></el-option>
                <el-option label="本产品" value="own_detail_product"></el-option>
                <el-option label="本人创建" value="own"></el-option>
                <!--                <el-option label="负责产品" value="own_product"></el-option>-->
                <el-option label="自定义地市" value="custom_city"></el-option>
                <el-option label="自定义区县" value="custom_county"></el-option>
                <el-option label="自定义行业" value="custom_industry"></el-option>
                <el-option label="自定义产品" value="custom_product"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="地市" prop="cityArr" v-if="formData['dataScope']==='custom_city'">
              <el-select
                v-model="formData.cityArr"
                filterable
                placeholder="请选择"
                :multiple="true"
                style="width: 100%"
              >
                <el-option
                  v-for="item in cityList"
                  :key="item"
                  :label="item.replace('市','')"
                  :value="item.replace('市','')"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="区县" prop="city" v-if="formData['dataScope']==='custom_county'">
              <el-select
                v-model="formData['tempCity']"
                filterable
                placeholder="请选择"
                style="width: calc(50% - 6px);"
                @change="getDistrictsAndCounties"
              >
                <el-option
                  v-for="item in cityList"
                  :key="item"
                  :label="item.replace('市','')"
                  :value="item.replace('市','')"
                >
                </el-option>
              </el-select>
              <el-select
                v-model="formData['countyArr']"
                filterable
                placeholder="请选择"
                style="width: calc(50% - 6px);margin-left:10px;"
                clearable
                multiple
                collapse-tags
              >
                <el-option
                  v-for="item in countyList"
                  :key="item"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="产品" prop="city" v-if="formData['dataScope']==='custom_product'">
              <el-select
                v-model="formData.products"
                placeholder="请选择"
                :multiple="true"
                style="width: 100%"
                clearable
                multiple
                collapse-tags
              >
                <el-option
                  v-for="item in productList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="行业" prop="city" v-if="formData['dataScope']==='custom_industry'">
              <el-select
                v-model="formData.industries"
                placeholder="请选择"
                :multiple="true"
                style="width: 100%"
                clearable
                multiple
                collapse-tags
              >
                <el-option
                  v-for="item in industryList"
                  :key="item.label"
                  :label="item.label"
                  :value="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="8">
          <div style="overflow: scroll;height: 480px">
            <div class="flex-between">
              <div class="table-title" style="padding-left:8px">权限列表</div>
              <div class="btn">

              </div>
            </div>

            <el-tree
              :data="treeData"
              show-checkbox
              ref="menu"
              :props="{label:'title',children:'children'}"
              label="title"
              @check="hanleCheck"
              :check-strictly="true"
              node-key="id"
            >
            </el-tree>
          </div>

        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button @click="onCancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { findAllProduct, findMenuById, findRole, menuTrees, saveRole } from '@/api/platform/role'
import { findCityGroup, findDistrictGroup } from '@/api/sw/customer/customerManager'
import { findIndustry } from '@/api/sw/product/product'

export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    reloadTable: {
      type: Object, default: function() {
      }
    }
  },
  data() {
    return {
      openTabDialog: false,
      open: false,

      // 表格loading
      tableLoading: false,
      titles: '',
      // 列表数据
      infoData: {},
      // 表格数据
      tableData: [],
      // 表单校验
      rules: {
        roleCode: [
          { required: true, message: '请输入角色编码', trigger: 'blur' }
        ],
        roleName: [
          { required: true, message: '请输入角色名称', trigger: 'blur' }
        ]
      },
      title: '',
      //表单参数
      formData: {
        type: Object,
        default: {}
      },
      treeData: [],
      cityList: [],
      singCityVal: '',
      countyList: [],
      productList: [],
      industryList: []
    }
  },
  // 方法集合
  methods: {
    initOpen(title, formData) {
      this.openTabDialog = true
      this.title = title
      menuTrees({}).then((response) => {
        this.treeData = response.data
      })
      setTimeout(() => {
        if (this.$refs['myForm']) {
          this.$refs['myForm'].resetFields()
        }
        this.$refs.menu.setCheckedKeys(['0'], false)

        this.formData = formData
        if (formData.id) {
          findRole({ id: formData.id }).then((result) => {
            console.log(result, 'findOne的result')
            this.formData = result['data']
            this.formData['products'] = this.formData['products'] ? this.formData['products'].split(',') : []
            this.formData['industries'] = this.formData['industries'] ? this.formData['industries'].split(',') : []
            this.formData['cityArr'] = this.formData['cityArr'] ? this.formData['cityArr'].split(',') : []
            this.formData['countyArr'] = this.formData['countyArr'] ? this.formData['countyArr'].split(',') : []
          })
          findMenuById({ roleId: formData.id }).then((result) => {
            let menusIds = result['data'].map(item => {
              return item['id']
            })
            this.$refs.menu.setCheckedKeys(menusIds, false)
          })
        }

        findCityGroup().then((res) => {
          this.cityList = res.data
        })
        findIndustry().then((response) => {
          if (response['code'] === '1') {
            this.industryList = response.data
          }
        })
        findAllProduct({}).then(res => {
          this.productList = res.data
        })
      }, 100)

    },
    // 获取区县
    getDistrictsAndCounties(val) {
      findDistrictGroup({
        city: this.formData['tempCity'] + '市'
      }).then((response) => {
        this.countyList = response.data
      })
    },
    // 打开窗口
    onOpen() {
      // this.form = { ...this.formData }
      // this.tableLoading = true
    },
    /** 提交按钮 */
    submitForm() {
      console.log('选中的菜单', menus)
      let menus = this.$refs.menu.getCheckedNodes().filter((item) => {
        return item['parentId']
      })

      let menuIds = menus.map(item => item['id'])
      console.log('选中的菜单', menus, menuIds)

      this.$refs['myForm'].validate(async(valid) => {
        this.formData['products'] = this.formData['products'] ? this.formData['products'] + '' : ''
        this.formData['industries'] = this.formData['industries'] ? this.formData['industries'] + '' : ''
        this.formData['cityArr'] = this.formData['cityArr'] ? this.formData['cityArr'] + '' : ''
        this.formData['countyArr'] = this.formData['countyArr'] ? this.formData['countyArr'] + '' : ''

        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          saveRole({ ...this.formData, menuIds: menuIds }).then((response) => {
            this.$message.success('保存成功')
            loading.close()
            this.onCancel()
          }).catch(() => {
            loading.close()
            this.$message.error('保存失败')
          })
        }
      })
    },

    // 取消按钮
    onCancel() {
      this.openTabDialog = false
      this.reloadTable()
    },
    // 表单重置
    reset() {
      this.form = {}
      // this.resetForm("form");
    },
    hanleCheck(data, node) {
      const _this = this
      // 获取当前节点是否被选中
      const isChecked = this.$refs.menu.getNode(data).checked
      // 如果当前节点被选中，则遍历上级节点和下级子节点并选中，如果当前节点取消选中，则遍历下级节点并取消选中
      if (isChecked) {
        // 判断是否有上级节点，如果有那么遍历设置上级节点选中
        data.parentId && setParentChecked(data.parentId)
        // 判断该节点是否有下级节点，如果有那么遍历设置下级节点为选中
        if (_this.menuCheckStrictly) {
          data.children && setChildreChecked(data.children, true)
        }
      } else {
        // 如果节点取消选中，则取消该节点下的子节点选中
        data.children && setChildreChecked(data.children, false)
      }
      function setParentChecked(parentId) {
        // 获取该id的父级node
        const parentNode = _this.$refs.menu.getNode(parentId)
        // 如果该id的父级node存在父级id则继续遍历
        parentNode && parentNode.data && parentNode.data.parentId && setParentChecked(parentNode.data.parentId)
        //  设置该id的节点为选中状态
        _this.$refs.menu.setChecked(parentId, true)
      }
      function setChildreChecked(node, isChecked) {
        node.forEach(item => {
          item.children && setChildreChecked(item.children, isChecked)
          _this.$refs.menu.setChecked(item.permissionId, isChecked)
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  padding: 0 0.08333rem;
  background-color: #f0f2f5;
  margin-bottom: 0.08333rem;
  font-weight: bold;
  font-size: 0.125rem;
}

.el-button--text {
  color: #3377ff !important;
}

.info {
  padding: 0.16667rem;
}
</style>
