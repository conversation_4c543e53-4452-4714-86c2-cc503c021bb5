import request from "@/utils/request";

// 查询字典类型列表
export function queryDictTypeList(params) {
  return request({
    url: "/sanquan/dict/queryDictTypeList/",
    method: "get",
    params
  });
}

/**
 * 新增字典列表
 */
export function addDictType(data) {
  return request({
    url: "/sanquan/dict/addDictType",
    method: "post",
    data
  });
}

/**
 * 新增字典值
 */
export function addDictValue(data) {
  return request({
    url: "/sanquan/dict/addDictValue",
    method: "post",
    data
  });
}

/**
 * 新增字典值   小增加
 */
export function addDictValueOnly(data) {
  return request({
    url: "/sanquan/dict/addDictValueOnly",
    method: "post",
    data
  });
}

/**
 * 字典详情
 */
export function dictDetails(data) {
  return request({
    method: "post",
    url: "/sanquan/dict/dictDetails",
    data
  });
}

/**
 * 字典删除
 */
export function dictTypeDelete(data) {
  return request({
    url: "/sanquan/dict/dictTypeDelete",
    method: "post",
    data
  });
}

/**
 * 字典删除   只删除值
 */
export function dictValueDelete(data) {
  return request({
    method: "post",
    url: "/sanquan/dict/dictValueDelete",
    data
  });
}
/**
 *  字典Type修改
 */

export function dictTypeModify(data) {
  return request({
    method: "post",
    url: "/sanquan/dict/dictTypeModify",
    data
  });
}
/**
 *  字典列表不分页
 */

export function findDictTypeList() {
  return request({
    method: "post",
    url: "/sanquan/dict/findDictTypeList",
  });
}
/**
 *  字典列表根据主键查信息
 */

export function findDictValueList(id) {
  return request({
    method: "post",
    url: "/sanquan/dict/findDictValueList",
    params: {
      id
    }
  });
}

export function findDetails(params) {
  return request({
    method: "post",
    url: "/sanquan/dict/findDetails",
    params
  });
}
