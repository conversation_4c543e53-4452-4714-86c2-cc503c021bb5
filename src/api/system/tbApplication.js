import request from "@/utils/request";

// 查询分页
export function page(params) {
  return request({
    url: "/user/application",
    method: "get",
    params: params
  });
}

// 根据ID查询详细
export function getById(id) {
  return request({
    url: "/user/application/" + id,
    method: "get"
  });
}

// 新增
export function add(data) {
  return request({
    url: "/user/application",
    method: "post",
    data: data
  });
}

// 修改用户
export function updateById(data) {
  return request({
    url: "/user/application/" + data.id,
    method: "put",
    data: data
  });
}

// 删除
export function del(id) {
  return request({
    url: "/user/application/" + id,
    method: "delete"
  });
}

// 查询详细
export function get(data) {
  return request({
    url: "/user/application/findApplicationByApplication",
    method: "post",
    data: data
  });
}

// 修改
export function update(data) {
  return request({
    url: "/user/application/updateById",
    method: "post",
    data: data
  });
}


// 校验钉钉应用id唯一性
export function checkApplication(data) {
  return request({
    url: "/user/application/checkApplication",
    method: "post",
    data: data
  });
}
