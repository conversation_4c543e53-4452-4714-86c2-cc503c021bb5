import request from '@/utils/request'

//tab字典
export function selectConfig() {
  return request({
    url: '/dingding/scholarly/selectConfig',
    method: 'get',
    params: {}
  })
}

//列表
export function selectSFByDataAndTitle(data) {
  return request({
    url: '/dingding/scholarly/selectSFByDataAndTitle',
    method: 'post',
    data
  })
}

//新增
export function insertScholarlyFragrance(data) {
  return request({
    url: '/dingding/scholarly/insertScholarlyFragrance',
    method: 'post',
    data
  })
}

//新增点赞
export function addScholarlySupport(params) {
  return request({
    url: '/dingding/scholarly/addScholarlySupport',
    method: 'get',
    params
  })
}

//删除点赞
export function delScholarlySupport(data) {
  return request({
    url: '/dingding/scholarly/delScholarlySupport',
    method: 'post',
    data
  })
}

//新增浏览量
export function updateSFViewsAdd(params) {
  return request({
    url: '/dingding/scholarly/updateSFViewsAdd',
    method: 'get',
    params
  })
}

//书详情
export function selectSFById(params) {
  return request({
    url: '/dingding/scholarly/selectSFById',
    method: 'get',
    params
  })
}

//新增评论
export function insertComment(data) {
  return request({
    url: '/dingding/scholarly/insertCommentselectSFByDataAndTitle',
    method: 'post',
    data
  })
}

//查询评论列表
export function selectCommentByInteractionId(data) {
  return request({
    url: '/dingding/scholarly/selectCommentByInteractionId',
    method: 'post',
    data
  })
}

//查询评论列表
export function selectNotCheckCommentByInteractionId(data) {
  return request({
    url: '/dingding/scholarly/selectNotCheckCommentByInteractionId',
    method: 'post',
    data
  })
}

//审核书
export function updateReviewed(data) {
  return request({
    url: '/dingding/scholarly/updateReviewed',
    method: 'post',
    data
  })
}

//审核评论
export function updateComment(data) {
  return request({
    url: '/dingding/scholarly/updateComment',
    method: 'post',
    data
  })
}

//修改书
export function updateScholarlyFragrance(data) {
  return request({
    url: '/dingding/scholarly/updateScholarlyFragrance',
    method: 'post',
    data
  })
}

//删除书
export function updateDeleteSF(data) {
  return request({
    url: '/dingding/scholarly/updateDeleteSF',
    method: 'post',
    data
  })
}

export function selectSFByAgency(data) {
  return request({
    url: '/dingding/scholarly/selectSFByAgency',
    method: 'post',
    data: {
      bookTitle: data.bookTitle,
      category: data.category,
      pageNum:data.pageNum,
      pageSize:data.pageSize
    }
  })
}

//书详情
export function getCommentDbPage() {
  return request({
    url: '/dingding/scholarly/comment/dbPage',
    method: 'get'
  })
}

