<template>
  <div
    class="app-wrapper"
    id="1.23452123412415384164.123412415" v-loading="true"
  >
  </div>
</template>

<script>
import { mapState } from "vuex";
import variables from "@/assets/styles/variables.scss";
import { getToken } from '@/utils/auth'
export default {
  name: "LoginSso",
  data() {
    return{
      url: 'http://sso.portal.unicom.local/eip_sso/aiportalLogin.html?appid=na186&success=http://service.aiportal.unicom.local//ssoclient/ssologin%3Faction%3Dlogin&error=http://sso.portal.unicom.local/eip_sso/aiportalLogin.html&return=http://sso.portal.unicom.local/eip_sso/aiportalLogin.html&oawx_t=A0002'
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.user.userInfo,
    }),
    variables() {
      return variables;
    },
  },
  mounted() {
    if (getToken()) {
      this.$router.push("/system/base/home")
    }else{
      window.location.href = this.url;
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/mixin.scss";
@import "~@/assets/styles/variables.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$sideBarWidth});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.mobile .fixed-header {
  width: 100%;
}
</style>
