<template>
  <div class="navbar">
    <div id="bannerSide">
      <div class="banner">

            <div class="banner-title">
              <div class="title">
                <div class="logos"/>
                <div class="title-font">{{ systemTitle !== '' ? systemTitle : '三全画像' }}</div>

              </div>
            </div>


            <div class="potal-right-menu">
              <template>
                <img class="header-img" src="@/assets/ghLogo/head1.png" alt/>

              </template>
              <el-dropdown class="avatar-container" placement="bottom-end" trigger="click">
                <div class="avatar-wrapper">
                    <span class="el-dropdown-link user-info">
                          您好，{{ userInfo.staffName }}!<i class="el-icon-caret-bottom el-icon--right"
                                                         style="font-size: 22px"
                    ></i>
                    </span>
                </div>

                <el-dropdown-menu slot="dropdown" :destroy-on-close="false">
                  <router-link to="/portal/userInfo/index">
                    <el-dropdown-item>个人中心</el-dropdown-item>
                  </router-link>
                  <el-dropdown-item divided @click.native="logout">
                    <span>退出登录</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
      </div>
      <div class="banner1">
        <div class="banner1-kb">
        <div class="banner1-kb-menu">
          <el-menu :default-active="defaultActive">
            <sidebar-item
              v-for="(route, index) in permission_routes"
              :key="route.path + index"
              :item="route"
              :base-path="route.path"
              class="sidebar_item"
            />
            <div  class="sidebar_item" v-has-permi="['sys:index:home']">
              <a href="/#/system/base/menu" target="_blank">
              <li  role="menuitem" tabindex="-100" class="el-menu-item"
                  style="padding-left: 20px;"
              >
                <div  class="menu-item"><span data-v-cccadf4c="" class="system-menu-title">系统管理</span>
                </div>
              </li>
            </a></div>
          </el-menu>

        </div>
          <!-- <div class="banner1-serch">
            <el-input
              size="mini"
              v-model="queryParams.keyWords"
              type="text"
              auto-complete="off"
              placeholder="请输入关键字"
            >
              <i slot="suffix" class="el-input__icon el-icon-search"></i>
            </el-input>
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { getMailNum } from '/src/api/system/notice'
import SidebarItem from '../components/Sidebar/SidebarItem.vue'
import Screenfull from '@/components/Screenfull'
import { configData } from '@/api/login'
import {
  getPersonalConfig,
  selectGeneralConfigForTenant
} from '/src/api/system/config'

export default {
  components: { SidebarItem, Screenfull },
  data() {
    return {
      defaultActive:'/portal/home',
      activeIndex: 1,
      userIcon: require('@/assets/images/ghLTImg/user1.png'),
      pathname: '',
      logo: require('@/assets/ghLogo/logo.png'),
      sysList: [],
      jumptUrl: undefined,
      queryParams: {
        keyWords: '',
        source: '',
        page: 1,
        pageSize: 10
      },
      mailNum: '', //站内信
      systemTitle: ''
    }
  },
  computed: {
    ...mapState({
      sidebarOpened: (state) => state.app.sidebarOpened,
      routes_data: (state) => state.permission.routes,
      userInfo: (state) => state.user.userInfo,
      pageSetting: (state) => state.app.pageSetting
    }),
    ...mapGetters(['permission_routes'])
  },
  watch: {
    '$route.path': function(newVal, oldVal) {
      this.pathname = newVal
    }
  },
  created() {
    this.getJumptUrl()
    this.getTitle()
    this.getLogo()
    this.getDicts('sys_list').then((response) => {
      this.sysList = response.data
    })
  },
  mounted() {
    // this.getNum()
  },
  methods: {
    // 获取系统配置动态修改
    getJumptUrl() {
      getPersonalConfig({
        configCode: 'jumptUrl'
      }).then((r) => {
        if (r.success && r.data) {
          this.jumptUrl = r.data.configValue
        } else {
          this.$message.error(r.message)
        }
      })
    },
    // 获取动态系统标题
    getTitle() {
      configData({
        configCodes: ['tenant_title'],
        clientType: '1',
        tenantId: this.$store.getters.customParam.tenantId
      }).then((r) => {
        if (r.success === true) {
          if (r.data[0]) {
            this.systemTitle = r.data[0].configValue
            document.title = this.systemTitle
          }
        }
      })
    },
    getLogo() {
      configData({
        configCodes: ['tenant_logo'],
        clientType: '1',
        tenantId: this.$store.getters.customParam.tenantId
      }).then((res) => {
        if (res.success === true) {
          if (res.data[0]) {
            // this.logo = res.data[0].configValue;
            // } else {
            //   this.logo = require("@/assets/logo/logo.png");
          }
        }
      })
    },
    setup() {
      this.$store.dispatch('app/changePageSetting')
    },
    // 获取站内信息的数量
    getNum() {
      getMailNum()
        .then((res) => {
          this.mailNum = res.data
        })
        .catch()
    },
    toggleSideBar() {
    },
    async logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          var tenantName = this.$store.getters.customParam.tenantLoginName
          this.$router.push({
            path: `/login`,
            query: { redirect: this.$route.fullPath, tenantName: tenantName }
          })
        })
      })
    },
    goBack() {
      this.$router.back(-1)
    },
    toSystem() {
      let routeData = this.$router.resolve({
        name: 'syshome'
      })
      window.open(routeData.href, '_blank')
    },
    // noticeSearch() {
    //   let keyWords = this.queryParams.keyWords.trim()
    //   if (!keyWords) {
    //     return
    //   }
    //   let routeData = this.$router.resolve({
    //     path: '/portal/home/<USER>',
    //     query: { source: this.queryParams.source, keyWords }
    //   })
    //
    //   if (this.$route.path.includes('/listFuzzy')) {
    //     this.$router.replace({
    //       path: '/portal/home/<USER>',
    //       query: { source: this.queryParams.source, keyWords }
    //     })
    //   } else {
    //     // this.$router.push({
    //     //   path: routeData.href,
    //     // });
    //     window.open(routeData.href, '_blank')
    //     // window.open(routeData.href, "framename");
    //   }
    // }
  }
}
</script>

<style lang="scss">
@import "./index.scss";

.right-menu-item {
  display: inline-block;
  padding: 0 8px;
  font-size: 18px;
  color: #5a5e66;
  vertical-align: text-bottom;

  &.hover-effect {
    cursor: pointer;
    transition: background 0.3s;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }
}

.el-input--mini .el-input__inner {
  height: 50px;
  line-height: 28px;
}

//.menu-item {
//  height: 56px;
//  display: -webkit-box;
//  display: -ms-flexbox;
//  display: flex;
//  -webkit-box-orient: vertical;
//  -webkit-box-direction: normal;
//  -ms-flex-direction: column;
//  flex-direction: column;
//  -webkit-box-pack: center;
//  -ms-flex-pack: center;
//  justify-content: center;
//  -webkit-box-align: center;
//  -ms-flex-align: center;
//  align-items: center;
//  -ms-flex-item-align: center;
//  align-self: center;
//}
</style>
