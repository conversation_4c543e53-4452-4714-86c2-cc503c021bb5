<template>
  <div ref="wrap" class="wrap">
    <div ref="wrap1" class="wrap1">
      <div ref="box" class="box">
        <div ref="marquee" class="marquee">{{text}}</div>
      </div>
      <div ref="node" class="node">{{text}}</div>
    </div>
  </div>
</template>
<script>
export default {
  name : 'Marquee',
  props: ['lists'], // 父组件传入数据， 数组形式 [ "连雨不知春去","一晴方觉夏深"]
  data () {
    return {
      text: '', // 数组文字转化后的字符串
      timer: null
    }
  },
  mounted(){
    // 把父组件传入的arr转化成字符串
    for (let i = 0; i < this.lists.length; i++) {
        this.text += ' ' + this.lists[i]
      }
  },
  
  updated () {
    //当父组件传过来的值改变时 清除定时器
    clearInterval(this.timer)
    //只有当文字长度超出父元素宽度时才移动
    if(this.$refs.wrap.clientWidth<=this.$refs.node.clientWidth){
      // 更新的时候运动
      this.move()
    }
  },
  watch: {
    // 把父组件传入的arr改变时 改变text
    lists(){
      this.text = ''
      for (let i = 0; i < this.lists.length; i++) {
        this.text += ' ' + this.lists[i]
      }
    }
  },
  methods: {
    move () {
      // 获取文字text 的计算后宽度  （由于overflow的存在，直接获取不到，需要独立的node计算）
      let width = this.$refs.node.getBoundingClientRect().width
      let distance = 0 // 位移距离
      // 设置位移
      this.timer = setInterval(() => {
        distance = distance - 0.5
        // 如果位移超过文字宽度，则回到起点
        if (-distance >= width) {
          distance = width+10
        }
        this.$refs.box.style.transform = 'translateX(' + distance + 'px)'
      }, 20)
    },
  },
  beforeDestroy () {
    clearInterval(this.timer)
  },

}
</script>
<style scoped>

.wrap {
  flex-grow:1;
  display: flex;
  overflow: hidden;
}
.wrap1{
  overflow: hidden;
}
.box {
  width: 80000%;
}

.box div {
  float: left;
}

.marquee {
  margin: 0 16px 0px 1px;
}

.node {
  position: absolute;
  z-index: -999;
  top: -999999px;
}
</style>
