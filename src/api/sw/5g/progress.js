import request from '@/utils/request'

// 查询地市列表
export function getCityList() {
    return request({
        url: '/sanquan/private5GWorkFlow/findCityList',
        method: 'get',
    })
}
// excel 导出
export function exportWorkFlowList(query) {
    return request({
        url: '/sanquan/private5GWorkFlow/exportWorkFlowList',
        method: 'get',
        params: query
    })
}
// 查询5G专网端到端流程进度详情
export function queryWorkFlowDetail(data) {
    return request({
        url: '/sanquan/private5GWorkFlow/queryWorkFlowDetail',
        method: 'post',
        data
    })
}

