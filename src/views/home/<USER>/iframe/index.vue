<template>
  <div style="width: 100%; height: 100%">
    <el-card
      :class="this.bgColor === true ?'iframe-card' :'unshow-iframe-card'"
      v-loading="iframeLoading"
      shadow="never"
      v-if="editLayout"
    >
      <div slot="header" v-if="show">
        <span class="card-title">
          <div class="title">
            <span>URL嵌入 （必须要设置访问地址）</span>
          </div>
          <el-button class="exec" type="text" @click="setUrl"
            >设置地址<i class="el-icon-d-arrow-right"></i
          ></el-button>
        </span>
      </div>
      <iframe
        :key="item.i + item.iframeUrl + 'iframecard'"
        v-once
        :src="item.iframeUrl"
        frameborder="0"
        class="ele-iframe"
        ref="eleIframe"
        v-if="item.iframeUrl"
      ></iframe>
      <el-empty
        v-else
        style="padding: 20px 0"
        :image-size="50"
        description="暂未设置Iframe地址，功能不可用！"
      ></el-empty>
    </el-card>
    <iframe
      v-else-if="item.iframeUrl"
      :key="item.i + item.iframeUrl + 'iframe'"
      v-once
      :src="item.iframeUrl"
      frameborder="0"
      class="ele-iframe"
    ></iframe>
    <el-empty
      v-else
      style="padding: 20px 0"
      :image-size="50"
      description="暂未设置Iframe地址，功能不可用！"
    ></el-empty>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="30%"
      :before-close="() => {}"
      :append-to-body="true"
    >
      <el-input
        type="textarea"
        :autosize="{ minRows: 2, maxRows: 4 }"
        placeholder="请输入嵌入页面地址"
        v-model="iframeUrl"
      >
      </el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveUrl">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name:"iframe",
  props: {
    editLayout: {
      type: Boolean,
      default: false,
    },
    item: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      show: "",
      iframeLoading: false,
      dialogVisible: false,
      iframeUrl: undefined,
      bgColor:"",
    };
  },
  created() {
    this.show = this.item.showTitle;
    this.bgColor = this.item.showBgColor;
  },
  watch: {
    "item.showTitle": function (val) {
      this.show = val;
    },
    "item.showBgColor" :function(val) {
      this.bgColor  = val
    }
  },
  methods: {
    setUrl() {
      this.dialogVisible = true;
    },
    saveUrl() {
      this.dialogVisible = false;
      this.item.iframeUrl = this.iframeUrl;
      this.$emit("setUrl", this.item);
      this.$refs.eleIframe.location.reload();
    },
  },
};
</script>

<style scoped lang="scss">
.iframe-card {
  background: #fff;
  height: 100%;
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 0;
    }
  }
  ::v-deep .el-card__body {
    padding: 0px;
    width: 100%;
    height: 100%;
  }
}
.unshow-iframe-card {
  border: 0px;
  height: 100%;
  background: #fff;
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 0;
    }
  }
  ::v-deep .el-card__body {
    padding: 0px;
    width: 100%;
    height: 100%;
  }
}

.ele-iframe {
  width: 100%;
  height: 100%;
}
</style>
