<template>
  <div class="wrapper">
    <div class="flex-start top-img-wrapper">
    </div>
    <div class="flex-content">
      <div class="daiban-wrapper" v-if="moduleIsShow('sys:index:home:daiban')">
        <div  class="daiban-bg">
          <div class="daiban-img-content">
            <img  class="daiban-img-content-img" src="@/assets/images/home/<USER>"/>
            <span style="color: white;margin-top: 5px">待办</span>
          </div>
        </div>
        <div class="daiban-content">
          <div class="daiban-content-wrapper">
            <div class="daiban-content-wrapper-text">
              法律法规 <span style="color: red">（0）</span>
            </div>
            <div class="daiban-content-wrapper-text">
              制度与办法 <span style="color: red">（0）</span>
            </div>
          </div>
          <div class="daiban-content-wrapper">
            <div class="daiban-content-wrapper-text" >
              <router-link :to="{path:'/home/<USER>',query:{type:'active',id:0,params:{activeName:'1',type:0,status: '1'}}}">
                竞赛活动 <span style="color: red">（{{indexData.db.activity_type0}}）</span>
              </router-link>
            </div>
            <div class="daiban-content-wrapper-text" >
              <router-link :to="{path:'/home/<USER>',query:{type:'active',id:0,params:{activeName:'2',type:1,status: '1'}}}">
                文体活动 <span style="color: red">（{{indexData.db.activity_type1}}）</span>
              </router-link>
            </div>
            <div class="daiban-content-wrapper-text" >
              <router-link :to="{path:'/home/<USER>',query:{type:'active',id:0,params:{activeName:'3',type:2,status: '1'}}}">
                工会要闻 <span style="color: red">（{{indexData.db.activity_type2}}）</span>
              </router-link>
            </div>
          </div>
          <div class="daiban-content-wrapper">
            <div class="daiban-content-wrapper-text">
              <router-link :to="{path:'/home/<USER>',query:{type:'active',id:0,params:{activeName:'4'}}}">
                好书推荐 <span style="color: red">（{{indexData.db.recommend_book}}）</span>
              </router-link>
            </div>
            <div class="daiban-content-wrapper-text" >
              <router-link :to="{path:'/home/<USER>',query:{type:'active',id:0,params:{activeName:'5'}}}">
                读书心得 <span style="color: red">（{{indexData.db.Experience_book}}）</span>
              </router-link>
            </div>
            <div class="daiban-content-wrapper-text" >
              <router-link :to="{path:'/home/<USER>',query:{type:'active',id:0,params:{activeName:'6'}}}">
                评论审核 <span style="color: red">（{{indexData.db.comment_db}}）</span>
              </router-link>
            </div>
<!--            <div class="daiban-content-wrapper-text" >-->
<!--              <router-link :to="{path:'/home/<USER>',query:{type:'active',id:0,params:{activeName:'6'}}}">-->
<!--                读书活动 <span style="color: red">（{{indexData.db.activity_book}}）</span>-->
<!--              </router-link>-->
<!--            </div>-->
          </div>
          <div class="daiban-content-wrapper">
            <div class="daiban-content-wrapper-text">
              <router-link :to="{path:'/home/<USER>',query:{type:'xs',id:0,params:{activeName:'1',type:0}}}">
                总经理信箱 <span style="color: red">（{{indexData.db.zjlEmailNum}}）</span>
              </router-link>
            </div>
            <div class="daiban-content-wrapper-text">
              <router-link :to="{path:'/home/<USER>',query:{type:'xs',id:0,params:{activeName:'2',type:1}}}">
                工会主席信箱 <span style="color: red">（{{indexData.db.ghzxEmailNum}}）</span>
              </router-link>
            </div>
            <!--          <div class="daiban-content-wrapper-text">-->
            <!--            建言献策 <span style="color: red">（0）</span>-->
            <!--          </div>-->
          </div>
          <div class="daiban-content-wrapper">
            <div class="daiban-content-wrapper-text">
              <router-link :to="{path:'/home/<USER>',query:{type:'person',id:0,params:{activeName:'6'}}}">
                暖心工程 <span style="color: red">（{{indexData.db.careNum}}）</span>
              </router-link>
            </div>
          </div>
        </div>
      </div>
      <div class="yaowen-wrapper" v-if="moduleIsShow('sys:index:home:yaowen')">
        <div class="yaowen-wrapper-zcfg">
          <div class="yaowen-wrapper-content">
            <div class="yaowen-wrapper-content-1">
              <div class="yaowen-wrapper-content-1-1">
                <img  style="width: 34px;height: 30px" src="@/assets/images/home/<USER>"/>
              </div>
              <div class="yaowen-wrapper-content-1-2">
                政策法规
              </div>
              <div class="yaowen-wrapper-content-1-3">
                <router-link to="law/law">
                  更多>
                </router-link>
              </div>
            </div>
            <div class="yaowen-wrapper-content-line">
            </div>
            <div class="yaowen-wrapper-content-3">
              <div v-for="(item,index) in indexData.low_list" :key="index"  class="yaowen-wrapper-content-3-wrapper">
                <router-link :to="{path:'/home/<USER>',query:{type:'law',id:item.id,params:{}}}">
                  <div :class="index<=2?'number_top_circle':'number_top_no_circle'">
                    {{index+1}}
                  </div>
                  <div class="yaowen-wrapper-content-3-wrapper-text">
                    {{item.title}}
                  </div>
                </router-link>
              </div>
            </div>
          </div>
        </div>
        <div class="yaowen-wrapper-ghxw">
          <div class="yaowen-wrapper-content">
            <div class="yaowen-wrapper-content-1">
              <div class="yaowen-wrapper-content-1-1">
                <img  style="width: 34px;height: 30px" src="@/assets/images/home/<USER>"/>
              </div>
              <div class="yaowen-wrapper-content-1-2">
                工会新闻
              </div>
              <div class="yaowen-wrapper-content-1-3">
                <router-link to="active/2">
                  更多>
                </router-link>
              </div>
            </div>
            <div class="yaowen-wrapper-content-line">
            </div>
            <div class="yaowen-wrapper-content-3">
              <div v-for="(item,index) in indexData.gh_new_list" :key="index" class="yaowen-wrapper-content-3-wrapper">
                <router-link :to="{path:'/home/<USER>',query:{type:'new',id:item.id,params:{id:item.id,type:item.type}}}">
                  <div :class="index<=2?'number_top_circle':'number_top_no_circle'">
                    {{index+1}}
                  </div>
                  <div class="yaowen-wrapper-content-3-wrapper-text">
                    {{item.title}}
                  </div>
                </router-link>
              </div>
            </div>
          </div>
        </div>
        <div class="yaowen-wrapper-hdzx">
          <div class="yaowen-wrapper-content">
            <div class="yaowen-wrapper-content-1">
              <div class="yaowen-wrapper-content-1-1">
                <img  style="width: 34px;height: 30px" src="@/assets/images/home/<USER>"/>
              </div>
              <div class="yaowen-wrapper-content-1-2">
                活动展现
              </div>
              <div class="yaowen-wrapper-content-1-3">
                <router-link to="active/0">
                  更多>
                </router-link>
              </div>
            </div>
            <div class="yaowen-wrapper-content-line">
            </div>
            <div class="yaowen-wrapper-content-3">
              <div v-for="(item,index) in indexData.activity_list" :key="index" class="yaowen-wrapper-content-3-wrapper">
                <router-link :to="{path:'/home/<USER>',query:{type:'new',id:item.id,params:{id:item.id,type:item.type}}}">
                  <div :class="index<=2?'number_top_circle':'number_top_no_circle'">
                    {{index+1}}
                  </div>
                  <div class="yaowen-wrapper-content-hd-wrapper-text">
                    {{item.title}}
                  </div>
                  <div class="yaowen-wrapper-content-hd-wrapper-text-date">
                    {{dateFormat(item.createDatetime)}}
                  </div>
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="img-wrapper" v-if="moduleIsShow('sys:index:home:read')">
      </div>
      <div class="read-wrapper" v-if="moduleIsShow('sys:index:home:read')">
        <div class="read-wrapper-children">
          <div class="read-wrapper-children-top">好书推荐</div>
          <div class="read-wrapper-children-bottom">
            <div class="read-wrapper-children-bottom-top">
            </div>
            <div class="read-wrapper-children-bottom-bottom">
              <div v-for="(item,index) in indexData.recommend_book_list" :key="index"  class="read-wrapper-content-3-wrapper">
                <router-link :to="{path:'/home/<USER>',query:{type:'book',id:item.id,params:{id:item.id,type:1}}}">
                  <div class="star_icon">
                  </div>
                  <div class="yaowen-wrapper-content-3-wrapper-text">
                    {{item.bookTitle}}
                  </div>
                </router-link>
              </div>
            </div>
          </div>
        </div>
        <div class="read-wrapper-children">
          <div class="read-wrapper-children-top">读书心得</div>
          <div class="read-wrapper-children-bottom">
            <div class="read-wrapper-children-bottom-bottom-2">
              <div v-for="(item,index) in indexData.experience_book_list" :key="index" class="read-wrapper-content-3-wrapper">
                <router-link :to="{path:'/home/<USER>',query:{type:'book',id:item.id,params:{id:item.id,type:1}}}">
                  <div class="star_icon">
                  </div>
                  <div class="yaowen-wrapper-content-3-wrapper-text">
                    {{item.bookTitle}}
                  </div>
                </router-link>
              </div>
            </div>
          </div>
        </div>
        <div class="read-wrapper-children">
          <div class="read-wrapper-children-top">读书排行</div>
          <div class="read-wrapper-children-bottom">
            <div class="read-wrapper-children-bottom-bottom-2">

              <div  v-for="(item,index) in indexData.activity_book_list" :key="index" class="read-wrapper-content-3-wrapper">
                  <div :class="index<=2?'number_top_circle':'number_top_no_circle'">
                    {{index+1}}
                  </div>
                  <div class="yaowen-wrapper-content-hd-wrapper-text">
                    {{item.org_name}}
                  </div>
                  <div class="yaowen-wrapper-content-hd-wrapper-text-date">
                    {{item.num}}
                  </div>
              </div>

            </div>
          </div>
        </div>
      </div>
      <div class="jbz-img-wrapper" v-if="moduleIsShow('sys:index:home:walking')">
      </div>
      <div class="jbz-wrapper" v-if="moduleIsShow('sys:index:home:walking')">
        <div class="jbz-wrapper-chat">
          <div class="jbz-wrapper-chat-top">
            <div class="jbz-wrapper-chat-top-title">
              <div class="jbz-wrapper-chat-top-title-1">今日排行</div>
              <div  class="jbz-wrapper-chat-top-title-2">

              </div>
            </div>
            <div ref="chart_department" class="jbz-wrapper-chat-top" />
          </div>
          <div ref="chart_user" class="jbz-wrapper-chat-bottom" />
        </div>
        <div class="jbz-wrapper-chat">
          <div class="jbz-wrapper-chat-top">
            <div class="jbz-wrapper-chat-top-title">
              <div class="jbz-wrapper-chat-top-title-1">月度</div>
              <div class="jbz-wrapper-chat-top-title-2"></div>
            </div>
            <div  ref="chart_company" class="jbz-wrapper-chat-top" />
          </div>
          <div ref="chart_company_hb" class="jbz-wrapper-chat-bottom">

          </div>
        </div>
      </div>
    </div>
    <!--    <div class="flex-bottom">-->

    <!--    </div>-->
  </div>
</template>

<script>
const animationDuration = 6000
// import echarts from 'echarts';
import {
  getBookList,
  getLowList,
  getGhNewList,
  getEchartData,
  getEchartDataHb,
  getDbHd,
  getDbBook,
  getDbCare,
  getDbEmail,
  getAllDbCount, getReadList, getDbBookComment
} from '@/api/home/<USER>'
require('echarts/theme/macarons') // echarts theme
import { mapState } from "vuex";
export default {
  name: "home",
  components: {

  },
  data() {
    return {
      chart_department: null,
      chart_bm: null,
      chart_gh_tb: null,
      chart_bm_tb: null,
      //首页数据
      indexData:{
        //好书推荐
        recommend_book_list:[],
        //读书心得
        experience_book_list:[],
        //读书活动
        activity_book_list:[],
        //政策法规列表
        low_list:[],
        //工会新闻列表
        gh_new_list:[],
        //活动展现
        activity_list:[],
        //健步走数据
        walkingDate:{
          // //部门数据
          department:{
            // xData: ['第一公会', '第二工会', '第三工会', '第四工会', '第五工会', '第六工会'],
            // Data: [79, 52, 200, 334, 390, 330]
            xData: [],
            Data: []
          },
          //当日部门排行
          user:{
            // xData: ['第一公会', '第二工会', '第三工会', '第四工会', '第五工会', '第六工会'],
            // Data: [79, 52, 200, 334, 390, 330]
            xData: [],
            Data: []
          },
          company:{
            // xData: ['01/01', '01/02', '01/03', '01/04', '01/05', '01/06','01/07'],
            // Data: [35, 52, 60, 120, 390, 280,400]
            xData: [],
            Data: []
          },
          companyHB:{
            // xData: ['01/01', '01/02', '01/03', '01/04', '01/05', '01/06','01/07'],
            // Data: [69, -59, 170, -120, 400, 330,280]
            xData: [],
            Data: []
          },
        },
        //待办
        db:{
          //竞赛活动
          activity_type0:0,
          //文体活动
          activity_type1:0,
          //工会要闻
          activity_type2:0,
          //好书推荐
          recommend_book:0,
          //读书心得
          Experience_book:0,
          //评论审核
          comment_db:0,
          //读书活动
          activity_book:0,
          zjlEmailNum:0,
          ghzxEmailNum:0,
          careNum:0
        }
      },
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.user.userInfo,
      pageSetting: (state) => state.app.pageSetting,
    }),
  },
  mounted() {
    if(this.userInfo.userid) {
      //拥有书香中国权限
      if(this.moduleIsShow('sys:index:home:read')){
        //好书推荐
        getBookList({
          pageNum:1,
          pageSize:4,
          reviewedType:'2',
          category:'recommend_book',
          jurisdiction:'finish'
        }).then((response) => {
          // console.log('selectSFByDataAndTitle。。。。。'+response.code);
          // console.log('selectSFByDataAndTitle。。。。。'+JSON.stringify(response));
          // JSON.stringify(
          if(response.code == '1' && response.data.records.length>0){
            this.indexData.recommend_book_list=response.data.records;
          }
        });
        //读书心得
        getBookList({
          pageNum:1,
          pageSize:7,
          reviewedType:'2',
          category:'Experience_book',
          jurisdiction:'finish'
        }).then((response) => {
          if(response.code == '1' && response.data.records.length>0){
            this.indexData.experience_book_list=response.data.records;
          }
        });
        //读书排行
        getReadList().then((response) => {
          if(response.code == '1' && response.data.length>0){
            this.indexData.activity_book_list=response.data;
          }
        });
      }
      //拥有要闻权限
      if(this.moduleIsShow('sys:index:home:yaowen')){
        //法律法规
        getLowList({
          pageNum:1,
          pageSize:6
        }).then((response) => {
          if(response.code == '1' && response.data.records.length>0){
            // console.log('getLowList。。。。。'+JSON.stringify(response));
            this.indexData.low_list=response.data.records;
            // console.log('getLowList。。。。。'+this.indexData.low_list.length);
          }
        });
        //工会新闻
        getGhNewList({
          pageNum:1,
          pageSize:6,
          type:'2',
        }).then((response) => {
          if(response.code == '1' && response.data.records.length>0){
            // console.log('getGhNewList。。。。。'+JSON.stringify(response));
            this.indexData.gh_new_list=response.data.records;
            // console.log('getGhNewList。。。。。'+this.indexData.gh_new_list.length);
          }
        });

        //活动展现
        getGhNewList({
          pageNum:1,
          pageSize:6,
          type:'0',
        }).then((response) => {
          if(response.code == '1' && response.data.records.length>0){
            // console.log('getGhNewList。。。。。'+JSON.stringify(response));
            this.indexData.activity_list=response.data.records;
            // console.log('getGhNewList。。。。。'+this.indexData.gh_new_list.length);
          }
        });

      }

      //拥有健步走权限
      if(this.moduleIsShow('sys:index:home:walking')){

        //部门和个人排行
        getEchartData({}).then((response) => {
          if(response.code == '1'){
            // console.log('健步走数据请求成功。。。。。'+JSON.stringify(response.data));
            // this.indexData.activity_list=response.data.records;
            // console.log('getGhNewList。。。。。'+this.indexData.gh_new_list.length);
            this.indexData.walkingDate.department=response.data.walkingDate.department;
            this.indexData.walkingDate.user=response.data.walkingDate.user;
            this.initChart_department();
            this.initChart_user();
          }
        });

        //近6个月趋势和环比
        getEchartDataHb({}).then((response) => {
          if(response.code == '1'){
            // console.log('健步走数据请求成功。。。。。'+JSON.stringify(response.data));
            // this.indexData.activity_list=response.data.records;
            // console.log('getGhNewList。。。。。'+this.indexData.gh_new_list.length);
            this.indexData.walkingDate.company=response.data.walkingDate.company;
            this.indexData.walkingDate.companyHB=response.data.walkingDate.companyHB;
            this.initChart_company();
            this.initChart_company_hb();
          }
        });
      }
      //拥有审批权限
      if(this.moduleIsShow('sys:index:home:daiban')){
        //活动展现
        getDbHd({
          pageNum:1,
          pageSize:10
        }).then((response) => {
          if(response.code == '1' && response.data.length>0){
            // console.log('竞赛活动待办。。。。。'+JSON.stringify(response));
            response.data.forEach((item,index)=>{
              switch (item.type){
                case '0':
                  this.indexData.db.activity_type0=item.value;
                  break;
                case '1':
                  this.indexData.db.activity_type1=item.value;
                  break;
                case '2':
                  this.indexData.db.activity_type2=item.value;
                  break;
              }
            })
          }
        });

        //书香中国，好书推荐
        getDbBook({
          pageNum:1,
          pageSize:10
        }).then((response) => {
          if(response.code == '1'&& response.data.length>0){
            console.log('好书推荐。。。。。'+JSON.stringify(response));
            response.data.forEach((item,index)=>{
              switch (item.category){
                case 'recommend_book':
                  this.indexData.db.recommend_book=item.countNum;
                  break;
                case 'Experience_book':
                  this.indexData.db.Experience_book=item.countNum;
                  break;
                case 'activity_book':
                  this.indexData.db.activity_book=item.countNum;
                  break;
              }
            })
          }
        });

        //书香评论审核
        getDbBookComment().then((response) => {
          console.log('书香评论审核。。。。。'+JSON.stringify(response));
          if(response.code == '1'){
            this.indexData.db.comment_db=response.data.dbnum;
          }
        });


        //暖心工程
        getDbCare({
          pageNum:1,
          pageSize:10
        }).then((response) => {
          if(response.code == '1'){
            this.indexData.db.careNum=response.data;
          }
        });

        //总经理邮箱
        getDbEmail({
          pageNum:1,
          pageSize:10,
          type:0
        }).then((response) => {
          if(response.code == '1'){
            this.indexData.db.zjlEmailNum=response.data;
          }
        });

        //工会主席邮箱
        getDbEmail({
          pageNum:1,
          pageSize:10,
          type:1
        }).then((response) => {
          if(response.code == '1'){
            this.indexData.db.ghzxEmailNum=response.data;
          }
        });

        //
        // //获取待办列表
        // getAllDbCount(
        // ).then((response) => {
        //   if(response.code == '1'){
        //     console.log('获取待办列表。。。。。'+JSON.stringify(response));
        //   }
        // });
      }
    }
  },
  // watch: {
  //
  // },
  methods: {
    moduleIsShow(authorkey){
      return this.userInfo.authorities.indexOf(authorkey)>-1;
    },
    dateFormat(date){
      return this.$moment(date).format("YYYY-MM-DD")
    },
    initChart_department() {
      this.chart_department = this.$echarts.init(this.$refs.chart_department);
      this.chart_department.setOption({
        title: {
          text: '部门排行',
          x: 'center',
          y: '10px',
          textStyle:{
            fontStyle: 'normal',
            fontSize: 16,
            fontWeight: 'normal'
          },
          textAlign: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        grid: {
          top: 50,
          left: '2%',
          right: '5%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          data: this.indexData.walkingDate.department.xData,
          axisTick: {
            alignWithLabel: true
          },
          axisLine: {            // 坐标轴线
            show: true,        // 默认显示，属性show控制显示与否
            lineStyle: {       // 属性lineStyle控制线条样式
              color: '#DFD8D5',
              width: 1,
              type: 'solid'
            }
          },
          axisLabel:{
            show:true,
            color: '#333131',
            rotate: '45',// 刻度标签旋转的角度,在类目轴的类目标签显示不下的时候可以通过旋转防止标签之间重叠。
          }
        }],
        yAxis: [{
          type: 'value',
          axisTick: {
            show: false
          },
          axisLine: {            // 坐标轴线
            show: true,        // 默认显示，属性show控制显示与否
            lineStyle: {       // 属性lineStyle控制线条样式
              color: '#DFD8D5',
              width: 1,
              type: 'solid'
            }
          },
          axisLabel:{
            show:true,
            color: '#333131'
          },
          splitLine: {
            show: true,
            lineStyle:{
              type: 'dashed'
            }
          }
        }],
        series: [
          {
            name: '步数',
            type: 'bar',
            barWidth: '60%',
            itemStyle:{
              normal: {
                color:function(params){
                  let colorList = ['#EDE4DF','#DCD4D0','#DE6734'];
                  return colorList[params.dataIndex % colorList.length];
                }
              }
            },
            data: this.indexData.walkingDate.department.Data,
            animationDuration
          }]
      })

    },
    initChart_user() {
      // this.chart = echarts.init(this.$el, 'chart');
      console.log('Chart初始化。。。。。');

      this.chart_user = this.$echarts.init(this.$refs.chart_user);
      // this.chart = echarts.init(this.$el, 'macarons')

      this.chart_user.setOption({
        title: {
          text: '个人排行',
          x: 'center',
          y: '10px',
          textStyle:{
            fontStyle: 'normal',
            fontSize: 16,
            fontWeight: 'normal'
          },
          textAlign: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        grid: {
          top: 50,
          left: '2%',
          right: '5%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          data: this.indexData.walkingDate.user.xData,
          axisTick: {
            alignWithLabel: true
          },
          axisLine: {            // 坐标轴线
            show: true,        // 默认显示，属性show控制显示与否
            lineStyle: {       // 属性lineStyle控制线条样式
              color: '#DFD8D5',
              width: 1,
              type: 'solid'
            }
          },
          axisLabel:{
            show:true,
            color: '#333131'
          }
        }],
        yAxis: [{
          type: 'value',
          axisTick: {
            show: false
          },
          axisLine: {            // 坐标轴线
            show: true,        // 默认显示，属性show控制显示与否
            lineStyle: {       // 属性lineStyle控制线条样式
              color: '#DFD8D5',
              width: 1,
              type: 'solid'
            }
          },
          axisLabel:{
            show:true,
            color: '#333131'
          },
          splitLine: {
            show: true,
            lineStyle:{
              type: 'dashed'
            }
          }
        }],
        series: [
          {
            name: '步数',
            type: 'bar',
            barWidth: '60%',
            itemStyle:{
              normal: {
                color:function(params){
                  let colorList = ['#EDE4DF','#DCD4D0','#DE6734'];
                  return colorList[params.dataIndex % colorList.length];
                }
              }
            },
            data: this.indexData.walkingDate.user.Data,
            animationDuration
          }]
      })

    },
    initChart_company(){
      this.chart_company = this.$echarts.init(this.$refs.chart_company);

      this.chart_company.setOption({
        title: {
          text: '公司近六月趋势',
          x: 'center',
          y: '10px',
          textStyle:{
            fontStyle: 'normal',
            fontSize: 16,
            fontWeight: 'normal'
          },
          textAlign: 'center'
        },
        grid: {
          top: 50,
          left: '2%',
          right: '5%',
          bottom: '5%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          axisLine: {            // 坐标轴线
            show: false,        // 默认显示，属性show控制显示与否
            lineStyle: {       // 属性lineStyle控制线条样式
              color: '#DFD8D5',
              width: 1,
              type: 'solid'
            }
          },
          axisLabel:{
            show:true,
            color: '#333131'
          },
          data: this.indexData.walkingDate.company.xData
        },
        yAxis: {
          type: 'value',
          axisLine: {            // 坐标轴线
            show: false,        // 默认显示，属性show控制显示与否
            lineStyle: {       // 属性lineStyle控制线条样式
              color: '#DFD8D5',
              width: 1,
              type: 'solid'
            }
          },
          axisLabel:{
            show:true,
            color: '#333131'
          },
          splitLine: {
            show: true,
            lineStyle:{
              type: 'dashed'
            }
          }

        },
        series: [
          { name: '步数',
            data: this.indexData.walkingDate.company.Data,
            type: 'line',
            smooth: true,
            // symbol: 'circle',设置为实心
            // color: ['#FF5816'], // 折线条的颜色
            // itemStyle:{
            //   normal: {
            //     Color: '#FF5816'
            //     // lineStyle: {
            //     //   Color: '#FF5816',
            //     //   width: 5,
            //     //   type: 'solid' //'dotted'虚线 'solid'实线
            //     // }
            //   }
            // },
            // lineStyle: {
            //   Color: '#ffffff',
            //   width: 5,
            //   type: 'solid' //'dotted'虚线 'solid'实线
            // },
            itemStyle:{//折线拐点标志的样式
              borderColor:"#FF5816",//拐点的边框颜色
              borderWidth:1
            },
            lineStyle:{//折线的样式
              color:"#FF5816",
              width: 2,
              type: 'solid' //'dotted'虚线 'solid'实线
            },
            // areaStyle: {}
            areaStyle: {//填充的颜色
              color: {//线性渐变前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置
                type: 'linear',
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [{
                  offset: 0, color: 'rgb(255,251,250)' // 0% 处的颜色
                }, {
                  offset: 1, color: 'rgb(255,229,218)' // 100% 处的颜色
                }],
                globalCoord: false// 缺省为 false
              }
            }
          }
        ]
      });

    },
    initChart_company_hb(){
      this.chart_company_hb = this.$echarts.init(this.$refs.chart_company_hb);

      this.chart_company_hb.setOption({
        title: {
          text: '公司近六月环比',
          x: 'center',
          y: '10px',
          textStyle:{
            fontStyle: 'normal',
            fontSize: 16,
            fontWeight: 'normal'
          },
          textAlign: 'center'
        },
        grid: {
          top: 50,
          left: '2%',
          right: '5%',
          bottom: '5%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          },
          formatter: function (params) {
            let html = params[0].name+"<br>";
            html+="<div style='display:flex;justify-content:space-between;margin-bottom: 5px;'>"
            html+="<div style='margin-right: 20px;'>"
            html+='<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:'+params[0].color+';"></span>'
            html+=params[0].seriesName;
            html+="</div>"
            html+="<div style='font-weight: 700;'>"+ params[0].value*100+"%<br>"+"</div>";
            html+="</div>"
            return html;
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          axisLine: {            // 坐标轴线
            show: false,        // 默认显示，属性show控制显示与否
            lineStyle: {       // 属性lineStyle控制线条样式
              color: '#DFD8D5',
              width: 1,
              type: 'solid'
            }
          },
          axisLabel:{
            show:true,
            color: '#333131'
          },
          data: this.indexData.walkingDate.companyHB.xData
        },
        yAxis: {
          type: 'value',
          axisLine: {            // 坐标轴线
            show: false,        // 默认显示，属性show控制显示与否
            lineStyle: {       // 属性lineStyle控制线条样式
              color: '#DFD8D5',
              width: 1,
              type: 'solid'
            }
          },
          axisLabel:{
            show:true,
            color: '#333131',
            formatter: function(value,index){
              return (value*100).toFixed(2)+'%';
              // return (value*100)+'%';
            }
          },
          splitLine: {
            show: true,
            lineStyle:{
              type: 'dashed'
            }
          }

        },
        series: [
          {
            name: '环比',
            data: this.indexData.walkingDate.companyHB.Data,
            type: 'line',
            symbol: 'circle',//设置为实心
            smooth: true,
            itemStyle:{//折线拐点标志的样式
              borderColor:"#E3B37E",//拐点的边框颜色
              borderWidth:1,
              normal : {
                color:'#E3B37E'
              }
            },
            lineStyle:{//折线的样式
              color: '#E3B37E',
              width: 2,
              type: 'solid' //'dotted'虚线 'solid'实线
            },
            // areaStyle: {}
            areaStyle: {//填充的颜色
              color: {//线性渐变前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置
                type: 'linear',
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [{
                  offset: 0, color: 'rgb(255,251,250)' // 0% 处的颜色
                }, {
                  offset: 1, color: 'rgb(251,243,235)' // 100% 处的颜色
                }],
                globalCoord: false// 缺省为 false
              }
            }
          }
        ]
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  width: 100%;
  margin: 0px auto;

  .top-img-wrapper {
    width: 100%;
    height: 179px;
    display: flex;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    background-image: url("~@/assets/images/home/<USER>");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
  }
  @media screen and (max-width:1366px){
    .flex-content {
      width: 100%;
    }
  }
  @media screen and (min-width:1400px){
    .flex-content {
      width: 1300px;
    }
  }
  .flex-content {
    // width: 1300px;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    display: flex;
    flex-direction: column;
    .daiban-wrapper {
      width: 100%;
      height: 131px;
      display: flex;
      flex-direction:row;
      border: 1px solid;
      border-radius: 8px;
      border-color: #CCA466;

      .daiban-bg{
        background-image: url("~@/assets/images/home/<USER>") ;
        background-repeat: no-repeat;
        background-position: center;
        margin-top: -2px;
        margin-left: -2px;
        width: 146px;
        height: 133px;
        object-fit: cover;/*图片完全填充*/
        object-position: center; /*设置头像选取照片的哪个区域object-position: x y*/
        border-radius: 8px;/*头像框圆形设置*/
        display: flex;
        flex-direction:column;
        .daiban-img-content{
          width: 41px;
          height: 85px;
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          margin: auto;
          display: flex;
          flex-direction: column;
          .daiban-img-content-img{
            width: 41px;
            height: 40px;
          }
        }
      }

      .daiban-content{
        width: 1129px;
        height: 100%;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;
        display: flex;
        justify-content: space-around;
        .daiban-content-wrapper{
          height: 100%;
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          margin: auto;
          display: flex;
          flex-direction:column;
          .daiban-content-wrapper-text{
            height: 43px;
            line-height: 43px;
          }
        }
      }
    }

    .yaowen-wrapper {
      width: 100%;
      height: 336px;
      margin-top: 28px;
      display: flex;
      //border: 1px solid;
      //border-radius: 8px;
      //border-color: #CCA466;
      justify-content:space-between;
      .yaowen-wrapper-zcfg{
        width: 431px;
        height: 336px;
        background-image: url("~@/assets/images/home/<USER>") ;
        background-repeat: no-repeat;
        background-position: center;
        //margin-top: -2px;
        //margin-left: -5px;
        object-fit: cover;/*图片完全填充*/
        object-position: center; /*设置头像选取照片的哪个区域object-position: x y*/
        border-radius: 8px;/*头像框圆形设置*/
        display: flex;
        flex-direction:column;
      }
      .yaowen-wrapper-ghxw{
        width: 431px;
        height: 336px;
        background-image: url("~@/assets/images/home/<USER>") ;
        background-repeat: no-repeat;
        background-position: center;
        //margin-top: -2px;
        //margin-left: -5px;
        object-fit: cover;/*图片完全填充*/
        object-position: center; /*设置头像选取照片的哪个区域object-position: x y*/
        border-radius: 8px;/*头像框圆形设置*/
        display: flex;
        flex-direction:column;

      }
      .yaowen-wrapper-hdzx{
        width: 431px;
        height: 336px;
        background-image: url("~@/assets/images/home/<USER>") ;
        background-repeat: no-repeat;
        background-position: center;
        //margin-top: -2px;
        //margin-left: -5px;
        object-fit: cover;/*图片完全填充*/
        object-position: center; /*设置头像选取照片的哪个区域object-position: x y*/
        border-radius: 8px;/*头像框圆形设置*/
        display: flex;
        flex-direction:column;
      }

    }
    .img-wrapper {
      width: 1282px;
      height: 119px;
      margin-top: 24px;
      background-image: url("~@/assets/images/home/<USER>") ;
      background-repeat: no-repeat;
      background-position: center;
      object-fit: cover;/*图片完全填充*/
      object-position: center; /*设置头像选取照片的哪个区域object-position: x y*/
      border-radius: 8px;/*头像框圆形设置*/
    }
    .read-wrapper{
      width: 1287px;
      height: 356px;
      margin-top: 24px;
      //border: 1px solid;
      //border-radius: 8px;
      //border-color: #CCA466;
      display: flex;
      justify-content:space-between;
      .read-wrapper-children{
        width: 410px;
        height: 100%;
        //border: 1px solid;
        //border-radius: 8px;
        //border-color: #CCA466;
        display: flex;
        flex-direction:column;
        .read-wrapper-children-top{
          width: 408px;
          height: 50px;
          background-image: url("~@/assets/images/home/<USER>");
          background-repeat: no-repeat;
          background-position: center;
          background-size: 100% 100%;
          line-height: 50px;
          text-align: left;
          padding-left: 22px;
          vertical-align: middle;
          color: white;
          font-size: 20px;

        }
        .read-wrapper-children-bottom{
          width: 408px;
          height: 305px;
          display: flex;
          flex-direction:column;
          //border: 1px solid;
          //border-radius: 8px;
          //border-color: #CCA466;
          .read-wrapper-children-bottom-top{
            width: 359px;
            height: 112px;
            margin: 7px auto;
            bottom: 0;
            left: 0;
            right: 0;
            //margin: auto;
            background-image: url("~@/assets/images/home/<USER>");
            background-repeat: no-repeat;
            background-position: center;
            background-size: 100% 100%;
          }
          .read-wrapper-children-bottom-bottom{
            width: 390px;
            height: 193px;
            top:0;
            bottom: 0;
            left: 0;
            right: 0;
            margin: auto;
            //border: 1px solid;
            //border-radius: 8px;
            //border-color: #CCA466;
            display: flex;
            flex-direction:column;
          }

          .read-wrapper-children-bottom-bottom-2{
            width: 390px;
            height: 100%;
            top:0;
            bottom: 0;
            left: 0;
            right: 0;
            margin: auto;
            //border: 1px solid;
            //border-radius: 8px;
            //border-color: #CCA466;
            display: flex;
            flex-direction:column;
          }
        }
      }
    }
    .jbz-img-wrapper {
      width: 1278px;
      height: 101px;
      margin-top: 24px;
      background-image: url("~@/assets/images/home/<USER>") ;
      background-repeat: no-repeat;
      background-position: center;
      object-fit: cover;/*图片完全填充*/
      object-position: center; /*设置头像选取照片的哪个区域object-position: x y*/
      //border-radius: 8px;/*头像框圆形设置*/
    }

    .jbz-wrapper{
      width: 1278px;
      height: 725px;
      margin-top: -1px;
      padding-top: 24px;
      //margin-top: 24px;
      //border: 1px solid;
      //border-radius: 8px;
      //border-color: #CCA466;
      background: linear-gradient(#F6F1EC,white,white);
      display: flex;
      justify-content:space-between;

      .jbz-wrapper-chat{
        width: 600px;
        height: 701px;
        top:0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;
        //border: 1px solid;
        //border-radius: 8px;
        //border-color: #CCA466;
        background: rgba(255, 255, 255, 0.6);
        display: flex;
        flex-direction:column;
        .jbz-wrapper-chat-top{
          width: 600px;
          height: 367px;
          top:0;
          bottom: 0;
          left: 0;
          right: 0;
          margin: auto;
          //border: 1px solid;
          //border-radius: 8px;
          //border-color: #CCA466;
          display: flex;
          flex-direction:column;
          .jbz-wrapper-chat-top-title{
            width: 100%;
            height: 33px;
            //border: 1px solid;
            //border-color: #CCA466;
            .jbz-wrapper-chat-top-title-1{
              float: left;
              width: 199px;
              height: 33px;
              background-image: url("~@/assets/images/home/<USER>");
              background-repeat: no-repeat;
              background-position: center;
              background-size: 100% 100%;
              line-height: 33px;
              text-align: center;
              //padding-left: 22px;
              vertical-align: middle;
              color: white;
              font-size: 20px;
            }
            .jbz-wrapper-chat-top-title-2{
              float: right;
              width: 168px;
              height: 33px;
            }

          }
          .jbz-wrapper-chat-top{
            width: 100%;
            height: 334px;
            //border: 1px solid;
            //border-color: rgba(208, 16, 23, 0.91);
          }

        }
        .jbz-wrapper-chat-bottom{
          width: 590px;
          height: 334px;
          top:0;
          bottom: 0;
          left: 0;
          right: 0;
          margin: auto;
          //border: 1px solid;
          //border-radius: 8px;
          //border-color: #CCA466;
        }
      }
    }
  }
}

.yaowen-wrapper-content {
  width: 90%;
  height: 90%;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  //border: 1px solid;
  //border-color: #CCA466;
  display: flex;
  flex-direction: column;

  .yaowen-wrapper-content-1 {
    width: 100%;
    height: 50px;
    top: 0;

  }
}
.yaowen-wrapper-content-1-1{
  float: left;
  line-height: 30px;
}
.yaowen-wrapper-content-1-2{
  float: left;
  line-height: 30px;
  margin-left: 10px;
  font-size: 16px;
  font-weight: bold;
}
.yaowen-wrapper-content-1-3{
  float: right;
  line-height: 30px;
  font-size: 16px;
}
.yaowen-wrapper-content-line{
  width: 100%;
  height: 0.5px;
  background-color: rgba(243, 223, 193, 0.99);
}
.yaowen-wrapper-content-3{
  width: 100%;
  height: 250px;
  margin-top: 5px;
  display: flex;
  flex-direction:column;
}
.yaowen-wrapper-content-3-wrapper{
  width: 100%;
  height: 40px;
}
.read-wrapper-content-3-wrapper{
  width: 100%;
  height: 40px;
  border-bottom: 0.5px dashed #ccc;
}
.star_icon{
  width: 20px;
  height: 20px;
  margin-top: 10px;
  margin-bottom: 10px;
  line-height: 20px;
  float: left;
  background-image: url("~@/assets/images/home/<USER>");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 6px 6px;
}
.number_top_circle{
  width: 20px;
  height: 20px;
  margin-top: 10px;
  margin-bottom: 10px;
  float: left;
  background-color: #d01017;
  object-fit: cover;/*图片完全填充*/
  object-position: center; /*设置头像选取照片的哪个区域object-position: x y*/
  border-radius: 50%;/*头像框圆形设置*/
  text-align: center;
  vertical-align: middle;
  color: white;
  font-size: 18px;
  line-height: 20px;
}
.number_top_no_circle{
  width: 20px;
  height: 20px;
  margin-top: 10px;
  margin-bottom: 10px;
  float: left;
  background-color: #cccccc;
  object-fit: cover;/*图片完全填充*/
  object-position: center; /*设置头像选取照片的哪个区域object-position: x y*/
  border-radius: 50%;/*头像框圆形设置*/
  text-align: center;
  vertical-align: middle;
  color: white;
  font-size: 16px;
  line-height: 20px;
}

.yaowen-wrapper-content-3-wrapper-text{
  //display: table-cell;
  width: calc(100% - 40px);
  height: 20px;
  margin-top: 10px;
  margin-bottom: 10px;
  margin-left: 10px;
  float: left;
  text-align: left;
  vertical-align: middle;
  color: black;
  font-size: 16px;
  line-height: 20px;
  min-height: 20px;
  max-height:20px;
  overflow: hidden;
  text-overflow: ellipsis;  /* 超出部分省略号 */
  word-break: break-all;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

//.read-wrapper-content-line{
//  width: 100%;
//  height: 0.5px;
//  background-color: rgba(243, 223, 193, 0.99);
//}
.yaowen-wrapper-content-hd-wrapper-text{
  //display: table-cell;
  width: calc(100% - 140px);
  height: 100%;
  margin-top: 10px;
  margin-bottom: 10px;
  margin-left: 10px;
  float: left;
  text-align: left;
  vertical-align: middle;
  color: black;
  font-size: 16px;
  line-height: 20px;
  min-height: 20px;
  max-height:20px;
  overflow: hidden;
  text-overflow: ellipsis;  /* 超出部分省略号 */
  word-break: break-all;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.yaowen-wrapper-content-hd-wrapper-text-date{
  width: 100px;
  height: 100%;
  margin-top: 10px;
  margin-bottom: 10px;
  //margin-left: 10px;
  float: right;
  text-align: center;
  vertical-align: middle;
  color: black;
  font-size: 16px;
  line-height: 20px;
}
//.flex-bottom {
//  width: 100%;
//  height: 179px;
//  display: flex;
//  top: 0;
//  bottom: 0;
//  left: 0;
//  right: 0;
//  margin: auto;
//  background-image: url("~@/assets/images/home/<USER>");
//  background-repeat: no-repeat;
//  background-position: center;
//  background-size: 100% 100%;
//}
.flex-bottom {
  width: 100%;
  height: 183px;
  display: flex;
  flex-direction:column;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  //background-image: url("~@/assets/images/home/<USER>");
  //background-repeat: no-repeat;
  //background-position: center;
  //background-size: 100% 100%;

}

</style>
