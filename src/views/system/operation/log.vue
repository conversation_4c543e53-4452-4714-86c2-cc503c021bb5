<template>
  <div class="app-container">
    <el-card shadow="never">
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        :inline="true"
      >
        <el-form-item
          v-if="$store.getters.customParam.userType === 'admin'"
          label="租户"
        >
          <el-select
            v-model="queryParams.tenantId"
            style="width: 200px"
            filterable
            remote
            :remote-method="getTenantList"
            :loading="getTenantLoading"
            @change="tenantChange"
            size="small"
          >
            <el-option
              v-for="item in tenantList"
              :key="item.tenantId"
              :label="item.tenantName"
              :value="item.tenantId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="员工名称" prop="staffName">
          <el-input
            v-model="queryParams.staffName"
            placeholder="请输入员工名称"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="业务类型" prop="busiType">
          <el-input
            v-model="queryParams.busiType"
            placeholder="请输入业务类型"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="操作类型" prop="operType">
          <el-select
            v-model="queryParams.operType"
            style="width: 200px"
            size="small"
          >
            <el-option
              v-for="item in operTypeList"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="操作日期">
          <el-date-picker
            v-model="dateRange"
            size="small"
            style="width: 330px"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :clearable="true"
            :picker-options="pickerOptions"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
          >搜索
          </el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置
          </el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="loading" :data="logList">
        <el-table-column type="expand">
          <template slot-scope="props">
            <el-form label-position="left">
              <el-form-item label="列名描述:">
                <span>{{ props.row.columnDesc }}</span>
              </el-form-item>
              <el-form-item label="改动前值:">
                <span>{{ props.row.oldValue }}</span>
              </el-form-item>
              <el-form-item label="改动后值:">
                <span>{{ props.row.newValue }}</span>
              </el-form-item>
            </el-form>
          </template>
        </el-table-column>
        <el-table-column label="序号" type="index" width="50" align="center">
        </el-table-column>
        <el-table-column
          label="操作人"
          prop="staffName"
          :show-overflow-tooltip="true"
          align="center"
        />
        <el-table-column
          label="业务类型"
          prop="busiType"
          :show-overflow-tooltip="true"
          align="center"
        />
        <el-table-column
          label="操作类型"
          prop="operType"
          :show-overflow-tooltip="true"
          align="center"
          width="180"
        >
          <template slot-scope="scope">
            <el-tag :type="selectDictRemark(operTypeList,scope.row.operType)">
              {{ selectDictLabel(operTypeList, scope.row.operType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作时间"
          prop="createTime"
          :show-overflow-tooltip="true"
          width="180"
          align="center"
        />
        <el-table-column
          label="操作表"
          prop="tableName"
          :show-overflow-tooltip="true"
          align="center"
        />
        <el-table-column
          label="操作列"
          prop="columnName"
          :show-overflow-tooltip="true"
          align="center"
        />
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script>
import {listLog} from "@/api/system/log";
import {list as tenantList} from "@/api/system/tenant";

export default {
  name: "Role",
  data() {
    return {
      // 遮罩层
      loading: false,
      getTenantLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        staffName: "",
        busiType: "",
        operType: "",
        beginTime: "",
        endTime: "",
        tenantId: this.$store.getters.customParam.tenantId,
      },
      logList: [],
      tenantList: [],
      operTypeList: [],
      //日期选择范围在一个月内
      pickerOptions: {
        disabledDate: (time) => {
          const one = 30 * 24 * 3600 * 1000
          return time.getTime() > (Date.now()) || time.getTime() <= (Date.now() - one)
        }
      }
    }
  },
  created() {
    // this.getList();
    this.getTenantList();
    this.getDicts("operation_type").then((response) => {
      this.operTypeList = response.data;
    });
  },
  methods: {
    /** 查询系统日志 */
    getList() {
      if (null != this.dateRange && "" != this.dateRange) {
        this.queryParams.beginTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      }
      this.loading = true;
      listLog(this.queryParams).then((res) => {
        this.logList = res.data.records;
        this.total = res.data.total;
        this.loading = false;
      });
    },
    getTenantList(tenantName) {
      this.getTenantLoading = true;
      let query = {}
      if (tenantName !== undefined && tenantName !== '') {
        query.tenantName = tenantName
        query.tenantId = undefined
      } else {
        query.tenantId = this.queryParams.tenantId
      }
      tenantList(query).then((response) => {
        this.tenantList = response.data;
      }).finally(() => {
        this.getTenantLoading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.beginTime = "";
      this.queryParams.endTime = "";
      // this.handleQuery();
    },
    tenantChange() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
  },
};
</script>

<style lang="scss"></style>
