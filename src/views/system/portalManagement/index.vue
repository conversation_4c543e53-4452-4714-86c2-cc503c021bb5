<template>
  <div class="app-container">
    <el-card shadow="never">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <el-form-item label="门户名称" prop="eipName">
          <el-input
            v-model="queryParams.eipName"
            placeholder="请输入门户名称"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="门户编码" prop="eipCode">
          <el-input
            v-model="queryParams.eipCode"
            placeholder="请输入字典类型"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="10">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:dict:add']"
            >新增</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table
        v-loading="loading"
        :data="typeList"
      >
        <el-table-column
          label="编码"
          align="left"
          prop="eipCode"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <a @click="copyToClipboard(scope.row.eipCode)">{{ scope.row.eipCode }}</a>
          </template>
        </el-table-column>
        <el-table-column
          label="门户名称"
          align="left"
          prop="eipName"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <router-link
              :to="'/system/portalManagement/data/' + scope.row.eipId"
              class="link-type"
            >
              <!-- <span>{{ scope.row.eipName }}</span> -->
              <a @click="copyToClipboard(scope.row.eipName)">{{ scope.row.eipName }}</a>
            </router-link>
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          align="left"
          prop="createDate"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          label="创建人"
          align="center"
          prop="createBy"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="编辑时间"
          align="center"
          prop="updateDate"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="编辑人"
          align="center"
          prop="updateBy"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:dict:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:dict:remove']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改参数配置对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="open"
        width="500px"
        append-to-body
        v-dialogDrag
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-row>
            <el-col :span="20">
              <el-form-item label="门户名称" prop="eipName">
                <el-input v-model="form.eipName" placeholder="请输入字典名称" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="20">
              <el-form-item label="门户编码" prop="eipCode">
                <el-input v-model="form.eipCode" placeholder="请输入字典类型" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="20">
              <el-form-item label="菜单内容" prop="menuContent">
                <el-col :span="22">
                  <el-input type="textarea" :autosize="{ minRows: 8}" maxlength="9999999999999" v-model="form.menuContent"
                            placeholder="仅支持json对象格式，示例：{'title': '门户首页'}" />
                  <!-- <json-viewer :value="form.menuContent" v-model="form.menuContent" :expand-depth=5 copyable></json-viewer> -->
                </el-col>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="saveLoading"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import {
  refreshCache,
} from "@/api/system/dict/type";

import {
  getAllPortal,
  getPortalDetails,
  portalDetailsUpdate,
  addPortal,
  deletePortal,
} from "@/api/system/portalManagement";

export default {
  name: "PortalManagement",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      typeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 状态数据字典
      statusOptions: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        eipName: undefined,
        eipCode: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        eipName: [
          { required: true, message: "门户名称不能为空", trigger: "blur" },
        ],
        eipCode: [
          { required: true, message: "门户编码不能为空", trigger: "blur" },
        ],
      },
      saveLoading: false,
    };
  },
  created() {
    this.getList();
    this.getDicts("sys_normal_disable").then((response) => {
      this.statusOptions = response.data;
    });
  },
  methods: {
    goRow(row,column) {
      if(column.label !== "操作") {
        this.$router.push({
          path: "/system/portalManagement/data/" + row.eipId,
        });
      }
    },
    /** 查询门户列表 */
    getList() {
      this.loading = true;
      getAllPortal(this.queryParams).then(
        (response) => {
          this.typeList = response.data.records;
          this.total = response.data.total;
          this.loading = false;
        }
      );
    },
    // 字典状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        eipId: undefined,
        eipName: undefined,
        eipCode: undefined,
      };
      this.resetForm("form");
      this.saveLoading = false;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加字典类型";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const eipId = row.eipId || this.ids;
      getPortalDetails(eipId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改门户信息";
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.saveLoading = true;
          if (this.form.eipId !== undefined) {
            if(this.form.menuContent !== undefined && this.form.menuContent !== "") {
              if(!this.isJSON(this.form.menuContent)) {
                this.$message.error("菜单内容数据格式不正确，请参考示例");
                return false;
              }
            }
            portalDetailsUpdate(this.form).then((response) => {
              this.saveLoading = false;
              if (response.success) {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              } else {
                this.$message.error(response.message);
              }
            });
          } else {
            if(this.form.menuContent !== undefined && this.form.menuContent !== "") {
              if(!this.isJSON(this.form.menuContent)) {
                this.$message.error("菜单内容数据格式不正确，请参考示例");
                return false;
              }
            }
            addPortal(this.form).then((response) => {
              this.saveLoading = false;
              if (response.success) {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              } else {
                this.$message.error(response.message);
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm(
        '是否确认删除字典名称为"' + row.eipName + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          return deletePortal({
            eipId: row.eipId,
          }).then((res) => {
            if(res.code == 1){
              this.getList();
              this.msgSuccess("删除成功");
            }else{
              this.msgError(res.message)
            }
          });;
        })

    },
    /** 刷新缓存按钮操作 */
    handleRefreshCache() {
      refreshCache().then(() => {
        this.msgSuccess("刷新成功");
      });
    },
    // json数据格式校验
    isJSON(str) {
      if (typeof str == 'string') {
        try {
          var obj = JSON.parse(str);
          if (typeof obj == 'object' && obj) {
            return true;
          } else {
            // console.log(typeof obj)
            return false;
          }

        } catch (e) {
          console.log('error：' + str + '!!!' + e);
          return false;
        }
      }
      console.log('It is not a string!')
    },
  },
};
</script>
