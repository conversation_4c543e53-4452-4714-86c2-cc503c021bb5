<template>
  <el-card class="add-item-card" shadow="never">
    <el-link type="primary" icon="el-icon-plus" @click="parentClick"
      >添加一个</el-link
    >
    <el-divider direction="vertical"></el-divider>
    <el-link
      type="primary"
      icon="el-icon-check"
      @click="saveClick"
      :loading="saveLoading"
      >保存</el-link
    >
  </el-card>
</template>

<script>
export default {
  name: "addItem",
  props: {
    item: {
      type: Object,
      default: {},
    },
    saveLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {
    parentClick() {
      this.$emit("parentClick", this.item);
    },
    saveClick() {
      this.$emit("saveClick", null);
    },
  },
};
</script>

<style scoped lang="scss">
.add-item-card {
  width: 100%;
  height: 100%;
  display: flex;
  background-color: #f5f9fa;
  justify-content: center;
  align-items: center;
  border: 2px solid #6959cd;
}
</style>
