<script>
import UniTable from '@/components/UniTable/UniTable.vue'
import RoleEdit from './comps/edit.vue'
import { deletedRole } from '@/api/platform/role'
import RoleUser from '@/views/sw/platform/roles/comps/RoleUser.vue'

export default {
  name: 'index',
  components: { RoleUser, UniTable, RoleEdit },
  data() {
    return {
      tableColumns: [
        { props: 'roleName', label: '角色名称' },
        { props: 'roleCode', label: '角色编码' },
        { props: 'opt', label: '操作', slot: true, align: 'center' }
      ],
      queryParams: {}
    }
  },
  methods: {
    resetQuery() {
      this.$refs.uniTable.resetPage()

      this.queryParams = {}
      this.handleQuery()
    },
    handleQuery() {
      this.$refs.uniTable.loadData(this.queryParams)
    },
    handleEdit(title, row) {
      //新增页面
      this.$refs.roleEdit.initOpen(title, row)
    },
    reloadTable() {
      this.$refs.uniTable.loadData(this.queryParams)
    },
    deleteById(id) {
      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deletedRole({ id: id }).then(() => {
          this.$message.success('删除成功')
          this.handleQuery()
        })
      })
    },
    grantUser(id,roleName) {
      this.$refs.roleUserEdit.openDialog(id,roleName)
    }
  }
}
</script>

<template>
  <div class="portal-body">
    <div class="uni-table-search search-wrapper box-bg" style="padding-top:20px;">
      <el-form
        :model="queryParams"
        @submit.native.prevent
        ref="queryForm"
        label-width="80px"
        label-position="left"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item label="角色名称" prop="roleName">
              <el-input
                v-model="queryParams.roleName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <div class="btn-group" style="right: 0;">
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        </div>
      </el-form>
    </div>
    <div class="table-wrapper box-bg">
      <div class="flex-between">
        <div class="table-title">角色列表</div>
        <div class="btn">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-plus"
            @click="handleEdit('新增',{})"
          >新增
          </el-button
          >
        </div>
      </div>
      <el-divider></el-divider>
      <uni-table
        action="/sanquan/role/findPage"
        :columns="tableColumns"
        ref="uniTable"
      >
        <template v-slot:opt="{row}">
          <div class="action-buttons flex-center">
            <el-button type="text" class="action-button"
                       @click="handleEdit('编辑',row)"
            >编辑
            </el-button>
            <span class="separator"></span>

            <el-button type="text" class="action-button"
                       @click="deleteById(row['id'])"

            >删除
            </el-button>
            <span class="separator"></span>
            <el-button type="text" class="action-button"
                       @click="grantUser(row['id'],row['roleName'])"

            >分配用户
            </el-button>
          </div>
        </template>
      </uni-table>
    </div>

    <RoleEdit ref="roleEdit" :reloadTable="reloadTable"/>
    <RoleUser ref="roleUserEdit"/>
  </div>

</template>

<style scoped lang="scss">

</style>
