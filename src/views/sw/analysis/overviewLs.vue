<template>
  <div class="portal-body">
    <div class="feedback-list">
      <div class="search-wrapper box-bg">
        <el-form :model="formQuery" ref="queryForm" label-width="0.8rem" label-position="left">
          <el-row>
            <el-col :span="7">
              <el-form-item label="客户名称" prop="custName">
                <el-input v-model="formQuery.custName" clearable placeholder="请输入客户名称"/>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item label="月份" prop="monthId">
                <el-date-picker
                  v-model="monthId"
                  format="yyyyMM"
                  type="month"
                  @change="changeValue"
                  placeholder="选择月"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="btn-group" style="right: 0;">
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="conditionQuery">查询</el-button>
          </div>
        </el-form>
      </div>
      <div class="table-wrapper box-bg">
        <div class="flex-between" style="border-bottom:2px #f5f5f5 solid;margin-bottom: 24px;padding:0 0 10px 0">
          <div class="table-title">六数统计表</div>
          <div>
            <el-button type="success" icon="el-icon-download" size="mini" @click="exportExcelEnvent">导出</el-button>
          </div>
        </div> 
        <div class="table-con">
          <div style="border:1px solid #dfe6ec;border-left:1px solid #dfe6ec;border-right:1px solid #dfe6ec;
line-height: 48px;text-align: center;font-weight: bolder;text-space: 2;font-size:18px;"
          >
            {{ this.formQuery['monthId'] }}月份top30客户六数收入统计
          </div>
          <UniTable
            :merge-row="true"
            mergeGroupName="custName"
            :mergeColIndex="[1,2,3]"
            :pageSize="100" action="/sanquan/ls/findPage" ref="uniTable" :columns="tableColumns" :params="{...formQuery,monthId:formQuery['monthId'].replace('-','')}"
          >
          </UniTable>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import UniTable from '@/components/UniTable/UniTable.vue'
import dayjs from 'dayjs'

export default {
  name: 'overviewLs',
  components: { UniTable },
  data() {
    return {
      formQuery: { monthId: this.getPrevMonth() },
      monthId:this.getPrevMonth(),
      tableColumns: [
        { props: 'custName', label: '客户名称', width: 240 },
        {
          label: '客户六数',
          align: 'center',
          children: [
            { props: 'lsCustTypeNum', label: '数量', align: 'center', width: 100 },
            { props: 'custLsIncome', label: '收入(元)', align: 'center', width: 100 }
          ]
        },
        { props: 'rosterCustomerName', label: '名单制客户名称', align: 'center', width: 300 },
        {
          label: '名单制客户六数', align: 'center', children: [
            { props: 'lsCustomerTypeNum', label: '数量', align: 'center', width: 100 },
            { props: 'rosterCustomerLsIncome', label: '收入(元)', align: 'center', width: 100 }
          ]
        },

        {
          label: '数传', align: 'center', children: [
            { props: 'isShuchuan', label: '是否涵盖', align: 'center', width: 100 },
            { props: 'schuanIncome', label: '收入(元)', align: 'center', width: 100 }
          ]
        },
        {
          label: '数管', align: 'center', children: [
            { props: 'isShuguan', label: '是否涵盖', align: 'center', width: 100 },
            { props: 'sgIncome', label: '收入(元)', align: 'center', width: 100 }
          ]
        },
        {
          label: '数安', align: 'center', children: [
            { props: 'isShuan', label: '是否涵盖', align: 'center', width: 100 },
            { props: 'saIncome', label: '收入(元)', align: 'center', width: 100 }
          ]
        },
        {
          label: '数用', align: 'center', children: [
            { props: 'isShuyong', label: '是否涵盖', align: 'center', width: 100 },
            { props: 'syIncome', label: '收入(元)', align: 'center', width: 100 }
          ]
        },
        {
          label: '数存', align: 'center', children: [
            { props: 'isShucun', label: '是否涵盖', align: 'center', width: 100 },
            { props: 'scunIncome', label: '收入(元)', align: 'center', width: 100 }
          ]
        },
        {
          label: '数采', align: 'center', children: [
            { props: 'isShucai', label: '是否涵盖', align: 'center', width: 100 },
            { props: 'scIncome', label: '收入(元)', align: 'center', width: 100 }
          ]
        }
      ]
    }
  },
  created() {
  },
  // 监听属性类似于data概念
  computed: {},
  //属性改变监听
  watch: {},
  mounted() {
    this.resetQuery()
  },
  methods: {
    mergeRows({ row, column, rowIndex, columnIndex }) {
      console.log(row, column, rowIndex, columnIndex,'************132')
      return {
        rowspan: 0,
        colspan: 0
      }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.formQuery = { monthId: this.getPrevMonth() }
      this.$refs.uniTable.resetPage()
      this.resetForm('queryForm')
      this.conditionQuery()
    },
    // 条件查询
    conditionQuery() {
      this.$refs.uniTable.loadData({
        ...this.formQuery,
        'monthId': this.formQuery['monthId'].replace('-', '')
      })
    },
    changeValue(v) {
      this.monthId = dayjs(v).format('YYYY-MM')
      this.formQuery['monthId'] = dayjs(v).format('YYYY-MM')
      this.conditionQuery()
    },
    exportExcelEnvent() {
      this.exportExcel(
        '/sanquan/ls/export',
        {
          ...this.formQuery,
          'monthId': this.formQuery['monthId'].replace('-', '')
        },
        `六数统计导出.xlsx`
      )
    },
    getNowMonth() {
      var date = new Date()
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      month = month > 9 ? month : '0' + month
      return year + '-' + month
    },
    getPrevMonth() {
      var arr = this.getNowMonth().split('-')
      var year = arr[0] //获取当前日期的年份
      var month = arr[1] //获取当前日期的月份

      var year2 = year
      var month2 = parseInt(month) - 1
      if (month2 === 0) {
        //1月的上一月是前一年的12月
        year2 = parseInt(year2) - 1
        month2 = 12
      }

      if (month2 < 10) {
        //10月之前都需要补0
        month2 = '0' + month2
      }
      return year2 + '-' + month2
    }
  }
}
</script>
<style scoped lang="scss">

</style>

