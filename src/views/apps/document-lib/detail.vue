<!--<template>-->
<!--  <div class="app-container" style="height: calc(100vh - 120px)">-->
<!--    <split-pane :min-percent="10" :max-percent="30" :default-percent="25" split="vertical">-->
<!--      <template slot="paneL">-->
<!--        <el-card class="dep-card" wid shadow="never" v-loading="treeLoading">-->
<!--          <el-tabs v-model="activeName" type="card" @tab-click="handleTabClick">-->
<!--            <el-tab-pane label="页面" name="publish"></el-tab-pane>-->
<!--            <el-tab-pane label="草稿" name="draft"></el-tab-pane>-->
<!--          </el-tabs>-->
<!--          <div slot="header" class="clearfix">-->
<!--            <span>{{ this.currentDocumentLibName }}</span>-->
<!--            <el-button-->
<!--              style="float: right; padding: 3px 0"-->
<!--              type="text"-->
<!--              icon="el-icon-refresh"-->
<!--              @click="reloadTree"-->
<!--            >刷新</el-button>-->
<!--            <el-button-->
<!--              style="float: right; padding: 3px 20px"-->
<!--              type="text"-->
<!--              icon="el-icon-s-home"-->
<!--              @click="goBack"-->
<!--            >返回</el-button>-->
<!--          </div>-->
<!--          <el-tree-->
<!--            :props="lazyTreeProps"-->
<!--            :load="loadNode"-->
<!--            lazy-->
<!--            default-expand-all-->
<!--            draggable-->
<!--            @node-drag-end="handleDragEnd"-->
<!--            :expand-on-click-node="false"-->
<!--            ref="asyncTree"-->
<!--            @node-click="handleNodeClick"-->
<!--            node-key="id"-->
<!--            highlight-current-->
<!--          >-->
<!--            <span class="custom-tree-node" slot-scope="{ node, data }">-->
<!--              <el-tooltip :content="node.label" placement="top">-->
<!--                <span class="label">{{ node.label }}</span>-->
<!--              </el-tooltip>-->
<!--              <span>-->
<!--                <el-button-->
<!--                  v-if="isPublish === '1'"-->
<!--                  type="text"-->
<!--                  size="mini"-->
<!--                  @click="() => append(data)"-->
<!--                >新增</el-button>-->
<!--                <el-button-->
<!--                  type="text"-->
<!--                  size="mini"-->
<!--                  @click="() => remove(node, data)"-->
<!--                  v-if="-->
<!--                    data.parent !== '0' &&-->
<!--                    data.createBy === currentLoginUser.userInfo.staffId-->
<!--                  "-->
<!--                >删除</el-button>-->
<!--              </span>-->
<!--            </span>-->
<!--          </el-tree>-->
<!--        </el-card>-->
<!--      </template>-->
<!--      <template slot="paneR">-->
<!--        <el-card class="dep-card" shadow="never">-->
<!--          <el-row :gutter="10" class="mb8">-->
<!--            <el-col :span="1.5">-->
<!--              <el-button-->
<!--                type="primary"-->
<!--                plain-->
<!--                icon="el-icon-edit"-->
<!--                size="mini"-->
<!--                @click="handleEdit"-->
<!--                v-if="-->
<!--                  this.currentDocCreateBy === currentLoginUser.userInfo.staffId-->
<!--                "-->
<!--              >编辑</el-button>-->
<!--            </el-col>-->
<!--          </el-row>-->
<!--          &lt;!&ndash; <p class="editor-view" v-html="viewContent"></p> &ndash;&gt;-->
<!--          <editor-view :value="viewContent" ref="editorView" class="editor-view" />-->
<!--          <div v-if="docInfo.createByName && docInfo.createDate" class="styx-entity-creator">-->
<!--            <span class="creator">-->
<!--              <div-->
<!--                class="avatar-name ng-star-inserted"-->
<!--              >{{ docInfo.createByName }} 创建于 {{ docInfo.createDate }}</div>-->
<!--            </span>-->
<!--            <span class="divide">-->
<!--              <svg-->
<!--                viewBox="0 0 16 16"-->
<!--                xmlns="http://www.w3.org/2000/svg"-->
<!--                fit-->
<!--                height="1em"-->
<!--                width="1em"-->
<!--                preserveAspectRatio="xMidYMid meet"-->
<!--                focusable="false"-->
<!--              >-->
<!--                <g id="hwnormal/eye" stroke-width="1" fill-rule="evenodd">-->
<!--                  <path-->
<!--                    d="M8 3.4c2.823 0 5.04 1.367 7.38 3.637a2.037 2.037 0 0 1 0 2.925C13.04 12.232 10.824 13.6 8 13.6c-2.823 0-5.032-1.364-7.376-3.637a2.037 2.037 0 0 1 0-2.925C2.967 4.764 5.176 3.4 8 3.4zm0 1.2c-2.417 0-4.405 1.228-6.542 3.3a.837.837 0 0 0 0 1.2C3.596 11.173 5.584 12.4 8 12.4s4.413-1.231 6.546-3.3a.837.837 0 0 0 0-1.2C12.412 5.83 10.415 4.6 8 4.6zM8 11a2.5 2.5 0 1 1 0-5 2.5 2.5 0 0 1 0 5zm0-1.2a1.3 1.3 0 1 0 0-2.6 1.3 1.3 0 0 0 0 2.6z"-->
<!--                    id="hw形状结合"-->
<!--                  />-->
<!--                </g>-->
<!--              </svg>-->
<!--              {{ this.docInfo.counter }}-->
<!--            </span>-->
<!--          </div>-->
<!--        </el-card>-->
<!--      </template>-->
<!--    </split-pane>-->

<!--    &lt;!&ndash;文档编辑&ndash;&gt;-->
<!--    <el-drawer-->
<!--      title="添加文章"-->
<!--      :visible.sync="visible"-->
<!--      direction="btt"-->
<!--      size="100%"-->
<!--      :with-header="false"-->
<!--    >-->
<!--      <el-card class="rich-text-main" shadow="never">-->
<!--        <div slot="header">-->
<!--          <el-row :gutter="20" type="flex" align="middle" style="height: 40px">-->
<!--            <el-col :span="6" :offset="8">-->
<!--              <el-input-->
<!--                v-model="docInfo.name"-->
<!--                placeholder="请输入标题(最多50个字)"-->
<!--                maxlength="50"-->
<!--                style="width:520px"-->
<!--                :disabled="useDisabled"-->
<!--              />-->
<!--            </el-col>-->
<!--            <el-col :span="6" :offset="4">-->
<!--              <el-button style="float: right; padding: 3px 0" type="text" @click="cancel">取消</el-button>-->

<!--              <el-button-->
<!--                style="float: right; padding: 3px 0; margin-right: 10px"-->
<!--                type="text"-->
<!--                @click="draftForm"-->
<!--                v-if="isPublish === 0"-->
<!--              >保存草稿</el-button>-->

<!--              <el-button-->
<!--                style="float: right; padding: 3px 0; margin-right: 10px"-->
<!--                type="text"-->
<!--                @click="submitForm"-->
<!--                v-if="isPublish === 0"-->
<!--              >发布</el-button>-->
<!--            </el-col>-->
<!--          </el-row>-->
<!--        </div>-->
<!--        <editor v-if="editorContent" v-model="editorContent" ref="editor" />-->
<!--      </el-card>-->
<!--    </el-drawer>-->
<!--  </div>-->
<!--</template>-->

<!--<script>-->
<!--import {-->
<!--  attachmentDownload,-->
<!--  createDocuments,-->
<!--  deleteDocuments,-->
<!--  documentsTree,-->
<!--  getDocumentInfoById,-->
<!--  updateDocument,-->
<!--} from "@/api/apps/documentLib";-->
<!--import Editor from "@/components/RichText";-->
<!--import EditorView from "@/components/RichText/view";-->

<!--export default {-->
<!--  name: "DocumentLibDetail",-->
<!--  components: {-->
<!--    Editor,-->
<!--    EditorView,-->
<!--  },-->
<!--  data() {-->
<!--    return {-->
<!--      useDisabled: false,-->
<!--      // 遮罩层-->
<!--      loading: true,-->
<!--      // 显示搜索条件-->
<!--      showSearch: true,-->
<!--      submitLoading: false,-->
<!--      // 总条数-->
<!--      total: 0,-->
<!--      // 弹出层标题-->
<!--      title: "",-->
<!--      // 表单参数-->
<!--      docInfo: {},-->
<!--      lazyTreeProps: {-->
<!--        children: "children",-->
<!--        label: "name",-->
<!--        isLeaf: "leaf",-->
<!--      },-->
<!--      treeLoading: false,-->
<!--      selectNodeName: "主页",-->
<!--      selectNode: undefined,-->
<!--      getDeptLoading: false,-->
<!--      loadOu: undefined,-->
<!--      open: false,-->
<!--      defaultOrgId: this.$store.getters.orgId,-->
<!--      viewContent: `加载中，请稍后。。。`,-->
<!--      editorContent: `加载中，请稍后。。。`,-->
<!--      currentDocId: "0", // 当前选中的文档Id-->
<!--      currentDocCreateBy: "0", // 当前选中的文档创建者Id-->
<!--      currentDocumentLibId:-->
<!--        this.$route.params.documentLibId || this.$route.query.documentLib,-->
<!--      currentDocumentLibName:-->
<!--        this.$route.params.documentLibName || this.$route.query.documentLibName,-->
<!--      isPublish: "1", //默认展示已发布文档，0代表草稿-->
<!--      activeName: "publish",-->
<!--      visible: false,-->
<!--      isAppend: false,-->
<!--    };-->
<!--  },-->
<!--  computed: {-->
<!--    currentLoginUser() {-->
<!--      return this.$store.state.user;-->
<!--    },-->
<!--  },-->
<!--  watch: {-->

<!--  },-->
<!--  created() {},-->
<!--  methods: {-->
<!--    // 节点单击事件-->
<!--    handleNodeClick(data) {-->
<!--      this.selectNode = data;-->
<!--      this.selectNodeName = data.name;-->
<!--      this.currentDocId = data.id;-->
<!--      this.currentDocCreateBy = data.createBy;-->
<!--      if (this.isAppend) {-->
<!--        this.visible = true;-->
<!--      } else {-->
<!--        getDocumentInfoById({-->
<!--          id: this.currentDocId,-->
<!--          documentLibId: this.currentDocumentLibId,-->
<!--          publish: this.isPublish,-->
<!--        }).then((res) => {-->
<!--          this.viewContent =-->
<!--            this.isPublish === "0" ? res.data.draftContent : res.data.content;-->
<!--          this.docInfo = res.data;-->
<!--        });-->
<!--      }-->
<!--    },-->
<!--    //懒加载树形结构的文档-->
<!--    loadNode(node, resolve) {-->
<!--      if (!node.data) {-->
<!--        this.treeLoading = true;-->
<!--      }-->
<!--      documentsTree({-->
<!--        id: node.data ? node.data.id : 0,-->
<!--        documentLibId: this.currentDocumentLibId,-->
<!--        type: node.data ? "down" : "current",-->
<!--        publish: this.isPublish,-->
<!--      }).then((response) => {-->
<!--        if (response.data.length > 0) {-->
<!--          //查看草稿-->
<!--          if (this.isPublish === "0") {-->
<!--            this.viewContent = response.data[0].draftContent;-->
<!--          }-->
<!--          if (response.data[0].parent === "0") {-->
<!--            this.viewContent = response.data[0].content;-->
<!--            this.currentDocId = response.data[0].id;-->
<!--            this.docInfo = response.data[0];-->
<!--            this.currentDocCreateBy = response.data[0].createBy;-->
<!--          }-->
<!--        }-->
<!--        resolve(response.data);-->
<!--        this.treeLoading = false;-->
<!--      });-->
<!--    },-->
<!--    // 重新加载树形结构的组织-->
<!--    reloadTree() {-->
<!--      this.$refs.asyncTree.root.loaded = false;-->
<!--      this.$refs.asyncTree.root.expand();-->
<!--    },-->
<!--    handleEdit() {-->
<!--      if (this.docInfo.draftContent !== null && this.docInfo.publish === "0") {-->
<!--        this.useDisabled = false;-->
<!--        this.editorContent = this.docInfo.draftContent;-->
<!--      } else if (-->
<!--        this.docInfo.publish === "1" &&-->
<!--        this.docInfo.content !== null-->
<!--      ) {-->
<!--        this.useDisabled = true;-->
<!--        this.editorContent = this.docInfo.content;-->
<!--      }-->
<!--      this.visible = true;-->
<!--    },-->
<!--    handleTabClick(tab, event) {-->
<!--      if (tab.name === "draft") {-->
<!--        this.isPublish = "0";-->
<!--      }-->
<!--      if (tab.name === "publish") {-->
<!--        this.isPublish = "1";-->
<!--      }-->
<!--      this.currentDocCreateBy = "";-->
<!--      this.viewContent = "";-->
<!--      this.reset();-->
<!--      this.reloadTree();-->
<!--    },-->
<!--    append(data) {-->
<!--      this.docInfo = {-->
<!--        id: undefined,-->
<!--        name: "",-->
<!--        parent: data.id,-->
<!--        content: undefined,-->
<!--        contentText: undefined,-->
<!--        publish: undefined,-->
<!--        sort: undefined,-->
<!--      };-->
<!--      this.editorContent = " ";-->
<!--      this.isAppend = true;-->
<!--    },-->
<!--    remove(node, data) {-->
<!--      console.log(node, data);-->
<!--      if (data.parent === "0") {-->
<!--        this.msgError("根节点文档无法删除。");-->
<!--        return;-->
<!--      } else {-->
<!--        if (node.childNodes !== undefined && node.childNodes.length > 0) {-->
<!--          this.msgError("当前文档存在下级文档，不允许删除。");-->
<!--          return;-->
<!--        }-->
<!--        deleteDocuments({-->
<!--          id: data.id,-->
<!--          documentLibId: data.documentLibId,-->
<!--          publish: this.isPublish,-->
<!--        }).then((res) => {-->
<!--          if (res.success) {-->
<!--            this.$confirm(-->
<!--              "是否确认删除名称为（" + data.name + "）的文档库?",-->
<!--              "警告",-->
<!--              {-->
<!--                confirmButtonText: "确定",-->
<!--                cancelButtonText: "取消",-->
<!--                type: "warning",-->
<!--              }-->
<!--            ).then(() => {-->
<!--              setTimeout(() => {-->
<!--                this.msgSuccess("删除成功");-->
<!--                this.reset();-->
<!--                this.reloadTree();-->
<!--                this.viewContent = "";-->
<!--                this.currentDocCreateBy = "";-->
<!--              }, 700);-->
<!--            });-->
<!--          } else {-->
<!--            this.msgError("根节点文档无法删除。");-->
<!--          }-->
<!--        });-->
<!--      }-->
<!--    },-->
<!--    /** 提交按钮 */-->
<!--    submitForm() {-->
<!--      this.isPublish = "1";-->
<!--      this.realSubmitCheck();-->
<!--    },-->
<!--    //保存草稿-->
<!--    draftForm() {-->
<!--      this.isPublish = "0";-->
<!--      if (this.docInfo.publish === "1") {-->
<!--        this.$message.error("已发布的内容不允许保存为草稿！");-->
<!--        return;-->
<!--      }-->
<!--      this.realSubmitCheck();-->
<!--    },-->
<!--    realSubmitCheck() {-->
<!--      if (this.docInfo.name === "") {-->
<!--        this.$message.error("文档标题不能为空！");-->
<!--        return;-->
<!--      }-->
<!--      this.realSubmit();-->
<!--    },-->
<!--    realSubmit() {-->
<!--      if (this.docInfo.id !== undefined) {-->
<!--        this.docInfo.content = this.editorContent;-->
<!--        this.docInfo.contentText = this.docInfo.content.replace(/<.*?>/g, "");-->
<!--        updateDocument({-->
<!--          ...this.docInfo,-->
<!--          attachmentList: this.attachmentArr,-->
<!--          publish: this.isPublish,-->
<!--        }).then((response) => {-->
<!--          this.msgSuccess("修改成功");-->
<!--          // this.visible = false;-->
<!--          getDocumentInfoById({-->
<!--            id: this.currentDocId,-->
<!--            documentLibId: this.currentDocumentLibId,-->
<!--            publish: this.isPublish,-->
<!--          }).then((res) => {-->
<!--            this.docInfo = res.data;-->
<!--          });-->
<!--        });-->
<!--      } else {-->
<!--        //提取富文本框内的纯文本  并赋值给空字符串-->
<!--        this.docInfo.content = this.editorContent;-->
<!--        this.docInfo.contentText = this.docInfo.content.replace(/<.*?>/g, "");-->
<!--        createDocuments({-->
<!--          ...this.docInfo,-->
<!--          parent: this.currentDocId,-->
<!--          documentLibId: this.currentDocumentLibId,-->
<!--          attachmentList: this.attachmentArr,-->
<!--          publish: this.isPublish,-->
<!--        }).then((response) => {-->
<!--          if (response.code === "1") {-->
<!--            this.msgSuccess("新增成功");-->
<!--            this.visible = false;-->
<!--            this.isAppend = false;-->
<!--          }-->
<!--        });-->
<!--      }-->
<!--      setTimeout(() => {-->
<!--        if (this.isPublish === "1") {-->
<!--          this.activeName = "publish";-->
<!--        }-->
<!--        if (this.isPublish === "0") {-->
<!--          this.activeName = "draft";-->
<!--        }-->
<!--        this.cancel();-->
<!--        this.reloadTree();-->
<!--      }, 700);-->
<!--    },-->
<!--    // 取消按钮-->
<!--    cancel() {-->
<!--      this.isAppend = false;-->
<!--      this.visible = false;-->
<!--    },-->
<!--    // 表单重置-->
<!--    reset() {-->
<!--      this.docInfo = {-->
<!--        name: undefined,-->
<!--        parent: undefined,-->
<!--        content: undefined,-->
<!--        contentText: undefined,-->
<!--        publish: undefined,-->
<!--        sort: undefined,-->
<!--      };-->
<!--      this.isAppend = false;-->
<!--    },-->
<!--    downLoadHandler(item) {-->
<!--      attachmentDownload(item.fileId).then((res) => {-->
<!--        // 文件导出-->
<!--        if (!res) {-->
<!--          return;-->
<!--        }-->
<!--        let url = window.URL.createObjectURL(new Blob([res]));-->
<!--        let link = document.createElement("a");-->
<!--        link.style.display = "none";-->
<!--        link.href = url;-->
<!--        link.setAttribute("download", item.originalName);-->
<!--        document.body.appendChild(link);-->
<!--        link.click();-->
<!--      });-->
<!--    },-->
<!--    handleClose(done) {-->
<!--      done();-->
<!--    },-->
<!--    handleDragEnd(draggingNode, dropNode, dropType, ev) {-->
<!--      if (this.isPublish === "0") {-->
<!--        return;-->
<!--      }-->
<!--      //拖动的Node-->
<!--      let draggingNodeId = draggingNode.data.id;-->
<!--      //当做新的parent-->
<!--      let dropNodeId = dropNode.data.id;-->
<!--      updateDocument({-->
<!--        id: draggingNodeId,-->
<!--        documentLibId: this.currentDocumentLibId,-->
<!--        parent: dropNodeId,-->
<!--        name: draggingNode.data.name,-->
<!--        publish: this.isPublish,-->
<!--      }).then((response) => {-->
<!--        if (response.code === "1") {-->
<!--          this.msgSuccess("修改成功");-->
<!--          this.open = false;-->
<!--          this.content = this.docInfo.content;-->
<!--        }-->
<!--      });-->
<!--    },-->
<!--    goBack() {-->
<!--      window.history.back();-->
<!--    },-->
<!--  },-->
<!--};-->
<!--</script>-->
<!--<style lang="scss" scoped>-->
<!--.dep-card {-->
<!--  min-height: calc(100vh - 120px);-->
<!--}-->

<!--.custom-tree-node {-->
<!--  flex: 1;-->
<!--  display: flex;-->
<!--  align-items: center;-->
<!--  justify-content: space-between;-->
<!--  font-size: 14px;-->
<!--  padding-right: 8px;-->
<!--  .label {-->
<!--    display: block;-->
<!--    width: 150px;-->
<!--    white-space: nowrap;-->
<!--    text-overflow: ellipsis;-->
<!--    overflow: hidden;-->
<!--  }-->
<!--}-->

<!--.styx-entity-creator {-->
<!--  display: flex;-->
<!--  align-items: center;-->
<!--  color: #aaa;-->
<!--  font-size: 0.75rem;-->
<!--  padding-bottom: 20px;-->
<!--}-->

<!--.creator {-->
<!--  display: flex;-->
<!--  align-items: center;-->
<!--}-->

<!--.divide {-->
<!--  margin-left: 30px;-->
<!--  position: relative;-->
<!--}-->
<!--</style>-->
