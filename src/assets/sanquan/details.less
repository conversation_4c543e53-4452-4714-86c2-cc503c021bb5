.x-app-container{
  width: 100vw;
  height: 100%;
  background: url(../../assets/images/sw/bg.png) no-repeat center;
  background-size: 100%;
  .wrapper{
    width: 75%;
    height: 800px;
    max-width: 1440px;
    // min-width: 1200px;
    margin: 0 auto;
  }
}
.portal-body {
  font-size: 0.125rem;
  width: 100%;
  height: 100%;
  .box-bg{
    background-image: linear-gradient(180deg, #F5F9FF 0%, #FFFFFF 100%);
    border: 2px solid rgba(255,255,255,1);
    box-shadow: 0px 4px 12px 0px rgba(11,38,110,0.08);
    border-radius: 6px;
  }
  .menus-body {
    padding: 0.09rem;
    width: 100%;

    .link-user-info {
      text-align: center;
      vertical-align: middle;
      height: 0.4rem;
      line-height: 0.4rem;
      background: #ffffff;
      border: 1px #f5f5f5 solid;
      color: #0f9af2;
      font-size: 0.125rem;
    }

    .menus {
      list-style: none;
      margin: 0.1rem 0 0 0;
      padding: 0.09rem;
      background: #ffffff;

      .menus-item {
        list-style: none;

        .title {
          background: rgb(232, 231, 231);
          height: 0.28rem;
          line-height: 0.26rem;
          padding: 0 0.09rem;
          border-bottom: 0.015rem solid rgb(139, 142, 148);
          border-radius: 0.02rem 0.02rem 0 0;

          .text {
            background: rgb(232, 231, 231);
            border-bottom: 0.020rem solid rgb(59, 116, 240);
            display: inline-block;
          }
        }

        ul {
          list-style: none;
          margin: 0;
          padding: 0 0.2rem;
          background: #f5f5f5;

          li {
            list-style: none;
            // height: 0.23rem;
            // line-height: 0.23rem;
            width: 1.25rem;

            .content {
              width: 1.25rem;
              height: 0.45rem;
              line-height: 0.45rem;
              margin: 0.08rem 0 0.08rem 0;
              padding-left: 0.1rem;
              border: 0.01rem solid #0f9af2;
              border-radius: 0.04rem;
              background-color: #dcebf4;
              display: flex;

              .icon-star {
                color: #1791dc;
                margin-right: 0.08rem;
                font-size: 0.135rem;
              }

            }

          }

          li:hover {
            color: #0f9af2;
          }
        }
      }
    }
  }

  //潜在机会列表
  .opportunity-list {
    padding: 0.09rem;
    width: 100%;
    height: 100%;

    .link-user-info {
      text-align: center;
      vertical-align: middle;
      height: 0.4rem;
      line-height: 0.4rem;
      background: #ffffff;
      border: 1px #f5f5f5 solid;
      color: #0f9af2;
      font-size: 0.125rem;
    }

    .list-query {
      margin-top: 0.25rem;
      padding-top: 0.09rem;
      min-height: 0.4rem;
      line-height: 0.4rem;
      background: #ffffff;
      border: 1px #f5f5f5 solid;
      color: #0f9af2;

      .a-row .el-form-item__label {
        font-size: 0.125rem !important;
      }
    }

    .list-table {
      margin: 0.25rem 0 30px 0;
      padding-top: 0.1rem;
      min-height: 1.5rem;
      background: #ffffff;
      border: 1px #f5f5f5 solid;
      color: #0f9af2;
      font-size: 0.125rem;
      .list-table-page {
        margin-top: 0.09rem;
      }
    }
  }

  //潜在机会详情
  .opportunity-details {
    padding: 0rem;
    width: 100%;
    height: 100%;
    
    .detail-top {
      width: 100%;
      height: 1.5667rem;
      border-radius: 10px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      margin-top: .2rem;
      margin-bottom: .2rem;

      .detail-type2 {
        height: 100%;
        width: 2rem;
        color: #FFFFFF;
        font-size: 0.23rem;
        text-align: center;
       
        .hy-img {
          margin-top: 0.2rem;
          width: .9333rem;
          height: .9333rem;
        }

        .hy-text{
          font-family: PingFangSC-Medium;
          font-size: .135rem;
          color: #FFFFFF;
          letter-spacing: 0;
          font-weight: 500;
        }

      }

      .detail-info {
        width: 100%;
        padding: 0.1rem 0.3rem;

        .company_name {
          width:8rem;
          height: .5rem;
          line-height: 0.5rem;
          font-weight: bold;
          overflow:hidden;
          white-space:nowrap;
          text-overflow:ellipsis;
          font-family: PingFangSC-Medium;
          font-size: .1833rem;
          color: #FFFFFF;
          letter-spacing: 0;
          font-weight: 500;
          position: relative;
        }

        .company_name::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 0.4rem;
          height: .025rem;
          background-color: #FFFFFF;
          border-radius: 3px;
        }

        .user-info {
          line-height: 0.35rem;
          font-size: 0.135rem;
          width: 33.33%;
          white-space: nowrap; 
          overflow: hidden; 
          text-overflow: ellipsis; 

          .info-text{
            font-family: PingFangSC-Regular;
            font-size: 0.135rem;
            color: #FFFFFF;
            letter-spacing: 0;
            font-weight: 400;
          }

        }
        
       
      }

      .detail-pie {
        width: 6rem;
        height: 5rem;
      }
    }

    .push-produce {
      margin-top: 0.1rem;
      min-height: 3rem;
    }
    .divider-span {
      font-size: 0.125rem;
      font-weight: bold;
    }
    // 搜索
    .search-body {
      margin: 0.3rem 0 0.3rem 0;
      height: 0.8rem;
      background-color: #f5f9ff;
      border: 0.02rem solid #FFFFFF;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
    .divider {
      display: inline-block;
      width: 5px;
      height: 1.5em;
      margin: 0 8px;
      vertical-align: middle;
      background-color: #0f9af2;
      position: relative;
    }
    .proportion {
      background-color: #f5f9ff;
      padding: 10px;
      border: 0.02rem solid #FFFFFF;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
    .recommendation-list {
      height:auto;
      background-color: #f5f9ff;
      padding: 10px;
      border: 0.02rem solid #FFFFFF;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
    #myecharts {
      width: 100%;
      height: 300px;
      margin-top: -35px;
      margin-bottom: 0.1rem;
    }
  }

  .button-like {
    display: inline-block;
    padding: 6px 12px;
    background-color: #ccc;
    color: #000000 65%;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    cursor: pointer;
    margin-right: 10px;
    width: 1.2417rem;
    height: .325rem;
    font-size: .1333rem;
    text-align: center;
  }
  
  .button-like.active {
    background-color: #3370FF;
    color: #fff;
  }
  
  .button-like.active::before {
    background: #EBEDFA;
  }
  


  //潜在机会反馈


  //产品管理列表

  //产品详情

  //产品新增

//工单反馈页面

}
