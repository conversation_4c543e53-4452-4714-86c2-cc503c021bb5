<template>
  <div class="app-container" style="height: calc(100vh - 120px)">
    <split-pane
      v-on:resize="resize"
      :min-percent="10"
      :max-percent="30"
      :default-percent="15"
      split="vertical"
    >
      <template slot="paneL">
        <el-card class="dep-card" shadow="never" v-loading="treeLoading" body-style>
          <div slot="header" class="clearfix">
            <span>组织</span>
            <el-button
              style="float: right; padding: 3px 0"
              type="text"
              icon="el-icon-refresh"
              @click="reloadTree"
            >刷新</el-button>
            <el-form
              style="margin-top: 20px; margin-bottom: -20px"
              v-if="$store.getters.customParam.userType === 'admin'"
            >
              <el-form-item label="租户：">
                <el-select
                  v-model="queryParams.tenantId"
                  style="width: 120px"
                  filterable
                  remote
                  :remote-method="getTenantList"
                  :loading="getTenantLoading"
                  @change="tenantChange"
                >
                  <el-option
                    v-for="item in tenantList"
                    :key="item.tenantId"
                    :label="item.tenantName"
                    :value="item.tenantId"
                  />
                </el-select>
              </el-form-item>
            </el-form>
          </div>
          <el-tree
            :props="lazyTreeProps"
            :load="loadNode"
            lazy
            :expand-on-click-node="false"
            ref="asyncTree"
            @node-click="handleNodeClick"
            :default-expanded-keys="[defaultOrgId]"
            node-key="orgId"
            highlight-current
          ></el-tree>
        </el-card>
      </template>
      <template slot="paneR">
        <el-card class="dep-card" shadow="never">
          <div slot="header" class="clearfix">
            <span>{{ selectNodeName }}</span>
            <el-button
              style="float: right; padding: 3px 0"
              type="text"
              icon="el-icon-refresh"
              @click="allUser"
            >全部人员</el-button>
          </div>
          <el-form
            :model="queryParams"
            ref="queryForm"
            :inline="true"
            v-show="showSearch"
            label-width="68px"
          >
            <el-form-item label="用户名称" prop="staffName">
              <el-input
                v-model="queryParams.staffName"
                placeholder="请输入用户名称"
                clearable
                size="small"
                style="width: 240px"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="手机号" prop="cellphone">
              <el-input
                v-model="queryParams.cellphone"
                placeholder="请输入手机号"
                clearable
                size="small"
                style="width: 240px"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="queryParams.email"
                placeholder="请输入邮箱"
                clearable
                size="small"
                style="width: 240px"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>

          <el-row :gutter="10" class="mb8">
<!--            <el-col :span="1.5">-->
<!--              <el-button-->
<!--                type="primary"-->
<!--                plain-->
<!--                icon="el-icon-plus"-->
<!--                size="mini"-->
<!--                @click="handleAdd"-->
<!--                v-hasPermi="['system:user:add']"-->
<!--              >新增</el-button>-->
<!--            </el-col>-->
<!--            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>-->
          </el-row>

          <el-table v-loading="loading" :data="userList">
            <el-table-column
              label="用户编号"
              align="left"
              key="staffId"
              prop="staffId"
              v-if="columns[0].visible"
            />
<!--            <el-table-column-->
<!--              label="组织"-->
<!--              align="left"-->
<!--              key="orgName"-->
<!--              prop="orgName"-->
<!--              v-if="columns[1].visible"-->
<!--              :show-overflow-tooltip="true"-->
<!--            />-->
<!--            <el-table-column-->
<!--              label="组织"-->
<!--              align="left"-->
<!--              key="orgName"-->
<!--              v-if="columns[1].visible">-->
<!--              <template slot-scope="scope">-->
<!--                <el-popover trigger="hover" placement="top">-->
<!--                  <p>{{scope.row.allOrgName }}</p>-->
<!--                  <div slot="reference" class="name-wrapper">-->
<!--                    <el-tag size="medium">{{formatEllipsis(scope.row.orgName)}}</el-tag>-->
<!--                  </div>-->
<!--                </el-popover>-->
<!--              </template>-->
<!--            </el-table-column>-->
            <el-table-column
              label="组织"
              align="left"
              key="orgName"
              v-if="columns[1].visible">
              <template slot-scope="scope">
                <el-popover trigger="hover"  title="组织详情" placement="top">
                  <p v-html="scope.row.allOrgName.replaceAll('->','&lt;br /&gt;')"></p>
                  <div slot="reference" class="name-wrapper">
                    <div size="medium">{{formatEllipsis(scope.row.orgName)}}</div>
                  </div>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column
              label="用户名称"
              align="left"
              key="staffName"
              prop="staffName"
              v-if="columns[2].visible"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="登录名称"
              align="left"
              key="loginName"
              prop="loginName"
              v-if="columns[3].visible"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="用户类型"
              align="center"
              key="staffKind"
              prop="staffKind"
              v-if="columns[4].visible"
              :formatter="staffKindFormat"
            >
              <template slot-scope="scope">
                <el-tag
                  effect="plain"
                  :type="
                    selectDictRemark(
                      staffKindOptions, scope.row.staffKind
                    )
                  "
                >
                  {{
                  selectDictLabel(
                  staffKindOptions, scope.row.staffKind
                  )
                  }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              label="用户状态"
              align="left"
              key="staffStatus"
              prop="staffStatus"
              v-if="columns[5].visible"
              width="120"
              :formatter="userStatusFormat"
            >
              <template slot-scope="scope">
                <el-tag
                  :type="
                    selectDictLabel(
                      userStatusOptions,
                      scope.row.staffStatus
                    ) === '正常'
                      ? ''
                      : 'danger'
                  "
                >
                  {{
                  selectDictLabel(userStatusOptions, scope.row.staffStatus)
                  }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              label="手机号码"
              align="left"
              key="cellphone"
              prop="cellphone"
              v-if="columns[6].visible"
              :formatter="phoneHide"
              width="120"
            />
            <el-table-column
              label="邮箱"
              align="left"
              key="email"
              prop="email"
              v-if="columns[7].visible"
            />
            <el-table-column
              label="操作"
              align="center"
              width="260"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <div v-if="scope.row.staffStatus === 'valid'">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-user"
                    :loading="
                      getUserLoading && scope.row.staffId === loadUserId
                    "
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['system:user:edit']"
                  >详情</el-button>
                  <el-tooltip
                    v-if="scope.row.staffId !== 1"
                    content="租户管理员不允许删除"
                    :disabled="scope.row.staffId !== queryParams.tenantAdminId"
                    placement="top"
                  >
                    <span style="margin-left: 10px; margin-right: 15px">
                      <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['system:user:remove']"
                        :disabled="
                          scope.row.staffId === queryParams.tenantAdminId
                        "
                      >删除</el-button>
                    </span>
                  </el-tooltip>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-key"
                    @click="handleResetPwd(scope.row)"
                    v-hasPermi="['system:user:resetPwd']"
                  >重置</el-button>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-lock"
                    @click="handleStatusChange(scope.row)"
                  >冻结</el-button>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-circle-check"
                    @click="openAddRole(scope.row)"
                  >赋权</el-button>
                </div>
                <div v-if="scope.row.staffStatus === 'invalid'">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-unlock"
                    @click="handleStatusChange(scope.row)"
                  >启用</el-button>
                </div>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-s-cooperation"
                  @click="handlePosition(scope.row)"
                >岗位</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-card>
      </template>
    </split-pane>

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body  v-dialogDrag>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="用户名称" prop="staffName">
              <el-input v-model="form.staffName" placeholder="请输入用户名称" maxlength="64" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归属组织" prop="orgName">
              <el-input v-model="form.orgName" placeholder="请选择归属组织" disabled>
                <template slot="append">
                  <el-link @click="$refs.treeSelect.open()">选择</el-link>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item hidden>
              <el-input v-model="form.orgId"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="登录名称" prop="loginName">
              <el-input
                v-model="form.loginName"
                placeholder="请输入登录名称"
                maxlength="64"
                :disabled="title === '修改用户'"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户类型" prop="staffKind">
              <el-select v-model="form.staffKind">
                <el-option
                  v-for="item in staffKindOptions"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
<!--          <el-col :span="12">-->
<!--            <el-form-item label="手机号码" prop="cellphone">-->
<!--              <el-input v-model="form.cellphone" placeholder="请输入手机号码" maxlength="11" />-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--          <el-col :span="12">-->
<!--            <el-form-item label="邮箱" prop="email">-->
<!--              <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />-->
<!--            </el-form-item>-->
<!--          </el-col>-->
          <el-col :span="12">
            <el-form-item label="租户">
              <el-input v-model="form.tenantName" placeholder="租户" maxlength="50" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="staffStatus">
              <el-radio-group v-model="form.staffStatus">
                <el-radio
                  v-for="dict in userStatusOptions"
                  :key="dict.dictValue"
                  :label="dict.dictValue"
                >{{ dict.dictLabel }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.staffStatus === 'invalid'">
            <el-form-item label="冻结原因" prop="attra">
              <el-input v-model="form.attra" placeholder="请输入冻结原因" maxlength="200" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="saveLoading">确 定</el-button>
        <el-button @click="cancel('edit')">取 消</el-button>
      </div>
      <dept-select
        ref="treeSelect"
        name
        value
        :tenant-id="queryParams.tenantId"
        @selected="selected"
      />
    </el-dialog>

    <!-- 岗位调整对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="openPositionDialog"
      width="600px"
      append-to-body
      v-dialogDrag
    >
      <el-form ref="positionForm" :model="positionForm" :rules="positionRules" label-width="80px">
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="用户">
              <el-input v-model="positionForm.staffName" placeholder="用户" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="归属组织" prop="orgName">
              <el-input v-model="positionForm.orgName" placeholder="请选择归属组织" disabled>
                <template slot="append">
                  <el-link @click="$refs.treeSelect.open()">选择</el-link>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item hidden>
              <el-input v-model="positionForm.orgId"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="角色选择">
              <el-button @click="openPositionRole">选择</el-button>
              <div v-if="positionRoleErrorMsg" style="margin: -30px 10px -10px 80px">
                <span class="error-msg">请选择角色</span>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="岗位类别" prop="staffOrgType">
              <el-radio-group v-model="positionForm.staffOrgType">
                <el-radio label="T">兼职</el-radio>
                <el-radio label="J">借调</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPositionForm" :loading="saveLoading">确 定</el-button>
        <el-button @click="cancel('position')">取 消</el-button>
      </div>
      <dept-select
        ref="treeSelect"
        name
        value
        :tenant-id="queryParams.tenantId"
        @selected="selected"
      />
    </el-dialog>

    <!-- 角色赋权对话框 -->
    <transfer-table
      ref="transferTable"
      modelName="角色赋权"
      @loadData="loadData"
      :listRole="listRole"
      :listProp="{
        key: 'roleName',
        name: '角色名称',
      }"
      :selectedProp="{
        key: 'roleName',
        name: '角色名称',
      }"
      @add="addUserRole"
      @remove="delUserRole"
    />
    <!-- 岗位赋权对话框 -->
    <transfer-table
      ref="transferPositionTable"
      modelName="角色赋权"
      @loadData="loadPositionRoleData"
      :listRole="listRole"
      :listProp="{
        key: 'roleName',
        name: '角色名称',
      }"
      :selectedProp="{
        key: 'roleName',
        name: '角色名称',
      }"
      @add="addPositionRole"
      @remove="delPositionRole"
    />
  </div>
</template>

<script>
import {
  listUser,
  getUser,
  delUser,
  addPortaluser,
  updateUser,
  resetUserPwd,
  findUserRoles,
  addUserRoles,
  delUserRoleInfo,
  enableOrDisablePortaluser,
  addStaffOrgAndRole,
} from "/src/api/system/user";
import { list as tenantList } from "@/api/system/tenant";
import { findRoleListByScope } from "@/api/system/role";
import { treeselect, getDept } from "@/api/system/dept";
import DeptSelect from "@/components/DeptSelect";
import TransferTable from "@/components/TransferTable";
import { mapState } from 'vuex'

export default {
  name: "User",
  components: { DeptSelect, TransferTable },
  data() {
    return {
      // 遮罩层
      loading: true,
      getTenantLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 状态数据字典
      statusOptions: [],
      //用户类型
      userStatusOptions: [],
      staffKindOptions: [],
      userTypeOptions: [],
      // 表单参数
      form: {
        staffStatus: "valid",
      },
      positionForm: {
        orgId: "",
        orgName: "",
        staffId: "",
        staffName: "",
        staffOrgType: "T",
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        email: undefined,
        cellphone: undefined,
        orgId: this.$store.getters.orgId,
        staffOrgType: "F",
        tenantId: this.$store.getters.customParam.tenantId,
        tenantAdminId: this.$store.getters.customParam.tenantAdminId,
        tenantName: this.$store.getters.customParam.tenantName,
      },
      // 列信息
      columns: [
        { key: 0, label: `用户编号`, visible: false },
        { key: 1, label: `组织`, visible: true },
        { key: 2, label: `用户名称`, visible: true },
        { key: 3, label: `登录名称`, visible: true },
        { key: 4, label: `用户类型`, visible: true },
        { key: 5, label: `用户状态`, visible: true },
        { key: 6, label: `手机号码`, visible: true },
        { key: 7, label: `邮箱`, visible: false },
      ],
      // 表单校验
      rules: {
        staffName: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        loginName: [
          { required: true, message: "请输入登录名", trigger: "blur" },
        ],
        orgName: [{ required: true, message: "请选择组织", trigger: "blur" }],
        staffStatus: [
          { required: true, message: "请选择用户状态", trigger: "blur" },
        ],
        staffKind: [
          { required: true, message: "请选择用户类型", trigger: "blur" },
        ],
        attra: [{ required: true, message: "请输入冻结原因", trigger: "blur" }],
        email: [
          {
            type: "email",
            message: "请输入正确的邮箱地址",
            trigger: ["blur", "change"],
          },
        ],
        cellphone: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
      },
      positionRules: {
        orgName: [{ required: true, message: "请选择组织", trigger: "blur" }],
        staffOrgType: [
          { required: true, message: "请选择岗位类别", trigger: "change" },
        ],
      },
      lazyTreeProps: {
        children: "children",
        label: "orgName",
        isLeaf: "leaf",
      },
      treeLoading: false,
      selectNodeName: "全部人员",
      selectNode: undefined,
      getUserLoading: false,
      loadUserId: undefined,
      loadStaffOrgId: undefined,
      roleOpen: false,
      userRoles: [],
      rolesData: [],
      listRole: [],
      open: false,
      openPositionDialog: false,
      userList: [],
      tenantList: [],
      positionRoleList: [],
      saveLoading: false,
      defaultOrgId: this.$store.getters.orgId,
      positionRoleErrorMsg: true,
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.user.userInfo,
      pageSetting: (state) => state.app.pageSetting,
    }),
  },
  created() {
    if(this.hasPermission('system_admin')){
      this.queryParams.orgId='0037';
      this.defaultOrgId='0037';
    }
    this.getTenantList();
    this.getList();
    this.getDicts("sys_normal_disable").then((response) => {
      this.statusOptions = response.data;
    });
    this.getDicts("sys_user_sex").then((response) => {
      this.sexOptions = response.data;
    });
    this.getDicts("user_status").then((response) => {
      this.userStatusOptions = response.data;
    });
    this.getDicts("staff_kind").then((response) => {
      this.staffKindOptions = response.data;
    });
  },
  methods: {
    hasPermission(permission) {
      if(permission){
        return this.userInfo.authorities.indexOf(permission) >= 0
      }
      else{
        return true;
      }
    },
    // 手机号做脱敏处理
    phoneHide(row, column){
        let reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/; // 定义手机号正则表达式
        let phone = row.cellphone.replace(reg, '$1****$2');
        return phone; // 185****6696
    },
    // typeFormat(row, column) {
    //   return this.selectDictLabel(this.typeOptions, row.noticeType);
    // },
    formatEllipsis(str = '', limitLen = 20) {
      let
        len = 0,
        reg = /[\x00-\xff]/, //半角字符的正则匹配
        strs = str.split(''),
        inx = strs.findIndex(s => {
          len += reg.test(s) ? 1 : 2
          if (len > limitLen) return true
        })
      return inx === -1 ? str : str.substr(0, inx) + '...'
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(
        (response) => {
          this.userList = response.data.records;
          this.total = response.data.total;
          this.loading = false;
        }
      );
    },
    getTenantList(tenantName) {
      this.getTenantLoading = true;
      let query = {};
      if (tenantName !== undefined && tenantName !== "") {
        query.tenantName = tenantName;
        query.tenantId = undefined;
      } else {
        query.tenantId = this.queryParams.tenantId;
      }
      tenantList(query)
        .then((response) => {
          this.tenantList = response.data;
        })
        .finally(() => {
          this.getTenantLoading = false;
        });
    },
    userStatusFormat(row, column) {
      return this.selectDictLabel(this.userStatusOptions, row.staffStatus);
    },
    staffKindFormat(row, column) {
      return this.selectDictLabel(this.staffKindOptions, row.staffKind);
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.orgId = data.orgId;
      this.selectNode = data;
      this.selectNodeName = data.orgName;
      this.getList();
    },
    allUser() {
      const node = this.$refs.asyncTree.root;
      node.loaded = false;
      node.expand();
      this.queryParams.orgId = this.defaultOrgId;
      this.selectNodeName = "全部人员";
      this.selectNode = undefined;
      this.getList();
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text = row.staffStatus === "valid" ? "冻结" : "启用";
      if (row.staffStatus === "valid") {
        this.$prompt(
          '确认要"' + text + '""' + row.staffName + '"用户吗？请输入冻结原因！',
          "警告",
          {
            confirmButtonText: text,
            cancelButtonText: "取消",
            type: "warning",
            inputValidator: (value) => {
              if (!value) {
                return false;
              }
              return true;
            },
            inputErrorMessage: "请输入冻结原因!",
            beforeClose: (action, instance, done) => {
              if (action === "confirm") {
                instance.confirmButtonLoading = true;
                instance.confirmButtonText = text + "中...";
                enableOrDisablePortaluser({
                  staffId: row.staffId,
                  staffStatus:
                    row.staffStatus === "valid" ? "invalid" : "valid",
                  attra: instance.inputValue,
                })
                  .then((response) => {
                    done();
                    if (response.success) {
                      this.msgSuccess(text + "成功");
                      this.getList();
                    } else {
                      this.$message.error(response.message);
                    }
                    instance.confirmButtonLoading = false;
                  })
                  .catch((e) => {
                    done();
                    instance.confirmButtonLoading = false;
                  });
              } else {
                done();
              }
            },
          }
        ).catch(function (r) {
          // this.$message.error(r);
        });
      } else {
        this.$confirm(
          '确认要"' + text + '""' + row.staffName + '"用户吗?',
          "警告",
          {
            confirmButtonText: text,
            cancelButtonText: "取消",
            type: "warning",
            beforeClose: (action, instance, done) => {
              if (action === "confirm") {
                instance.confirmButtonLoading = true;
                instance.confirmButtonText = text + "中...";
                enableOrDisablePortaluser({
                  staffId: row.staffId,
                  staffStatus:
                    row.staffStatus === "vaild" ? "invalid" : "valid",
                })
                  .then((response) => {
                    done();
                    if (response.success) {
                      this.msgSuccess(text + "成功");
                      this.getList();
                    } else {
                      this.$message.error(response.message);
                    }
                    instance.confirmButtonLoading = false;
                  })
                  .catch((e) => {
                    done();
                    instance.confirmButtonLoading = false;
                  });
              } else {
                done();
              }
            },
          }
        ).catch(function () {});
      }
    },
    // 取消按钮
    cancel(type) {
      if (type === "edit") {
        this.open = false;
      } else if (type === "position") {
        this.openPositionDialog = false;
      }
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        staffId: undefined,
        orgId: undefined,
        staffName: undefined,
        cellphone: undefined,
        email: undefined,
        staffStatus: "valid",
        orgName: "",
        tenantName: undefined,
      };
      this.resetForm("form");
      this.resetForm("positionForm");
      this.positionRoleList = [];
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      if (this.selectNode) {
        this.form = {
          ...this.form,
          orgId: this.selectNode.orgId,
          orgName: this.selectNode.orgName,
        };
        this.open = true;
        this.title = "添加用户";
      } else {
        this.userTypeOptions = [];
        this.open = true;
        this.title = "添加用户";
      }
      this.form.tenantName = this.queryParams.tenantName;
      this.form.tenantId = this.queryParams.tenantId;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const userid = row.staffId;
      this.loadUserId = userid;
      this.getUserLoading = true;
      getUser(userid)
        .then((response) => {
          getDept(response.data.orgId).then((r) => {
            this.getUserLoading = false;
            this.loadUserId = undefined;
            if (response.data) {
              this.form = {
                ...this.form,
                ...response.data,
                orgName: r.data.orgName,
              };
              this.open = true;
              this.title = "用户详情";
            } else {
              this.$message.error("数据异常！");
            }
          });
        })
        .catch((e) => {
          this.getUserLoading = false;
          this.loadUserId = undefined;
          this.$message.error("数据异常！");
        });
    },
    handlePosition(row) {
      this.reset();
      this.positionForm.staffOrgType = "T";
      const staffId = row.staffId;
      this.loadUserId = staffId;
      getUser(staffId)
        .then((response) => {
          this.getUserLoading = false;
          this.loadUserId = undefined;
          if (response.data) {
            this.positionForm = {
              staffId: response.data.staffId,
              staffName: response.data.staffName,
              staffOrgType: "T",
            };
            this.openPositionDialog = true;
            this.title = "新增岗位";
          } else {
            this.$message.error("数据异常！");
          }
        })
        .catch((e) => {
          this.loadUserId = undefined;
          this.$message.error("数据异常！");
        });
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      this.$prompt('请输入 ' + row.loginName + ' 的新密码', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(({ value }) => {
        resetUserPwd(row.staffId, value).then((response) => {
          this.msgSuccess("修改成功");
        });
      }).catch(() => {});
    },
    /** 提交按钮 */
    submitForm: function () {
      // this.$refs["form"].validate((valid) => {
      //   if (valid) {
      //     this.saveLoading = true;
      //     if (this.form.staffId !== undefined) {
      //       this.form.passwd = undefined;
      //       delete this.form.displayName;
      //       delete this.form.menus;
      //       delete this.form.permissions;
      //       delete this.form.permissionsStr;
      //       delete this.form.roles;
      //       delete this.form.staffOrgs;
      //       updateUser(this.form).then((response) => {
      //         this.saveLoading = false;
      //         if (response.success) {
      //           this.msgSuccess("修改成功");
      //           this.open = false;
      //           this.getList();
      //         } else {
      //           this.$message.error(response.message);
      //         }
      //       });
      //     } else {
      //       addPortaluser(this.form).then((response) => {
      //         this.saveLoading = false;
      //         if (response.success) {
      //           this.msgSuccess("新增成功");
                this.open = false;
                // this.getList();
      //         } else {
      //           this.$message.error(response.message);
      //         }
      //       });
      //     }
      //   }
      // });
    },
    submitPositionForm: function () {
      this.positionForm.roleIds = this.positionRoleList.map(
        (item) => item.roleId
      );
      this.positionRoleErrorMsg = true;
      this.positionRoleErrorMsg = this.positionRoleList.length <= 0;
      this.$refs["positionForm"].validate((valid) => {
        if (valid && !this.positionRoleErrorMsg) {
          this.saveLoading = true;
          if (this.positionForm.staffId !== undefined) {
            this.positionForm.roleIds = this.positionRoleList.map(
              (item) => item.roleId
            );
            addStaffOrgAndRole(this.positionForm).then((response) => {
              this.saveLoading = false;
              if (response.success) {
                this.msgSuccess("设置成功");
                this.openPositionDialog = false;
                this.getList();
              } else {
                this.$message.error(response.message);
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const staffId = row.staffId;
      this.$confirm(
        '是否确认删除用户"' + row.staffName + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delUser({ staffId: staffId });
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    //懒加载树形结构的组织
    loadNode(node, resolve) {
      if (!node.data) {
        this.treeLoading = true;
      }
      treeselect({
        orgId: node.data ? node.data.orgId : this.defaultOrgId,
        queryType: node.data ? "down" : "current",
        tenantId: this.queryParams.tenantId,
      }).then((response) => {
        resolve(response.data);
        this.treeLoading = false;
      });
    },
    // 重新加载树形结构的组织
    reloadTree() {
      if (this.selectNode) {
        const node = this.$refs.asyncTree.getNode(this.selectNode);
        node.childNodes = [];
        node.loaded = false;
        node.expand();
      } else {
        this.$refs.asyncTree.root.loaded = false;
        this.$refs.asyncTree.root.expand();
      }
    },
    // 组织选择的回调
    selected(data) {
      this.form = {
        ...this.form,
        orgId: data.orgId,
        orgName: data.orgName,
      };
      this.positionForm = {
        ...this.positionForm,
        orgId: data.orgId,
        orgName: data.orgName,
      };
      this.$refs.treeSelect.close();
    },
    roleClose() {
      this.roleOpen = false;
    },
    loadData() {
      findRoleListByScope(this.queryParams).then((r) => {
        this.getDicts("scope").then((response) => {
          const newData = response.data.map((v) => {
            v["children"] = r.data.filter((i) => i.roleScope === v.dictValue);
            v["roleName"] = v.dictLabel;
            v["roleId"] = v.dictDataId;
            v["isOp"] = true;
            return v;
          });
          this.$refs.transferTable.setData(newData);
        });
      });
      findUserRoles(this.loadStaffOrgId).then((r) => {
        this.$refs.transferTable.setRightData({ data: r.data.havelist });
      });
    },
    openAddRole(row) {
      this.loadUserId = row.staffId;
      this.loadStaffOrgId = row.staffOrgId;
      this.$refs.transferTable.open();
    },
    addUserRole(row) {
      addUserRoles(this.loadStaffOrgId, row.roleId).then((r) => {
        this.$refs.transferTable.reload();
      });
    },
    delUserRole(row) {
      delUserRoleInfo(this.loadStaffOrgId, row.roleId).then((r) => {
        this.$refs.transferTable.reload();
      });
    },
    resize() {
      console.log("resize");
    },
    tenantChange(tenantId) {
      if (tenantId !== "") {
        const tenantObj = this.tenantList.find(
          (item) => item.tenantId === tenantId
        );
        this.queryParams.tenantName = tenantObj.tenantName;
        this.queryParams.tenantAdminId = tenantObj.tenantAdminId;
      }
      this.$refs.asyncTree.root.loaded = false;
      this.$refs.asyncTree.root.expand();
      this.selectNode = undefined;
      this.queryParams.orgId = undefined;
      this.handleQuery();
    },
    openPositionRole() {
      if (
        this.positionForm.orgId === "" ||
        typeof this.positionForm.orgId === "undefined"
      ) {
        this.$message.error("请先选择对应的归属组织！");
      } else {
        this.loadUserId = this.positionForm.staffId;
        this.loadStaffOrgId = this.positionForm.orgId;
        this.$refs.transferPositionTable.open();
      }
    },
    loadPositionRoleData() {
      findRoleListByScope(this.queryParams).then((r) => {
        this.getDicts("scope").then((response) => {
          const newData = response.data.map((v) => {
            v["children"] = r.data.filter((i) => i.roleScope === v.dictValue);
            v["roleName"] = v.dictLabel;
            v["roleId"] = v.dictDataId;
            v["isOp"] = true;
            return v;
          });
          this.$refs.transferPositionTable.setData(newData);
        });
      });
      this.$refs.transferPositionTable.setRightData({
        data: this.positionRoleList,
      });
    },
    addPositionRole(row) {
      const result = this.positionRoleList.find(
        (item) => item.roleId === row.roleId
      );
      if (typeof result !== "undefined") {
        this.$message.error(`请勿重复添加角色：${row.roleName}`);
        this.$refs.transferPositionTable.reload();
      } else {
        this.positionRoleList.push(row);
        this.$refs.transferPositionTable.reload();
      }
      this.positionRoleErrorMsg = this.positionRoleList.length <= 0;
    },
    delPositionRole(row) {
      this.positionRoleList.splice(
        this.positionRoleList.findIndex((item) => item.roleId === row.roleId),
        1
      );
      this.$refs.transferPositionTable.reload();
      this.positionRoleErrorMsg = this.positionRoleList.length <= 0;
    },
  },
};
</script>
<style lang="scss" scoped>
.dep-card {
  min-height: calc(100vh - 120px);
}
.error-msg {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
}
</style>
