<template>
  <el-card
    :class="this.bgColor === true ?'notice-card':'unshow-notice-card'"
    v-loading="noticeLoading"
    shadow="never"
  >
    <div slot="header" v-if="show">
      <span class="card-title">
        <div class="title">
          <span> <i class="icon iconfont icon-gonggao"></i>广告-轮播图 </span>
        </div>
      </span>
    </div>
    <div class="home-card-body">
      <el-carousel indicator-position="outside" v-if="list.imgList" >
        <el-carousel-item v-for="(item,index) in list.imgList" :key="index" >
          <img :src="item.coverUrl" alt="" style="width:100%;height:100%" >
        </el-carousel-item>
      </el-carousel>
      <img v-else  src="../../../../assets/images/nopicture.png" alt="" style="display:block;width:100%;height:100%">
      <div class="title">
        <span>广告</span>
      </div>  
    </div>
  </el-card>
</template>

<script>
import { indexData } from "@/api/poster/index.js";

export default {
  props:['item'],
  name: "posterCarousel",
  data() {
    return {
      //   notice: [],
      noticeLoading: false,
      list: [],
      form: {
        queryNum: 3,
      },
      show:"",
      bgColor:"",
    };
  },
  created() {
    this.getList()
    this.show = this.item.showTitle;
    this.bgColor = this.item.showBgColor;
  },
  watch:{
    'item.showTitle' :function(val) {
      this.show = val
    },
    'item.showBgColor' :function(val) {
      this.bgColor = val
    }
  },
  methods: {
    moreExt(type) {
      window.open(`/portal/home/<USER>/${type}`);
    },

    getList() {
      indexData(this.form).then((res) => {
        var news = res.data
        news.forEach(item=>{
          if(item.advertisingType === "carousel") {
            this.list = item
          }
        })
      });
    },
  },
};
</script>

<style scoped lang="scss">
.home-card-body {
  padding-top: 20px;
  position: relative;
  .title {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 40px;
    height: 25px;
    background: #d1dae1;
    color: #4f7c87;
    text-align: center;
    line-height: 25px;
  }
  .el-carousel__item h3 {
    color: #475669;
    font-size: 14px;
    opacity: 0.75;
    line-height: 250px;
    margin: 0;
  }

  .el-carousel__item:nth-child(2n) {
    background-color: #99a9bf;
  }

  .el-carousel__item:nth-child(2n + 1) {
    background-color: #d3dce6;
  }
}
.notice-card {
  height: 100%;
  background: #fff;
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 0;
    }
  }
  .notice {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    padding: 4px 0;
    font-size: 14px;
    .notice-title {
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: nowrap;
      flex-basis: 70%;
    }
    .notice-time {
      flex-basis: 20%;
      text-align: right;
    }
  }
}
.unshow-notice-card {
  height: 100%;
  border: 0px;
  background: #f5f9fa;
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 0;
    }
  }
  .notice {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    padding: 4px 0;
    font-size: 14px;
    .notice-title {
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: nowrap;
      flex-basis: 70%;
    }
    .notice-time {
      flex-basis: 20%;
      text-align: right;
    }
  }
}
.moreButtom {
  display: flex;
  justify-content: flex-end;
}
</style>
