import axios from "axios";
import { Notification, MessageBox, Message, Loading } from "element-ui";
import store from "@/store";
import { getToken } from "@/utils/auth";
import errorCode from "@/utils/errorCode";
import doEncrypt from "@/utils/crypto";
import { blobValidate } from "@/utils/validate";
import { saveAs } from 'file-saver';
let downloadLoadingInstance;

axios.defaults.headers["Content-Type"] = "application/json;charset=utf-8";
// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: process.env['VUE_APP_SESSION_TOKEN_URI'],
  // 超时
  timeout: 1000000000000,
});
// request拦截器
service.interceptors.request.use(
  config => {
    // 如果接口地址为login 就在请求头上添加ospf参数
    if (config.url === '/auth/login') {
      let timestamp = new Date().getTime();
      let reversal = sessionStorage.getItem("pgp")
      let news = timestamp + '@@' + reversal.split("").reverse().join("")
      config.headers["ospf"] = doEncrypt(news)
    }
    // 需要放到首行位置,如果是白名单接口会直接返回config 不携带clientId会导致报错
    config.headers["clientId"] = "bVS46ElU";
    // get请求映射params参数
    if (config.method === "get" && config.params) {
      let url = config.url + "?";
      for (const propName of Object.keys(config.params)) {
        const value = config.params[propName];
        var part = encodeURIComponent(propName) + "=";
        if (value !== null && typeof value !== "undefined") {
          if (typeof value === "object") {
            for (const key of Object.keys(value)) {
              let params = propName + "[" + key + "]";
              var subPart = encodeURIComponent(params) + "=";
              url += subPart + encodeURIComponent(value[key]) + "&";
            }
          } else {
            url += part + encodeURIComponent(value) + "&";
          }
        }
      }
      url = url.slice(0, -1);
      config.params = {};
      config.url = url;
    }
    return config;
  },
  error => {
    console.log(error);
    Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  res => {
    // 未设置状态码则默认成功状态
    const code = res.status || 200
    // console.log('status',code)
    // 获取错误信息
    const msg = errorCode[code] || res.data.msg || errorCode["default"];
    if (code === 401 || code === 400) {
      MessageBox.confirm(
        "登录状态已过期，您可以继续留在该页面，或者重新登录",
        "系统提示",
        {
          confirmButtonText: "重新登录",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).then(() => {
        store.dispatch("LogOut").then(() => {
          location.href = `/#/login?redirect=${location.pathname}`;
        });
      });
    } else if (code === 500) {
      Message({
        message: msg,
        type: "error",
      });
      return Promise.reject(new Error(msg));
    } else if (code !== 200) {
      Notification.error({
        title: msg
      });
      return Promise.reject("error");
    } else if (res.data && (res.data.code === "102"
        || res.data.code === "500"
        || res.data.code === "501")) {
      Notification.error({
        title: msg
      });
      return Promise.reject("error");
    } else {
      return res.data;
    }
  },
  error => {
    console.log("err:" + JSON.stringify(error.response.data.message));
    let { message } = error;
    if (message == "Network Error") {
      message = "后端接口连接异常";
    } else if (message.includes("timeout")) {
      message = "系统接口请求超时";
    } else if (message.includes("Request failed with status code")) {
      message = "系统接口" + message.substr(message.length - 3) + "异常";
      if (message.includes('400')) {
        message = "登录超时，请重新登录！"
      } else if (message.includes('401')) {
       // message = "登录失效，请重新登录！"
        store.dispatch("LogOut").then(() => {
          location.href = `/#/login?redirect=${location.pathname}`;
        });
      }
    }
    // Message({
    //   message: message,
    //   type: "error",
    //   duration: 5 * 1000
    // });
    return Promise.reject(error);
  }
);
// 通用下载导出方法
export function download(url, params, filename, config) {
  downloadLoadingInstance = Loading.service({ text: "正在下载数据，请稍候", spinner: "el-icon-loading", background: "rgba(0, 0, 0, 0.7)", })

  return service.post(url, params, {
    // transformRequest: [(params) => { return tansParams(params) }],
    // headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    responseType: 'blob',
    ...config
  }).then(async (data) => {
    const isBlob = blobValidate(data);
    if (isBlob) {
      const blob = new Blob([data])
      saveAs(blob, filename)
    } else {
      const resText = await data.text();
      const rspObj = JSON.parse(resText);
      const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']
      Message.error(errMsg);
    }
    downloadLoadingInstance.close();
  }).catch((r) => {
    console.error(r)
    Message.error('下载文件出现错误，请联系管理员！')
    downloadLoadingInstance.close();
  })
}

export default service;
