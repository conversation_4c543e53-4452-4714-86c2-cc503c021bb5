<template>
  <div
    class="search"
    :style="{
      background: datas.config.backgroundColor,
      border: `1px solid ${datas.config.backgroundColor}`,
    }"
  >
    <!-- 搜索框 -->
    <section
      class="searchs"
      :style="{
        height: datas.config.heights + 'px',
        'justify-content': datas.config.textPosition === 0 ? 'left' : 'center',
        background: datas.config.borderColor,
        'border-radius': datas.config.borderRadius === 0 ? '0px' : '10px',
      }"
    >
      <div class="search-left">
        <i class="el-icon-search" :style="{ color: datas.config.textColor }" />
        <span :style="{ color: datas.config.textColor }">搜索</span>
      </div>
      <!-- 扫一扫 -->
      <div
        class="sweep"
        v-show="datas.config.sweep"
        :style="{ color: datas.config.textColor }"
      >
        <span>扫一扫</span>
      </div>
    </section>

    <!-- 删除组件 -->
    <slot name="deles" />
  </div>
</template>

<script>
export default {
  name: 'search',
  props: {
    datas: Object,
  },
}
</script>

<style scoped lang="less">
.search {
  position: relative;
  /* 搜索框 */
  .searchs {
    position: relative;
    width: 345px;
    min-height: 28px;
    margin: 5px auto;
    display: flex;
    align-items: center;
    font-size: 14px;
    .search-left {
      display: flex;
      align-items: center;
    }
    .sweep {
      position: absolute;
      right: 10px;
    }
    i {
      margin: 0 5px;
    }
  }
}
</style>
