<template>
  <div v-if="show" class="uni-noticebar" :style="{ backgroundColor: backgroundColor }" @click="onClick">
    <i v-if="showIcon === true || showIcon === 'true'" class="el-icon-bell" :style="{ color: color }" />
    <div ref="textBox" class="uni-noticebar__content-wrapper"
          :class="{'uni-noticebar__content-wrapper--scrollable':scrollable, 'uni-noticebar__content-wrapper--single':!scrollable && (single || moreText)}">
      <div :id="elIdBox" class="uni-noticebar__content"
            :class="{'uni-noticebar__content--scrollable':scrollable, 'uni-noticebar__content--single':!scrollable && (single || moreText)}">
        <span :id="elId" ref="animationEle" class="uni-noticebar__content-text"
              :class="{'uni-noticebar__content-text--scrollable':scrollable,'uni-noticebar__content-text--single':!scrollable && (single || showGetMore)}"
              :style="{color:color, width:wrapWidth+'px', 'animationDuration': animationDuration, '-webkit-animationDuration': animationDuration ,animationPlayState: webviewHide?'paused':animationPlayState,'-webkit-animationPlayState':webviewHide?'paused':animationPlayState, animationDelay: animationDelay, '-webkit-animationDelay':animationDelay}">{{text}}</span>
      </div>
    </div>
    <div v-if="showGetMore === true || showGetMore === 'true'" class="uni-noticebar__more uni-cursor-point"
          @click="clickMore">
      <span v-if="moreText.length > 0" :style="{ color: moreColor }" class="uni-noticebar__more-text">{{ moreText }}</span>
      <i v-else class="el-icon-right" :style="{ color: moreColor }" />
    </div>
    <div class="uni-noticebar-close uni-cursor-point" v-if="(showClose === true || showClose === 'true') && (showGetMore === false || showGetMore === 'false')">
      <i class="el-icon-close" :style="{ color: color }" @click="close" />
    </div>
  </div>
</template>

<script>
/**
 * NoticeBar 自定义导航栏
 * @description 通告栏组件
 * @tutorial https://ext.dcloud.net.cn/plugin?id=30
 * @property {Number} speed 文字滚动的速度，默认100px/秒
 * @property {String} text 显示文字
 * @property {String} backgroundColor 背景颜色
 * @property {String} color 文字颜色
 * @property {String} moreColor 查看更多文字的颜色
 * @property {String} moreText 设置“查看更多”的文本
 * @property {Boolean} single = [true|false] 是否单行
 * @property {Boolean} scrollable = [true|false] 是否滚动，为true时，NoticeBar为单行
 * @property {Boolean} showIcon = [true|false] 是否显示左侧喇叭图标
 * @property {Boolean} showClose = [true|false] 是否显示左侧关闭按钮
 * @property {Boolean} showGetMore = [true|false] 是否显示右侧查看更多图标，为true时，NoticeBar为单行
 * @event {Function} click 点击 NoticeBar 触发事件
 * @event {Function} close 关闭 NoticeBar 触发事件
 * @event {Function} getmore 点击”查看更多“时触发事件
 */

export default {
  name: 'UniNoticeBar',
  emits: ['click', 'getmore', 'close'],
  props: {
    text: {
      type: String,
      default: ''
    },
    moreText: {
      type: String,
      default: ''
    },
    backgroundColor: {
      type: String,
      default: '#FFF9EA'
    },
    speed: {
      // 默认1s滚动100px
      type: Number,
      default: 100
    },
    color: {
      type: String,
      default: '#FF9A43'
    },
    moreColor: {
      type: String,
      default: '#FF9A43'
    },
    single: {
      // 是否单行
      type: [Boolean, String],
      default: false
    },
    scrollable: {
      // 是否滚动，添加后控制单行效果取消
      type: [Boolean, String],
      default: false
    },
    showIcon: {
      // 是否显示左侧icon
      type: [Boolean, String],
      default: false
    },
    showGetMore: {
      // 是否显示右侧查看更多
      type: [Boolean, String],
      default: false
    },
    showClose: {
      // 是否显示左侧关闭按钮
      type: [Boolean, String],
      default: false
    }
  },
  data() {
    const elId = `Uni_${Math.ceil(Math.random() * 10e5).toString(36)}`
    const elIdBox = `Uni_${Math.ceil(Math.random() * 10e5).toString(36)}`
    return {
      textWidth: 0,
      boxWidth: 0,
      wrapWidth: '',
      webviewHide: false,
      elId: elId,
      elIdBox: elIdBox,
      show: true,
      animationDuration: 'none',
      animationPlayState: 'paused',
      animationDelay: '0s'
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initSize()
    })
  },
  methods: {
    initSize() {
    },
    loopAnimation() {

    },
    clickMore() {
      this.$emit('getmore')
    },
    close() {
      this.show = false;
      this.$emit('close')
    },
    onClick() {
      this.$emit('click')
    }
  }
}
</script>
<style lang="scss" >
@import "~@/components/UniUI/uni-scss/index.scss";
@import "~@/components/UniUI/uni-scss/theme.scss";
@import "~@/components/UniUI/uni-scss/variables.scss";
.uni-noticebar {
  display: flex;
  width: 100%;
  box-sizing: border-box;
  flex-direction: row;
  align-items: center;
  padding: 10px 12px;
}

.uni-cursor-point {
  cursor: pointer;
}

.uni-noticebar-close {
  margin-left: 8px;
  margin-right: 5px;
}

.uni-noticebar-icon {
  margin-right: 5px;
}

.uni-noticebar__content-wrapper {
  flex: 1;
  flex-direction: column;
  overflow: hidden;
}

.uni-noticebar__content-wrapper--single {
  line-height: 18px;
}

.uni-noticebar__content-wrapper--single,
.uni-noticebar__content-wrapper--scrollable {
  flex-direction: row;
}

.uni-noticebar__content-wrapper--scrollable {
  position: relative;
  height: 18px;
}

.uni-noticebar__content--scrollable {
  flex: 1;
  display: block;
  overflow: hidden;
}

.uni-noticebar__content--single {
  display: flex;
  flex: none;
  width: 100%;
  justify-content: center;
}

.uni-noticebar__content-text {
  font-size: 14px;
  line-height: 18px;
  word-break: break-all;
}

.uni-noticebar__content-text--single {
  display: block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.uni-noticebar__content-text--scrollable {
  position: absolute;
  display: block;
  height: 18px;
  line-height: 18px;
  white-space: nowrap;
  padding-left: 100%;
  animation: notice 10s 0s linear infinite both;
  animation-play-state: paused;
}

.uni-noticebar__more {
  display: inline-flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  padding-left: 5px;
}

.uni-noticebar__more-text {
  font-size: 14px;
}

@keyframes notice {
  100% {
    transform: translate3d(-100%, 0, 0);
  }
}
</style>
