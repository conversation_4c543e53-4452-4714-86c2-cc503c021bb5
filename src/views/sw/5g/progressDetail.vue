<template>
    <div class="app-container" v-loading.fullscreen.lock="progressLoading" element-loading-text="数据加载中..."
        element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.7)">
        <!-- 基本信息卡片 -->
        <el-card class="box-card" shadow="hover">
            <div class="title-section">
                <div class="title-with-back">
                    <span>5G专网端到端流程进度详情</span>
                    <el-button icon="el-icon-back" type="text" size="small" class="back-btn"
                        @click="goBack">返回</el-button>
                </div>
            </div>
            <el-form :model="baseInfo" label-width="120px" class="info-form">
                <el-row :gutter="30">
                    <el-col :span="6">
                        <el-form-item label="地市：" class="bold-label-item">
                            <span>{{ baseInfo.city }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="投资申请人：" class="bold-label-item">
                            <span>{{ baseInfo.user }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="客户名称：">
                            <span>{{ baseInfo.client }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="投资决策单号：" label-cl>
                            <span>{{ baseInfo.number }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="30">
                    <el-col :span="6">
                        <el-form-item label="流程实例号：">
                            <span>{{ baseInfo.inc }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="投资申请时间：">
                            <span>{{ baseInfo.stime }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="项目名称：">
                            <span>{{ baseInfo.project }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="5G场景：">
                            <span>{{ baseInfo.changjing }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </el-card>
        <!-- 流程进度展示 -->
        <div class="progress-section">
            <el-card shadow="hover" class="progress-card">
                <el-steps :active="5" finish-status="success" align-center>
                    <el-step class="progress-step" title="投资申请"
                        @click.native="currentStep >= 1 && scrollToSection('投资申请')"
                        v-custom-icon="{ icon: currentStep >= 1 ? flowsuccessIcon : flowwaitIcon, active: currentStep >= 1 }"></el-step>
                    <el-step class="progress-step" title="投资决策"
                        @click.native="currentStep >= 2 && scrollToSection('投资决策')"
                        v-custom-icon="{ icon: currentStep >= 2 ? flowsuccessIcon : flowwaitIcon, active: currentStep >= 2 }"></el-step>
                    <el-step class="progress-step" title="项目实施"
                        @click.native="currentStep >= 3 && scrollToSection('项目实施')"
                        v-custom-icon="{ icon: currentStep >= 3 ? flowsuccessIcon : flowwaitIcon, active: currentStep >= 3 }"></el-step>
                </el-steps>
            </el-card>
        </div>

        <!-- 详细进度信息 -->
        <el-card v-for="(section, index) in progressData" :key="index" class="progress-detail-card" shadow="hover"
            v-loading="detailLoading" element-loading-text="加载详情..." element-loading-spinner="el-icon-loading"
            v-if="section.title === currentSection">
            <div class="section-title">{{ section.title }}</div>
            <div class="timeline-section">
                <el-timeline>
                    <el-timeline-item v-for="(item, itemIndex) in section.steps" :key="itemIndex"
                        :type="item.pendingStatusRemark === '已完成' ? 'success' : 'primary'" size="large">
                        <template slot="dot">
                            <img :src="item.pendingStatusRemark === '已完成' ? timelinesuccessIcon : timelinewaitIcon"
                                class="custom-timeline-icon" />
                        </template>
                        <div class="timeline-content">
                            <div class="step-title">{{ item.stepName }}</div>
                            <div class="step-info">
                                <el-row :gutter="20">
                                    <el-col :span="8">
                                        <div class="info-line">
                                            <span class="info-label">任务到达时间：</span>
                                            <span>{{ item.startTime }}</span>
                                        </div>
                                    </el-col>
                                    <el-col :span="8">
                                        <div class="info-line">
                                            <span class="info-label">处理人：</span>
                                            <span>{{ item.userName }}</span>
                                        </div>
                                    </el-col>
                                    <el-col :span="8">
                                        <div class="info-line">
                                            <span class="info-label">处理人电话：</span>
                                            <span>{{ item.userTel}}</span>
                                        </div>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="8">
                                        <div class="info-line">
                                            <span class="info-label">任务处理时间：</span>
                                            <span>{{ item.endTime }}</span>
                                        </div>
                                    </el-col>
                                    <el-col :span="8">
                                        <div class="info-line">
                                            <span class="info-label">处理说明：</span>
                                            <span>{{ item.notion }}</span>
                                        </div>
                                    </el-col>
                                    <el-col :span="8">
                                        <div class="info-line status-line">
                                            <span class="info-label">环节状态：</span>
                                            <span
                                                :class="['status', item.pendingStatusRemark === '已完成' ? 'success' : 'processing']">
                                                {{ item.pendingStatusRemark }}
                                            </span>
                                        </div>
                                    </el-col>
                                </el-row>
                            </div>
                        </div>
                    </el-timeline-item>
                </el-timeline>
            </div>
        </el-card>
    </div>
</template>
<script>
    import { queryWorkFlowDetail } from "@/api/sw/5g/progress";
    export default {
        name: 'Private5GProgressDetail',
        data() {
            return {
                currentStep: 0,
                currentSection: '',
                baseInfo: {},
                progressData: [],
                progressLoading: false,
                detailLoading: false,
                flowsuccessIcon: require('@/assets/images/sw/private5gProgress/flowsuccess.png'),
                flowwaitIcon: require('@/assets/images/sw/private5gProgress/flowwait.png'),
                timelinesuccessIcon: require('@/assets/images/sw/private5gProgress/timelinesuccess.png'),
                timelinewaitIcon: require('@/assets/images/sw/private5gProgress/timelinewait.png'),
            }
        },
        directives: {
            customIcon: {
                inserted: function (el, binding) {
                    const iconElement = el.querySelector('.el-step__icon');
                    if (iconElement) {
                        while (iconElement.firstChild) {
                            iconElement.removeChild(iconElement.firstChild);
                        }
                        iconElement.style.backgroundColor = '#ffffff';
                        iconElement.style.border = 'none';
                        const img = document.createElement('img');
                        img.src = binding.value.icon;
                        img.style.width = '122px';
                        img.style.height = '91px';
                        img.style.cursor = binding.value.active ? 'pointer' : 'not-allowed';
                        img.style.position = 'absolute';
                        img.style.top = '50%';
                        img.style.left = '50%';
                        img.style.transform = 'translate(-50%, -50%)';
                        img.style.zIndex = '2';
                        img.style.borderRadius = '50%';
                        iconElement.style.position = 'relative';
                        iconElement.appendChild(img);
                    }
                },
                update: function (el, binding) {
                    const iconElement = el.querySelector('.el-step__icon');
                    if (iconElement) {
                        while (iconElement.firstChild) {
                            iconElement.removeChild(iconElement.firstChild);
                        }
                        iconElement.style.backgroundColor = '#ffffff';
                        iconElement.style.border = 'none';
                        const img = document.createElement('img');
                        img.src = binding.value.icon;
                        img.style.width = '122px';
                        img.style.height = '91px';
                        img.style.cursor = binding.value.active ? 'pointer' : 'not-allowed';
                        img.style.position = 'absolute';
                        img.style.top = '50%';
                        img.style.left = '50%';
                        img.style.transform = 'translate(-50%, -50%)';
                        img.style.zIndex = '2';
                        img.style.borderRadius = '50%';
                        iconElement.style.position = 'relative';
                        iconElement.appendChild(img);
                    }
                }
            }
        },
        methods: {
            goBack() {
                this.$router.push({ name: 'Private5GProgress' });
            },
            scrollToSection(title) {
                this.currentSection = title;
            },
            getProgressDetail() {
                const progressInfo = JSON.parse(sessionStorage.getItem('Private5GProgressData'));
                try {
                    this.baseInfo = {
                        city: progressInfo.city,
                        user: progressInfo.user,
                        client: progressInfo.client,
                        number: progressInfo.number,
                        inc: progressInfo.inc,
                        stime: progressInfo.stime,
                        project: progressInfo.project,
                        changjing: progressInfo.changjing
                    }
                    this.progressLoading = true;
                    this.detailLoading = true;
                    queryWorkFlowDetail(progressInfo).then(res => {
                        if (res && res.data) {
                            this.progressData = res.data;
                            this.currentStep = Array.isArray(res.data) ? res.data.length : 0;
                            if (Array.isArray(res.data) && res.data.length > 0) {
                                this.currentSection = res.data[res.data.length - 1].title;
                            }
                            this.$forceUpdate();
                        }
                        this.progressLoading = false;
                        this.detailLoading = false;
                    }).catch(error => {
                        console.error('获取进度详情API错误：', error);
                        this.progressLoading = false;
                        this.detailLoading = false;
                    })
                } catch (error) {
                    console.error('获取进度详情失败：', error)
                    this.progressLoading = false;
                    this.detailLoading = false;
                }
            },
        },
        mounted() {
            this.getProgressDetail();
        }
    }
</script>

<style lang="scss" scoped>
    .app-container {
        padding: 20px;
        background-color: #f5f7fa;

        .box-card {
            width: 100%;
            margin-top: 0px;
            margin-bottom: 25px;
            border-radius: 8px;
        }

        .title-section {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 24px;
            padding-bottom: 15px;
            border-bottom: 1px solid #ebeef5;

            .title-with-back {
                display: flex;
                align-items: center;
                justify-content: space-between;

                .back-btn {
                    color: #ffffff;
                    background-color: #02a7f0;
                    padding: 8px 15px;
                    border-radius: 4px;
                    margin-left: 15px;

                    &:hover {
                        opacity: 0.9;
                    }
                }
            }
        }

        .info-form {
            font-size: 16px;

            .el-form-item {
                margin-bottom: 18px;
            }

            ::v-deep .el-form-item__label {
                font-weight: bold;
            }
        }

        .progress-section {
            font-size: 0px;
            margin: 25px 0 30px;

            .progress-card {
                padding: 30px 20px;
                border-radius: 8px;
            }

            ::v-deep .el-card__body {
                padding: 40px 10px 10px 10px;
            }

            ::v-deep .el-step__title {
                margin-top: 30px;
                color: #02a7f0;
                font-size: 20px;
                font-weight: bold;
            }

            ::v-deep .el-step__line {
                width: 100%;
                padding: 0px 10%;
                background-color: white;
            }

            ::v-deep .el-step__line-inner {
                border-color: #02a7f0;
                width: 100% !important;
                transition: none !important;
                border-width: 2px;
            }
        }

        .progress-detail-card {
            margin-bottom: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

            .section-title {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 20px;
                padding-bottom: 15px;
                border-bottom: 1px solid #ebeef5;
            }

            .timeline-section {
                padding: 0 20px;
            }

            .custom-timeline-icon {
                width: 74px;
                height: 50px;
                position: absolute;
                transform: translate(38%, 20%);
            }

            ::v-deep .el-timeline-item {
                margin-bottom: 50px;
            }

            ::v-deep .el-timeline-item__tail {
                background-color: #02a7f0;
                width: 3px;
                margin-top: 35px;
            }

            .timeline-content {
                padding: 0px 15px;

                .step-title {
                    font-size: 18px;
                    font-weight: 600;
                    margin-bottom: 15px;
                }

                .step-info {
                    padding: 10px 0px;
                    background-color: #fafafa;
                    border-radius: 6px;

                    .el-row {
                        margin-bottom: 20px;
                    }

                    .info-line {
                        margin-bottom: 10px;
                        font-size: 16px;
                        display: flex;
                        align-items: flex-start;

                        &.description-line {
                            margin-top: 15px;
                        }

                        &.status-line {
                            margin-top: 5px;
                        }

                        .info-label {
                            margin-right: 8px;
                            min-width: 90px;
                            font-weight: 500;
                        }
                    }

                    .status {
                        &.success {
                            color: #67c23a;
                            font-weight: 500;
                        }

                        &.processing {
                            color: #409eff;
                            font-weight: 500;
                        }
                    }
                }
            }
        }
    }
</style>