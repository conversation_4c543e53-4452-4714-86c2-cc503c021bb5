import request from '@/utils/request'

// 查询客户树
export function getFindAllTree() {
  return request({
    url: '/sanquan/roster/customerHierarchy/findAllTree',
    method: 'get'
  })
}
// 分页列表
export function infoDel(data) {
  return request({
    url: '/sanquan/customer/info/delete',
    method: 'get',
    params: data
  })
}
// 新增/编辑
export function addOrUpdate(data) {
  return request({
    url: '/sanquan/customer/info/addOrUpdate',
    method: 'post',
    data
  })
}
// 详情
export function getFindDetail(id) {
  return request({
    url: '/sanquan/customer/info/findDetail',
    method: 'get',
    params: {
      id
    }
  })
}
// 编辑回显数据查询
export function findOne(id) {
  return request({
    url: '/sanquan/customer/info/findOne',
    method: 'get',
    params: {
      id
    }
  })
}

// 删除
export function getPageList(data) {
  return request({
    url: '/sanquan/customer/info/findPage',
    method: 'get',
    params: data
  })
}
// 查询行业信息
export function getIndustry(data) {
  return request({
    url: '/sanquan/product/findIndustry',
    method: 'get',
    params: data
  })
}

// 获取市信息
export function getProvName(data) {
  return request({
    url: '/sanquan/dimProvCityCode/findListByProvName',
    method: 'get',
    params: data
  })
}

// 获取区县信息
export function getCityName(data) {
  return request({
    url: '/sanquan/dimProvCityCode/findList',
    method: 'get',
    params: data
  })
}

// 获取名单制客户
export function getCustomer(data) {
  return request({
    url: '/sanquan/roster/customerHierarchy/findList',
    method: 'get',
    params: data
  })
}

// 根据城市信息查询名单制客户信息
export function getCustomerByCityName(data) {
  return request({
    url: '/sanquan/roster/customerHierarchy/findTreeByCityName',
    method: 'get',
    params: data
  })
}

// 获取联通通讯未认领数据
export function userRelations(data) {
  return request({
    url: '/sanquan/customer/userRelations/findPage',
    method: 'get',
    params: data
  })
}
// 获取算网数智未认领数据
export function projectOrderRelations(data) {
  return request({
    url: '/sanquan/customer/projectOrderRelations/findPage',
    method: 'get',
    params:data
  })
}
// 获取客户经理
export function getCustomerManagert(data) {
  return request({
    url: '/sanquan/customer/mapping/findCustomerManager',
    method: 'post',
    data
  })
}

// 根据名单制客户id查询客户经理信息
export function findCustomerManagerInfo(rosterCustomerId) {
  return request({
    url: '/sanquan/customer/info/findCustomerManagerInfo',
    method: 'get',
    params: {
      rosterCustomerId
    }
  })
}

// 获取联通通信
export function getLtDataPage(params) {
  return request({
    url: '/sanquan/customer/userRelationsSelected/findPage',
    method: 'get',
    params
  })
}
// 获取算网数据
export function getSwDataPage(params) {
  return request({
    url: '/sanquan/customer/projectOrderRelationsSelected/findPage',
    method: 'get',
    params
  })
}

// 根据客户经理工号查询树形结构
export function findTreeByManagerId(managerId) {
  return request({
    url: '/sanquan/roster/customerHierarchy/findTreeByManagerId',
    method: 'get',
    params: {
      managerId
    }
  })
}

// 获取算网数据
export function statisticsResult(params) {
  return request({
    url: '/sanquan/statistic/customer/findPage',
    method: 'get',
    params
  })
}


// 获取算网数据
export function findDetailsStatisticsResultPage(params) {
  return request({
    url: '/sanquan/statistic/customer/findDetailsPage',
    method: 'get',
    params
  })
}

// 地市名称
export function findCityGroup() {
  return request({
    url: '/sanquan/statistic/customer/findCityGroup',
    method: 'get'
  })
}

// 地市名称及全省，地市排序
export function findCityGroupAndAllOrder() {
  return request({
    url: '/sanquan/statistic/customer/findCityGroupAndAllOrder',
    method: 'get'
  })
}

// 地市名称
export function findDistrictGroup(params) {
  return request({
    url: '/sanquan/statistic/customer/findDistrictGroup',
    method: 'get',
    params
  })
}


// 地市接口人获取树形结构
export function findTreeByCityPerson(params) {
  return request({
    url: '/sanquan/roster/customerHierarchy/findTreeByCityPerson',
    method: 'get',
    params
  })
}

// 验证客户名称
export function verifyCustomerName(params) {
  return request({
    url: '/sanquan/customer/info/verifyCustomerName',
    method: 'get',
    params
  })
}
