import request from '@/utils/request'

export function findTableList(id) {
  return request({
    url: '/generator/table/findTableList',
    method: 'get',
    params: {
      id
    }
  })
}
export function findTableInfo(params) {
  return request({
    url: '/generator/table/findTableInfo',
    method: 'get',
    params: {
      tableName: params.tableName,
      id: params.datasourceId
    }
  })
}
