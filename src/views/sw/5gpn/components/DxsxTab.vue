<template>
  <div class="tab-content">
    <!-- 5G专网端到端流程进度 -->
    <div class="query-header">
      <span class="query-title">5G专网端到端流程进度</span>
    </div>

    <!-- 查询条件 -->
    <div class="search-wrapper">
      <el-form ref="form" :model="query" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="地市">
              <el-select v-model="query.city" placeholder="全省" clearable size="small" style="width: 100%;">
                <el-option label="全省" value=""></el-option>
                <el-option label="深圳" value="深圳"></el-option>
                <el-option label="广州" value="广州"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请时间">
              <el-date-picker
                v-model="query.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                size="small"
                style="width: 100%;">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户名称">
              <el-input v-model="query.customerName" placeholder="请输入客户名称" clearable size="small"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="流程号">
              <el-input v-model="query.processNo" placeholder="请输入流程号" clearable size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请状态">
              <el-select v-model="query.status" placeholder="全部" clearable size="small" style="width: 100%;">
                <el-option label="全部" value=""></el-option>
                <el-option label="处理中" value="处理中"></el-option>
                <el-option label="已完成" value="已完成"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目名称">
              <el-input v-model="query.projectName" placeholder="请输入项目名称" clearable size="small"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24" style="text-align: right;">
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">查询</el-button>
              <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
              <el-button type="success" icon="el-icon-download" size="small" @click="exportData">导出</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 表格 -->
    <div class="table-wrapper">
      <div class="table-con">
        <el-table :data="tableData" border stripe style="width: 100%">
          <el-table-column prop="processNo" label="投资申请流程号" width="150"></el-table-column>
          <el-table-column prop="applicant" label="申请人" width="100"></el-table-column>
          <el-table-column prop="applyTime" label="申请时间" width="120"></el-table-column>
          <el-table-column prop="city" label="地市" width="100"></el-table-column>
          <el-table-column prop="projectName" label="项目名称" width="200"></el-table-column>
          <el-table-column prop="projectType" label="项目类别" width="120"></el-table-column>
          <el-table-column prop="customerName" label="客户名称" width="180"></el-table-column>
          <el-table-column prop="fiveGScene" label="5G场景" width="120"></el-table-column>
          <el-table-column prop="needAnalysis" label="需求分析" width="150"></el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="query.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="query.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "DxsxTab",
  data() {
    return {
      query: {
        pageNum: 1,
        pageSize: 10,
        city: '',
        dateRange: ['2025-06-03', '2025-06-10'],
        projectName: '',
        processNo: '',
        customerName: '',
        status: ''
      },
      total: 0,
      tableData: [
        {
          processNo: '3415',
          applicant: '张三',
          applyTime: '2025-05-24',
          city: '深圳',
          projectName: '政务专网5G专网建设',
          projectType: '5G专网',
          customerName: '政务委员会委员会',
          fiveGScene: '5G基站专网',
          needAnalysis: '需求分析'
        }
      ]
    }
  },
  methods: {
    handleQuery() {
      console.log('独享随行专网查询', this.query);
      this.$message.success('查询成功');
    },

    resetQuery() {
      this.query = {
        pageNum: 1,
        pageSize: 10,
        city: '',
        dateRange: ['2025-06-03', '2025-06-10'],
        projectName: '',
        processNo: '',
        customerName: '',
        status: ''
      };
    },

    exportData() {
      this.$message.success('导出成功');
    },

    handleSizeChange(val) {
      this.query.pageSize = val;
      this.handleQuery();
    },

    handleCurrentChange(val) {
      this.query.pageNum = val;
      this.handleQuery();
    },

    refreshTab() {
      this.$message.success('刷新成功');
    },

    closeTab() {
      this.$emit('close-tab', 'dxsx');
    }
  }
}
</script>

<style lang="scss" scoped>
.tab-content {
  padding: 0;
  height: 100%;
  background: #f5f7fa;

  .query-header {
    padding: 15px 24px;
    margin: 20px 20px 0 20px;
    background: #fff;
    border-radius: 4px 4px 0 0;
    border: 1px solid #e4e7ed;
    border-bottom: none;
    position: relative;

    .query-title {
      font-size: 14px;
      font-weight: 500;
      color: #303133;
      position: relative;
      padding-left: 12px;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        background: #409eff;
        border-radius: 2px;
      }
    }
  }

  .search-wrapper {
    padding: 20px 24px;
    margin: 0 20px;
    background: #fff;
    border: 1px solid #e4e7ed;
    border-top: none;

    ::v-deep .el-form-item {
      margin-bottom: 18px;
    }

    ::v-deep .el-form-item__label {
      font-weight: 400;
      color: #606266;
      font-size: 13px;
      line-height: 32px;
    }

    ::v-deep .el-input__inner {
      font-size: 13px;
      height: 32px;
      line-height: 32px;
    }

    ::v-deep .el-select .el-input__inner {
      font-size: 13px;
      height: 32px;
      line-height: 32px;
    }

    ::v-deep .el-date-editor .el-input__inner {
      font-size: 13px;
      height: 32px;
      line-height: 32px;
    }

    ::v-deep .el-button {
      font-size: 13px;
      height: 32px;
      padding: 8px 15px;
    }

    ::v-deep .el-row {
      margin-bottom: 0;
    }

    ::v-deep .el-row:last-child {
      margin-bottom: 0;
    }
  }

  .table-wrapper {
    margin: 20px 20px 20px 20px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #e4e7ed;

    .table-con {
      padding: 24px;
    }

    .pagination-wrapper {
      padding: 20px 24px;
      text-align: right;
      border-top: 1px solid #e4e7ed;
    }
  }
}
</style>
