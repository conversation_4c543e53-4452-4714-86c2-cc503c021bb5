<template>
  <div class="app-container">
    <!-- 主页面 -->
    <el-card v-loading="loading" class="app-card" shadow="never">
      <div slot="header">
        <span class="card-title">
          <span>应用中心管理</span>
          <span>
            <el-button class="exec" type="text" @click="refresh">
              刷新
              <i class="el-icon-refresh"></i>
            </el-button>
            <el-button class="exec" type="text" @click="drawerOpen">
              应用管理
              <i class="el-icon-d-arrow-right"></i>
            </el-button>
          </span>
        </span>
      </div>
      <el-collapse v-model="activeNames" @change="handleChange" class="app-collapse">
        <el-collapse-item v-for="item in list" :key="item.id" :name="item.id">
          <template slot="title" class="card-title">
            <i class="icon iconfont icon-shuxian" style="height: 47px;"></i>
            {{ item.name }}
          </template>
          <div class="apps" v-if="item.apps.length > 0">
            <div v-for="citem in item.apps" :key="citem.id" class="app-item">
              <img class="app-item-logoTemp" :src="citem.logoTemp" />
              <span>{{ citem.name }}</span>
            </div>
            <div
              class="app-item plus"
              @click="openCenter(item)"
              v-hasPermi="['system:app-center:add']"
            >
              <i class="el-icon-plus"></i>
              <span>添加应用</span>
            </div>
          </div>
          <el-empty class="app-empty" v-if="item.apps.length <= 0" :image-size="200">
            <el-button
              type="primary"
              icon="el-icon-plus"
              @click="openCenter(item)"
              v-hasPermi="['system:app-center:add']"
            >添加应用</el-button>
          </el-empty>
        </el-collapse-item>
      </el-collapse>
    </el-card>

    <!-- 应用综合管理 -->
    <el-drawer :with-header="false" :visible.sync="open" size="70%">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>应用综合管理</span>
          <el-button
            style="float: right; padding: 3px 0"
            type="text"
            icon="el-icon-plus"
            @click="
              categoryVisble = true;
              categoryTitle = '新增应用分类';
            "
            v-hasPermi="['system:app-center:add']"
          >新增分类</el-button>
        </div>
        <el-table :data="listAll" v-loading="allloading">
          <el-table-column type="expand">
            <template slot-scope="props">
              <el-card class="box-card">
                <div slot="header" class="clearfix">
                  <span>应用管理</span>
                  <el-button
                    style="float: right; padding: 3px 0"
                    type="text"
                    icon="el-icon-plus"
                    @click="openCenter(props.row)"
                    v-hasPermi="['system:app-center:add']"
                  >新增应用</el-button>
                </div>

                <div>
                  <el-table :data="props.row.apps" style="width: 100%">
                    <el-table-column label="序号" type="index" width="50px"></el-table-column>
                    <el-table-column property="logoTemp" width="80px" label="图标">
                      <template slot-scope="scope">
                        <img :src="scope.row.logoTemp" style="width: 50px; height: 50px" />
                      </template>
                    </el-table-column>
                    <el-table-column property="name" label="应用名称" width="150px"></el-table-column>
                    <el-table-column property="sort" label="排序" width="50px"></el-table-column>
                    <el-table-column property="url" label="url" width="150px"></el-table-column>
                    <el-table-column property="status" label="状态" width="80px">
                      <template slot-scope="scope">
                        <el-switch
                          :value="scope.row.status"
                          @change="handleAppStatusChange(scope.row)"
                        ></el-switch>
                      </template>
                    </el-table-column>
                    <el-table-column property="createTime" label="创建时间"></el-table-column>
                    <el-table-column property="remark" label="备注"></el-table-column>
                    <el-table-column label="操作">
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="small"
                          @click="editCenter(scope.row)"
                          v-hasPermi="['system:app-center:edit']"
                        >编辑</el-button>
                        <el-button
                          @click="delCenter(scope.row)"
                          type="text"
                          size="small"
                          v-hasPermi="['system:app-center:remove']"
                        >删除</el-button>
                        <el-button
                          @click="authCenter(scope.row)"
                          type="text"
                          size="small"
                          v-hasPermi="['system:app-center:auth']"
                        >授权</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-card>
            </template>
          </el-table-column>
          <el-table-column label="序号" type="index" width="80px"></el-table-column>
          <el-table-column property="name" label="名称" width="180px"></el-table-column>
          <el-table-column property="type" label="分类" :formatter="appCategoryFormat" width="180px"></el-table-column>
          <el-table-column property="sort" label="排序" width="100px"></el-table-column>
          <el-table-column property="status" label="状态" width="100px">
            <template slot-scope="scope">
              <el-switch :value="scope.row.status" @change="handleStatusChange(scope.row)"></el-switch>
            </template>
          </el-table-column>
          <el-table-column property="createTime" label="创建时间" width="180px"></el-table-column>
          <el-table-column property="remark" label="备注"></el-table-column>
          <el-table-column label="操作" width="100px">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="editCategory(scope.row)"
                v-hasPermi="['system:app-center:edit']"
              >编辑</el-button>
              <el-button
                @click="delCategory(scope.row)"
                type="text"
                size="small"
                v-hasPermi="['system:app-center:remove']"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-drawer>

    <!-- 新增/编辑 应用分类页面 -->
    <el-drawer
      :title="categoryTitle"
      :before-close="cancelForm"
      :visible.sync="categoryVisble"
      ref="drawer"
      size="50%"
      class="category"
      destroy-on-close
    >
      <div class="body">
        <el-form :model="categoryForm" :rules="rules" ref="categoryForm">
          <el-form-item label="分类" label-width="100px" prop="type">
            <el-select v-model="categoryForm.type" placeholder="请选择分类">
              <el-option
                v-for="dict in appCategoryOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="名称" label-width="100px" prop="name">
            <el-input v-model="categoryForm.name" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="排序" label-width="100px" prop="sort">
            <el-input-number :min="1" v-model="categoryForm.sort"></el-input-number>
          </el-form-item>
          <el-form-item label="备注" label-width="100px" prop="remark">
            <el-input v-model="categoryForm.remark" autocomplete="off"></el-input>
          </el-form-item>
        </el-form>
        <div class="foot">
          <el-button
            type="primary"
            icon="icon iconfont icon-save"
            @click="
              categoryTitle == '新增应用分类' ? saveCategory() : updCategory()
            "
            :loading="categoryloading"
          >{{ categoryloading ? "提交中 ..." : "确 定" }}</el-button>
          <el-button @click="cancelForm" icon="el-icon-close">取 消</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 新增/编辑 应用页面 -->
    <el-drawer
      :title="centerTitle"
      :before-close="closeCenter"
      :visible.sync="centerVisble"
      ref="drawer"
      size="50%"
      class="category"
      destroy-on-close
    >
      <div class="body">
        <el-form :model="centerForm" :rules="centerRules" ref="centerForm">
          <el-form-item label="分类" label-width="100px" prop="categoryId">
            <el-select v-model="centerForm.categoryId" placeholder="请选择分类">
              <el-option v-for="item in listAll" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="客户端" label-width="100px" prop="categoryId">
            <el-select v-model="centerForm.clientId" placeholder="请选择分类">
              <el-option
                v-for="item in oauthClientList"
                :key="item.clientId"
                :label="item.clientApp"
                :value="item.clientId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="名称" label-width="100px" prop="name">
            <el-input v-model="centerForm.name" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="系统地址" label-width="100px" prop="url">
            <el-input v-model="centerForm.url" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="图标" label-width="100px" prop="logoTemp">
            <el-upload
              class="avatar-uploader"
              action="null"
              :on-change="onChange"
              :auto-upload="false"
              :show-file-list="false"
              ref="logoTemp"
            >
              <img v-if="imageUrl" :src="imageUrl" class="avatar" />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过2MB</div>
            </el-upload>
            <el-input v-model="centerForm.logoTemp" autocomplete="off" style="display: none"></el-input>
          </el-form-item>
          <!--          <el-form-item-->
          <!--            label="是否全员可看"-->
          <!--            label-width="100px"-->
          <!--            prop="isAllShow"-->
          <!--          >-->
          <!--            <el-switch-->
          <!--              v-model="centerForm.isAllShow"-->
          <!--              active-text="配置权限"-->
          <!--              inactive-text="全员可看"-->
          <!--            >-->
          <!--            </el-switch>-->
          <!--          </el-form-item>-->
          <!--          <el-form-item-->
          <!--            v-if="centerForm.isAllShow"-->
          <!--            label="操作权限"-->
          <!--            label-width="100px"-->
          <!--            prop="orgList"-->
          <!--          >-->
          <!--            <treeselect-->
          <!--              v-model="centerForm.orgList"-->
          <!--              :options="allTree"-->
          <!--              :normalizer="normalizer"-->
          <!--              :show-count="true"-->
          <!--              :flat="true"-->
          <!--              placeholder="选择操作权限"-->
          <!--              :multiple="true"-->
          <!--              :default-expand-level="1"-->
          <!--            />-->
          <!--          </el-form-item>-->
          <el-form-item label="排序" label-width="100px" prop="sort">
            <el-input-number :min="1" v-model="centerForm.sort"></el-input-number>
          </el-form-item>
          <el-form-item label="备注" label-width="100px" prop="remark">
            <el-input v-model="centerForm.remark" autocomplete="off"></el-input>
          </el-form-item>
        </el-form>
        <div class="foot">
          <el-button @click="closeCenter" icon="el-icon-close">取 消</el-button>
          <el-button
            type="primary"
            icon="icon iconfont icon-save"
            @click="centerTitle === '新增应用' ? saveApp() : updApp()"
            :loading="centerloading"
          >{{ centerloading ? "提交中 ..." : "确 定" }}</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 租户授权页面，已弃用，现使用角色授权 -->
    <el-drawer title="租户中心" :visible.sync="authVisible" size="50%">
      <el-form :model="queryParams" ref="queryForm" :inline="true" style="padding: 10px">
        <el-form-item label="租户名称" prop="tenantName">
          <el-input
            v-model="queryParams.tenantName"
            placeholder="请输入租户名称"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleTenantQuery"
          />
        </el-form-item>
        <el-form-item label="租户登录名" prop="tenantLoginName">
          <el-input
            v-model="queryParams.tenantLoginName"
            placeholder="请输入租户登录名"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleTenantQuery"
          />
        </el-form-item>
        <el-form-item label="租户管理员" prop="displayName">
          <el-input
            v-model="queryParams.displayName"
            placeholder="请输入租户管理员"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleTenantQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleTenantQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetTenantQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table
        v-loading="authLoading"
        :data="tenantList"
        style="padding: 10px"
        @selection-change="selectTenantApp"
        ref="multipleTable"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column
          label="租户名称"
          align="left"
          prop="tenantName"
          :show-overflow-tooltip="true"
          width="80px"
        />
        <el-table-column
          label="租户登录名"
          align="left"
          prop="tenantLoginName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="租户管理员"
          align="left"
          prop="displayName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="有效截止时间"
          align="left"
          prop="effectiveDate"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="租户状态"
          align="left"
          prop="tenantStatus"
          :formatter="effectiveStatusFormat"
          :show-overflow-tooltip="true"
        />
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getTenantList"
      />
      <div class="demo-drawer__footer">
        <el-button type="primary" @click="addTenantApp">确 定</el-button>
        <el-button @click="cancelAuth">取 消</el-button>
      </div>
    </el-drawer>

    <!-- 角色授权页面 -->
    <transfer-table
      ref="transferTable"
      modelName="角色赋权"
      @loadData="loadData"
      :listRole="listRole"
      :listProp="{
        key: 'roleName',
        name: '角色名称',
      }"
      :selectedProp="{
        key: 'roleName',
        name: '角色名称',
      }"
      @add="addAppRole"
      @remove="delAppRole"
    />
  </div>
</template>

<script>
import {
  list,
  update,
  listAll,
  addCategory,
  deleteCategory,
  addCenter,
  deleteCenter,
  updCenter,
  getCenter,
  authApp,
  selectTenantIds,
  getHadRolesAndNoRoles,
  addAppRole,
  delAppRole,
} from "@/api/apps/app";
import { getOauthClientList } from "@/api/system/oauthClientInfo";
import { treeAll } from "@/api/system/dept";
import { findRoleListByScope } from "@/api/system/role";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import TransferTable from "@/components/TransferTable";
import { page as tenantPageList } from "@/api/system/tenant";

export default {
  name: "apps",
  components: {
    Treeselect,
    TransferTable,
  },
  data() {
    var checklogoTemp = (rule, value, callback) => {
      if (
        !this.$refs.logoTemp.uploadFiles.length &&
        this.centerTitle !== "修改应用"
      ) {
        callback(new Error("请上传应用图标"));
      } else {
        callback();
      }
    };
    return {
      list: [],
      listRole: [],
      loading: true,
      activeNames: [],
      open: false,
      allloading: false,
      listAll: [],
      categoryVisble: false,
      categoryloading: false,
      categoryTitle: "新增应用分类",
      categoryForm: {
        sort: 1,
      },
      rules: {
        name: [
          { required: true, message: "请输入名称", trigger: "blur" },
          { min: 1, max: 64, message: "最大输入64个字符", trigger: "blur" },
        ],
        remark: [
          { min: 1, max: 255, message: "最大输入255个字符", trigger: "blur" },
        ],
        type: [{ required: true, message: "请选择分类", trigger: "blur" }],
      },
      // 以下下为应用的
      centerVisble: false,
      centerloading: false,
      centerTitle: "新增应用",
      centerForm: {
        sort: 1,
      },
      centerRules: {
        name: [
          { required: true, message: "请输入名称", trigger: "blur" },
          { min: 1, max: 64, message: "最大输入64个字符", trigger: "blur" },
        ],
        url: [
          { required: true, message: "请输入访问地址", trigger: "blur" },
          { min: 1, max: 255, message: "最大输入255个字符", trigger: "blur" },
        ],
        remark: [
          { min: 1, max: 255, message: "最大输入255个字符", trigger: "blur" },
        ],
        logoTemp: [{ validator: checklogoTemp }],
      },
      imageUrl: "",
      fileList: [],
      allTree: [],
      // 应用分类
      appCategoryOptions: [],
      defaultOrgId: this.$store.getters.orgId,
      authVisible: false,
      authLoading: true,
      tenantList: [],
      // 是否有效数据字典
      effectiveStatusOptions: [],
      // 总条数
      total: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenantName: undefined,
        tenantLoginName: undefined,
        displayName: undefined,
      },
      selectTenantIds: [],
      oauthClientList: [],
      appId: undefined,
    };
  },
  watch: {
    imageUrl: function (newVal, oldVal) {
      this.centerForm.logoTemp = newVal;
    },
  },
  created() {
    this.refresh();
    // this.getTreeAll();
    this.getDicts("app_category").then((response) => {
      this.appCategoryOptions = response.data;
    });
    this.getDicts("effective_status").then((response) => {
      this.effectiveStatusOptions = response.data;
    });
  },
  methods: {
    // getTreeAll() {
    //   treeAll({ orgId: this.defaultOrgId }).then((r) => {
    //     console.log(r)
    //     this.allTree = r.data;
    //   });
    // }, ???

    // 刷新
    refresh() {
      this.getList();
      this.all();
    },
    // 折叠面板组件激活改变时所触发的方法
    handleChange(val) {
      // console.log(val);
    },
    // switch开关组件状态发生变化时所触发的方法
    handleStatusChange(value) {
      update({
        id: value.id,
        status: value.status ? false : true,
      }).then((res) => {
        this.all();
        this.getList();
      });
    },
    // 获取应用中心数据
    getList(id) {
      this.loading = true;
      list(2).then((res) => {
        if (id) {
          this.list.map((v) => {
            if (v.id === id && res.data.filter((i) => i.id === id).length > 0) {
              v.apps = res.data.find((i) => i.id === id).apps;
            }
            return v;
          });
        } else {
          this.list = res.data;
        }
        this.activeNames = this.list.map((v) => v.id);
        this.loading = false;
      });
    },
    // 获取应用综合数据
    all(id) {
      this.allloading = true;
      listAll(2).then((res) => {
        if (id) {
          this.listAll.map((v) => {
            if (v.id === id && res.data.filter((i) => i.id === id).length > 0) {
              v.apps = res.data.find((i) => i.id === id).apps;
            }
            return v;
          });
        } else {
          this.listAll = res.data;
        }
        this.allloading = false;
      });
    },
    drawerOpen() {
      this.cancelForm();
      this.open = !this.open;
      getOauthClientList({}).then((res) => {
        this.oauthClientList = res.data;
      });
      this.all();
    },
    cancelForm() {
      this.categoryloading = false;
      this.categoryVisble = false;
      this.categoryForm = { sort: 1 };
    },
    //新增应用分类
    saveCategory() {
      this.$refs["categoryForm"].validate((valid) => {
        if (valid) {
          this.categoryloading = true;
          addCategory(this.categoryForm).then((r) => {
            this.categoryloading = false;
            if (r.success) {
              this.$message({
                message: "添加成功！",
                type: "success",
              });
              this.cancelForm();
              this.all();
              this.getList();
            } else {
              this.$message.error("添加失败！");
            }
          });
        } else {
          return false;
        }
      });
    },
    delCategory(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "删除",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
        beforeClose: (action, instance, done) => {
          if (action === "confirm") {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = "删除中...";
            deleteCategory(row.id).then((r) => {
              instance.confirmButtonLoading = false;
              if (r.success) {
                this.$message({
                  message: "删除成功！",
                  type: "success",
                });
                this.all();
                this.getList();
                done();
              } else {
                this.$message.error("删除失败！");
                done();
              }
            });
          } else {
            done();
          }
        },
      }).catch(() => {
        this.$message({
          type: "info",
          message: "已取消删除",
        });
      });
    },
    //修改应用分类
    updCategory() {
      this.$refs["categoryForm"].validate((valid) => {
        if (valid) {
          this.categoryloading = true;
          update(this.categoryForm).then((r) => {
            this.categoryloading = false;
            if (r.success) {
              this.$message({
                message: "修改成功！",
                type: "success",
              });
              this.cancelForm();
              this.all();
              this.getList();
            } else {
              this.$message.error("修改失败！");
            }
          });
        } else {
          return false;
        }
      });
    },
    editCategory(row) {
      this.categoryTitle = "修改应用分类";
      this.categoryForm = {
        ...row,
      };
      this.categoryForm.type = this.categoryForm.type.toString();
      this.categoryVisble = true;
    },
    // 以下下为应用的
    openCenter(row) {
      getOauthClientList({}).then((res) => {
        this.oauthClientList = res.data;
      });
      this.centerVisble = true;
      this.centerloading = false;
      this.centerTitle = "新增应用";
      this.centerForm = {
        sort: 1,
        categoryId: row.id,
      };
    },
    closeCenter() {
      this.centerVisble = false;
      this.centerloading = false;
      this.centerTitle = "新增应用";
      this.imageUrl = "";
      this.centerForm = {
        sort: 1,
      };
    },
    onChange(file, fileList) {
      if (fileList.length && fileList.length >= 1) {
        /**引用对象然后验证表单域-这个可以清除上一步不通过时的提示*/
        this.$refs.centerForm.validateField("logoTemp");
      }
      const isJPG = file.raw.type === "image/jpeg";
      const isPng = file.raw.type === "image/png";
      const isLt2M = file.raw.size / 1024 / 1024 < 2;

      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
      }
      if (isJPG || isPng) {
        this.getBase64(file.raw).then((res) => {
          this.imageUrl = res;
        });
      } else {
        this.$message.error("上传图片只能是 JPG/Png 格式!");
      }
    },
    getBase64(file) {
      return new Promise(function (resolve, reject) {
        let reader = new FileReader();
        let imgResult = "";
        reader.readAsDataURL(file);
        reader.onload = function () {
          imgResult = reader.result;
        };
        reader.onerror = function (error) {
          reject(error);
        };
        reader.onloadend = function () {
          resolve(imgResult);
        };
      });
    },
    saveApp() {
      this.$refs["centerForm"].validate((valid) => {
        if (valid) {
          this.centerloading = true;
          const centerForm = this.centerForm;
          addCenter({
            ...this.centerForm,
            isAllShow: this.centerForm.isAllShow ? 0 : 1,
          }).then((r) => {
            this.centerloading = false;
            if (r.success) {
              this.$message({
                message: "添加成功！",
                type: "success",
              });
              this.closeCenter();
              this.all(centerForm.categoryId);
              this.getList(centerForm.categoryId);
            } else {
              this.$message.error("添加失败！");
            }
          });
        } else {
          return false;
        }
      });
    },
    editCenter(row) {
      let data = {};
      getCenter(row.id)
        .then((res) => {
          if (res.success) {
            data = res.data;
          } else data = row;
        })
        .catch((err) => {
          data = row;
          console.log(`inside error:${err}`);
        })
        .finally(() => {
          this.centerForm = {
            ...data,
            isAllShow: data.isAllShow !== 1,
          };
          this.imageUrl = this.centerForm.logoTemp;
        });
      this.centerVisble = true;
      this.centerloading = false;
      this.centerTitle = "修改应用";
    },
    updApp() {
      this.$refs["centerForm"].validate((valid) => {
        if (valid) {
          this.centerloading = true;
          const centerForm = this.centerForm;
          updCenter({
            ...this.centerForm,
            isAllShow: this.centerForm.isAllShow ? 0 : 1,
          }).then((r) => {
            this.centerloading = false;
            if (r.success) {
              this.$message({
                message: "修改成功！",
                type: "success",
              });
              this.closeCenter();
              this.all(centerForm.categoryId);
              this.getList(centerForm.categoryId);
            } else {
              this.$message.error("修改失败！");
            }
          });
        } else {
          return false;
        }
      });
    },
    delCenter(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "删除",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
        beforeClose: (action, instance, done) => {
          if (action === "confirm") {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = "删除中...";
            deleteCenter(row.id).then((r) => {
              instance.confirmButtonLoading = false;
              if (r.success) {
                this.$message({
                  message: "删除成功！",
                  type: "success",
                });
                this.all(row.categoryId);
                this.getList(row.categoryId);
                done();
              } else {
                this.$message.error("删除失败！");
                done();
              }
            });
          } else {
            done();
          }
        },
      }).catch(() => {
        this.$message({
          type: "info",
          message: "已取消删除",
        });
      });
    },
    handleAppStatusChange(value) {
      updCenter({
        id: value.id,
        status: value.status ? false : true,
      }).then((res) => {
        if (res.success) {
          this.all(value.categoryId);
          this.getList(value.categoryId);
        } else {
          this.$message.error("修改失败！");
        }
      });
    },
    normalizer(node) {
      if (node.children == null || node.children === "null") {
        delete node.children;
      }
      return {
        id: node.orgId,
        label: node.orgName,
        children: node.children,
      };
    },
    // 任务组名字典翻译
    appCategoryFormat(row) {
      return this.selectDictLabel(this.appCategoryOptions, row.type);
    },
    authCenter(row) {
      // this.authVisible = true;
      this.appId = row.id;
      // this.queryTenantIds(row.id);
      // this.showAppTenants(this.tenantList)
      this.$refs.transferTable.open();
    },
    /** 查询参数列表 */
    getTenantList() {
      this.authLoading = true;
      tenantPageList({
        ...this.addDateRange({}, this.dateRange).params,
        ...this.queryParams,
      }).then((response) => {
        this.tenantList = response.data.records;
        this.total = response.data.total;
        this.authLoading = false;
      });
    },
    // 参数系统内置字典翻译
    effectiveStatusFormat(row, column) {
      return this.selectDictLabel(
        this.effectiveStatusOptions,
        row.tenantStatus
      );
    },
    selectTenantApp(selection) {
      this.selectTenantIds = selection.map((item) => item.tenantId);
    },
    addTenantApp() {
      const data = {
        appCenterId: this.appId,
        tenantIds: this.selectTenantIds,
      };
      authApp(data).then((res) => {
        this.$message.success("操作成功！");
      });
    },
    /** 搜索按钮操作 */
    handleTenantQuery() {
      this.authLoading = true;
      this.queryParams.pageNum = 1;
      // this.getTenantList();
      // this.queryTenantIds(this.appId);
      page({
        ...this.addDateRange({}, this.dateRange).params,
        ...this.queryParams,
      }).then((response) => {
        this.tenantList = response.data.records;
        this.total = response.data.total;
        this.$nextTick(() => {
          this.$refs.multipleTable.clearSelection();
        });
        selectTenantIds(this.appId).then((res) => {
          if (res.data.length > 0) {
            for (let i = 0; i < this.tenantList.length; i++) {
              if (res.data.indexOf(this.tenantList[i].tenantId) !== -1) {
                this.$refs.multipleTable.toggleRowSelection(
                  this.tenantList[i],
                  true
                );
              }
            }
            this.authLoading = false;
          } else {
            this.authLoading = false;
          }
        });
      });
    },
    /** 重置按钮操作 */
    resetTenantQuery() {
      this.resetForm("queryForm");
      this.handleTenantQuery();
    },
    queryTenantIds(appId) {
      this.authLoading = true;
      this.$nextTick(() => {
        this.$refs.multipleTable.clearSelection();
      });
      selectTenantIds(appId).then((res) => {
        if (res.data.length > 0) {
          for (let i = 0; i < this.tenantList.length; i++) {
            if (res.data.indexOf(this.tenantList[i].tenantId) !== -1) {
              this.$refs.multipleTable.toggleRowSelection(
                this.tenantList[i],
                true
              );
            }
          }
          this.authLoading = false;
        } else {
          this.authLoading = false;
        }
      });
    },
    cancelAuth() {
      this.authVisible = false;
    },
    loadData() {
      findRoleListByScope().then((r) => {
        this.getDicts("scope").then((response) => {
          const newData = response.data.map((v) => {
            v["children"] = r.data.filter((i) => i.roleScope === v.dictValue);
            v["roleName"] = v.dictLabel;
            v["roleId"] = v.dictDataId;
            v["isOp"] = true;
            return v;
          });
          this.$refs.transferTable.setData(newData);
        });
      });
      getHadRolesAndNoRoles({ appCenterId: this.appId }).then((r) => {
        this.$refs.transferTable.setRightData({ data: r.data.havelist });
      });
    },
    addAppRole(row) {
      addAppRole(this.appId, row.roleId).then((r) => {
        this.$refs.transferTable.reload();
      });
    },
    delAppRole(row) {
      delAppRole(this.appId, row.roleId).then((r) => {
        this.$refs.transferTable.reload();
      });
    },
  },
};
</script>

<style scoped lang="scss">
.app-card {
  margin: 0px 0px 10px 0px;
  min-height: calc(100vh - 200px);
  border: 0px solid #e6ebf5;

  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > span {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .icon {
      font-size: 22px;
      color: #419eee;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }

    .exec {
      padding: 3px 0;
    }
  }

  .app-collapse {
    background-color: #00000000;
    border: 0px solid #e6ebf5;

    .el-collapse-item__header {
      background-color: #00000000;

      .icon {
        font-size: 22px;
        color: #419eee;
        -webkit-transition: font-size 0.25s linear, width 0.25s linear;
        -moz-transition: font-size 0.25s linear, width 0.25s linear;
        transition: font-size 0.25s linear, width 0.25s linear;
      }
    }
  }

  .app-empty {
    background-color: #f5f9fa;
  }

  .apps {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    background-color: #f5f9fa;

    .app-item {
      padding: 40px;
      flex-basis: 30%;
      background-color: #ffffff;
      margin: 15px;
      border-radius: 10px;
      display: flex;
      flex-direction: row;
      align-items: center;
      font-size: 20px;

      > span {
        font-weight: bold;
        margin-left: 20px;
      }

      &:hover {
        box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
      }

      .app-item-logoTemp {
        width: 50px;
        height: 50px;
      }

      cursor: pointer;
    }

    .plus {
      background-color: #ffffff80;
      color: #919594;

      > span {
        font-weight: 300;
        margin-left: 20px;
      }

      .el-icon-plus {
        font-size: 40px;
      }

      &:hover {
        box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
        font-size: 30px;
        transition: 200ms;
        color: #1890ff;

        .el-icon-plus {
          font-size: 60px;
          transition: 200ms;
        }
      }
    }
  }
}

.box-card {
  height: 100%;
  overflow: auto;
  width: 100%;
  margin-top: 0;
}

.category {
  .body {
    margin: 40px;

    .foot {
      text-align: center;
      margin-top: 50px;
    }
  }
}
</style>
<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.demo-drawer__footer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
}
</style>
