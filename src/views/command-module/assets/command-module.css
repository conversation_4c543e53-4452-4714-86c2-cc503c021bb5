.cm-page {
  position: relative;
  width: 100%;
  height: 1080px;
  overflow: hidden;
}

.cm-group1 {
  background-color: rgba(13, 19, 66, 1);
  height: 1080px;
  width: 100%;
}

.cm-bd1 {
  height: 1080px;
  background: url(./img/psa4mqfpxv7w6iodnjqgurnh3sde6i3wz2ye807d332-a131-4351-89b2-fd3479fdc39f.png) -4px -12px
    no-repeat;
  width: 100%;
}

.cm-outer1 {
  z-index: 6;
  height: 1080px;
  background: url(./img/pssd6amie62ti6nwqzem6ektbitjz7pnfb37c7372-e2bc-440c-9437-529e2de2b188.png) -103px -12px
    no-repeat;
  width: 100%;
  position: relative;
}

.cm-mod1 {
  width: 100%;
  height: 1080px;
  background: url(./img/psnmy7nk0w8ggvjui1azvi6zyp0vr78g6ff4a8e6c7-bb90-404a-bf78-edb2b3d9ac07.png) no-repeat;
  margin-left: 114px;
  z-index: 20;
}

.cm-mod2 {
  height: 1080px;
  background: url(./img/pss3uz0k67dmqhdejibhg7roj7t7y5q6dedd9a4d55-73b8-4206-a0d3-103094602370.png) no-repeat;
  background-size: 100% 100%;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
}

.cm-layer18 {
  height: 1019px;
  width: 100%;
}

.cm-main1 {
  height: 100px;
  background: url(./img/psr8o1glyytukcg69svv7jji59g9mzarkdsd3dec351-559b-432d-b69d-37c45ae8c20e.png) -9px -35px
    no-repeat;
  width: 100%;
  background-size: 100% ;
}

.cm-info1 {
  background-image: linear-gradient(
    180deg,
    rgba(213, 254, 255, 1) 0,
    rgba(213, 254, 255, 1) 0,
    rgba(136, 251, 255, 1) 98.779297%,
    rgba(136, 251, 255, 1) 100%
  );
  width: 600px;
  height: 40px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 40px;
  font-family: MicrosoftYaHei-Bold;
  text-align: center;
  white-space: nowrap;
  line-height: 40px;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: block;
  margin: 26px auto;
}

.cm-main2 {
  height: 447px;
  width: 100%;
  margin: 12px auto auto 12px;
  display: flex;
  justify-content: center;
}

.cm-box12 {
  height: 396px;
  background: url(./img/psly872isr5m1s9m2yasppdkdl3a1hq12h43ff6934-9c77-480e-9e68-94f94cdea27c.png)
    100% no-repeat;
  margin-top: 31px;
  width: 466px;
}

.cm-group3 {
  width: 446px;
  height: 353px;
  margin: 16px 0 0 16px;
}

.cm-section11 {
  width: 120px;
  height: 32px;
  margin-left: 7px;
}

.cm-label1 {
  width: 28px;
  height: 32px;
}

.cm-word1 {
  width: 87px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 22px;
  font-family: MicrosoftYaHei-Bold;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-top: 4px;
  display: block;
}

.cm-pic1 {
  width: 442px;
  height: 10px;
  margin: 1px 0 0 4px;
}

.cm-box2 {
  z-index: 88;
  height: 272px;
  background: url(./img/cmbig.png)
    100% no-repeat;
  margin-top: 58px;
  width: 437px;
  position: relative;
}

.cm-box2-data {
  width: 100%;
  height: 20%;
  text-align: center;
  color: rgba(214, 253, 254, 1);
  font-size: 40px;
  font-family: MicrosoftYaHei-Bold;
}

.cm-box2-cicle {
  transform-style: preserve-3d;
  position: absolute;
  top: 50%;
  left: 76%;
  margin: -12rem 0 0 -15rem;
  width: 15rem;
  height: 30rem;
  background: url(./img/cmcircle.png) no-repeat center;
  background-size: contain;
  transform: rotateX(70deg);
  animation: circle 5s linear infinite;
}

.cm-box3 {
  width: 147px;
  height: 63px;
  margin: 44px 0 0 135px;
}

.cm-bd2 {
  height: 63px;
  background: url(./img/psq08kslpo9dmmlea7tv5qs29e0864e3jhcefc467b-cebf-44d5-904a-ac6dade8dc40.png) -10px -9px
    no-repeat;
  width: 42px;
}

.cm-info2 {
  width: 16px;
  height: 12px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 14px;
  font-family: MicrosoftYaHei-Bold;
  text-align: left;
  white-space: nowrap;
  line-height: 14px;
  display: block;
  margin: 14px 0 0 11px;
}

.cm-bd3 {
  height: 63px;
  background: url(./img/psrrd8j7u5nnrnugsp9dfr891yjrfbgmyk3f69b8cc-7f9c-435b-b77d-91891b6db379.png) -10px -9px
    no-repeat;
  width: 42px;
}

.cm-info3 {
  width: 9px;
  height: 12px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 14px;
  font-family: MicrosoftYaHei-Bold;
  text-align: left;
  white-space: nowrap;
  line-height: 14px;
  display: block;
  margin: 15px 0 0 15px;
}

.cm-outer5 {
  width: 24px;
  height: 24px;
  margin: 58px 0 0 171px;
}

.cm-layer19 {
  background-color: rgba(100, 230, 174, 0.5);
  border-radius: 50%;
  width: 24px;
  height: 24px;
}

.cm-outer6 {
  width: 22px;
  height: 12px;
  margin: 18px 0 53px 171px;
}

.cm-box13 {
  height: 12px;
  background: url(./img/psqfr7b0kek4ljak1awntv93lzy2ishhkubd82a921-23eb-4c51-81e2-e4f38aa96e51.png)
    100% no-repeat;
  width: 22px;
}

.cm-bd23 {
  height: 12px;
  background: url(./img/psejggixppf0842bng80qrve5lbgyvkv1hkc2cddd88-ffa8-4f38-8f57-afc65810e380.png)
    100% no-repeat;
  width: 22px;
}

.cm-icon13 {
  width: 22px;
  height: 12px;
}

.cm-outer7 {
  z-index: 173;
  height: 42px;
  background: url(./img/ps57ibsiiq1kt7niptobncpw631nf1pcucjfb329b0c-1c17-4ce3-958d-8ac66dfd7f0f.png)
    100% no-repeat;
  width: 42px;
  position: absolute;
  left: 162px;
  top: 156px;
}

.cm-mod14 {
  height: 28px;
  background: url(./img/psl0zp1irfapllyfq7sfiraietdyqei70a408f724e-d647-4752-baa2-29db16dc39f2.png)
    100% no-repeat;
  width: 28px;
  margin: 7px 0 0 6px;
}

.cm-box14 {
  background-color: rgba(100, 230, 174, 0.5);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  margin: 2px 0 0 3px;
}

.cm-mod15 {
  z-index: 182;
  height: 42px;
  background: url(./img/ps6aavg3lzxvhy5hntisc4qltywerq4laeo86f6cd2b-5e72-4fb2-89fd-c5ca2fe4d6b9.png)
    100% no-repeat;
  width: 42px;
  position: absolute;
  left: 0;
  top: 0;
}

.cm-outer8 {
  height: 28px;
  background: url(./img/psfmgev3nt7gyafcj22j81vl1erpq0zq978af5a9-30c4-43a9-b913-11c3e523d0f8.png)
    100% no-repeat;
  width: 28px;
  margin: 7px 0 0 6px;
}

.cm-word2 {
  width: 9px;
  height: 12px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 14px;
  font-family: MicrosoftYaHei-Bold;
  text-align: left;
  white-space: nowrap;
  line-height: 14px;
  display: block;
  margin: 8px 0 0 9px;
}

.cm-icon14 {
  z-index: 184;
  position: absolute;
  left: 11px;
  top: 36px;
  width: 18px;
  height: 22px;
}

.cm-label14 {
  z-index: 175;
  position: absolute;
  left: 11px;
  top: 36px;
  width: 18px;
  height: 22px;
}

.cm-outer9 {
  z-index: 178;
  height: 12px;
  background: url(./img/psojviyivhirzbz588axr8ejhrlcgjwqoide614f3f-ddd3-405f-9b06-8f0132c3def8.png)
    100% no-repeat;
  width: 22px;
  position: absolute;
  left: 171px;
  top: 207px;
}

.cm-group4 {
  height: 12px;
  background: url(./img/ps80xfd6vo7o6gjowgugjgc560eqy32ji6i302830a6-4452-4146-bff0-6b29915b4bd5.png)
    100% no-repeat;
  width: 22px;
}

.cm-icon15 {
  width: 22px;
  height: 12px;
}

.cm-box15 {
  width: 884px;
  height: 407px;
  margin: 40px 0 0 11px;
}

.cm-layer2 {
  width: 784px;
  height: 54px;
  margin-left: 76px;
}

.cm-word3 {
  width: 41px;
  overflow-wrap: break-word;
  color: rgba(170, 252, 255, 1);
  font-size: 36px;
  font-family: MicrosoftYaHei-Bold;
  text-align: left;
  white-space: nowrap;
  line-height: 36px;
  margin-top: 21px;
  display: block;
  z-index: 200;
}

.cm-mod3 {
  height: 51px;
  background: url(./img/psn496hu1m0jm83my24ohnynii4d1pf0nfef873f0-442a-41b8-b860-e3866addb190.png) -1px
    0px no-repeat;
  width: 108px;
  margin: 3px 0 0 476px;
}

.cm-word4 {
  width: 42px;
  overflow-wrap: break-word;
  color: rgba(170, 252, 255, 1);
  font-size: 36px;
  font-family: MicrosoftYaHei-Bold;
  text-align: left;
  white-space: nowrap;
  line-height: 36px;
  display: block;
  margin: 18px 0 0 34px;
  z-index: 200;
}

.cm-mod4 {
  height: 51px;
  background: url(./img/pslvxvyc4cb5ewdd4t6ooa41k25p245vgz51330e9a-cbb2-451c-8b2f-6bc3bfca3a55.png) -1px
    0px no-repeat;
  margin-left: 51px;
  width: 108px;
}

.cm-word5 {
  width: 42px;
  overflow-wrap: break-word;
  color: rgba(170, 252, 255, 1);
  font-size: 36px;
  font-family: MicrosoftYaHei-Bold;
  text-align: left;
  white-space: nowrap;
  line-height: 36px;
  display: block;
  margin: 21px 0 0 34px;
  z-index: 200;
}

.cm-layer3 {
  width: 743px;
  height: 80px;
  margin-top: 8px;
}

.cm-section2 {
  z-index: 139;
  height: 79px;
  background: url(./img/ps23v5mevx7ohslovjsc2juvpwwzrifszn9f4589b1-002d-4292-8666-d9112e33ac4f.png)
    100% no-repeat;
  margin-top: 1px;
  width: 192px;
  position: relative;
}

.cm-word6 {
  z-index: 142;
  position: static;
  left: 573px;
  top: 270px;
  width: 66px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(170, 252, 255, 1);
  font-size: 22px;
  font-family: MicrosoftYaHei-Bold;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  display: block;
  margin: 55px 0 0 71px;
}

.cm-img1 {
  z-index: 140;
  position: absolute;
  left: 48px;
  top: -30px;
  width: 97px;
  height: 75px;
}

.cm-section3 {
  z-index: 149;
  height: 79px;
  background: url(./img/pse3tzisu47jpwri8mxocsipayfijdtppi883aac1ab-f469-4f5c-a874-499536a9460c.png)
    100% no-repeat;
  width: 192px;
  position: relative;
}

.cm-word7 {
  z-index: 152;
  position: static;
  left: 1119px;
  top: 271px;
  width: 88px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(170, 252, 255, 1);
  font-size: 22px;
  font-family: MicrosoftYaHei-Bold;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  display: block;
  margin: 57px 0 0 76px;
}

.cm-img2 {
  z-index: 150;
  position: absolute;
  left: 50px;
  top: -29px;
  width: 97px;
  height: 75px;
}

.cm-wrap1 {
  z-index: 155;
  height: 79px;
  background: url(./img/psxn0sg3slv1igd24p9td1tey2af6o5py6s93d9effc-8207-4df4-92d3-01425b5bd0bb.png)
    100% no-repeat;
  width: 192px;
  position: absolute;
  left: 158px;
  top: 0;
}

.cm-txt1 {
  z-index: 159;
  position: static;
  left: 1290px;
  top: 270px;
  width: 64px;
  height: 23px;
  overflow-wrap: break-word;
  color: rgba(170, 252, 255, 1);
  font-size: 22px;
  font-family: MicrosoftYaHei-Bold;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  display: block;
  margin: 56px 0 0 77px;
}

.cm-img3 {
  z-index: 156;
  position: absolute;
  left: 50px;
  top: -29px;
  width: 97px;
  height: 75px;
}

.cm-main19 {
  z-index: 96;
  height: 267px;
  background: url(./img/psmvzojsxb22sae5hdqo046ostzhphgpzhda209a79-b86d-448a-aa90-7d1aafa65494.png)
    100% no-repeat;
  width: 863px;
  position: relative;
  margin: 28px 0 0 21px;
}

.cm-mod16 {
  width: 843px;
  height: 220px;
  margin: 10px 0 0 13px;
}

.cm-main20 {
  width: 113px;
  height: 31px;
  margin-left: 11px;
}

.cm-outer2 {
  z-index: 113;
  position: relative;
  width: 19px;
  height: 31px;
  background: url(./img/psqavxtdws8c497jza62rp4fje7pxjc1ko31d22d65-66b0-49eb-946a-93fc64be88fa.png)
    100% no-repeat;
}

.cm-label5 {
  z-index: 114;
  position: absolute;
  left: -9px;
  top: -1px;
  width: 19px;
  height: 31px;
}

.cm-txt2 {
  width: 80px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 20px;
  font-family: MicrosoftYaHei-Bold;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-top: 5px;
  display: block;
}

.cm-main21 {
  width: 841px;
  height: 3px;
  margin-top: 5px;
}

.cm-main22 {
  width: 777px;
  height: 2px;
}

.cm-mod5 {
  background-color: rgba(101, 219, 223, 1);
  width: 102px;
  height: 1px;
  margin-left: 35px;
}

.cm-mod6 {
  z-index: 107;
  position: relative;
  width: 777px;
  height: 1px;
  background: url(./img/pslek6wdmtbf2xvkxc82zgefw3214wbntj33fd6c4e-d655-48b6-9c6d-899645205432.png)
    100% no-repeat;
}

.cm-section4 {
  background-color: rgba(195, 253, 255, 1);
  z-index: 109;
  position: absolute;
  left: -1px;
  top: -2px;
  width: 4px;
  height: 4px;
}

.cm-layer6 {
  background-color: rgba(249, 158, 81, 1);
  width: 16px;
  height: 2px;
}

.cm-layer7 {
  background-color: rgba(106, 207, 255, 1);
  width: 16px;
  height: 2px;
  margin-left: 8px;
}

.cm-layer8 {
  background-color: rgba(101, 219, 223, 1);
  width: 16px;
  height: 3px;
  margin-left: 8px;
}

.cm-bd6 {
  background-color: rgba(101, 219, 223, 0.15);
  height: 32px;
  margin-top: 15px;
  width: 843px;
}

.cm-bd6-table-tr {
  margin: 8px 0 0 20px;
  width: 709px;
  overflow-wrap: break-word;
  color: rgba(170, 252, 255, 1);
  font-size: 18px;
  white-space: nowrap;
  line-height: 18px;
  font-family: MicrosoftYaHei;
  text-align: center;
}

.cm-bd7 {
  color: rgba(255, 255, 255, 1);
  font-size: 14px;
  font-family: MicrosoftYaHei;
  line-height: 32px;
  white-space: nowrap;
  text-align: center;
}

.cm-bd8 {
  background-color: rgba(101, 219, 223, 0.08);
  color: rgba(255, 255, 255, 1);
  font-size: 14px;
  font-family: MicrosoftYaHei;
  line-height: 32px;
  white-space: nowrap;
  text-align: center;
}

.cm-main5 {
  width: 500px;
  height: 441px;
  margin-left: 33px;
  display: flex;
  align-items: center;
  z-index: 30;
  margin-top: -30px;
}

.cm-layer9 {
  height: 143px;
  background: url(./img/psi05kebwe4jqo5egp3vj6ipdayxdadp53a64bd50-4afc-458e-9c2e-8493c0854506.png)
    100% no-repeat;
  width: 453px;
}

.cm-basic-service-txt {
  width: 88px;
  height: 23px;
  overflow-wrap: break-word;
  color: rgba(214, 253, 254, 1);
  font-size: 18px;
  font-family: MicrosoftYaHei-Bold;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  display: block;
  margin: 67px 0 0 240px;
}

.cm-check-tip-txt {
  width: 88px;
  height: 23px;
  overflow-wrap: break-word;
  color: rgba(214, 253, 254, 1);
  font-size: 15px;
  font-family: MicrosoftYaHei-Bold;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  display: block;
  margin: 0 0 0 130px;
  /*margin: 0 auto;*/
}

.cm-redis-txt {
  width: 88px;
  height: 23px;
  overflow-wrap: break-word;
  color: rgba(214, 253, 254, 1);
  font-size: 18px;
  font-family: MicrosoftYaHei-Bold;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  display: block;
  margin: 67px 0 0 250px;
}

.cm-db-txt {
  width: 88px;
  height: 23px;
  overflow-wrap: break-word;
  color: rgba(214, 253, 254, 1);
  font-size: 18px;
  font-family: MicrosoftYaHei-Bold;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  display: block;
  margin: 69px 0 0 245px;
}

.cm-es-txt {
  width: 88px;
  height: 23px;
  overflow-wrap: break-word;
  color: rgba(214, 253, 254, 1);
  font-size: 18px;
  font-family: MicrosoftYaHei-Bold;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  display: block;
  margin: 69px 0 0 220px;
}

.cm-mq-txt {
  width: 87px;
  height: 23px;
  overflow-wrap: break-word;
  color: rgba(214, 253, 254, 1);
  font-size: 18px;
  font-family: MicrosoftYaHei-Bold;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  display: block;
  margin: 69px 0 0 240px;
}

.cm-oss-txt {
  width: 87px;
  height: 23px;
  overflow-wrap: break-word;
  color: rgba(214, 253, 254, 1);
  font-size: 18px;
  font-family: MicrosoftYaHei-Bold;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  display: block;
  margin: 69px 0 0 240px;
}

.cm-layer10 {
  height: 143px;
  background: url(./img/pslbhp5bap0doftgqcgipf5l247ujp7gbhq3ee042d9-6799-49d1-81ff-5f400ebdaf37.png)
    100% no-repeat;
  margin-top: 7px;
  width: 453px;
}

.cm-layer11 {
  height: 143px;
  background: url(./img/ps88va43xgi1tnprp0osj7lfd0pypnyrje364fa09d-8061-4e2e-80b2-111b770eab1a.png)
    100% no-repeat;
  margin-top: 5px;
  width: 453px;
}

.cm-outer10 {
  width: 100%;
  height: 432px;
  margin: 12px auto auto 12px;
  display: flex;
  justify-content: center;
}

.cm-section12 {
  height: 400px;
  background: url(./img/pspviwrmwjsmka9ut63l1p1wbn8s0zklv3ef7ec11-5901-471e-9113-ecba9a20e49d.png)
    100% no-repeat;
  width: 466px;
  margin-left: 53px;
  margin-top: -20px;
}

.cm-section13 {
  width: 446px;
  height: 394px;
  margin: 12px 0 0 16px;
}

.cm-layer20 {
  width: 107px;
  height: 31px;
  margin-left: 16px;
}

.cm-main7 {
  z-index: 133;
  position: relative;
  width: 19px;
  height: 31px;
  background: url(./img/psv2hv6kliqfunvzmoxpuvcuhpyxwlt83s830b6af1-fcc4-477e-9d91-89bc8e02cca4.png)
    100% no-repeat;
}

.cm-icon4 {
  z-index: 134;
  position: absolute;
  left: -9px;
  top: -1px;
  width: 19px;
  height: 31px;
}

.cm-word8 {
  width: 80px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 20px;
  font-family: MicrosoftYaHei-Bold;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-top: 5px;
  display: block;
}

.cm-layer21 {
  width: 441px;
  height: 3px;
  margin: 5px 0 0 5px;
}

.cm-box16 {
  width: 377px;
  height: 2px;
}

.cm-main8 {
  background-color: rgba(101, 219, 223, 1);
  width: 102px;
  height: 1px;
  margin-left: 35px;
}

.cm-main9 {
  z-index: 127;
  position: relative;
  width: 377px;
  height: 1px;
  background: url(./img/ps8uvm5il368x3n1jvqo32diqp1252l8perf1e7bd14-b5b8-4348-88ca-00d0dec39abf.png)
    100% no-repeat;
}

.cm-mod7 {
  background-color: rgba(195, 253, 255, 1);
  z-index: 129;
  position: absolute;
  left: -1px;
  top: -2px;
  width: 4px;
  height: 4px;
}

.cm-bd12 {
  background-color: rgba(249, 158, 81, 1);
  width: 16px;
  height: 2px;
}

.cm-bd13 {
  background-color: rgba(106, 207, 255, 1);
  width: 16px;
  height: 2px;
  margin-left: 8px;
}

.cm-bd14 {
  background-color: rgba(101, 219, 223, 1);
  width: 16px;
  height: 3px;
  margin-left: 8px;
}

.cm-bd15 {
  width: 429px;
  height: 37px;
  margin-top: 27px;
}

.cm-block2 {
  height: 37px;
  background: url(./img/pscp3aae2iqdw3ac9gndffdyc39yrykkd5s1908313d-676f-4b61-9e67-9436cd17c792.png)
    100% no-repeat;
  width: 104px;
}

.cm-txt5 {
  width: 56px;
  height: 15px;
  overflow-wrap: break-word;
  color: rgba(214, 253, 254, 1);
  font-size: 14px;
  font-family: MicrosoftYaHei;
  text-align: left;
  white-space: nowrap;
  line-height: 14px;
  display: block;
  margin: 11px 0 0 24px;
}

.cm-block3 {
  height: 37px;
  background: url(./img/psf0ckzzhbc76f844iqtleftqk4eyk9d1qo1ebe846e-bf42-4315-9e38-4a6a363e1b54.png)
    100% no-repeat;
  width: 104px;
}

.cm-word9 {
  width: 56px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(77, 153, 250, 1);
  font-size: 14px;
  font-family: MicrosoftYaHei;
  text-align: left;
  white-space: nowrap;
  line-height: 14px;
  display: block;
  margin: 12px 0 0 24px;
}

.cm-block4 {
  height: 36px;
  background: url(./img/psuyi4hqq0pkaqfj93gw4wwe40fts0kb39v5d5444bb-f12d-433f-aedd-8b3f1e1640cc.png)
    0px 0px no-repeat;
  width: 104px;
}

.cm-txt6 {
  width: 84px;
  height: 15px;
  overflow-wrap: break-word;
  color: rgba(77, 153, 250, 1);
  font-size: 14px;
  font-family: MicrosoftYaHei;
  text-align: left;
  white-space: nowrap;
  line-height: 14px;
  display: block;
  margin: 11px 0 0 10px;
}

.cm-block5 {
  height: 36px;
  background: url(./img/psz0bmh6idutfxbad0psj4kanrvoyb64c4aceb2794-613f-4d30-9a81-385da24d61eb.png)
    0px 0px no-repeat;
  width: 104px;
}

.cm-word10 {
  width: 56px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(77, 153, 250, 1);
  font-size: 14px;
  font-family: MicrosoftYaHei;
  text-align: left;
  white-space: nowrap;
  line-height: 14px;
  display: block;
  margin: 12px 0 0 24px;
}

.cm-bd16 {
  height: 100%;
  width: 100%;
  margin: 10px 0 0 3px;
}

.cm-bd16-content {
  margin-right: 10px;
}

.cm-bd16-table {
  border-collapse: collapse;
  width: 100%;
  height:30%;
  color: #fff;
  text-align: center;
}

.cm-bd16-tr {
  margin: 8px 0 0 20px;
  width: 709px;
  overflow-wrap: break-word;
  color: rgba(170, 252, 255, 1);
  font-size: 18px;
  white-space: nowrap;
  line-height: 18px;
  font-family: MicrosoftYaHei;
  text-align: center;
}

.cm-bd16-data-tr {
  line-height: 60px;
}

.cm-bd16-data-span {
  overflow:hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
  width: 10px;
}

.cm-outer3 {
  width: 366px;
  height: 212px;
  margin: 17px 0 0 40px;
}

.cm-outer3-child {
  display: flex;
  position: relative
}

.cm-outer3-child-text {
  position: absolute;
  bottom: -15px;
}

.cm-wrap3 {
  background-image: linear-gradient(
    rgba(80, 179, 255, 0.69) 0,
    rgba(80, 179, 255, 0.69) 0,
    rgba(121, 251, 247, 0.69) 100%,
    rgba(121, 251, 247, 0.69) 100%
  );
  width: 10px;
  height: 207px;
}

.cm-wrap4 {
  background-image: linear-gradient(
    rgba(121, 251, 247, 0.69) 0,
    rgba(121, 251, 247, 0.69) 0,
    rgba(121, 251, 247, 0.69) 100%,
    rgba(121, 251, 247, 0.69) 100%
  );
  width: 10px;
  height: 156px;
  margin: 51px 0 0 7px;
}

.cm-wrap5 {
  background-image: linear-gradient(
    rgba(80, 179, 255, 0.69) 0,
    rgba(80, 179, 255, 0.69) 0,
    rgba(121, 251, 247, 0.69) 100%,
    rgba(121, 251, 247, 0.69) 100%
  );
  width: 10px;
  height: 159px;
  margin: 53px 0 0 41px;
}

.cm-wrap6 {
  background-image: linear-gradient(
    rgba(121, 251, 247, 0.69) 0,
    rgba(121, 251, 247, 0.69) 0,
    rgba(121, 251, 247, 0.69) 100%,
    rgba(121, 251, 247, 0.69) 100%
  );
  width: 10px;
  height: 120px;
  margin: 92px 0 0 7px;
}

.cm-wrap7 {
  background-image: linear-gradient(
    rgba(80, 179, 255, 0.69) 0,
    rgba(80, 179, 255, 0.69) 0,
    rgba(121, 251, 247, 0.69) 100%,
    rgba(121, 251, 247, 0.69) 100%
  );
  width: 10px;
  height: 207px;
  margin-left: 42px;
}

.cm-wrap8 {
  background-image: linear-gradient(
    rgba(121, 251, 247, 0.69) 0,
    rgba(121, 251, 247, 0.69) 0,
    rgba(121, 251, 247, 0.69) 100%,
    rgba(121, 251, 247, 0.69) 100%
  );
  width: 10px;
  height: 156px;
  margin: 51px 0 0 7px;
}

.cm-wrap9 {
  background-image: linear-gradient(
    rgba(80, 179, 255, 0.69) 0,
    rgba(80, 179, 255, 0.69) 0,
    rgba(121, 251, 247, 0.69) 100%,
    rgba(121, 251, 247, 0.69) 100%
  );
  width: 10px;
  height: 159px;
  margin: 53px 0 0 37px;
}

.cm-wrap10 {
  background-image: linear-gradient(
    rgba(121, 251, 247, 0.69) 0,
    rgba(121, 251, 247, 0.69) 0,
    rgba(121, 251, 247, 0.69) 100%,
    rgba(121, 251, 247, 0.69) 100%
  );
  width: 10px;
  height: 120px;
  margin: 92px 0 0 7px;
}

.cm-wrap11 {
  background-image: linear-gradient(
    rgba(80, 179, 255, 0.69) 0,
    rgba(80, 179, 255, 0.69) 0,
    rgba(121, 251, 247, 0.69) 100%,
    rgba(121, 251, 247, 0.69) 100%
  );
  width: 10px;
  height: 159px;
  margin: 53px 0 0 42px;
}

.cm-wrap12 {
  background-image: linear-gradient(
    rgba(121, 251, 247, 0.69) 0,
    rgba(121, 251, 247, 0.69) 0,
    rgba(121, 251, 247, 0.69) 100%,
    rgba(121, 251, 247, 0.69) 100%
  );
  width: 10px;
  height: 120px;
  margin: 92px 0 0 7px;
}

.cm-wrap13 {
  background-image: linear-gradient(
    rgba(80, 179, 255, 0.69) 0,
    rgba(80, 179, 255, 0.69) 0,
    rgba(121, 251, 247, 0.69) 100%,
    rgba(121, 251, 247, 0.69) 100%
  );
  width: 10px;
  height: 176px;
  margin: 36px 0 0 42px;
}

.cm-wrap14 {
  background-image: linear-gradient(
    rgba(121, 251, 247, 0.69) 0,
    rgba(121, 251, 247, 0.69) 0,
    rgba(121, 251, 247, 0.69) 100%,
    rgba(121, 251, 247, 0.69) 100%
  );
  width: 10px;
  height: 133px;
  margin: 79px 0 0 7px;
}

.cm-txt7 {
  width: 353px;
  height: 12px;
  overflow-wrap: break-word;
  color: rgba(77, 153, 250, 1);
  font-size: 12px;
  font-family: MicrosoftYaHei;
  text-align: left;
  white-space: nowrap;
  line-height: 12px;
}

.cm-section14 {
  height: 351px;
  background: url(./img/psr7eh3alijnk2qxf2xw338dl6495wiqersce7830d3-104e-4b33-beef-200f96e52a89.png)
    100% no-repeat;
  width: 884px;
  margin: 30px 0 0 -22px;
  z-index: 30;
}

.cm-group5 {
  width: 844px;
  height: 405px;
  margin: 11px 0 0 32px;
}

.cm-mod17 {
  width: 104px;
  height: 31px;
  margin-left: 14px;
}

.cm-main11 {
  z-index: 136;
  position: relative;
  width: 19px;
  height: 31px;
  background: url(./img/psekfpombd0eqhrogn240utc1k4ongzlbhx9f053493-2704-4fb1-9a36-0112f1c9aa37.png)
    100% no-repeat;
  margin-top: 4px;
}

.cm-icon7 {
  z-index: 137;
  position: absolute;
  left: -9px;
  top: -1px;
  width: 19px;
  height: 31px;
}

.cm-word11 {
  width: 80px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 20px;
  font-family: MicrosoftYaHei-Bold;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-top: 10px;
  display: block;
}

.cm-img4 {
  width: 842px;
  height: 10px;
  margin: 0 0 0 2px;
}

.cm-layer9-box10 {
  background-color: rgba(170, 252, 255, 0.01);
  border-radius: 14px;
  height: 28px;
  border: 1px solid rgba(101, 219, 223, 1);
  width: 60px;
  margin: -25px 0 0 380px;
}

.cm-word12 {
  width: 56px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(101, 219, 223, 1);
  font-size: 14px;
  font-family: MicrosoftYaHei;
  text-align: left;
  white-space: nowrap;
  line-height: 14px;
  display: block;
  margin: 7px 0 0 15px;
  cursor: pointer;
}

.cm-main17 {
  background-color: rgba(170, 252, 255, 0.01);
  border-radius: 14px;
  height: 36px;
  border: 1px solid rgba(101, 219, 223, 1);
  margin-left: 10px;
  width: 130px;
  float: right;
  margin-top: -30px;
  margin-right: 10px;
  cursor: pointer;
}

.cm-info7 {
  width: 56px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(101, 219, 223, 1);
  font-size: 14px;
  font-family: MicrosoftYaHei;
  text-align: left;
  white-space: nowrap;
  line-height: 14px;
  display: block;
  margin: 11px auto;
}

.cm-section15 {
  height: 431px;
  margin-top: -55px;
  width: 500px;
  margin-right: 53px;
  display: flex;
  align-items: center;
  z-index: 30;
}

.cm-layer14 {
  z-index: 143;
  position: absolute;
  left: 552px;
  top: 162px;
  width: 108px;
  height: 51px;
  background: url(./img/psx3dc4tigwk53xjuoaksbicw751bdkof86b2c44-a926-426f-b041-38c5f512aa48.png) -1px
    0px no-repeat;
}

.cm-layer15 {
  z-index: 157;
  height: 201px;
  background: url(./img/psr1egp6m6vg264i57lcqn2t6m6ezih7te7dad578-83cd-447b-8c65-d0459eb311be.png)
    100% no-repeat;
  width: 271px;
  position: absolute;
  left: 826px;
  top: 101px;
}

.cm-layer16 {
  width: 111px;
  height: 167px;
  margin: 33px 0 0 82px;
}

.cm-info12 {
  width: 59px;
  overflow-wrap: break-word;
  color: rgba(214, 253, 254, 1);
  font-size: 50px;
  font-family: MicrosoftYaHei-Bold;
  text-align: left;
  white-space: nowrap;
  line-height: 50px;
  /*margin-left: 26px;*/
  display: block;
  margin: 0 auto;
}

.cm-word18 {
  width: 111px;
  height: 28px;
  overflow-wrap: break-word;
  color: rgba(214, 253, 254, 1);
  font-size: 28px;
  font-family: MicrosoftYaHei-Bold;
  text-align: left;
  white-space: nowrap;
  line-height: 28px;
  margin-top: 99px;
  display: block;
  margin-left: 29px;
}

.cm-layer17 {
  z-index: 144;
  height: 141px;
  background: url(./img/psak4scg4bgj5eftoly4jafxsx85c1m3bfcbcb27f4-2fde-4b5b-9249-57f6c2c1ac6c.png)
    100% no-repeat;
  width: 192px;
  position: absolute;
  left: 668px;
  top: 152px;
}

.cm-bd22 {
  width: 65px;
  height: 119px;
  margin: 21px 0 0 65px;
}

.cm-word19 {
  width: 41px;
  overflow-wrap: break-word;
  color: rgba(170, 252, 255, 1);
  font-size: 36px;
  font-family: MicrosoftYaHei-Bold;
  text-align: left;
  white-space: nowrap;
  line-height: 36px;
  /*margin-left: 13px;*/
  margin: 0 auto;
  display: block;
}

.cm-info13 {
  width: 65px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(170, 252, 255, 1);
  font-size: 22px;
  font-family: MicrosoftYaHei-Bold;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-top: 69px;
  display: block;
  margin-left: 12px;
}

.cm-info-gateway {
  width: 65px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(170, 252, 255, 1);
  font-size: 22px;
  font-family: MicrosoftYaHei-Bold;
  text-align: center;
  white-space: nowrap;
  line-height: 22px;
  margin-top: 97px;
  display: block;
}

.cm-layer-gateway {
  z-index: 144;
  height: 141px;
  background: url(./img/psak4scg4bgj5eftoly4jafxsx85c1m3bfcbcb27f4-2fde-4b5b-9249-57f6c2c1ac6c.png) 100% no-repeat;
  width: 192px;
  position: relative;
  /*left: 68px;*/
  top: 110px;
}

.cm-layer-auth {
  z-index: 144;
  height: 141px;
  background: url(./img/psak4scg4bgj5eftoly4jafxsx85c1m3bfcbcb27f4-2fde-4b5b-9249-57f6c2c1ac6c.png) 100% no-repeat;
  width: 192px;
  position: relative;
  left: 328px;
  top: -30px;
}

.cm-layer-usercenter {
  z-index: 144;
  height: 141px;
  background: url(./img/psak4scg4bgj5eftoly4jafxsx85c1m3bfcbcb27f4-2fde-4b5b-9249-57f6c2c1ac6c.png) 100% no-repeat;
  width: 192px;
  position: relative;
  left: 160px;
  top: -270px;
}

.cm-layer-cms {
  z-index: 144;
  height: 141px;
  background: url(./img/psak4scg4bgj5eftoly4jafxsx85c1m3bfcbcb27f4-2fde-4b5b-9249-57f6c2c1ac6c.png) 100% no-repeat;
  width: 192px;
  position: relative;
  left: 528px;
  top: -410px;
}

.cm-layer-exchange {
  z-index: 144;
  height: 141px;
  background: url(./img/psak4scg4bgj5eftoly4jafxsx85c1m3bfcbcb27f4-2fde-4b5b-9249-57f6c2c1ac6c.png)
  100% no-repeat;
  width: 192px;
  position: relative;
  left: 638px;
  top: -452px;
}

.cm-layer-healthy-pic {
  background: url(./img/boxgreen.png) 100% no-repeat;
}

.cm-layer-unhealthy-pic {
  background: url(./img/boxorange.png) 100% no-repeat;
}

.cm-info-healthy {
  color: #67c23a;
}

.cm-info-unhealthy {
  color: #e6a23c;
}

.cm-common-txtcolor-normal {
  color: rgba(95, 212, 183, 1);
}

.cm-common-txtcolor-unnormal {
  color: rgba(255, 117, 41, 1);
}

.cm-table-tr-td-box10 {
  background-color: rgba(170, 252, 255, 0.01);
  border-radius: 14px;
  height: 28px;
  border: 1px solid rgba(101, 219, 223, 1);
  width: 60px;
  margin: 0 auto;
}
