<template>
  <div class="con-left table-wrapper box-bg">
    <div class="flex-between">
      <div class="table-title">{{ title }}</div>
    </div>
    <slot></slot>
  </div>
</template>
<script>
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    title: "",
  },
  data() {
    return {};
  },
  // 监听属性类似于data概念
  computed: {},
  // 方法集合
  methods: {},
};
</script>
<style lang="scss" scoped>
.con-left {
  width: 25%;
  margin-right: 0.16667rem;
  .flex-between {
    display: block;
    padding: 0 0.16667rem 0.08333rem 0.16667rem;
    border-bottom: 0.00833rem solid;
    border-bottom: 0.00833rem solid #f1f1f1;
  }
}
</style>
