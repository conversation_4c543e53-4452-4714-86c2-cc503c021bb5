{"name": "unifast-cloud", "version": "3.5.0", "description": "三全画像", "author": "技术部", "license": "MIT", "scripts": {"dev": "vue-cli-service serve --mode development", "build": "vue-cli-service build --mode production", "build:prod": "vue-cli-service build --mode production_prod", "build:test": "vue-cli-service build --mode production_test", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "dependencies": {"@antv/g2": "^4.1.36", "@antv/g2plot": "^2.4.5", "@aomao/engine": "^2.4.7", "@aomao/plugin-alignment": "^2.4.7", "@aomao/plugin-backcolor": "^2.4.7", "@aomao/plugin-bold": "^2.4.7", "@aomao/plugin-code": "^2.4.7", "@aomao/plugin-file": "^2.4.7", "@aomao/plugin-fontcolor": "^2.4.7", "@aomao/plugin-fontfamily": "^1.2.7", "@aomao/plugin-fontsize": "^2.4.7", "@aomao/plugin-heading": "^2.4.7", "@aomao/plugin-hr": "^2.4.7", "@aomao/plugin-image": "^2.4.8", "@aomao/plugin-indent": "^2.4.7", "@aomao/plugin-italic": "^2.4.7", "@aomao/plugin-line-height": "^1.2.7", "@aomao/plugin-mark": "^2.4.7", "@aomao/plugin-math": "^2.4.7", "@aomao/plugin-mention": "^1.2.7", "@aomao/plugin-orderedlist": "^2.4.7", "@aomao/plugin-paintformat": "^2.4.7", "@aomao/plugin-quote": "^2.4.7", "@aomao/plugin-redo": "^2.4.7", "@aomao/plugin-removeformat": "^2.4.7", "@aomao/plugin-selectall": "^2.4.7", "@aomao/plugin-status": "^1.2.7", "@aomao/plugin-strikethrough": "^2.4.7", "@aomao/plugin-sub": "^2.4.7", "@aomao/plugin-sup": "^2.4.7", "@aomao/plugin-table": "^2.4.10", "@aomao/plugin-tasklist": "^2.4.7", "@aomao/plugin-underline": "^2.4.7", "@aomao/plugin-undo": "^2.4.7", "@aomao/plugin-unorderedlist": "^2.4.7", "@aomao/plugin-video": "^2.4.7", "@iconfu/svg-inject": "^1.2.3", "@riophae/vue-treeselect": "0.4.0", "axios": "0.21.0", "clipboard": "2.0.6", "codemirror": "^5.63.1", "core-js": "^3.37.1", "dayjs": "^1.11.4", "echarts": "^4.9.0", "echarts-wordcloud": "^1.1.3", "el-table-infinite-scroll": "^2.0.0", "element-ui": "^2.15.3", "file-saver": "2.0.4", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "js-beautify": "1.13.0", "jsencrypt": "3.0.0-rc.1", "lodash-es": "^4.17.21", "moment": "^2.29.4", "nprogress": "0.2.0", "qweather-icons": "^1.1.0", "screenfull": "^5.0.2", "sm-crypto": "^0.3.0", "sortablejs": "1.10.2", "swiper": "^5.3.6", "v-scale-screen": "^0.1.1", "vue": "2.6.12", "vue-ckeditor5-auto": "^1.0.0", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-grid-layout": "^2.3.12", "vue-json-viewer": "^2.2.22", "vue-router": "3.4.9", "vue-simple-verify": "^1.1.0", "vue-splitpane": "^1.0.6", "vuedraggable": "^2.24.3", "vuex": "3.6.0"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "chalk": "4.1.0", "connect": "3.6.6", "element-china-area-data": "^5.0.2", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "less": "^3.13.1", "less-loader": "^5.0.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.0", "sass-loader": "10.1.0", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}