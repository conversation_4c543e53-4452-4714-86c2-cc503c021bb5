<template>
  <div class="portal-body">
    <div class="product-list">
      <div class="table-wrapper box-bg">
        <div class="search-wrapper box-bg" style="padding: 0 0">
          <el-form ref="form" :model="query" label-width="200px" label-position="left">
            <el-row>
              <el-col :span="7">
                <el-form-item label="地市">
                  <el-col>
                    <el-select v-model="query.city" placeholder="全部" clearable>
                      <el-option label="全部" value=""></el-option>
                      <el-option label="深圳" value="深圳"></el-option>
                      <el-option label="广州" value="广州"></el-option>
                      <el-option label="东莞" value="东莞"></el-option>
                    </el-select>
                  </el-col>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="专线受理时间">
                  <el-date-picker
                    v-model="query.dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="yyyy-MM-dd"
                    style="width: 255px;">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="客户名称">
                  <el-col>
                    <el-input v-model="query.customerName" clearable placeholder="请输入"></el-input>
                  </el-col>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="7">
                <el-form-item label="专线开通订单号">
                  <el-col>
                    <el-input v-model="query.openCode" clearable placeholder="请输入订单号"></el-input>
                  </el-col>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="装机地址">
                  <el-col>
                    <el-input v-model="query.installAddress" clearable placeholder="请输入装机地址"></el-input>
                  </el-col>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="业务号码">
                  <el-col>
                    <el-input v-model="query.businessCode" clearable placeholder="请输入业务号码"></el-input>
                  </el-col>
                </el-form-item>
              </el-col>
            </el-row>
            <div class="btn-group" style="top: 4px;right: 0;">
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
            </div>
          </el-form>
        </div>
        <div class="flex-between">
          <div class="table-title">专线受理流程查询</div>
          <div>
            <el-button size="small" icon="el-icon-upload2" @click="exportData">导出</el-button>
          </div>
        </div>
        <el-divider></el-divider>
        <div class="table-con">
          <el-table :data="tableData" border stripe>
            <el-table-column prop="orderNo" label="订单号"></el-table-column>
            <el-table-column prop="city" label="地市"></el-table-column>
            <el-table-column prop="productName" label="商品名称"></el-table-column>
            <el-table-column prop="customerName" label="客户名称"></el-table-column>
            <el-table-column prop="acceptTime" label="受理时间"></el-table-column>
            <el-table-column prop="installAddress" label="装机地址"></el-table-column>
            <el-table-column prop="businessCode" label="业务号码"></el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="viewDetail(scope.row)">查看流程详情</el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="query.pageNum"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="query.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total">
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "GxsxTab",
  data() {
    return {
      query: {
        pageNum: 1,
        pageSize: 10,
        city: '',
        dateRange: ['2025-06-03', '2025-06-10'],
        installAddress: '',
        openCode: '',
        customerName: '',
        businessCode: ''
      },
      total: 0,
      tableData: [
        {
          orderNo: '250117001710700',
          city: '深圳',
          productName: '专网云网大网（全国通用）',
          customerName: '公安局',
          acceptTime: '2025-01-17',
          installAddress: '深圳市南山区科技园',
          businessCode: '173*********'
        }
      ]
    }
  },
  methods: {
    handleQuery() {
      console.log('共享随行专网查询', this.query);
      this.$message.success('查询成功');
    },

    resetQuery() {
      this.query = {
        pageNum: 1,
        pageSize: 10,
        city: '',
        dateRange: ['2025-06-03', '2025-06-10'],
        installAddress: '',
        openCode: '',
        customerName: '',
        businessCode: ''
      };
    },

    exportData() {
      this.$message.success('导出成功');
    },

    viewDetail(row) {
      this.$message.info(`查看${row.orderNo}的详情`);
    },

    handleSizeChange(val) {
      this.query.pageSize = val;
      this.handleQuery();
    },

    handleCurrentChange(val) {
      this.query.pageNum = val;
      this.handleQuery();
    },


  }
}
</script>

<style scoped lang="less">
@import "../../../../assets/sanquan/index.less";

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
}

.pagination-wrapper {
  padding: 20px 24px;
  text-align: right;
  border-top: 1px solid #e4e7ed;
}
</style>
