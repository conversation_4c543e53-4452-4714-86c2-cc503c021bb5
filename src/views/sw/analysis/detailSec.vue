<template>
    <div class="portal-body">
        <div class="analysis">
            <div class="flex-start back" @click="goBack()">
                <img src="../../../assets/images/sw/back.png" alt=""  style="width: 14px;height: 14px;">
                <p>返回</p>
            </div>

            <div class="search-wrapper box-bg">
                <el-form ref="form" :model="formQuery" label-width="1rem">
                    <el-row>
                        <el-col :span="7">
                            <el-form-item label="账期">
                                <el-col>
                                    <el-date-picker v-model="formQuery.month" type="month" placeholder="选择月">
                                    </el-date-picker>
                                </el-col>
                            </el-form-item>
                        </el-col>
                        <el-col :span="7">
                            <el-form-item label="地市">
                                <el-col>
                                    <!-- <el-input v-model="formQuery.city" clearable></el-input> -->
                                    <el-select v-model="formQuery.city" placeholder="请选择">
                                        <el-option v-for="item in citylist" :key="item.value" :label="item.label"
                                            :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-col>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <div class="btn-group">
                        <el-button @click="resetForm">重置</el-button>
                        <el-button type="primary" @click="conditionQuery">查询</el-button>
                    </div>
                </el-form>
            </div>

            <div class="table-wrapper box-bg">
                <div class="flex-between" style="padding: 0 0.2rem;">
                    <div class="table-title">执行中工单列表</div>
                </div>
                <el-divider></el-divider>
                <div class="table-con">
                    <el-table :data="tableData" style="width: 100%">
                        <!-- <el-table-column type="selection"></el-table-column> -->
                        
                        <el-table-column v-for="item in tableColumns" :key="item.id" :prop="item.value"
                            :label="item.name" :align="'center'">
                        </el-table-column>
                    </el-table>

                    <div class="flex-end" style="margin-top: 0.14rem;">

                        <!-- <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                            :current-page.sync="currentPage" :page-size="100" layout="total, prev, pager, next"
                            :total="1000">
                        </el-pagination> -->
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>

<style scoped lang="less">
p {
    margin: 0;
}

ul,
li {
    list-style: none;
}

.analysis {
    padding-bottom: 20px;

    .back {
        margin-top: 10px;
        cursor: pointer;
    }

    .search-wrapper {
        margin-top: 10px;
    }
}
</style>

<script>
import { executingDownTwo } from '@/api/sw/analysis/analysis'
export default {
    name: 'index',
    data() {
        return {
            title: this.$route.params.title,
            industry:this.$route.params.industry,
            type:this.$route.params.type,
            tableColumns: [
                {
                    name: '客户名称',
                    value: 'customerName',
                }, {
                    name: '客户经理',
                    value: 'customerManager',
                }, {
                    name: '执行开始时间',
                    value: 'beginExecTime',
                }, {
                    name: '执行天数',
                    value: 'execdays',
                }
            ],
            tableData: [],
            formQuery: {
                monthId: '202404',
                industry: '',
                city: ''
            },
            currentPage: 1,
            citylist: [
                {
                    label:'全省',
                    value:''
                },{
                    label:'济南市',
                    value:'济南'
                },{
                    label:'青岛市',
                    value:'青岛'
                },{
                    label:'潍坊市',
                    value:'潍坊'
                },{
                    label:'淄博市',
                    value:'淄博'
                },{
                    label:'枣庄市',
                    value:'枣庄'
                },{
                    label:'东营市',
                    value:'东营'
                },{
                    label:'烟台市',
                    value:'烟台'
                },{
                    label:'济宁市',
                    value:'济宁'
                },{
                    label:'泰安市',
                    value:'泰安'
                },{
                    label:'威海市',
                    value:'威海'
                },{
                    label:'日照市',
                    value:'日照'
                },{
                    label:'临沂市',
                    value:'临沂'
                },{
                    label:'德州市',
                    value:'德州'
                },{
                    label:'聊城市',
                    value:'聊城'
                },{
                    label:'滨州市',
                    value:'滨州'
                },{
                    label:'菏泽市',
                    value:'菏泽'
                },
            ],

        }
    },
    props: ['title', 'industry', 'type'],
    computed: {

    },
    created(){
        
    },
    mounted() {
        this.formQuery.industry = this.industry
        this.getData()
    },
    methods: { 
        getData(){
            executingDownTwo(this.formQuery).then(res=>{
                if(res.success){

                }
            })
        },
        //返回
        goBack() {
            this.$router.push({
                name: "analysisDetail",
                params: { 
                    title:this.title,
                    industry: this.industry ,
                    type:this.type
                }
            });
            
        },
        
        // 重置表单数据
        resetForm() {
            this.formQuery = {
                month: ''
            };
        },
        conditionQuery() {

        },
        handleCurrentChange(){

        },
        handleSizeChange(){

        }

    }
}
</script>