<template>
  <div class="notify">
    <uni-notice-bar
      :text="datas.config.notifyText"
      :background-color="datas.config.backColor"
      :color="datas.config.textColor"
      :moreColor="datas.config.textColor"
      show-get-more show-icon
    />
    <!-- 删除组件 -->
    <slot name="deles" />
  </div>
</template>

<script>
import uniNoticeBar from "@/components/UniUI/uni-notice-bar"
export default {
  name: 'notify',
  components: { uniNoticeBar },
  props: {
    datas: Object,
  },
}
</script>

<style scoped lang="less">
.notify {
  position: relative;
}
</style>
