<template>
  <div class="app-container">
    <el-card shadow="never">
      <el-row :gutter="20" type="flex" justify="center">
        <el-col :span="12">
          <el-form
            :model="watermark"
            :rules="rules"
            ref="watermark"
            label-width="100px"
          >
            <el-form-item label="是否开启水印" prop="delivery">
              <el-switch v-model="watermark.status"></el-switch>
            </el-form-item>
            <el-form-item label="水印内容" prop="content">
              <el-input v-model="watermark.content"></el-input>
            </el-form-item>
            <el-form-item label="水印密度" prop="density">
              <el-slider v-model="watermark.density"></el-slider>
            </el-form-item>
            <el-form-item label="水印偏移度" prop="rotate">
              <el-slider v-model="watermark.rotate"></el-slider>
            </el-form-item>
            <el-form-item label="字体大小" prop="fontSize">
              <el-slider v-model="watermark.fontSize"></el-slider>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                :loading="saveLoading"
                @click="submitForm()"
                >更新</el-button
              >
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { getPersonalConfig, updateConfig } from "@/api/system/config";

export default {
  name: "Watermark",
  data() {
    return {
      saveLoading: false,
      watermark: {
        status: undefined,
        content: undefined,
        density: undefined,
        rotate: undefined,
        fontSize: undefined,
        color: undefined,
        transparent: undefined
      },
      obj: {},
      rules: {
        name: [
          { required: true, message: "请输入活动名称", trigger: "blur" },
          { min: 3, max: 5, message: "长度在 3 到 5 个字符", trigger: "blur" },
        ],
        region: [
          { required: true, message: "请选择活动区域", trigger: "change" },
        ],
        date1: [
          {
            type: "date",
            required: true,
            message: "请选择日期",
            trigger: "change",
          },
        ],
        date2: [
          {
            type: "date",
            required: true,
            message: "请选择时间",
            trigger: "change",
          },
        ],
        type: [
          {
            type: "array",
            required: true,
            message: "请至少选择一个活动性质",
            trigger: "change",
          },
        ],
        resource: [
          { required: true, message: "请选择活动资源", trigger: "change" },
        ],
        desc: [{ required: true, message: "请填写活动形式", trigger: "blur" }],
      },
      loading: false,
    };
  },
  created() {
    this.getConfig();
  },
  methods: {
    getConfig() {
      this.loading = true;
      getPersonalConfig({
        configCode: "Watermark",
      }).then((r) => {
        this.loading = false;
        if (r.success) {
          var atta = JSON.stringify(r.data.attra)
          var attb = JSON.parse(atta)
          this.watermark = {...attb}
          //           this.watermark = {
          //   ...JSON.parse(r.data.attra),
          // };
          this.obj = r.data;
        } else {
          this.$message.error(r.message);
        }
      });
    },
    submitForm: function () {
      this.$refs["watermark"].validate((valid) => {
        if (valid) {
          this.saveLoading = true;
          updateConfig({
            configId: this.obj.configId,
            attra: JSON.stringify(this.watermark),
          }).then((r) => {
            this.saveLoading = false;
            if (r.success) {
              this.msgSuccess("修改成功");
            } else {
              this.$message.error(r.message);
            }
          });
        }
      });
    },
  },
};
</script>
