import request from '@/utils/request'

// 查询参数列表
export function listConfig(query) {
    return request({
        url: '/user/configs',
        method: 'get',
        params: query
    })
}

// 查询参数详细
export function getConfig(configId) {
    return request({
        url: '/user/configs/' + configId,
        method: 'get'
    })
}

// 根据参数键名查询参数值
export function selectByConfigCode(data) {
    return request({
        url: '/user/configs/selectByConfigCode',
        method: 'post',
        data
    })
}

// 新增参数配置
export function addConfig(data) {
    return request({
        url: '/user/configs',
        method: 'post',
        data: data
    })
}

// 修改参数配置
export function updateConfig(data) {
    return request({
        url: '/user/configs/edit',
        method: 'post',
        data: data
    })
}

// 删除参数配置
export function delConfig(configId) {
    return request({
        url: '/user/configs/delete/' + configId,
        method: 'get'
    })
}

export function getPersonalConfig(params) {
    return request({
        url: '/user/personalConfig/getPersonalConfig',
        method: 'post',
        params
    })
}

export function getPersonalThemeConfig(params) {
  return request({
    url: '/user/personalConfig/getPersonalThemeConfig',
    method: 'post',
    data: params
  })
}

export function getAllLayoutList(params) {
    return request({
        url: '/user/personalConfig/getAllLayoutList',
        method: 'post',
        data: params
    })
}

export function selectAllTenantConfig(params) {
  return request({
    url: '/user/personalConfig/selectConfigForTenant',
    method: 'post',
    params
  })
}

export function selectPersonalTenantConfig(params) {
  return request({
    url: '/user/personalConfig/selectConfigByTenantId',
    method: 'post',
    params
  })
}

export function selectGeneralConfigForTenant(params) {
  return request({
    url: '/user/personalConfig/selectGeneralConfigForTenant',
    method: 'post',
    params
  })
}

export function updateStatus(data) {
    return request({
        url: '/user/personalConfig/updateStatus',
        method: 'post',
        data
    })
}

export function updateTenantStatus(data) {
  return request({
    url: '/user/personalConfig/updateTenantStatus',
    method: 'post',
    data
  })
}

export function updateOrgStatus(data) {
  return request({
    url: '/user/personalConfig/updateOrgStatus',
    method: 'post',
    data
  })
}

export function saveSysPersonalConfig(data) {
    return request({
        url: '/user/personalConfig/SysPersonalConfig',
        method: 'post',
        data
    })
}

export function saveSysTenantConfig(data) {
  return request({
    url: '/user/personalConfig/addConfigForTenant',
    method: 'post',
    data
  })
}

export function addLayout(data) {
  return request({
    url: '/user/personalConfig/addLayout',
    method: 'post',
    data
  })
}


export function updateConfigInfo(data) {
    return request({
        url: '/user/personalConfig/updateConfigInfo',
        method: 'post',
        data
    })
}

export function updatePersonalThemeMode(data) {
  return request({
    url: '/user/personalConfig/updatePersonalThemeMode',
    method: 'post',
    data
  })
}

export function deletePersonalId(personalId) {
    return request({
        url: '/user/personalConfig/delete/' + personalId,
        method: 'get',
    })
}


export function statSpeed() {
    return request({
        url: '/auth/stat/speed',
        method: 'post',
    })
}

// export function oauthToken(data) {
//   return request({
//     url: '/auth/oauth/token?grant_type='+data.grant_type+'&code='+data.code+'&client_id='+data.client_id+'&client_secret='+data.client_secret+'&redirect_uri='+data.redirect_uri,
//     method: 'post'
//   })
// }

export function oauthToken(data) {
  return request({
    url: '/auth/oauth/token',
    method: 'get',
    params: data
  })
}

// export function oauthToken2(data) {
//   return request({
//     url: '/auth/oauth/authorize',
//     method: 'post',
//     data
//   })
// }
