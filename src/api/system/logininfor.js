import request from '@/utils/request'

// 查询登录日志列表
export function list(query) {
  return request({
    url: '/user/syslog/loginInfoPage',
    method: 'post',
    data: query
  })
}

// 删除登录日志
export function delLogininfor(infoId) {
  return request({
    url: '/user/syslog/' + infoId,
    method: 'delete'
  })
}

// 清空登录日志
export function cleanLogininfor() {
  return request({
    url: '/user/syslog/clean',
    method: 'delete'
  })
}
