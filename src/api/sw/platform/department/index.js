import request from "@/utils/request";


// 查询部门树
export function treeDept(params) {
    return request({
      url: "/sanquan/dept/findTree",
      method: "get",
      params: params
    });
  }

  // 新增部门
  export function addDept(data) {
    return request({
      url: "/sanquan/dept/save",
      method: "post",
      data: data
    });
  }

 // 删除部门
 export function deleteDept(params) {
    return request({
      url: "/sanquan/dept/delete",
      method: "get",
      data: params
    });
  }

  // 分页查询部门信息
export function findPageDept(data) {
    return request({
      url: "/sanquan/dept/findPage",
      method: "post",
      params: data
    });
  }

  // 查询单个部门
export function findOneDept(params) {
    return request({
      url: "/sanquan/dept/findOne",
      method: "get",
      params: params
    });
  }