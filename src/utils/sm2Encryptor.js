import { sm2 } from "sm-crypto";
import {generateKeyPairHex} from "sm-crypto/src/sm2/utils";
import {getPublicKey} from "@/api/login";

/**
 * 生成密钥对
 */
function doGenerate() {
    var keypair = generateKeyPairHex();
    console.log("key: " + keypair);

    var privateKey = keypair.ecprvhex;
    var publicKey = keypair.ecpubhex;
    console.log("privateKey:" + privateKey);
    console.log("publicKey:" + publicKey);
}

export function cmdSM2Encrypt(str) {
  return new Promise((resolve,reject)=>{
    getPublicKey().then((data) => {
      var key = data;
      if(data == "" || data == undefined){
        reject('解析错误')
      }
      // console.log("公钥："+ key);
      // sm2 加密 模式选择已封装为C1C3C2
      resolve(doCrypt(key, str))
    });
  })
}

/**
 * sm2加密方法
 * @param publickey
 * @param value
 */
export function doCrypt(publickey, value) {
  let encryptData = sm2.doEncrypt(value, publickey, 1);
  // console.log("加密后："+"04" + encryptData);
  return "04" + encryptData;
}
/**
 * sm2解密方法
 * @param publickey
 * @param msg
 */
export function doDecrypt(privateKey, value) {
  value = value.slice(2);
  var data = sm2.doDecrypt(value, privateKey, 1);
  // console.log("解密后："+ data );
  return data;
}
