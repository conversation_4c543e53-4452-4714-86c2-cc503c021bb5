import request from '@/utils/request'

// 分页查询交辅警业务发展数据报表明细
export function getPageList(params) {
  return request({
    url: '/sanquan/targetMarketing/police/findPage',
    method: 'get',
    params,
  })
}

// 查询交辅警业务发展数据报表明细详细信息
export function getInfo(id) {
  return request({
    url: '/sanquan/targetMarketing/police/findInfo',
    method: 'get',
    params: {
        id
    }
  })
}

// 新增交辅警业务发展数据报表明细
export function addPolice(data) {
  return request({
    url: '/sanquan/targetMarketing/police/add',
    method: 'post',
    data,
  })
}

// 修改交辅警业务发展数据报表明细
export function updatePolice(data) {
  return request({
    url: '/sanquan/targetMarketing/police/update',
    method: 'post',
    data,
  })
}

// 删除交辅警业务发展数据报表明细
export function deletePolice(id) {
  return request({
    url: '/sanquan/targetMarketing/police/delete',
    method: 'post',
    params: {
        id
    }
  })
}
