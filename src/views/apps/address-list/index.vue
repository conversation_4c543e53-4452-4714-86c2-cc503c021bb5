<template>
  <div class="app-container" style="height: calc(100vh - 120px);">
    <el-card
      class="dep-card"
      shadow="never"
      v-loading="treeLoading"
      body-style="height:850px"
    >
      <el-tabs v-model="activeName" type="card" @tab-click="tabsHandleClick">
        <el-tab-pane label="个人" name="user"></el-tab-pane>
        <el-tab-pane label="机构" name="org"></el-tab-pane>
      </el-tabs>

      <split-pane
        :min-percent="10"
        :max-percent="30"
        :default-percent="15"
        split="vertical"
      >
        <template slot="paneL">
          <el-card
            class="dep-card"
            shadow="never"
            v-loading="treeLoading"
            body-style="width:500px"
          >
            <div slot="header" class="clearfix">
              <span>组织</span>
              <el-button
                style="float: right; padding: 3px 0"
                type="text"
                icon="el-icon-refresh"
                @click="reloadTree">
                刷新
              </el-button>
              <el-form style="margin-top: 20px;margin-bottom: -20px;" v-if="$store.getters.customParam.userType === 'admin'">
                <el-form-item label="租户：">
                  <el-select
                    v-model="queryParams.tenantId"
                    style="width: 100px;"
                    filterable
                    remote
                    :remote-method="getTenantList"
                    :loading="getTenantLoading"
                    @change="tenantChange"
                  >
                    <el-option
                        v-for="item in tenantList"
                        :key="item.tenantId"
                        :label="item.tenantName"
                        :value="item.tenantId"
                    />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
            <el-tree
              :props="lazyTreeProps"
              :load="loadNode"
              lazy
              :expand-on-click-node="false"
              ref="asyncTree"
              @node-click="handleNodeClick"
              node-key="orgId"
              :default-expanded-keys="[defaultOrgId]"
              highlight-current>
            </el-tree>
          </el-card>
        </template>
        <template slot="paneR">
          <el-card class="dep-card" shadow="never">
            <el-form
              :model="queryParams"
              ref="queryForm"
              :inline="true"
              v-show="showSearch"
              label-width="68px"
            >
              <el-form-item label="用户名称" prop="staffName"  v-if="activeName==='user'">
                <el-input
                  v-model="queryParams.staffName"
                  placeholder="请输入用户名称"
                  clearable
                  size="small"
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="邮箱" prop="email" v-if="activeName==='user'">
                <el-input
                  v-model="queryParams.email"
                  placeholder="请输入邮箱"
                  clearable
                  size="small"
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"/>
              </el-form-item>
               <el-form-item label="组织机构" prop="orgName"  v-if="activeName!=='user'">
                <el-input
                  v-model="queryParams.orgName"
                  placeholder="请输入用户名称"
                  clearable
                  size="small"
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="组织电话" prop="phone" v-if="activeName!=='user'">
                <el-input
                  v-model="queryParams.phone"
                  placeholder="办公电话"
                  clearable
                  size="small"
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"/>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  size="mini"
                  @click="handleQuery">
                  搜索
                </el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
                  重置
                </el-button>
              </el-form-item>
            </el-form>

            <el-table v-loading="loading" :data="tableList">
              <el-table-column v-for="column in columns"
                               :prop="column.name"
                               :label="column.label"
                               :key="column.name"
                               v-if="column.visible">
              </el-table-column>

              <el-table-column
                label="操作"
                align="center"
                width="100"
                class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <div>
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-view"
                      :loading="getUserLoading && scope.row.userId === loadUserId"
                      @click="queryUpdate(scope.row)">
                      查看
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"/>
          </el-card>
        </template>
      </split-pane>
    </el-card>

    <!-- 个人详细信息 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" label-width="80px">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="用户名称" prop="staffName">
              <el-input
                v-model="form.staffName"
                placeholder="无"
                maxlength="64"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归属组织" prop="orgName">
              <el-input
                v-model="form.orgName"
                placeholder="无"
                disabled>
              </el-input>
            </el-form-item>
            <el-form-item hidden prop="orgId">
              <el-input v-model="form.orgId"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="登录名称" prop="loginName">
              <el-input
                v-model="form.loginName"
                placeholder="无"
                maxlength="64"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="cellphone">
              <el-input
                v-model="form.cellphone"
                placeholder="无"
                maxlength="11"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="form.email"
                placeholder="无"
                maxlength="50"
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>

    <!-- 组织详细信息 -->
    <el-dialog :title="title" :visible.sync="orgOpen" width="800px" append-to-body>
      <el-form ref="form" :model="form" label-width="80px">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="组织名称" prop="orgName">
              <el-input
                v-model="form.orgName"
                placeholder="无"
                maxlength="64"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="组织电话" prop="phone">
              <el-input
                v-model="form.phone"
                placeholder="无"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="传真" prop="fax">
              <el-input
                v-model="form.fax"
                placeholder="无"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="form.email"
                placeholder="无"
                maxlength="50"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="组织类型">
              <el-input
                  v-model="form.orgTypeText"
                  placeholder="无"
                  maxlength="50"
                  disabled
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {getDept, listDept, treeselect} from "@/api/system/dept";
import {getUser, listUser} from "@/api/system/user";
import { list as tenantList } from '@/api/system/tenant'

export default {
  name: "addressList",
  data() {
    return {
      activeName: 'user',
      getTenantLoading: false,
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 状态数据字典
      statusOptions: [],
      //用户类型
      userStatusOptions: [],
      userTypeOptions: [],
      // 表单参数
      form: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        staffName: undefined,
        email: undefined,
        orgId: this.$store.getters.orgId,
        orgType: '',
        telephonenumber: '',
        name: '',
        orgEmail: '',
        phone:'',
        orgName:'',
        tenantId: this.$store.getters.customParam.tenantId,
        tenantName: this.$store.getters.customParam.tenantName,
      },
      // 列信息
      columns: [
        {key: 0, label: `用户编号`, name: 'userId', visible: false},
        {key: 1, label: `用户名称`, name: 'staffName', visible: true},
        {key: 2, label: `所属组织`, name: 'orgName', visible: true},
        {key: 4, label: `手机号码`, name: 'cellphone', visible: true},
        {key: 5, label: `邮箱`, name: 'email', visible: true},
      ],
      lazyTreeProps: {
        children: "children",
        label: "orgName",
        isLeaf: "leaf",
      },
      treeLoading: false,
      selectNodeName: "全部人员",
      selectNode: undefined,
      getUserLoading: false,
      loadUserId: undefined,
      roleOpen: false,
      userRoles: [],
      rolesData: [],
      listRole: [],
      open: false,
      orgOpen: false,
      tableList: [],
      saveLoading: false,
      defaultOrgId: this.$store.getters.orgId,
      orgTypeOptions: [],
      tenantList: [],
    };
  },
  created() {
    this.getTenantList();
    this.getList();
    this.getDicts("org_type").then((response) => {
      this.orgTypeOptions = response.data;
    });
  },
  watch: {
    activeName(val) {
      if (val === "user") {
        this.columns = [
          {key: 0, label: `用户编号`, name: 'userId', visible: false},
          {key: 1, label: `用户名称`, name: 'staffName', visible: true},
          {key: 2, label: `所属组织`, name: 'orgName', visible: true},
          {key: 4, label: `手机号码`, name: 'cellphone', visible: true},
          {key: 5, label: `邮箱`, name: 'email', visible: true},
        ]
      } else {
        this.expandAll = true
        this.columns = [
          {key: 0, label: `用户编号`, name: 'userId', visible: false},
          {key: 2, label: `组织名称`, name: 'orgName', visible: true},
          {key: 7, label: `组织电话`, name: 'phone', visible: true},
          {key: 8, label: `组织传真`, name: 'fax', visible: true},
          {key: 9, label: `组织电子邮箱`, name: 'email', visible: true},
        ];
      }
      this.reloadTree();
      this.getList();
    }
  },
  methods: {
    getTenantList(tenantName) {
      this.getTenantLoading = true;
      let query = {}
      if (tenantName !== undefined && tenantName !== '') {
        query.tenantName = tenantName
        query.tenantId = undefined
      } else {
        query.tenantId = this.queryParams.tenantId
      }
      tenantList(query).then((response) => {
        this.tenantList = response.data;
      }).finally(() => {
        this.getTenantLoading = false;
      });
    },
    tabsHandleClick(tab, event) {
      this.activeName = tab.name;
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      if (this.activeName === "user") {
        this.queryParams.orgType = null;
        this.queryParams.telephonenumber = null;
        listUser(this.addDateRange(this.queryParams, this.dateRange)).then(
          (response) => {
            if(response.data){
              this.tableList = response.data.records;
              this.total = response.data.total;
            }
            this.loading = false;
          }
        );
      } else {
        this.queryParams.orgType = 'all';
        this.queryParams.name = this.queryParams.staffName;
        this.queryParams.email = null;
        listDept(this.addDateRange(this.queryParams, this.dateRange)).then(
          (response) => {
            this.tableList = response.data.records;
            this.total = response.data.total;
            this.loading = false;
          }
        );
      }
    },
    // 重新加载树形结构的组织
    reloadTree() {
      if (this.selectNode) {
        const node = this.$refs.asyncTree.getNode(this.selectNode);
        node.childNodes = [];
        node.loaded = false;
        node.expand();
      } else {
        this.$refs.asyncTree.root.loaded = false;
        this.$refs.asyncTree.root.expand();
      }
    },
    //懒加载树形结构的组织
    loadNode(node, resolve) {
      if (!node.data) {
        this.treeLoading = true;
      }
      treeselect({
        orgId: node.data ? node.data.orgId : this.defaultOrgId,
        queryType: node.data ? "down" : "current",
        orgType: 'all',
        tenantId: this.queryParams.tenantId
      }).then((response) => {
        let treeList = response.data;
        resolve(treeList);
        this.treeLoading = false;
      });
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.orgId = data.orgId;
      this.selectNode = data;
      this.selectNodeName = data.orgName;
      this.getList();
    },
    allUser() {
      const node = this.$refs.asyncTree.root;
      node.loaded = false;
      node.expand();
      this.queryParams.orgId = this.defaultOrgId;
      this.selectNodeName = "全部人员";
      this.selectNode = undefined;
      this.getList();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 查看按钮操作 */
    queryUpdate(row) {
      this.reset();
      if (this.activeName === 'user') {
        const userid = row.staffId;
        this.loadUserId = userid;
        this.getUserLoading = true;
        getUser(userid)
          .then((response) => {
            getDept(response.data.orgId).then((r) => {
              this.getUserType(r.data.orgType, () => {
                this.getUserLoading = false;
                this.loadUserId = undefined;
                if (response.data) {
                  this.form = {
                    ...this.form,
                    ...response.data,
                    orgFullName: r.data.name,
                  };
                  this.open = true;
                  this.title = "查看用户";
                } else {
                  this.$message.error("数据异常！");
                }
              });
            });
          })
          .catch((e) => {
            this.getUserLoading = false;
            this.loadUserId = undefined;
            this.$message.error("数据异常！");
          });
      } else {
        getDept(row.orgId).then((response) => {
          getDept(response.data.orgId).then((r) => {
            this.getDeptLoading = false;
            this.loadOrgId = undefined;
            if (response.data) {
              this.form = {
                ...this.form,
                ...response.data,
                orgName: r.data.orgName,
                orgId: response.data.orgId,
              };
              this.convertDictLabel(this.form.orgType)
              this.orgOpen = true;
              this.title = "查看组织";
            } else {
              this.$message.error("数据异常！");
            }
          }).catch((e) => {
            this.getDeptLoading = false;
            this.loadOrgId = undefined;
            this.$message.error("数据异常！");
          });
        }).catch((e) => {
          this.getDeptLoading = false;
          this.loadOrgId = undefined;
          this.$message.error("数据异常！");
        });
      }
      this.title = "查看用户";
    },
    getUserType(orgType, fun) {
      this.getDicts(orgType).then((response) => {
        this.userTypeOptions = response.data;
        fun && fun(response.data);
      });
    },
    // 表单重置
    reset() {
      this.form = {
        userId: undefined,
        orgId: undefined,
        staffName: undefined,
        mobile: undefined,
        email: undefined,
        userFlag: "0",
        userType: undefined,
        remark: undefined,
        orgFullName: undefined,
      };
      this.resetForm("form");
    },
    convertDictLabel(val) {
      try {
        this.form.orgTypeText = this.orgTypeOptions.find(item => item.dictValue === val).dictLabel
      } catch (e) {
        this.form.orgTypeText = val
      }
    },
    tenantChange(tenantId) {
      if (tenantId !== '') {
        this.queryParams.tenantName = this.tenantList.find(item => item.tenantId === tenantId).tenantName
      }
      this.$refs.asyncTree.root.loaded = false;
      this.$refs.asyncTree.root.expand();
      this.selectNode = undefined;
      this.handleQuery()
    },
  }
};
</script>
