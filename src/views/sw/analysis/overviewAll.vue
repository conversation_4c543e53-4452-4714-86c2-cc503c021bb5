<template>
    <div class="portal-body">
        <div class="analysis">
            <div class="flex-between  title-wrapper">
                <div class="flex-start">
                    <p class="title">整体转化率分析</p>
                </div>
                <el-date-picker v-model="queryParams.monthId" type="month" placeholder="选择月" format="yyyyMM"
                    value-format="yyyyMM" @change="search">
                </el-date-picker>
            </div>
            <div>
                <div class="table-wrapper box-bg cl">
                    <div class="flex-start p-2">
                        <div class="table-title">策略转化分析</div>
                    </div>
                    <el-divider></el-divider>
                    <div class="table-con" style="padding: 0.1rem 0.2rem !important;">
                        <div class="flex-between cl-title">
                            <div class="cl-item"></div>
                            <div class="cl-item">策略数</div>
                            <div class="cl-item">策略执行次数</div>
                            <div class="cl-item">生成潜在机会数</div>
                            <div class="cl-item">工单数</div>
                            <div class="cl-item">商机数</div>
                            <div class="cl-item">商机转化率</div>
                        </div>
                        <div class="flex-between cl-con" v-for="item in analysisData">
                            <div class="cl-item industry" @click="go(item.industry)">{{ item.industry }}</div>
                            <div class="cl-item">{{ item.configNum }}</div>
                            <div class="cl-item">{{ item.resultExecNum }}</div>
                            <div class="cl-item">{{ item.potentialOpportunityNum }}</div>
                            <div class="cl-item">{{ item.orderNum }}</div>
                            <div class="cl-item">{{ item.businessOpportunityNum }}</div>
                            <div class="cl-item">{{item.orderNum > 0 ? (100 * item.businessOpportunityNum / item.orderNum).toFixed(2) : (0).toFixed(2)}}%</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex-between">
                <div style="width: 61%;">
                    <div class="table-wrapper box-bg" style="height: 360px">
                        <div class="flex-start p-2">
                            <div class="table-title">产品推荐成功排行</div>
                        </div>
                        <el-divider></el-divider>
                        <div class="table-con">
                            <div ref="barChart" class="bar-chart"></div>
                        </div>
                    </div>
                </div>

                <div class="table-wrapper box-bg" style="width: 35%;height: 360px">
                    <div class="flex-start" style="padding: 0 0.2rem;">
                        <div class="table-title">地市商机分布图</div>
                    </div>
                    <el-divider></el-divider>
                    <div class="map-chart" ref="mapChart"></div>
                </div>
            </div>

            <div class="table-wrapper box-bg">
                <div class="flex-start p-2">
                    <div class="table-title">商机转化客户排行</div>
                </div>
                <el-divider></el-divider>
                <div class="p-2">
                    <ul class="customer-top">
                        <li v-for="(item, index) in customerTop">
                            <div class="flex-start">
                                <span class="no">{{ index + 1 }}</span>
                                <span class="customer">{{ item.name }}</span>
                                <span :class="item.level == '世界500强' ? 'world-level' : 'country-level'">{{ item.level
                                    }}</span>
                            </div>
                            <div class="flex-start">
                                <p class="c-dtl-item flex-between">
                                    <span>挖掘商机数</span>
                                    <span class="num">{{ item.sj }}</span>
                                </p>
                                <p class="c-dtl-item flex-between">
                                    <span>商机转化数</span>
                                    <span class="num">{{ item.zh }}</span>
                                </p>
                                <p class="c-dtl-item flex-between">
                                    <span>客户标签数</span>
                                    <span class="num">{{ item.tag }}</span>
                                </p>
                                <p class="c-dtl-item flex-between">
                                    <span>产品匹配数</span>
                                    <span class="num">{{ item.product }}</span>
                                </p>
                                <p class="c-dtl-item flex-between">
                                    <span>地市</span>
                                    <span class="num city">{{ item.city }}</span>
                                </p>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

    </div>
</template>

<style scoped lang="less">
p {
    margin: 0;
}

ul,
li {
    list-style: none;
}

.analysis {
    padding: 20px 0;
}

.table-wrapper {
    margin-bottom: 0.2rem;
}

.title-wrapper {
    margin: 5px 0 24px 0;
}

.title {
    margin-left: 5px;
    font-family: PingFangSC-Regular;
    font-size: 0.135rem;
    color: #181818;
    letter-spacing: 0;
    line-height: 0.26rem;
    font-weight: 600;
}

.p-2 {
    padding: 0 0.2rem;
}

.table-con {
    padding: 12px 0 12px 0.33rem !important;
}

.cl-title {
    background-color: #3377FF;
    color: #fff;
    line-height: 0.38rem;
}

.cl-con:nth-child(odd) {
    background-image: linear-gradient(92deg, #E7F0FF 0%, rgba(247, 250, 255, 0.80) 100%);
}

.industry {
    cursor: pointer;
}

.cl-item {
    width: 14%;
    text-align: center;
    font-size: 0.125rem;
    // color: #373D41;
    letter-spacing: 0;
    line-height: 0.38rem;
    font-weight: 500;

    .cl-num {
        font-family: AlibabaSans102Dec10-Bold;
        font-size: 0.25rem;
        color: #181818;
        letter-spacing: 0;
        font-weight: 700;
        line-height: 0.35rem;
    }
}



.bar-chart {
    width: 97%;
    height: 280px;
}

.map-chart {
    width: 97%;
    height: 290px;
}

.customer-top {
    padding: 0;

    li {
        padding: 16px 0 19px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.06);
        ;
    }

    span {
        display: inline-block;
    }

    .no {
        width: 0.18rem;
        height: 0.18rem;
        color: #fff;
        background: rgba(169, 176, 180, 0.60);
        border: 1px solid rgba(169, 176, 180, 1);
        border-radius: 4px;
        font-size: 0.135rem;
        line-height: 0.18rem;
        text-align: center;
    }

    li:first-child {
        .no {
            background: rgba(224, 32, 32, 0.60);
            border: 1px solid rgba(224, 32, 32, 1);
        }
    }

    li:nth-child(2) {
        .no {
            background: rgba(250, 100, 0, 0.60);
            border: 1px solid rgba(250, 100, 0, 1);
        }
    }

    li:nth-child(3) {
        .no {
            background: rgba(247, 181, 0, 0.60);
            border: 1px solid rgba(247, 181, 0, 1);
        }
    }

    li:nth-child(4) {
        .no {
            background: rgba(51, 119, 255, 0.60);
            border: 1px solid rgba(51, 119, 255, 1);
        }
    }

    .customer {
        margin: 0 16px;
        font-family: PingFangSC-Medium;
        font-size: 0.125rem;
        color: rgba(0, 0, 0, 0.85);
        line-height: 0.2rem;
        font-weight: 500;
    }

    .world-level {
        padding: 0 8px;
        background: #FFF1F0;
        border: 1px solid rgba(255, 204, 199, 1);
        border-radius: 4px;
        font-family: PingFangSC-Regular;
        font-size: 0.125rem;
        color: #FF4D4F;
        line-height: 0.2rem;
        font-weight: 400;
    }

    .country-level {
        padding: 0 8px;
        background: #FFFBE6;
        border: 1px solid rgba(255, 241, 184, 1);
        border-radius: 4px;
        font-family: PingFangSC-Regular;
        font-size: 0.125rem;
        color: #FAAD14;
        line-height: 0.2rem;
        font-weight: 400;
    }

    .c-dtl-item {
        margin: 0.125rem 0.27rem 0;
        padding: 0 0.2rem;
        width: 1.52rem;
        height: 0.25rem;
        background: rgba(51, 119, 255, 0.1);
        border-radius: 2px;
        font-family: PingFangSC-Regular;
        font-size: 0.125rem;
        color: rgba(0, 0, 0, 0.45);
        line-height: 0.25rem;
        font-weight: 400;

        .num {
            margin-left: 0.2rem;
            font-family: AlibabaSans102Dec10-Bold;
            font-size: 0.135rem;
            color: #181818;
            letter-spacing: 0;
            line-height: 0.25rem;
            font-weight: 700;

        }

        .city {
            font-size: 0.125rem;
        }
    }

}
</style>

<script>
import echarts from 'echarts'
import { allConverRate, productRecommendation, cityOpportunityArea, oportunityConvCustTop } from '@/api/sw/analysis/analysis'
import mapjson from '@/assets/map/shandong.json'

export default {
    name: 'index',
    data() {
        return {
            barChart: null,
            modelChart: null,
            tagChart: null,
            mapChart: null,
            queryParams: {
                // monthId: '202407'
                monthId: new Date().getFullYear() + (new Date().getMonth() + 1 < 10 ? "0" : "") + (new Date().getMonth() + 1)
            },
            customerTop: [],
            analysisData: [],
        }
    },
    computed: {

    },
    created() {
      let date = new Date()
      this.queryParams.monthId = date.getFullYear() + (date.getMonth() + 1 < 10 ? "0" : "") + (date.getMonth() + 1);
    },
    mounted() {
        let _this = this

        this.barChart = echarts.init(this.$refs.barChart)
        this.mapChart = echarts.init(this.$refs.mapChart)

        console.log('overview')
        this.getData()

        window.onresize = function () {
            _this.barChart.resize()
            _this.mapChart.resize()
        }
    },
    methods: {
        search() {
            this.getData()
        },
        getData() {
            this.getAllConverRate()
            this.getProductRecommendation()
            this.getCityOpportunityArea()
            this.getOportunityConvCustTop()
        },
        // 整体转化率分析
        getAllConverRate() {
            allConverRate(this.queryParams).then(res => {
                if (res.success) {
                    this.analysisData = []
                    let allOpportunityNum = 0,
                        allconfigNum = 0,
                        allorderNum = 0,
                        allpotentialOpportunityNum = 0,
                        allresultExecNum = 0;

                    res.data.forEach(item => {
                        allOpportunityNum += item.businessOpportunityNum;
                        allconfigNum += item.configNum;
                        allorderNum += item.orderNum;
                        allpotentialOpportunityNum += item.potentialOpportunityNum;
                        allresultExecNum += item.resultExecNum;
                    })
                    this.analysisData.push({
                        businessOpportunityNum: allOpportunityNum,
                        // configNum: allconfigNum,
                        configNum: 1,
                        industry: '全行业',
                        orderNum: allorderNum,
                        potentialOpportunityNum: allpotentialOpportunityNum,
                        // resultExecNum: allresultExecNum
                        resultExecNum: 1
                    })
                    res.data.forEach(item => {
                        this.analysisData.push(item)
                    })
                }
            })
        },
        // 产品推荐成功排行
        getProductRecommendation() {
            productRecommendation(this.queryParams).then(res => {
                let _this = this
                if (res.success) {
                    let xarr = [], sdata = [];
                    for (let i = 0, len = res.data.length; i < len; i++) {
                        xarr.push(res.data[i].product_name)
                        sdata.push({
                            name: res.data[i].product_name,
                            value: res.data[i].num,
                            itemStyle: {
                                color: i % 2 == 0 ? '#5B8FF9' : '#5AD8A6'
                            }
                        })
                    }
                    this.initBarChart(xarr, sdata)
                    // setTimeout(function () {
                    //     _this.initBarChart(xarr, sdata)
                    // }, 1500)

                }
            })
        },
        initBarChart(xarr, sdata) {
            this.barChart.setOption({
                tooltip: {
                    trigger: 'axis',
                    axisPointer: { // 坐标轴指示器，坐标轴触发有效
                        type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
                    }
                },
                grid: {
                    top: 10,
                    left: '2%',
                    right: '2%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: [{
                    type: 'category',
                    data: xarr,
                    axisTick: {
                        alignWithLabel: true
                    }
                }],
                yAxis: [{
                    type: 'value',
                    axisTick: {
                        show: false
                    }
                }],
                series: [{
                    name: '数量',
                    type: 'bar',
                    stack: 'vistors',
                    barWidth: '50',
                    data: sdata
                }]
            })
        },
        // 地市商机分布
        getCityOpportunityArea() {
            let _this = this
            cityOpportunityArea(this.queryParams).then(res => {
                if (res.success) {
                    let mapData = []
                    res.data.forEach(item => {
                        mapData.push({
                            name: item.city_code + "市",
                            value: item.opportunityNum
                        })
                    })
                    this.initMapChart(mapData)
                }
            })
        },
        initMapChart(mapData) {
            this.mapChart = echarts.init(this.$refs.mapChart)
            echarts.registerMap('山东', mapjson);

            let option = {
                tooltip: {
                    show: true
                },
                visualMap: {
                    show: false,
                    borderColor: 'white',
                    min: 0,
                    max: 150,
                    y: 'center',
                    splitNumber: 10,
                    calculable: true,
                    textGap: 15,
                    text: [''],
                    seriesIndex: [0],
                    inRange: {
                        color: ['#e0ffff', '#006edd']
                    },
                    outOfRange: {
                        color: [
                            '#8c8c8c'
                        ]
                    },
                    textStyle: {
                        color: '#fff'
                    }
                },
                geo: {
                    map: '山东',
                    zoom: 1.1,
                    // itemStyle: {
                    //     shadowColor: 'black',
                    //     shadowBlur: 15,
                    //     shadowOffsetX: -5,
                    //     shadowOffsetY: 15,
                    // }
                },
                series: [
                    {
                        type: 'map',
                        map: '山东',
                        name: '商机数',
                        zoom: 1.1,
                        roam: false, // 是否开启鼠标缩放和平移漫游
                        label: {
                            normal: {
                                show: true,
                                textStyle: {
                                    fontSize: 12,
                                    color: 'black'
                                }
                            },
                            emphasis: {
                                textStyle: {
                                    fontSize: 12,
                                    color: '#fff'
                                }
                            }
                        },
                        itemStyle: { // 地图区域的多边形 图形样式
                            normal: { // 是图形在默认状态下的样式
                                label: {
                                    show: true, // 是否显示标签
                                    textStyle: {
                                        color: 'transparent'
                                    },
                                },
                                borderWidth: 1,
                                borderColor: '#3c64ff',
                                areaColor: '#2298ff'
                            },
                            emphasis: { // 是图形在高亮状态下的样式,比如在鼠标悬浮或者图例联动高亮时
                                label: {
                                    show: false,
                                    textStyle: {
                                        color: 'transparent'
                                    },
                                },
                                borderColor: 'white',
                                areaColor: '#e2cd00',
                            },
                        },
                        showLegendSymbol: false,
                        data: mapData
                    },

                ]
            }
            this.mapChart.setOption(option);
            let mapIndex = 1;
            // setInterval(() => {
            //     this.mapChart.dispatchAction({
            //         type: 'downplay',
            //         seriesIndex: 0,
            //     });
            //     this.mapChart.dispatchAction({
            //         type: 'highlight',
            //         seriesIndex: 0,
            //         dataIndex: mapIndex %= mapData.length
            //     });
            //     mapIndex++;
            // }, 3000);
        },
        // 商机转化客户排行榜
        getOportunityConvCustTop() {
            oportunityConvCustTop(this.queryParams).then(res => {
                if (res.success) {
                    this.customerTop = []
                    res.data.forEach(item => {
                        this.customerTop.push({
                            name: item.customer_name,
                            sj: item.potentialOpportunityNum,
                            zh: item.opportunityNum,
                            // tag: (Math.random()*10+5).toFixed(0),
                            tag: 0,
                            product: item.productNum,
                            city: item.city_code,
                            // level: '全国500强'
                        })
                    })
                }
            })
        },
        //详情页
        go(name) {
            if (name != "全行业") {
                this.$router.push({
                    name: "analysisOverview",
                    params: { industry: name }
                });
            }
        },
        //弹窗
        handleClose(done) {
            this.$confirm('确认关闭？')
                .then(_ => {
                    done();
                    this.dialogList = ''
                })
                .catch(_ => { });
        },
    }
}
</script>
