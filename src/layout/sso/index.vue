<template>
  <div
    :class="classObj"
    class="app-wrapper"
    :style="{ '--current-color': theme }"
    id="1.23452123412415384164.123412415" v-loading="true"
  >

  </div>
</template>

<script>
import { mapState } from "vuex";
import variables from "@/assets/styles/variables.scss";
import Watermark from "@/utils/warterMark";
import { getPersonalConfig, statSpeed,oauthToken} from "@/api/system/config";
import { setToken } from '@/utils/auth'
export default {
  name: "Layout",
  computed: {
    ...mapState({
      userInfo: (state) => state.user.userInfo,
    }),
    variables() {
      return variables;
    },
  },
  mounted() {
    this.code=this.$route.query.code;

    if (this.userInfo.userid) {
      console.log("userInfo为空");
      getPersonalConfig({
        configCode: "Watermark",
      }).then((r) => {
        if (r.success) {
          try {
            const obj = JSON.parse(r.data.attra);
            Object.keys(this.userInfo).map((v) => {
              obj.content = obj.content.replace(`$${v}$`, this.userInfo[v]);
            });
            if (obj.status) {
              Watermark.set(obj);
              Watermark.setH5(obj);
            }
          } catch (error) {}
        } else {
          this.$message.error(r.message);
        }
      });
      statSpeed({}).then((r) => {
        if (r.success) {
          try {
            Watermark.setBase64(r.data);
          } catch (error) {}
        }
      });
    }else{
      oauthToken({
        grant_type: "authorization_code",
        client_id: "bVS46ElU",
        client_secret: "58ea04ba02475c8da2321cc99849d2a10f15b749",
        code: this.code
      }).then((response) => {
        if (response.success) {
          setToken(response.data.access_token);
          this.$store.commit("SET_TOKEN", response.data.access_token);
          console.log("单点登录成功");
          this.$router.push("/portal/home");

        } else {

        }
      })
    }
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch("app/closeSideBar", { withoutAnimation: false });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/mixin.scss";
@import "~@/assets/styles/variables.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$sideBarWidth});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.mobile .fixed-header {
  width: 100%;
}
</style>
