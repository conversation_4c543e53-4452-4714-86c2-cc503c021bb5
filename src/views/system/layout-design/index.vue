<template>
  <div class="app-container">
    <el-card shadow="never">
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        :inline="true"
      >
        <el-form-item label="租户" v-if="$store.getters.customParam.userType === 'admin'">
          <el-select
            v-model="queryParams.tenantId"
            placeholder="请输入租户名称搜索并选择"
            size="small"
            style="width: 240px"
            filterable
            remote
            :remote-method="getTenantList"
            :loading="getTenantLoading"
            @change="tenantChange"
          >
            <el-option
              v-for="item in tenantList"
              :key="item.tenantId"
              :label="item.tenantName"
              :value="item.tenantId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="布局名称" prop="layoutDesignName">
          <el-input
            v-model="queryParams.layoutDesignName"
            placeholder="请输入布局名称"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
          >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
          >新增</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>
      <el-row>
        <el-alert
          title="如果以下数据都未启用时，系统默认使用配置编码为【default】的布局"
          type="info"
          :closable="false"
          show-icon
          style="margin-bottom: 10px"
        />
      </el-row>
      <el-table v-loading="loading" :data="dataList">
        <el-table-column label="配置编码" align="center" prop="layoutDesignCode" :show-overflow-tooltip="true" />
        <el-table-column label="配置名称" align="center" prop="layoutDesignName" :show-overflow-tooltip="true" />
        <el-table-column label="是否启用" align="center" prop="enabledStatus" :show-overflow-tooltip ="true">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.enabledStatus"
              active-value="1"
              inactive-value="0"
              @change="handleEnabled(scope.row)"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column label="布局设计" align="center" :show-overflow-tooltip ="true">
          <template slot-scope="scope">
            <el-link
              type="primary"
              @click="handleDesign(scope.row)"
            >点此设计</el-link
            >
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="260"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              :loading="reloadId === scope.row.id && reloadType === 'edit'"
              :disabled="scope.row.layoutDesignCode === 'default' && $store.getters.customParam.userType !== 'admin'"
            >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              :loading="reloadId === scope.row.id && reloadType === 'remove'"
              :disabled="scope.row.layoutDesignCode === 'default'"
            >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改参数配置对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="open"
        width="700px"
        append-to-body
        :close-on-press-escape="false"
        @close="cancel"
        v-dialogDrag
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="150px">
          <el-row>
            <el-col :span="20">
              <el-form-item label="布局编码" prop="layoutDesignCode">
                <el-input
                  v-model="form.layoutDesignCode"
                  placeholder="请输入布局编码"
                  maxlength="50"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="20">
              <el-form-item label="布局名称" prop="layoutDesignName">
                <el-input
                  v-model="form.layoutDesignName"
                  placeholder="请输入布局名称"
                  maxlength="50"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="20">
              <el-form-item label="布局描述">
                <el-input
                  v-model="form.layoutDesignDescribe"
                  placeholder="请输入布局描述"
                  type="textarea"
                  maxlength="255"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="saveLoading"
          >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import { add, del, get, page, update } from "@/api/system/layoutDesign";
import { list as tenantList } from "@/api/system/tenant";

export default {
  name: "Theme",
  data() {
    const validateLayoutDesignCode = (rule, value, callback) => {
      if (value === '') {
        callback(new Error("应用主键不能为空"));
      } else if (this.form.id === '') {
        get({ layoutDesignCode: this.form.layoutDesignCode, tenantId: this.queryParams.tenantId }).then((response) => {
          if (response.success && response.data) {
            callback("配置编码已存在，请重新输入");
          } else {
            callback()
          }
        });
      }else {
        callback()
      }
    }
    return {
      // 遮罩层
      loading: true,
      getTenantLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 参数表格数据
      dataList: [],
      tenantList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        layoutDesignName: undefined,
        tenantId: this.$store.getters.customParam.tenantId,
        tenantName: this.$store.getters.customParam.tenantName,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        layoutDesignCode: [
          { required: true, validator: validateLayoutDesignCode, trigger: "blur" },
        ],
        layoutDesignName: [
          { required: true, message: "布局名称不能为空", trigger: "blur" },
        ]
      },
      reloadId: undefined,
      reloadType: undefined,
      saveLoading: false,
    };
  },
  created() {
    if (this.$store.getters.customParam.userType === 'admin') {
      this.getTenantList();
    }
    this.getList();
  },
  methods: {
    getTenantList(tenantName) {
      this.getTenantLoading = true;
      let query = {}
      if (tenantName !== undefined && tenantName !== '') {
        query.tenantName = tenantName
        query.tenantId = undefined
      } else {
        query.tenantId = this.queryParams.tenantId
      }
      tenantList(query).then((response) => {
        this.tenantList = response.data;
      }).finally(() => {
        this.getTenantLoading = false;
      });
    },
    /** 查询参数列表 */
    getList() {
      this.loading = true;
      page(this.queryParams).then(
        (response) => {
          this.dataList = response.data.records;
          this.total = response.data.total;
          this.loading = false;
        }
      );
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reloadId = undefined;
      this.reloadType = undefined;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: '',
        layoutDesignCode: '',
        layoutDesignName: '',
        layoutDesignDescribe: '',
        tenantId: undefined,
        tenantName: undefined,
      };
      this.resetForm("queryForm");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加布局";
      this.reloadType = "add";
      this.form.tenantName = this.queryParams.tenantName;
      this.form.tenantId = this.queryParams.tenantId;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      this.reloadId = id;
      this.reloadType = "edit";
      get({ id: id }).then((response) => {
        if (response.data) {
          this.form = response.data;
          this.open = true;
          this.title = "修改布局";
        } else {
          this.$message.error("数据异常！");
        }
      });
    },
    // 启用
    handleEnabled(row){
      const tipText = row.enabledStatus === '1' ? '启用' : '停用'
      update(row).then((response) => {
        if (response.data) {
          this.msgSuccess(`${tipText}成功！`);
          this.getList();
        } else {
          this.msgError(response.message);
        }
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.saveLoading = true;
          if (this.form.id !== '') {
            update(this.form).then((response) => {
              if (!response.success) {
                this.$message.error(response.message);
                this.saveLoading = false;
              } else {
                this.msgSuccess("修改成功");
                this.open = false;
                this.saveLoading = false;
                this.getList();
              }
            });
          } else {
            add(this.form).then((response) => {
              if (!response.success) {
                this.$message.error(response.message);
                this.saveLoading = false;
              } else {
                this.msgSuccess("新增成功");
                this.open = false;
                this.saveLoading = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.reloadId = id;
      this.reloadType = "remove";
      const _this = this
      const h = this.$createElement;
      this.$confirm(
        '是否确认删除布局名称为"' + row.layoutDesignName + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          del(id).then((response) => {
            if (response.success) {
              _this.msgSuccess('删除成功');
              _this.getList();
            } else {
              _this.$notify.error({
                title: '禁止删除提醒',
                duration: 6000,
                message: h('i', { style: 'color: #F56C6C'},
                  response.message)
              });
              _this.reloadType = undefined
            }
          });
        })
        .catch(() => {
          this.reloadId = undefined;
        });
    },
    handleDesign(row) {
      this.$router.push('/system/layoutDesign/design/' + row.id);
    },
    tenantChange(tenantId) {
      if (tenantId !== '') {
        this.queryParams.tenantName = this.tenantList.find(item => item.tenantId === tenantId).tenantName
      }
      this.handleQuery()
    },
  },
};
</script>
<style lang="scss" scoped>
</style>
