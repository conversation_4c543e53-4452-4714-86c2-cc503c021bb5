import request from "/src/utils/request";

// 登录方法
export function getPublicKey() {
  return request({
    url: "/auth/publicKey",
    method: "get",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
      isToken: false
    }
  });
}
// 登录方法
export function login(data) {
  return request({
    url: "/auth/login",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
      isToken: false
    }
  });
}
//根据用户输入的租户名称得到租户id
export function tenant(params) {
  return request({
    url: "/user/tenants/info",
    method: "get",
    params:params
  })
}
// 获取用户详细信息
export function getInfo(params) {
  return request({
    url: "/auth/oauth/check_token",
    method: "post",
    params
  });
}

// 退出方法
export function logout() {
  return request({
    url: "/auth/logout",
    method: "post"
  });
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: "/user/captcha",
    method: "get"
  });
}

// 根据参数键名查询参数值
export function selectByConfigCode(data) {
  return request({
    url: '/user/configs/selectByConfigCode',
    method: 'post',
    data
  })
}

export function sendSms(params) {
  return request({
    url: '/auth/sms/sendSms',
    method: 'post',
    params
  })
}

export function smscode(data) {
  return request({
    url: '/auth/smslogin',
    method: 'post',
    data
  })
}

export function jobSelect(data) {
  return request({
    url: '/auth/jobSelect',
    method: 'post',
    data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
    }
  })
}

export function updateUserPwd(data) {
  return request({
    url: "/user/staffs/change",
    method: "post",
    data: data
  });
}

//租户注册申请
export function add(data) {
  return request({
    url: "/user/tenants/apply",
    method: "post",
    data: data
  });
}

//获取动态配置数据
export function configData(data) {
  return request({
    url:'/user/common/clientConfig',
    method:'post',
    data:data
  })
}
