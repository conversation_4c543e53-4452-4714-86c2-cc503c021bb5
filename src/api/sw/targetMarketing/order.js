import request from '@/utils/request'

// 分页查询靶向营销任务信息表
export function getPageList(params) {
  return request({
    url: '/sanquan/targetMarketing/order/findPage',
    method: 'get',
    params,
  })
}

// 查询靶向营销任务信息表详细信息
export function getInfo(id,superiorPolicyCode ,createTime) {
  return request({
    url: '/sanquan/targetMarketing/order/findInfo',
    method: 'get',
    params: {
        id, superiorPolicyCode,createTime
    }
  })
}
export function getTaskPageList(params) {
  return request({
    url: '/sanquan/targetMarketing/order/findTaskPage',
    method: 'get',
    params,
  })
}
