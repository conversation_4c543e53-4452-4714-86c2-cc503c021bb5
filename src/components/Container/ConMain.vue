<template>
  <div class="con-main table-wrapper box-bg">
    <slot name="mainHeader" />
    <div
      v-if="$slots.btn || title"
      class="flex-between"
      style="padding: 0 0"
    >
      <div v-if="title" class="table-title">{{ title }}</div>
      <div v-if="refuse" class="table-refuse">{{ refuse }}</div>
      <slot name="btn" />
    </div>
    <slot />
  </div>
</template>
<script>
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    title: "",
    refuse: "",
  },
  data() {
    return {};
  },
  // 监听属性类似于data概念
  computed: {},
  // 方法集合
  methods: {},
};
</script>
<style lang="scss" scoped>
.con-main {
  flex: 1;
  .flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 0.08333rem !important;
    border-bottom: 0.00833rem solid #f1f1f1;
    .table-refuse{
      font-family: PingFangSC-Regular;
      font-size: 0.125rem;
      color: #181818;
      letter-spacing: 0;
      font-weight: 600;
     
    }
  }
}
</style>
