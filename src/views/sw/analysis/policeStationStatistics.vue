<template>
  <div class="portal-body">
    <div class="feedback-list">
      <div class="search-wrapper box-bg">
        <el-form :model="formQuery" ref="queryForm" label-width="0.8rem" label-position="left">
          <el-row>
            <el-col :span="7">
              <el-form-item label="派出所名称" prop="customerName">
                <el-input v-model="formQuery.customerName" clearable/>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item label="地市" prop="listCustomerCity">
                <el-select
                  v-model="formQuery.cityName"
                  filterable
                  placeholder="请选择"
                  @change="getDistrictsAndCounties"
                  clearable
                >
                  <el-option
                    v-for="item in cityList"
                    :key="item"
                    :label="item"
                    :value="item"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item label="区县" prop="listCustomerDistrict">
                <!-- <el-input
                  v-model="queryParams.listCustomerDistrict"
                  placeholder="请输入"
                  clearable
                /> -->
                <el-select
                  v-model="formQuery.districtName"
                  filterable
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in countyList"
                    :key="item"
                    :label="item"
                    :value="item"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
              <el-col :span="7">
                <el-form-item label="月份" prop="monthId">
                  <el-date-picker
                    v-model="monthId"
                    format="yyyyMM"
                    type="month"
                    @change="changeValue"
                    placeholder="选择月"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
          <div class="btn-group" style="right: 0;">
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="conditionQuery">查询</el-button>
          </div>
        </el-form>
      </div>
      <div class="table-wrapper box-bg">
        <div class="flex-between" style="border-bottom:2px #f5f5f5 solid;margin-bottom: 24px;padding:0 0 10px 0">
          <div class="table-title">统计报表</div>
          <div>
            <el-button type="success" icon="el-icon-download" size="mini" @click="exportExcelEnvent">导出</el-button>
          </div>
        </div>
        <div class="table-con">
          <div style="border:1px solid #dfe6ec;border-left:1px solid #dfe6ec;border-right:1px solid #dfe6ec;
          line-height: 48px;text-align: center;font-weight: bolder;text-space: 2;font-size:18px;"
          >
            {{ this.formQuery['monthId'] }}派出所统计报表
          </div>
          <UniTable action="/sanquan/policeStationStatistics/findPage" ref="uniTable" :columns="tableColumns"
                    :params="{...formQuery,'monthId':formQuery.monthId.replace('-','')}"
          >
          </UniTable>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import UniTable from '@/components/UniTable/UniTable.vue'
import dayjs from 'dayjs'
import { findCityGroup, findDistrictGroup } from '@/api/sw/customer/customerManager'

export default {
  name: 'overviewLs',
  components: { UniTable },
  data() {
    return {
      formQuery: { monthId: this.getPrevMonth() },
      monthId: this.getPrevMonth(),
      cityList: [], // 市列表
      countyList: [],// 区县列表
      tableColumns: [
        { props: 'customerId', label: '派出所编号', width: 240, align: 'center' },
        { label: '派出所名称', props: 'customerName' },
        { label: '地市', props: 'cityName', width: 100 },
        { label: '区县', props: 'districtName', width: 150 },
        { label: '年累收入(元)', props: 'yearIncome' },
        { label: '业务号码数量(个)', props: 'businessDevelopCount' },
        { label: 'ICT项目收入(元)', props: 'ictIncome' }
      ]
    }
  }
  ,
  created() {
  }
  ,
// 监听属性类似于data概念
  computed: {}
  ,
//属性改变监听
  watch: {}
  ,
  mounted() {
    findCityGroup().then(res => {
      this.cityList = res.data
    })
    this.resetQuery()
  }
  ,
  methods: {
    /** 重置按钮操作 */
    resetQuery() {
      this.formQuery = {
        monthId: this.getPrevMonth()
      }
      this.$refs.uniTable.resetPage()
      this.resetForm('queryForm')
      this.conditionQuery()
    }
    ,
    // 条件查询
    conditionQuery() {
      this.$refs.uniTable.loadData({
        ...this.formQuery,
        'monthId': this.formQuery['monthId'].replace('-', '')
      })
    }
    ,
    changeValue(v) {
      this.monthId = dayjs(v).format('YYYY-MM')
      this.formQuery['monthId'] = dayjs(v).format('YYYY-MM')
      this.conditionQuery()
    }
    ,
    getDistrictsAndCounties(val) {
      findDistrictGroup({
        city:this.formQuery['cityName']
      }).then((response)=>{
        this.countyList=response.data;
      })
    },
    exportExcelEnvent() {
      this.exportExcel(
        '/sanquan/policeStationStatistics/export',
        {
          ...this.formQuery,
          'monthId': this.formQuery['monthId'].replace('-', '')
        },
        `派出所统计报表.xlsx`
      )
    }
    ,
    getNowMonth() {
      var date = new Date()
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      month = month > 9 ? month : '0' + month
      return year + '-' + month
    }
    ,
    getPrevMonth() {
      var arr = this.getNowMonth().split('-')
      var year = arr[0] //获取当前日期的年份
      var month = arr[1] //获取当前日期的月份

      var year2 = year
      var month2 = parseInt(month) - 1
      if (month2 === 0) {
        //1月的上一月是前一年的12月
        year2 = parseInt(year2) - 1
        month2 = 12
      }

      if (month2 < 10) {
        //10月之前都需要补0
        month2 = '0' + month2
      }
      return year2 + '-' + month2
    }
  }
}
</script>
<style scoped lang="scss">

</style>

