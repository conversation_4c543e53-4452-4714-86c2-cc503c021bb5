<template>
  <el-card
    :class="this.bgColor === true? 'notice-card':'unshow-notice-card'"
    v-loading="businessLoading"
    shadow="never"
  >
    <div slot="header" v-if="show">
      <span class="card-title">
        <div class="title">
          <div><i class="icon iconfont icon-yewudongtai"></i>统计数据</div>
        </div>
      </span>
    </div>
    <div class="home-card-body">
      <div class="content" v-if="this.total">
        <div class="organization" >
          <div class="text" >
            <h5 style="color: #0b1d30; margin-bottom: 5px">
              {{ total.staffTotal }}
            </h5>
            <h6 style="color: #515356">用户</h6>
          </div>
        </div>
        <div class="personnel">
          <div class="text">
            <h5 style="color: #0b1d30; margin-bottom: 5px">
              {{ total.orgTotal }}
            </h5>
            <h6 style="color: #515356">组织</h6>
          </div>
        </div>
        <div class="application">
          <div class="text">
            <h5 style="color: #0b1d30; margin-bottom: 5px">
              {{ total.tenantTotal }}
            </h5>
            <h6 style="color: #515356">租户</h6>
          </div>
        </div>
        <div class="routine" style="border-right: none">
          <div class="text">
            <h5 style="color: #0b1d30; margin-bottom: 5px">
              {{ total.appTotal }}
            </h5>
            <h6 style="color: #515356">应用</h6>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script>
import { mapState } from "vuex";
import { getTotals } from "@/api/monitor/areaChart";
export default {
  props:['item'],
  name:"areaChart",
  data() {
    return {
      businessLoading: false,
      total: {},
      show: "",
      bgColor:"",
    };
  },
  created() {
    this.getTotal();
    this.show = this.item.showTitle;
    this.bgColor = this.item.showBgColor;
  },
  mounted() {
  },
  watch: {
    "item.showTitle":function(val){
       this.show = val;
    },
    "item.showBgColor" :function(val) {
      this.bgColor = val
    }
  },
  methods: {
    getTotal() {
      getTotals().then((res) => {
        this.total = res.data;
      });
    }
  },
};
</script>

<style lang="scss" scoped>
.unshow-notice-card {
  background: #f5f9fa;
  border: 0px;
  height: 100%;
  display: flex;
  flex-direction: column;
  .home-card-body {
    height: 100%;
    .content {
      display: flex;
      height: 80px;
    }
    .content > div {
      width: 25%;
      height: 80px;
      border-right: 1px solid #d5d5d5;
      font-size: 20px;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    .content h5 {
      font-size: 28px;
      font-weight: 600;
    }
    .content h6 {
      font-size: 16px;
    }
  }
}
.notice-card {
  height: 100%;
  display: flex;
  background: #fff;
  flex-direction: column;
  .home-card-body {
    height: 100%;
    .content {
      display: flex;
      height: 80px;
    }
    .content > div {
      width: 25%;
      height: 80px;
      border-right: 1px solid #d5d5d5;
      font-size: 20px;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    .content h5 {
      font-size: 28px;
      font-weight: 600;
    }
    .content h6 {
      font-size: 16px;
    }
  }
}
</style>

