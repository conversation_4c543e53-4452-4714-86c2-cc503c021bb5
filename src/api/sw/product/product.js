import request from '@/utils/request'

// 产品新增
export function addProduct(data) {
    return request({
      url: '/sanquan/product/save',
      method: 'post',
      data,
    })
}

// 产品修改
export function updateProduct(data) {
    return request({
      url: '/sanquan/product/update',
      method: 'post',
      data,
    })
}

// 产品删除
export function deleteProduct(id) {
    return request({
      url: '/sanquan/product/delete/'+id,
      method: 'get',
    })
}

// 产品详情
export function productInfo(id) {
    return request({
      url: '/sanquan/product/findInfo/'+id,
      method: 'get',
    })
}

// 产品详情列表（分页）
export function productPageList(params) {
    return request({
      url: '/sanquan/product/findPage',
      method: 'get',
      params
    })
}
// 产品列表（ 不分页）
export function findProductList(params) {
  return request({
    url: '/sanquan/product/findProductList',
    method: 'get',
    params
  })
}
// 产品详情列表（分页）
export function productPageListFromResult(params) {
  return request({
    url: '/sanquan/product/findPageProductPageListFromResult',
    method: 'get',
    params
  })
}

// 产品打标
export function productMark(data) {
  return request({
    url: '/sanquan/product/mark',
    method: 'post',
    data
  })
}
export function findLabelTree(data) {
  return request({
    url: '/sanquan/product/findLabelTree',
    method: 'post',
    data
  })
}
export function findLabelTreeGroup(data) {
  return request({
    url: '/sanquan/product/findLabelTreeGroup',
    method: 'post',
    data
  })
}
//查询行业信息列表
export function findIndustry() {
  return request({
    url: '/sanquan/product/findIndustry',
    method: 'get',
  })
}

//查询字典信息列表
export function findDictionary(params) {
  return request({
    url: '/sanquan/product/findDictionary',
    method: 'get',
    params
  })
}

//查询经理信息列表
export function findPersonnel(params) {
  return request({
    url: '/sanquan/product/findPersonnel',
    method: 'get',
    params
  })
}

//查询经理信息列表
export function findPersonnelCity(prefecture) {
  return request({
    url: '/sanquan/product/findPersonnel',
    method: 'get',
    params:{
      prefecture
    }
  })
}

//添加联系人信息
export function addPersonnel(data) {
  return request({
    url: '/sanquan/personnel/addPersonnel',
    method: 'post',
    data
  })
}

//添加产品链接
export function addProductLink(data) {
  return request({
    url: '/sanquan/product/linkConfig/addOrUpdate',
    method: 'post',
    data
  })
}

//添加产品到期提醒
export function reminderSettings(data) {
  return request({
    url: '/sanquan/product/reminderSettings',
    method: 'post',
    data
  })
}


//校验工号是否存在
export function findPersonnelJobNumber(params) {
  return request({
    url: '/sanquan/personnel/findPersonnelJobNumber',
    method: 'get',
    params
  })
}

//添加字典信息
export function addDictionary(data) {
  return request({
    url: '/sanquan/dictionary/addDictionary',
    method: 'post',
    data
  })
}

//删除字典信息
export function deleteDictionary(id) {
  return request({
    url: '/sanquan/dictionary/deleteDictionary/'+id,
    method: 'get',
  })
}

// 根据用户表中添加产品使用的人员信息
export function addUserToPersonnel(data) {
  return request({
    url: '/sanquan/personnel/addUserToPersonnel',
    method: 'post',
    data
  })
}

//查询行业信息列表
export function findSegments(data) {
  return request({
    url: '/sanquan/getSegments',
    method: 'post',
    data
  })
}
