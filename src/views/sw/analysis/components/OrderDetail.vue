<template>
  <el-dialog
    :title="title"
    :visible.sync="openOrderDialog"
    width="80%"
    @close="handleClose">
      <div class="table-wrapper box-bg" >
        <div class="table-content">
          <el-table  :data="resultList" style="width: 100%" :row-class-name="tableRowStripeClassName" max-height="600">
            <el-table-column label="任务编号" align="center" prop="code" show-overflow-tooltip />
            <el-table-column label="自然客户ID" align="center" prop="customId" show-overflow-tooltip />
            <el-table-column label="自然客户名称" align="left" prop="customName" show-overflow-tooltip min-width="110">
            </el-table-column>
            <el-table-column label="执行人" align="center" prop="managerName" />
            <el-table-column label="执行人工号" align="center" prop="managerNo" />
            <el-table-column label="执行情况" align="center" prop="status" >
              <template slot-scope="scope">
<!--                {{getOrderStatus(scope.row.status)}}-->
                <div v-html="getOrderStatus(scope.row.status)"></div>
              </template>
            </el-table-column>
            <el-table-column label="关联商机数量" align="center" prop="assoOpportunityNum" />
            <el-table-column label="关联商机金额" align="left" prop="assoOppMoney" />
            <el-table-column label="商机数量" align="center" prop="dachanNum" />
            <el-table-column label="商机金额(万元)" align="center" prop="oppMoney" />
            <el-table-column label="关联项目数量" align="center" prop="assoProjectNum" />
            <el-table-column label="关联项目金额" align="center" prop="assoProjectMoney" />
            <el-table-column label="项目数量" align="center" prop="projectNum" />
            <el-table-column label="项目金额" align="center" prop="projectMoney" />
            <el-table-column label="命中业务数量" align="center" prop="addDevNum" />
            <el-table-column label="命中业务收入" align="center" prop="addDevFee" />
            <el-table-column label="拉动业务数量" align="center" prop="newDevNum" />
            <el-table-column label="拉动业务收入" align="center" prop="newDevFee" />

          </el-table>
          <div class="flex-end" style="margin-top: 0.14rem;">
            <el-pagination @current-change="handleCurrentChange"
                           :current-page.sync="queryParams.pageNum" :page-size="queryParams.pageSize" layout="total, prev, pager, next"
                           :total="total">
            </el-pagination>
          </div>
        </div>
      </div>
  </el-dialog>
</template>
<script>
import {resultError} from "@/utils/messageText";
import  { getPageList }  from '@/api/sw/targetMarketing/targetOppoProjInfo'
import { getTaskPageList } from "@/api/sw/targetMarketing/order";
import UniTable from '@/components/UniTable/UniTable.vue'
import { mapState } from 'vuex'
export default {
  // import引入的组件需要注入到对象中才能使用
  components: { UniTable },
  name: 'detail',
  props: {
    openOrderDialog: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    paramInfo: {
      dataType: {
        type: String,
        default: ''
      },
      status: {
        type: String,
        default: ''
      },
      city: {
        type: String,
        default: ''
      },
      strategyId: {
        type: String,
        default: ''
      },
      contactor: {
        type: String,
        default: '靶向营销'
      },
      beginDate: {
        type: String,
        default: ''
      },
      endDate: {
        type: String,
        default: ''
      }
    },
  },
  data() {
    return {
      resultList: [],//商机或项目列表
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0
    }
  },
  watch: {
    paramInfo: {
      deep: true,
      handler(newValue,oldVal){
        this.queryParams.pageNum = 1;//当重新打开时设置第一页,不然数据多的如果上次翻到第大页,再点击条数少的打开会无数据
        console.info("info："+JSON.stringify(this.paramInfo))
        this.queryParams.contactor = this.paramInfo.contactor
        this.getDetailList()
      }
    },
  },
  created() {

  },
  mounted() {

  },
  // 监听属性类似于data概念
  computed: {
  },
  // 方法集合
  methods: {
    tableRowStripeClassName({ row, rowIndex }) {
      let rowStyle = "row-bg-high";
      if (rowIndex % 2 === 0) {
        rowStyle = "row-bg-normal";
      }
      return rowStyle;
    },
    getDetailList() {
      this.resultList = [];
      this.total = 0;
      let strategyId = this.paramInfo.strategyId;
      let city = this.paramInfo.city
      let type = (this.paramInfo.contactor==='要客管家') ? '0' : '1'
      // let dataType = this.paramInfo.dataType;
      let status = this.paramInfo.status;
      this.queryParams = {...this.paramInfo,...this.queryParams,strategyId,type,status,city}
      let pageNum = this.queryParams.pageNum;
      getTaskPageList(this.queryParams).then(response => {
        if (response.code === '1') {
          this.resultList = response.data.records;
          this.total = response.data.total;
          this.queryParams.pageNum = pageNum;
        }else{
          this.$message.error(resultError);
        }
      });
    },
    // 当前页码
    handleCurrentChange(page) {
      this.currentPage = page;
      this.queryParams.pageNum = page;
      this.getDetailList();
    },
    sliceText(text){
      return text.length > 20 ? text.slice(0, 20) + '...' : text;
    },
    // 搜索
    handleQuery() {
      console.log(this.search, 'search')
    },
    handleClose() {
      this.$emit('update-open-order-dialog', false)
    },
    getOrderStatus(status){
      let result = '';
      if(status==='1'){
        result = '待执行'
      }else if(status==='2'){
        result = '已关单'
      }else if(status==='3'){
        result = '待改派'
      }else if(status==='4'){
        result = '跟进中'
      }else if(status==='5'){
        result = '到期未执行'
      }
      return result;
    },
  }
}
</script>
<style lang="scss" scoped>
.el-dialog__body {
  padding: 0;
}
::v-deep .el-dialog__body {
  padding: 0;
}
.header {
  font-size: 0.16667rem;

  padding: 0.16667rem;
  background-color: #fff;
  width: 100%;
  margin-top: 0.16667rem;
  border-radius: 0.04167rem;

  i {
    color: #3377ff;
  }
}


.m-con {
  margin: 0.08333rem;
  padding: 0.08333rem;
  background-color: #fff;

  .dict_search {
    width: 30%;
    padding-bottom: 0.08333rem;
  }
}

.table-content{
  padding: 0.1rem 0.2rem;
}

.action-button {
  padding: 0;
  font-size: 0.125rem;
  min-width: 0.125rem;
  background-color: transparent;
  border: none;
  cursor: pointer;
  color: #3377FF;
  font-family: PingFangSC-Regular;
  line-height: 0.18rem;
  font-weight: 400;
}
</style>
