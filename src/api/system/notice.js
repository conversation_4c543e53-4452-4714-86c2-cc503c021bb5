import request from "@/utils/request";

// 新增公告
export function addNotice(data) {
  return request({
    url: "/cms/v1.0/notice/insertNotice",
    method: "post",
    data: data
  });
}

// 删除公告
export function delNotice(params) {
  return request({
    url: "/cms/v1.0/notice/deleteByNoticeId",
    method: "post",
    params
  });
}

// 修改公告
export function updateNotice(data) {
  return request({
    url: "/cms/v1.0/notice/updateByNoticeId",
    method: "post",
    data: data
  });
}

// 查询公告详情
export function selectNoticeByNoticeId(params) {
  return request({
    url: "/cms/v1.0/notice/selectNoticeByNoticeId",
    method: "post",
    params
  });
}

// 查询公告列表
export function listNotice(data) {
  return request({
    url: "/cms/v1.0/notice/selectForPage",
    method: "post",
    data
  });
}

// 查询公告列表
export function listAll(data) {
  return request({
    url: "/cms/v1.0/notice/selectForList",
    method: "post",
    data
  });
}

// 去除租户限制的查询公告列表
export function selectAllForPage(data) {
  return request({
    url: "/cms/v1.0/notice/selectAllForPage",
    method: "post",
    data
  });
}

//查看公告添加日志
export function seeNoticeAdd(data) {
  return request({
    url: "/cms/v1.0/notice/insertNoticeLog",
    method: "post",
    data
  })
}

//获取站内未读信息的数量
export function getMailNum() {
  return request({
    url: "/cms/v1.0/notice/selectNotReadNum",
    method: "post",
  })
}

//分页查询获取站内信的数据
export function getMailInfo(data) {
  return request({
    url: "/cms/v1.0/notice/selectSysMessagePage",
    method: "post",
    data
  })
}

// 查询top5数据  有图
export function selectTop5Page(data) {
  return request({
    url: "/cms/v1.0/notice/selectTop5Page",
    method: "post",
    data
  });
}

// 查询top5数据  无图
export function selectTopPage(data) {
  return request({
    url: "/cms/v1.0/notice/selectTopPage",
    method: "post",
    data
  });
}

// 编辑公告状态
export function updateStatusById(data) {
  return request({
    url: "/cms/v1.0/notice/updateStatusById",
    method: "post",
    data
  });
}

// 首页查询top3数据标题
export function getNotices() {
  return request({
    url: "/cms/v1.0/notice/selectNoticeTop3",
    method: "post"
  })
}

// 更新置顶状态
export function updateTopByNoticeId(data) {
  return request({
    url: "/cms/v1.0/notice/updateTopByNoticeId",
    method: "post",
    data
  });
}

// --------------------------------------------

// 查询公告详细 - 弃用
export function getNotice(noticeId) {
  return request({
    url: "/system/notice/" + noticeId,
    method: "get"
  });
}

// 从ES查询公告/业务动态
export function termById(params) {
  return request({
    url: "/cms/v1.0/search/termById",
    method: "post",
    params
  });
}

// 根据索引名、查询关键字、字段以及其他数据模糊查询相关数据
export function boolWildcard(params) {
  return request({
    url: "/cms/v1.0/search/boolWildcard",
    method: "post",
    params
  });
}
