import request from '@/utils/request'

// 获取客户列表
export function getAreaManagerList(data) {
  return request({
    url: '/sanquan/areaManager/findList',
    method: 'post',
    data
  })
}

// 修改客户表
export function updateAreaManagerById(data) {
    return request({
      url: '/sanquan/areaManager/update',
      method: 'post',
      data,
    })
  }

// 新增客户表
export function addAreaManagerById(data) {
    return request({
      url: '/sanquan/areaManager/add',
      method: 'post',
      data,
    })
  }

// 详情
export function getAreaManagerDetail(id) {
  return request({
    url: '/sanquan/areaManager/findOne',
    method: 'get',
    params: {
      id
    }
  })
}

// 删除
export function deleteAreaManagerById(id) {
    return request({
      url: '/sanquan/areaManager/delete',
      method: 'get',
      params: {
        id
      }
    })
  }

  // 修改地市接口人信息
export function addOrUpdate(data) {
  return request({
    url: '/sanquan/areaManager/addOrUpdate',
    method: 'post',
    data
  })
}

  // 验证
  export function verifyJobNumber(params) {
    return request({
      url: '/sanquan/areaManager/verifyOa',
      method: 'get',
      params
    })
  }
