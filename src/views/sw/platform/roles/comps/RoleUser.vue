<script>
import UniTable from '@/components/UniTable/UniTable.vue'
import { uniTableData } from '@/api/uni/uni'

export default {
  name: 'RoleUser',
  components: { UniTable },
  data() {
    return {
      openTabDialog: false,
      open: false,
      roleId: '',
      roleName: '',
      formQuery: {},
      formQueryInner: {},
      tableColumns: [
        { props: 'jobNum', label: '工号' },
        { props: 'name', label: '姓名' },
        { props: 'city', label: '所属地市' },
        { props: 'county', label: '区县' },
        { props: 'industry', label: '所属行业' },
        { props: 'opt', label: '操作', slot: true, align: 'center' }
      ],
      innerVisible: false
    }
  },
  methods: {
    onOpen() {

    },
    onCancel() {
      this.$refs.uniTable.resetPage()
      this.openTabDialog = false
    },
    openDialog(roleId,roleName) {
      this.roleId = roleId
      this.roleName = roleName
      this.openTabDialog = true
      this.$nextTick(() => {
        this.formQuery.roleId = this.roleId
        const query = {
          ...this.formQuery
        }
        this.$refs.uniTable.loadData({
          query
        })
      })
    },
    resetQuery() {

      this.formQuery = {
        pageNum: 1,
        pageSize: 10
      };
      this.$refs.uniTable.resetPage()


      this.conditionQuery()
    },
    conditionQuery() {
      this.formQuery.roleId = this.roleId
      const query = {
        ...this.formQuery
      }
      this.$refs.uniTable.loadData({
        query
      })
    },
    resetQueryInner() {
      this.formQueryInner = {}
      this.$refs.uniTable2.resetPage()
      this.conditionQueryInner()
    },
    conditionQueryInner() {
      this.$refs.uniTable2.loadData({
        roleId: this.roleId,
        ...this.formQueryInner
      })
    },
    cancelRoleGrant(row) {
      uniTableData('/sanquan/role/cancelUserRole', {
        userId: row.id,
        roleId: this.roleId
      }).then(() => {
        this.conditionQuery()
      })
    },
    innerDialog() {
      this.innerVisible = true
      this.$nextTick(() => {
        this.conditionQueryInner()
      })
    },
    addUserRole(row) {
      uniTableData('/sanquan/role/addUserRole', {
        userId: row.id,
        roleId: this.roleId
      }).then(() => {
        this.conditionQueryInner()
        this.conditionQuery()
      })
    },
    batchDelete(){
      let userIds=this.$refs.uniTable.getCheckBoxVal();
      if(userIds.length>0){
        uniTableData('/sanquan/role/cancelBatchUserRole', {
          userIds: userIds,
          roleId: this.roleId
        },"post").then(() => {
          this.conditionQuery()
        })
      }else{
        this.$message.error("请选择要取消授权的用户")
      }
    },
    innerBatchUser(){
      let userIds=this.$refs.uniTable2.getCheckBoxVal();
      if(userIds.length>0){
        uniTableData('/sanquan/role/addBatchUserRole', {
          userIds: userIds,
          roleId: this.roleId
        },"post").then(() => {
          this.conditionQuery()
          this.conditionQueryInner()

        })
      }else{
        this.$message.error("请选择要授权的用户")
      }
    }
  }
}
</script>

<template>
  <div>
    <!-- 添加或修改标签弹框 -->
    <el-dialog
      title="分配用户"
      :visible.sync="openTabDialog"
      @open="onOpen"
      @close="onCancel"
      width="1000px"
      height="600px"
    >
      <template slot="title">
        <span class="dialog_title">为<span style="color: red">{{ this.roleName }}</span>分配用户</span>
      </template>

      <div style="width:100%;">
        <el-form :model="formQuery" ref="queryForm" label-width="80px" label-position="left">
          <el-row>
            <el-col :span="9">
              <el-form-item label="工号" prop="jobNum">
                <el-input v-model="formQuery.jobNum" clearable placeholder="请输入"/>
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item label="姓名" prop="name" style="margin-left:10px;">
                <el-input v-model="formQuery.name" clearable placeholder="请输入"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <div class="btn-group" style="right: 0;margin-left:20px">
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="conditionQuery">查询</el-button>
              </div>
            </el-col>
          </el-row>
          <el-row style="padding: 0 0 10px 0">
            <el-col :span="24">
              <el-button type="primary" size="mini" @click="innerDialog">添加用户</el-button>
              <el-button type="danger" size="mini" @click="batchDelete">批量删除</el-button>
            </el-col>
          </el-row>
        </el-form>

      </div>
      <UniTable action="/sanquan/user/findUserByRoleId"
                ref="uniTable" :columns="tableColumns" :params="formQuery"
      >
        <template v-slot:opt="{row}">
          <div class="action-buttons flex-center">
            <el-button type="text" class="action-button"
                       @click="cancelRoleGrant(row)"
            >取消授权
            </el-button>

          </div>
        </template>
      </UniTable>
      <el-dialog
        :visible.sync="innerVisible"
        width="800px"
        title="添加用户"
        append-to-body
        custom-class="myInnerDialog"
      >
        <div style="width:100%;">
          <el-form :model="formQueryInner" ref="queryForm" label-width="80px" label-position="left">
            <el-row>
              <el-col :span="9">
                <el-form-item label="工号" prop="jobNum">
                  <el-input v-model="formQueryInner.jobNum" clearable placeholder="请输入"/>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="姓名" prop="name" style="margin-left:10px;">
                  <el-input v-model="formQueryInner.name" clearable placeholder="请输入"/>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <div class="btn-group" style="right: 0;margin-left:20px">
                  <el-button icon="el-icon-refresh" size="mini" @click="resetQueryInner">重置</el-button>
                  <el-button type="primary" icon="el-icon-search" size="mini" @click="conditionQueryInner">查询
                  </el-button>
                </div>
              </el-col>
            </el-row>
            <el-row style="padding: 0 0 10px 0">
              <el-col :span="24">
                <el-button type="primary" size="mini" @click="innerBatchUser">批量授权</el-button>
              </el-col>
            </el-row>
          </el-form>

        </div>
        <UniTable action="/sanquan/user/findUserByNotRoleId"
                  ref="uniTable2" :columns="tableColumns"
        >
          <template v-slot:opt="{row}">
            <div class="action-buttons flex-center">
              <el-button type="text" class="action-button"
                         @click="addUserRole(row)"
              >授权
              </el-button>
            </div>
          </template>
        </UniTable>
      </el-dialog>
    </el-dialog>
  </div>
</template>
