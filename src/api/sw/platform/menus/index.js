import request from "@/utils/request";
// 查询菜单列表
// export function listMenu(params) {
//   return request({
//     url: "/sanquan/core/menu/findList",
//     method: "get",
//     params: params
//   });
// }
// 查询菜单列表
export function treeMenu(params) {
  return request({
    url: "/sanquan/core/menu/findTreeList",
    method: "get",
    params: params
  });
}
// 新增菜单
export function addMenu(data) {
  return request({
    url: "/sanquan/core/menu/save",
    method: "post",
    data: data
  });
}
/**
 * 根据主键删除标签分类(下级分类一块删除)
 * @param id 分类主键
 * @returns {*}
 */
export function deleteById(id) {
  return request({
    url: "/sanquan/core/menu/delete/",
    method: "get",
    params: {id}
  });
}
// 检查某个分类下是否有模型
export function checkHasChildren(params) {
  return request({
    method: "post",
    url: "/sanquan/core/menu/checkHasChildren",
    params,
  });
}
/**
 * 根据主键获取标签分类信息
 * @param id  标签分类主键
 * @returns {*}
 */
export function findInfo(id) {
  return request({
    url: "/sanquan/core/menu/findInfo",
    method: "get",
    params: {id}
  });
}

/**
 * 查询菜单系统配置参数
 * @returns 
 */
export function findMenusSystem() {
  return request({
    url: "/sanquan/core/menu/findMenusSystem",
    method: "get"
  });
}

export default {treeMenu,addMenu,deleteById,findInfo,checkHasChildren, findMenusSystem}
