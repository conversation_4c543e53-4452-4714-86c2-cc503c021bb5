.page {
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
  .outer1 {
    height: 1080px;
    background: url(./img/ps3h5ks5z2oncm5pnczqu6kgaxpalrhpy700450ec-30be-4ca2-bea8-663067a37911.png)
      100% no-repeat;
    width: 1920px;
    .outer2 {
      z-index: 5;
      height: 1080px;
      background: url(./img/psf98ac7dgzm99oenzlftwa8b39tiygsmp3802fdde-d9f1-4361-b76d-7f4824b96ff1.png) -534px -194px
        no-repeat;
      width: 1920px;
      position: relative;
      
      .group1 {
        width: 1920px;
        height: 893px;
        .main1 {
          width: 261px;
          height: 455px;
          background: url(./img/pskoeoc9eb9zzsiesylg8xbgib8btap8a871317977-7067-45cf-9936-b475303fda52.png) -178px
            0px no-repeat;
          margin-top: 249px;
        }
        .label1 {
          width: 43px;
          height: 42px;
          margin: 757px 0 0 25px;
        }
        .pic1 {
          width: 57px;
          height: 57px;
          margin: 141px 0 0 72px;
        }
        .main2 {
          width: 364px;
          height: 51px;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: MicrosoftYaHei-Bold;
          text-align: left;
          white-space: nowrap;
          line-height: 52px;
          margin: 142px 0 0 21px;
          .word1 {
            width: 364px;
            height: 51px;
            overflow-wrap: break-word;
            color: rgba(48, 61, 91, 1);
            font-size: 52px;
            font-family: MicrosoftYaHei-Bold;
            text-align: left;
            white-space: nowrap;
            line-height: 52px;
          }
          .info1 {
            width: 364px;
            height: 51px;
            overflow-wrap: break-word;
            color: rgba(241, 103, 34, 1);
            font-size: 52px;
            font-family: MicrosoftYaHei-Bold;
            text-align: left;
            white-space: nowrap;
            line-height: 52px;
          }
        }
        .main3 {
          width: 152px;
          height: 657px;
          margin: 236px 0 0 87px;
          .main4 {
            width: 31px;
            height: 30px;
            background: url(./img/ps3gunz230ek8ir7gsf2ksj2c4fec6cqaf7b978c05-3839-429a-95db-eae69f4a5d72.png)
              0px -1px no-repeat;
            margin-left: 50px;
          }
          .pic2 {
            width: 121px;
            height: 196px;
            margin: 191px 0 0 31px;
          }
          .pic3 {
            width: 131px;
            height: 136px;
            margin-top: 104px;
          }
        }
        .img1 {
          width: 133px;
          height: 196px;
          margin: 647px 0 0 99px;
        }
        .main5 {
          z-index: 27;
          position: relative;
          width: 35px;
          height: 65px;
          background: url(./img/pstpukpkbkwyn9iskz3l5aqbdzkk3fu0q89b79a0a62-a88d-45d5-9c1d-bf866e8f44c3.png)
            100% no-repeat;
          margin: 178px 0 0 1px;
          .pic4 {
            z-index: 28;
            position: absolute;
            left: -29px;
            top: 19px;
            width: 34px;
            height: 60px;
          }
        }
        .pic5 {
          width: 62px;
          height: 138px;
          margin: 241px 0 0 215px;
        }
        .main6 {
          width: 195px;
          height: 165px;
          background: url(./img/pssgl852q8brlkhqmoqs984ml5sk7cw658a7d88992-4263-4a61-b617-5bcd6ceacd43.png)
            0px -94px no-repeat;
          margin-left: 98px;
        }
      }
      .img2 {
        z-index: 17;
        position: absolute;
        left: 1000px;
        top: 247px;
        width: 51px;
        height: 142px;
      }
      .img3 {
        z-index: 20;
        position: absolute;
        left: 1466px;
        top: 254px;
        width: 163px;
        height: 169px;
      }
      .pic6 {
        z-index: 18;
        position: absolute;
        left: 1025px;
        top: 289px;
        width: 191px;
        height: 148px;
      }
      .img4 {
        z-index: 25;
        position: absolute;
        left: 1120px;
        top: 279px;
        width: 350px;
        height: 382px;
      }
      .group2 {
        z-index: 15;
        position: absolute;
        left: 1026px;
        top: 224px;
        width: 590px;
        height: 590px;
        background: url(./img/ps001xz9vidy81bmn5hbl1e70a95evjg5j8k51e701625-9e34-4c43-af4e-9312bd92074e.png)
          0px 0px no-repeat;
        .mod1 {
          z-index: 16;
          position: absolute;
          left: 30px;
          top: 219px;
          width: 625px;
          height: 315px;
          background: url(./img/ps8oup6okjgu7ryu4o521xkrzgbapv9bx1ed380f8d-c814-4e64-ad93-baf3a343320f.png)
            100% no-repeat;
          .pic7 {
            z-index: 21;
            position: absolute;
            left: 420px;
            top: 112px;
            width: 141px;
            height: 226px;
          }
        }
      }
      .group3 {
        box-shadow: 0px 15px 30px 4px rgba(243, 243, 243, 1);
        background-color: rgba(255, 255, 255, 1);
        border-radius: 10px;
        z-index: 29;
        height: 580px;
        width: 600px;
        position: absolute;
        left: 320px;
        top: 245px;
        .section1 {
          width: 483px;
          height: 492px;
          margin: 45px 0 0 59px;
          ::v-deep .el-tabs__item {
            font-size: 20px;
          }
          ::v-deep .el-tabs__item.is-active {
            color: #F16722;
          }
          ::v-deep .el-tabs__active-bar {
            background-color: #F16722;
            width: 120px;
          }
          ::v-deep .el-form-item__content {
            width: 100%;
            display: flex;
          }
          .op {
            display: flex;
          }
          .login-button {
            width: 242px;
            height: 50px;
            background: linear-gradient(90deg,#F06F24,#FBA03E);
            border-radius: 36px;
            border: 0;
            display: block;
            margin: 0 auto;
            font-size: 20px;
            font-weight: 700;
            font-family: Microsoft YaHei;
          }
          .tenantAppliation {
            color: #1890ff;
          }
          // .block1 {
          //   width: 399px;
          //   height: 24px;
          //   overflow-wrap: break-word;
          //   font-size: 0;
          //   font-family: MicrosoftYaHei-Bold;
          //   text-align: left;
          //   white-space: nowrap;
          //   line-height: 24px;
          //   margin-left: 9px;
          //   .txt1 {
          //     width: 399px;
          //     height: 24px;
          //     overflow-wrap: break-word;
          //     color: rgba(241, 103, 34, 1);
          //     font-size: 24px;
          //     font-family: MicrosoftYaHei-Bold;
          //     text-align: left;
          //     white-space: nowrap;
          //     line-height: 24px;
          //   }
          //   .txt2 {
          //     width: 399px;
          //     height: 24px;
          //     overflow-wrap: break-word;
          //     color: rgba(255, 254, 255, 1);
          //     font-size: 24px;
          //     font-family: MicrosoftYaHei;
          //     text-align: left;
          //     white-space: nowrap;
          //     line-height: 24px;
          //   }
          //   .txt3 {
          //     width: 399px;
          //     height: 24px;
          //     overflow-wrap: break-word;
          //     color: rgba(51, 146, 193, 1);
          //     font-size: 24px;
          //     font-family: MicrosoftYaHei;
          //     text-align: left;
          //     white-space: nowrap;
          //     line-height: 24px;
          //   }
          //   .word2 {
          //     width: 399px;
          //     height: 24px;
          //     overflow-wrap: break-word;
          //     color: rgba(241, 103, 34, 1);
          //     font-size: 24px;
          //     font-family: MicrosoftYaHei;
          //     text-align: left;
          //     white-space: nowrap;
          //     line-height: 24px;
          //   }
          // }
          // .block2 {
          //   background-color: rgba(244, 249, 254, 1);
          //   height: 4px;
          //   margin-top: 27px;
          //   width: 482px;
          //   .mod2 {
          //     background-color: rgba(241, 103, 34, 1);
          //     width: 150px;
          //     height: 4px;
          //   }
          // }
          // .ImageText1 {
          //   height: 20px;
          //   width: 120px;
          //   margin: 47px 0 0 8px;
          //   .bd1 {
          //     width: 120px;
          //     height: 20px;
          //     .label2 {
          //       width: 18px;
          //       height: 20px;
          //     }
          //     .TextGroup1 {
          //       height: 14px;
          //       margin-top: 3px;
          //       width: 90px;
          //       .txt4 {
          //         width: 90px;
          //         height: 14px;
          //         overflow-wrap: break-word;
          //         color: rgba(153, 153, 153, 1);
          //         font-size: 14px;
          //         font-family: MicrosoftYaHei;
          //         text-align: left;
          //         white-space: nowrap;
          //         line-height: 14px;
          //         display: block;
          //       }
          //     }
          //   }
          // }
          // .img5 {
          //   width: 482px;
          //   height: 1px;
          //   margin-top: 12px;
          // }
          // .ImageText2 {
          //   height: 18px;
          //   width: 105px;
          //   margin: 40px 0 0 9px;
          //   .layer1 {
          //     width: 105px;
          //     height: 18px;
          //     .label3 {
          //       width: 16px;
          //       height: 18px;
          //     }
          //     .TextGroup2 {
          //       height: 14px;
          //       margin-top: 2px;
          //       width: 76px;
          //       .word3 {
          //         width: 76px;
          //         height: 14px;
          //         overflow-wrap: break-word;
          //         color: rgba(153, 153, 153, 1);
          //         font-size: 14px;
          //         font-family: MicrosoftYaHei;
          //         text-align: left;
          //         white-space: nowrap;
          //         line-height: 14px;
          //         display: block;
          //       }
          //     }
          //   }
          // }
          // .img6 {
          //   width: 482px;
          //   height: 1px;
          //   margin-top: 14px;
          // }
          // .ImageText3 {
          //   height: 20px;
          //   width: 105px;
          //   margin: 38px 0 0 9px;
          //   .group4 {
          //     width: 105px;
          //     height: 20px;
          //     .icon1 {
          //       width: 18px;
          //       height: 20px;
          //     }
          //     .TextGroup3 {
          //       height: 14px;
          //       margin-top: 4px;
          //       width: 75px;
          //       .txt5 {
          //         width: 75px;
          //         height: 14px;
          //         overflow-wrap: break-word;
          //         color: rgba(153, 153, 153, 1);
          //         font-size: 14px;
          //         font-family: MicrosoftYaHei;
          //         text-align: left;
          //         white-space: nowrap;
          //         line-height: 14px;
          //         display: block;
          //       }
          //     }
          //   }
          // }
          // .img7 {
          //   width: 482px;
          //   height: 1px;
          //   margin-top: 13px;
          // }
          // .block3 {
          //   width: 473px;
          //   height: 45px;
          //   margin: 21px 0 0 9px;
          //   .ImageText4 {
          //     height: 20px;
          //     margin-top: 20px;
          //     width: 121px;
          //     .box1 {
          //       width: 121px;
          //       height: 20px;
          //       .label4 {
          //         width: 18px;
          //         height: 20px;
          //       }
          //       .TextGroup4 {
          //         height: 14px;
          //         margin-top: 3px;
          //         width: 91px;
          //         .word4 {
          //           width: 91px;
          //           height: 14px;
          //           overflow-wrap: break-word;
          //           color: rgba(153, 153, 153, 1);
          //           font-size: 14px;
          //           font-family: MicrosoftYaHei;
          //           text-align: left;
          //           white-space: nowrap;
          //           line-height: 14px;
          //           display: block;
          //         }
          //       }
          //     }
          //   }
          //   .pic8 {
          //     width: 125px;
          //     height: 45px;
          //   }
          // }
          // .img8 {
          //   width: 482px;
          //   height: 1px;
          //   margin-top: 7px;
          // }
          // .word5 {
          //   width: 76px;
          //   height: 16px;
          //   overflow-wrap: break-word;
          //   color: rgba(240, 111, 36, 1);
          //   font-size: 16px;
          //   font-family: MicrosoftYaHei;
          //   text-align: center;
          //   white-space: nowrap;
          //   line-height: 16px;
          //   display: block;
          //   margin: 24px 0 0 407px;
          // }
          // .block4 {
          //   height: 72px;
          //   background: url(./img/psk89nj64c44relk529h1wxlce43sp03bqoe9498c8c-18b6-4616-9c91-499cf1db12bb.png)
          //     0px 0px no-repeat;
          //   width: 342px;
          //   margin: 26px 0 0 81px;
          //   .word6 {
          //     width: 76px;
          //     height: 25px;
          //     overflow-wrap: break-word;
          //     color: rgba(255, 255, 255, 1);
          //     font-size: 26px;
          //     font-family: MicrosoftYaHei-Bold;
          //     text-align: center;
          //     white-space: nowrap;
          //     line-height: 26px;
          //     display: block;
          //     margin: 24px 0 0 133px;
          //   }
          // }
        }
      }
      .pic9 {
        z-index: 24;
        position: absolute;
        left: 1174px;
        top: 510px;
        width: 279px;
        height: 167px;
      }
    }
  }
  
}
::v-deep .step-button {
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 5px;
}
::v-deep .step-button {
  color: #fff;
  background-color: #1890ff;
  border-color: #1890ff
}
.steps-content {
  margin-top: 30px;
  ::v-deep .el-card__body {
    text-align: center;
    font-size: 20px
  }
  .buttoms {
    margin-top: 50px
  }
  ::v-deep .el-button--medium {
    width: 84px;
    height: 36px;
    color: #fff;
    color: #606266
  }
  ::v-deep .el-button--primary {
    width: 84px;
    height: 36px;
    color: #fff;
    background-color: #1890ff;
    border-color: #1890ff;
  }
}
::v-deep .el-button--medium {
  width: 84px;
  height: 36px;
  color: #fff;
  color: #606266
}
::v-deep .el-button--primary {
  width: 84px;
  height: 36px;
  color: #fff;
  background-color: #1890ff;
  border-color: #1890ff;
}
::v-deep .el-form-item__content {
  display: flex
}
.switch-org {
  margin: 15%;
  .switch-body {
    height: 170px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
  }
}
@import "./common.scss";