<template>
  <div class="page flex-col">
    <div class="outer1 flex-col">
      <div class="outer2 flex-col">
        <div class="group1 flex-row">
          <div class="main1 flex-col"></div>
          <img
            class="label1"
            referrerpolicy="no-referrer"
            src="./assets/img/psuy6k26sc9jokvtxvvxscpvxbyfb3yf6s43990506-75a0-4240-a921-6e1d560cca06.png"
          />
          <img
            class="pic1"
            referrerpolicy="no-referrer"
            src="./assets/img/psqjmhvbsxsnpzquwkmgw5fikpyzrbix0ob0691449-ea22-4ad9-8fec-374b08a5878a.png"
          />
          <div class="main2">
            <span class="word1">数字产业云</span>
            <span class="info1">平台</span>
          </div>
          <div class="main3 flex-col">
            <div class="main4 flex-col"></div>
            <img
              class="pic2"
              referrerpolicy="no-referrer"
              src="./assets/img/psscdttc9rg7nja2b61y7aa1lis55fmjpzaedb4269-e999-4838-abec-5d239e9c3c10.png"
            />
            <img
              class="pic3"
              referrerpolicy="no-referrer"
              src="./assets/img/psmwr8tbn96nil7nc7q3dbssd9ogi3v1cf67d5713-8e5c-4b59-a1e9-b115d27e7f59.png"
            />
          </div>
          <img
            class="img1"
            referrerpolicy="no-referrer"
            src="./assets/img/psxgcd5oms2k74kbqyk2no8apx5xvs08u80352540c-9c9a-404f-836e-9134df63fb4d.png"
          />
          <div class="main5 flex-col">
            <img
              class="pic4"
              referrerpolicy="no-referrer"
              src="./assets/img/psukffaahssbkb4iu0vwchk9uj5d4lr9xv353594ad-9c2a-4b82-93fc-af3331277ea1.png"
            />
          </div>
          <img
            class="pic5"
            referrerpolicy="no-referrer"
            src="./assets/img/ps88un9s8qmis3sfs17h8gt872suc5fz3dh8c004d9b-496f-456f-bfb0-5c5e6f302809.png"
          />
          <div class="main6 flex-col"></div>
        </div>
        <img
          class="img2"
          referrerpolicy="no-referrer"
          src="./assets/img/psaqbk9xz7es5nrjcn7lyd45owimiat7ec66640b1b-68b5-4fc0-8b84-6e3b6cacd93a.png"
        />
        <img
          class="img3"
          referrerpolicy="no-referrer"
          src="./assets/img/psyx7lxes2n2cfol73tk5o9o6kd2bobi4h40b6cf2d-1fc2-451a-adaf-f10a2f0ef4e0.png"
        />
        <img
          class="pic6"
          referrerpolicy="no-referrer"
          src="./assets/img/psj02f9zjdgq844qro9o8ag4qe5tcoc8s3788f40b3-02e4-4277-b5a9-7ce9e3b00135.png"
        />
        <img
          class="img4"
          referrerpolicy="no-referrer"
          src="./assets/img/psp1wdij51l2s5d5kqyx0imz9n0ej4u3ppc9d6ce75-a00e-456a-8548-5dbf6bdf16db.png"
        />
        <div class="group2 flex-col">
          <div class="mod1 flex-col">
            <img
              class="pic7"
              referrerpolicy="no-referrer"
              src="./assets/img/psvtjyd2shroazux05n85bonmytlhea22hqe092c38a-5cca-4c2d-a609-55c4554b72b0.png"
            />
          </div>
        </div>
        <div class="group3 flex-col">
          <div v-if="switchOrg" class="switch-org">
            <h4 class="switch-title">请选择岗位</h4>
            <div class="switch-body">
              <el-radio
                style="
                  margin-left: 0;
                  margin-top: 10px;
                  padding: 10px;
                  margin-right: 30px;
                "
                :label="item.orgId"
                border
                v-for="item in org"
                :key="item.orgId"
                v-model="selectOrgId"
                >{{ item.orgName }}</el-radio
              >
            </div>
            <el-button
              :loading="selectloading"
              size="medium"
              type="primary"
              style="width: 93%; margin-top: 30px"
              @click="loginByOrgId"
            >
              <span v-if="!selectloading">进入系统</span>
              <span v-else>进入系统中...</span>
            </el-button>
          </div>



          <div v-else class="section1 flex-col">
            <!-- <div class="block1">
              <span class="txt1">帐号密码登录&nbsp;</span>
              <span class="txt2">&nbsp;&nbsp;</span>
              <span class="txt3">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
              <span class="word2">手机号登录</span>
            </div>
            <div class="block2 flex-col"><div class="mod2 flex-col"></div></div> -->
            <!-- <div class="ImageText1 flex-col">
              <div class="bd1 flex-row justify-between">
                <img
                  class="label2"
                  referrerpolicy="no-referrer"
                  src="./assets/img/pss2kpn9xk271jtmrkhsc5zuhh63x6qucd1bacbc2-e87e-41b3-96f6-fd4067444d12.png"
                />
                <div class="TextGroup1 flex-col"><span class="txt4">请输入用户名</span></div>
                <input style="width: 360px; border: none" type="text">
              </div>
            </div>
            <img
              class="img5"
              referrerpolicy="no-referrer"
              src="./assets/img/psgzff8w60a5ho2dha3e92cfasd0qx92vlcf6d8447-9a9f-4dde-a87d-b5da13ebb518.png"
            />
            <div class="ImageText2 flex-col">
              <div class="layer1 flex-row justify-between">
                <img
                  class="label3"
                  referrerpolicy="no-referrer"
                  src="./assets/img/ps9u5hpebr6y4ccbu676rfzkkb5u250log231005fb-ec59-4355-925f-e9e2985347ed.png"
                />
                <div class="TextGroup2 flex-col"><span class="word3">请输入帐号</span></div>
              </div>
            </div>
            <img
              class="img6"
              referrerpolicy="no-referrer"
              src="./assets/img/psslzdi1lg54ny37d5okgxub18bdf6n6nlb46b2f80-6cce-4d7c-882d-f0cc0f87eba8.png"
            />
            <div class="ImageText3 flex-col">
              <div class="group4 flex-row justify-between">
                <img
                  class="icon1"
                  referrerpolicy="no-referrer"
                  src="./assets/img/ps36o2wcwntu4wg7ep07yyxi26s2pf9w62zd1c1d8e4-9fd2-4dba-b4de-e0df004b0397.png"
                />
                <div class="TextGroup3 flex-col"><span class="txt5">请输入密码</span></div>
              </div>
            </div>
            <img
              class="img7"
              referrerpolicy="no-referrer"
              src="./assets/img/psim586wqwqnezgigid49l0anz5l1b6qc2ece3a0685-9736-4f23-a58c-6d1ea26f3329.png"
            />
            <div class="block3 flex-row justify-between">
              <div class="ImageText4 flex-col">
                <div class="box1 flex-row justify-between">
                  <img
                    class="label4"
                    referrerpolicy="no-referrer"
                    src="./assets/img/ps908gxrlvmuoxcfwwx7tgveh5msh1u3gffffac06-0ee0-44d6-adef-e9de37275119.png"
                  />
                  <div class="TextGroup4 flex-col"><span class="word4">请输入验证码</span></div>
                </div>
              </div>
              <img
                class="pic8"
                referrerpolicy="no-referrer"
                src="./assets/img/pswxhuqo5547q1upp8190hsxcln6omphhq7ab5e68aa-ca2e-45d6-98a4-1bedea6a8bc6.png"
              />
            </div>
            <img
              class="img8"
              referrerpolicy="no-referrer"
              src="./assets/img/ps6z6yrekua9gvsdi6dlhspglb68x3i0o745c55f42-e6e0-41a1-85bb-8102c25cc04e.png"
            />
            <span class="word5">忘记密码？</span>
            <div class="block4 flex-col"><span class="word6">登&nbsp;&nbsp;录</span></div> -->

            <el-tabs v-model="loginType" @tab-click="handleClick">
              <el-tab-pane label="账号密码登录" name="web">
                <el-form
                  ref="userLoginForm"
                  :model="loginForm"
                  :rules="userLoginRules"
                  style="margin-top: 10px"
                >
                  <el-form-item
                    prop="tenantLoginName"
                    :id="
                      this.url.indexOf('tenantName') !== -1 &&
                      this.loginForm.tenantId
                        ? 'untenantLoginName'
                        : ''
                    "
                  >
                    <el-input
                      v-model="loginForm.tenantLoginName"
                      type="text"
                      auto-complete="off"
                      placeholder="租户名"
                      @blur="getUserTenantId"
                    >
                      <svg-icon
                        slot="prefix"
                        icon-class="user"
                        class="el-input__icon input-icon"
                      />
                    </el-input>
                    <el-input
                      v-model="loginForm.tenantId"
                      auto-complete="off"
                      placeholder="租户ID"
                      @blur="getTenantId"
                      style="display: none"
                    >
                      <svg-icon
                        slot="prefix"
                        icon-class="user"
                        class="el-input__icon input-icon"
                        style="display: none"
                      />
                      {{ loginForm.tenantId }}
                    </el-input>
                  </el-form-item>
                  <div v-if="errorMsgs" style="margin: -22px 0 4px">
                    <span class="error-msg">{{ errorMsgs }}</span>
                  </div>
                  <el-form-item prop="uni_name">
                    <el-input
                      v-model="loginForm.uni_name"
                      type="text"
                      auto-complete="off"
                      placeholder="账号"
                    >
                      <svg-icon
                        slot="prefix"
                        icon-class="user"
                        class="el-input__icon input-icon"
                      />
                    </el-input>
                  </el-form-item>
                  <el-form-item prop="inpk">
                    <el-input
                      v-model="loginForm.inpk"
                      type="password"
                      auto-complete="off"
                      placeholder="密码"
                    >
                      <svg-icon
                        slot="prefix"
                        icon-class="password"
                        class="el-input__icon input-icon"
                      />
                    </el-input>
                  </el-form-item>
                  <el-form-item prop="captcha">
                    <div class="op">
                      <el-input
                        type="input"
                        placeholder="图形验证码"
                        v-model="loginForm.captcha"
                        style="width: 53%; height: 100%"
                        @keyup.enter.native="handleLogin"
                      ></el-input>
                      <img
                        style="width: 40%; margin-left: 15px;"
                        v-if="captchaUrl"
                        class="captcha"
                        @click="refreshCaptcha"
                        alt="图形验证码"
                        :src="captchaUrl"
                      />
                    </div>
                  </el-form-item>
                  <div v-if="errorMsg" style="margin: -22px 0 4px">
                    <span class="error-msg">{{ errorMsg }}</span>
                  </div>
                  <div class="functions">
                    <el-button
                      :class="
                        this.forgetPassword == 1
                          ? 'forgetPassword'
                          : 'unforgetPassword'
                      "
                      type="text"
                      @click="handleForget"
                    >
                      <span>忘记密码?</span>
                    </el-button>
                    <!-- <el-button type="text" @click="changeTenant" id="changeTenant">
                      <span>切换租户</span>
                    </el-button>
                    <el-button
                      :class="
                        this.tenantApply == 1
                          ? 'tenantAppliation'
                          : 'untenantAppliation'
                      "
                      type="text"
                      @click="tenant"
                    >
                      <span>租户申请</span>
                    </el-button> -->
                  </div>

                  <el-form-item style="width: 100%;margin-top: 85px">
                    <el-button
                      :loading="loading"
                      size="medium"
                      type="primary"
                      class="login-button"
                      @click.native.prevent="handleLogin"
                    >
                      <span v-if="!loading">登 录</span>
                      <span v-else>登 录 中...</span>
                    </el-button>
                  </el-form-item>
                </el-form>
              </el-tab-pane>
              <el-tab-pane label="手机号码登录" name="cellphone">
                <transition name="el-fade-in-linear">
                  <el-form
                    ref="phoneLoginForm"
                    :model="loginForm"
                    :rules="phoneLoginRules"
                    style="margin-top: 10px"
                  >
                    <el-form-item
                      prop="tenantLoginName"
                      :id="this.loginForm.tenantId ? 'untenantLoginName' : ''"
                    >
                      <el-input
                        v-model="loginForm.tenantLoginName"
                        type="text"
                        auto-complete="off"
                        placeholder="租户名"
                        @blur="getTenantId"
                      >
                        <svg-icon
                          slot="prefix"
                          icon-class="user"
                          class="el-input__icon input-icon"
                        />
                      </el-input>
                      <el-input
                        v-model="loginForm.tenantId"
                        auto-complete="off"
                        placeholder="租户ID"
                        style="display: none"
                      >
                        <svg-icon
                          slot="prefix"
                          icon-class="user"
                          class="el-input__icon input-icon"
                          style="display: none"
                        />
                        {{ loginForm.tenantId }}
                      </el-input>
                    </el-form-item>
                    <div v-if="errorMsgs" style="margin: -22px 0 4px">
                      <span class="error-msg">{{ errorMsgs }}</span>
                    </div>
                    <el-form-item prop="phone">
                      <el-input
                        v-model="loginForm.phone"
                        type="text"
                        auto-complete="off"
                        placeholder="手机号"
                      >
                        <svg-icon
                          slot="prefix"
                          icon-class="phone"
                          class="el-input__icon input-icon"
                        />
                      </el-input>
                    </el-form-item>
                    <el-form-item prop="captcha">
                      <el-input
                        v-model="loginForm.captcha"
                        auto-complete="off"
                        placeholder="验证码"
                        style="width: 55%"
                        @keyup.enter.native="handleLogin"
                      >
                        <svg-icon
                          slot="prefix"
                          icon-class="validCode"
                          class="el-input__icon input-icon"
                        />
                      </el-input>
                      <div class="login-code" style="width: 40%;margin-left: 20px">
                        <el-button
                          type="text"
                          :disabled="smsTime > 0"
                          @click="sendSms"
                          style="font-size: 16px"
                          >{{
                            smsTime <= 0 ? "获取验证码" : `${smsTime}s 后重新获取`
                          }}</el-button
                        >
                      </div>
                    </el-form-item>
                    <div v-if="errorMsg" style="margin: -22px 0 4px">
                      <span class="error-msg">{{ errorMsg }}</span>
                    </div>
                    <div class="functions">
                      <div class="tenantAppliation" @click="tenant">
                        <span>租户申请</span>
                      </div>
                    </div>
                    <el-form-item style="width: 100%;margin-top: 85px">
                      <el-button
                        :loading="loading"
                        size="medium"
                        type="primary"
                        class="login-button"
                        @click.native.prevent="handlePhoneLogin"
                      >
                        <span v-if="!loading">登 录</span>
                        <span v-else>登 录 中...</span>
                      </el-button>
                    </el-form-item>
                  </el-form>
                </transition>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
        <!-- 租户申请的弹窗 -->
        <el-dialog
          title="租户申请"
          :visible.sync="dialogVisible"
          :before-close="handleClose"
          :append-to-body="true"
        >
          <!-- 租户申请填写 -->
          <div v-if="fillInformation">
            <a-steps :current="current">
              <a-step
                v-for="item in steps"
                :key="item.title"
                :title="item.title"
              />
            </a-steps>
            <!-- 第一步 -->
            <div class="steps-content" v-if="steps[0].content">
              <div class="chose">
                <el-card class="box-card">
                  <div slot="header" class="clearfix">
                    <span style="font-size: 18px">租户注册</span>
                  </div>
                  <div class="text-Item">
                    欢迎进入租户申请页面，请根据需求选择合适的入口进入
                  </div>
                  <div class="buttoms">
                    <el-button type="primary" @click="newApp">新申请</el-button>
                    <el-button @click="applying">已申请</el-button>
                  </div>
                </el-card>
              </div>
            </div>
            <!-- 第二步 -->
            <div class="steps-content" v-if="steps[1].content">
              <div class="fillInformation">
                <el-form
                  ref="submitList"
                  :model="lists"
                  :rules="rules"
                  class="form"
                  label-position="right"
                  label-width="135px"
                >
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="租户名称" prop="tenantName">
                        <el-input
                          v-model="lists.tenantName"
                          placeholder="请输入租户名称"
                          maxlength="64"
                          style="width: 200px"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="租户登录名" prop="tenantLoginName">
                        <el-input
                          v-model="lists.tenantLoginName"
                          placeholder="请输入租户登录名"
                          maxlength="64"
                          style="width: 200px"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="租户管理员登录名" prop="displayName">
                        <el-input
                          v-model="lists.displayName"
                          placeholder="请输入管理员登录名"
                          maxlength="64"
                          style="width: 200px"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="租户管理员姓名" prop="staffName">
                        <el-input
                          v-model="lists.staffName"
                          placeholder="请输入管理员姓名"
                          maxlength="11"
                          style="width: 200px"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="最大用户数" prop="maxStaff">
                        <el-input-number
                          v-model="lists.maxStaff"
                          controls-position="right"
                          :min="0"
                          style="width: 200px"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="有效截止时间" prop="effectiveDate">
                        <el-date-picker
                          v-model="lists.effectiveDate"
                          size="small"
                          type="datetime"
                          style="width: 200px"
                          value-format="yyyy-MM-dd HH:mm:ss"
                        ></el-date-picker>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="组织名称" prop="orgName">
                        <el-input
                          v-model="lists.orgName"
                          placeholder="请输入组织名称"
                          maxlength="50"
                          style="width: 200px"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="组织编码" prop="code">
                        <el-input
                          v-model="lists.code"
                          placeholder="请输入组织编码"
                          maxlength="50"
                          style="width: 200px"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </div>
            </div>
            <!-- 第三步 -->
            <div class="steps-content" v-if="steps[2].content">
              <div class="finish">
                <el-card class="box-card">
                  <div slot="header" class="clearfix">
                    <span style="font-size: 18px">租户完成注册</span>
                  </div>
                  <div
                    class="text item"
                    style="
                      margin: 0 auto;
                      line-height: 100px;
                      text-align: center;
                    "
                  >
                    <span style="font-size: 20px"
                      >您已提交申请，请耐心等待管理员审核通过...</span
                    >
                  </div>
                </el-card>
              </div>
            </div>
            <div class="steps-action">
              <el-button
                v-if="current !== 0 && current < steps.length - 1"
                type="primary"
                @click="submit('submitList')"
                :loading="iconLoading"
              >
                提交申请
              </el-button>
              <el-button
                v-if="current === steps.length - 1"
                type="primary"
                style="margin-top: 15px"
                @click="closeDialog"
              >
                关闭
              </el-button>
              <el-button
                v-if="current === 1"
                style="margin-left: 8px"
                @click="prev"
              >
                上一步
              </el-button>
            </div>
          </div>

          <!-- 租户信息查看 -->
          <div class="applyAll" v-if="applys">
            <el-card>
              <div class="textItem" style="height: 25px">
                <el-form
                  ref="findForm"
                  label-width="180px"
                  class="findForm"
                  :model="loginNameList"
                  @submit.native.prevent
                >
                  <el-row>
                    <el-col :span="5">
                      <el-form-item label="租户名称" prop="tenantName">
                        <el-input
                          maxlength="64"
                          style="width: 200px"
                          v-model="loginNameList.tenantName"
                          placeholder="请输入租户名称"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="19">
                      <el-button
                        type="primary"
                        @click="findInfo"
                        style="margin-left: 300px"
                        >查询</el-button
                      >
                    </el-col>
                  </el-row>
                </el-form>
              </div>
            </el-card>

            <el-card class="boxCard" style="margin-top: 15px">
              <div class="text-item">
                <el-form
                  ref="lists"
                  :model="lists"
                  class="tenantInfo"
                  label-position="right"
                  label-width="135px"
                >
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="租户名称" prop="tenantName">
                        <el-input
                          v-model="lists.tenantName"
                          maxlength="64"
                          style="width: 200px; color: red"
                          :disabled="true"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="租户登录名" prop="tenantLoginName">
                        <el-input
                          v-model="lists.tenantLoginName"
                          maxlength="64"
                          style="width: 200px"
                          :disabled="true"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="租户管理员登录名" prop="displayName">
                        <el-input
                          v-model="lists.displayName"
                          maxlength="64"
                          style="width: 200px"
                          :disabled="true"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="租户管理员姓名" prop="staffName">
                        <el-input
                          v-model="lists.staffName"
                          maxlength="11"
                          style="width: 200px"
                          :disabled="true"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="最大用户数" prop="maxStaff">
                        <el-input-number
                          v-model="lists.maxStaff"
                          controls-position="right"
                          style="width: 200px"
                          :disabled="true"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="有效截止时间" prop="effectiveDate">
                        <el-date-picker
                          v-model="lists.effectiveDate"
                          size="small"
                          type="datetime"
                          style="width: 200px"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          :disabled="true"
                        ></el-date-picker>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </div>
            </el-card>
            <div style="width:100%;margin-top: 15px">
              <el-button type="primary" @click="returnIndex" style="margin:0 auto;display:block">返 回</el-button>
            </div>

          </div>
          <!-- 租户信息查看结束 底部关闭按钮 -->
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="closeDialog">关闭窗口</el-button>
          </span>
          <!-- 底部关闭按钮结束 -->
        </el-dialog>

        <!-- 忘记密码的弹窗 -->
        <el-dialog
          title="重置密码"
          :visible.sync="open"
          width="500px"
          append-to-body
          :close-on-click-modal="false"
          :close-on-press-escape="false"
        >
          <el-form
            ref="forgetPassWordForm"
            class="forget-form"
            :model="forgetPassWordForm"
            :rules="forgetPassWordRules"
          >
            <el-form-item prop="loginName">
              <el-input
                placeholder="请输入需要重置的账号"
                v-model="forgetPassWordForm.loginName"
              />
            </el-form-item>
            <el-form-item prop="cellphone">
              <el-input
                v-model="forgetPassWordForm.cellphone"
                type="text"
                auto-complete="off"
                placeholder="手机号"
              >
                <svg-icon
                  slot="prefix"
                  icon-class="phone"
                  class="el-input__icon input-icon"
                />
              </el-input>
            </el-form-item>
            <el-form-item prop="verificationCode">
              <el-input
                v-model="forgetPassWordForm.verificationCode"
                auto-complete="off"
                placeholder="验证码"
                style="width: 53%"
              >
                <svg-icon
                  slot="prefix"
                  icon-class="validCode"
                  class="el-input__icon input-icon"
                />
              </el-input>
              <div class="login-code">
                <el-button
                  type="text"
                  :disabled="forgetPassWordFormTime > 0"
                  @click.stop.prevent="forGetSmsTime"
                  >{{
                    forgetPassWordFormTime <= 0
                      ? "获取验证码"
                      : `${forgetPassWordFormTime}s 后重新获取`
                  }}</el-button
                >
              </div>
            </el-form-item>
            <el-form-item label-width="180px">
              <el-button type="primary" class="step-button" @click="nextStep">下一步</el-button>
            </el-form-item>
          </el-form>
        </el-dialog>
        <!-- 忘记密码的弹窗2 -->
        <el-dialog
          title="重置密码"
          :visible.sync="next"
          width="500px"
          append-to-body
          :close-on-click-modal="false"
          :close-on-press-escape="false"
        >
          <el-form
            ref="forgetPassWordForm"
            class="forget-form"
            :model="forgetPassWordForm"
            :rules="forgetPassWordRules"
          >
            <el-form-item prop="newPassWord">
              <el-input
                v-model="forgetPassWordForm.newPassWord"
                type="password"
                auto-complete="off"
                placeholder="新的密码"
                @keyup.enter.native="handleLogin"
              >
                <svg-icon
                  slot="prefix"
                  icon-class="password"
                  class="el-input__icon input-icon"
                />
              </el-input>
            </el-form-item>
            <el-form-item label-width="180px">
              <el-button type="primary" class="step-button" @click="updatePassWord">确定</el-button>
            </el-form-item>
          </el-form>
        </el-dialog>



        <img
          class="pic9"
          referrerpolicy="no-referrer"
          src="./assets/img/psd00z5ylxbydq004wf7rfjmgrpqoxa1k540d8cc679-b7ec-41f5-a0b5-187caaafabe5.png"
        />
      </div>
    </div>
  </div>
</template>
<script>
import refreshCaptcha from "@/utils/refreshCaptcha";
import UMarquee from "../../components/UMarquee";
import { sendSms, jobSelect, add, tenant, configData } from "@/api/login";
import { getVerificationCode, forgottenPassword } from "@/api/system/user";
import SliderCheck from "@/components/SliderCheck";
import doEncrypt from "@/utils/crypto";
import { setToken } from "@/utils/auth";
// import { getPersonalConfig, statSpeed } from "@/api/system/config";
// import { selectGeneralConfigForTenant } from "/src/api/system/config";
import { mapState } from "vuex";
export default {
  name: "NewLogin",
  components: {
    UMarquee,
    SliderCheck,
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.user.userInfo,
    }),
  },
  data() {
    return {
      constants: {},
      show: "",
      // 租户名称是否显示
      showTenant: "",
      forgetPassword: 1,
      tenantApply: 1,
      telLogin: "",
      systemTitle: "",
      iconLoading: false,
      current: 0,
      steps: [
        {
          title: "用户选择入口",
          content: false,
        },
        {
          title: "填写申请信息",
          content: false,
        },
        {
          title: "等待审核",
          content: false,
        },
      ],
      //忘记密码
      forgetPassWordForm: {
        loginName: "",
        newPassWord: "",
        cellphone: "",
        verificationCode: "",
      },
      next: false,
      open: false,
      noticeLists: [],
      codeUrl: "",
      errorMsgs: "",
      errorMsg: "",
      loginForm: {
        uni_name: "",
        inpk: "",
        captcha: "",
        token: "",
        phone: "",
        tenantLoginName: "",
        tenantId: "",
        pgp: "", //随机数
      },
      loginNameList: {
        tenantName: "",
      },
      rules: {
        tenantName: [
          { required: true, message: "租户名称不能为空", trigger: "blur" },
        ],
        tenantLoginName: [
          { required: true, message: "租户登录名不能为空", trigger: "blur" },
        ],
        staffName: [
          {
            required: true,
            message: "租户管理员姓名不能为空",
            trigger: "blur",
          },
        ],
        maxStaff: [
          { required: true, message: "最大用户数不能为空", trigger: "blur" },
        ],
        effectiveDate: [
          { required: true, message: "有效截止时间不能为空", trigger: "blur" },
        ],
        displayName: [
          {
            required: true,
            message: "租户管理员登录名不能为空",
            trigger: "blur",
          },
        ],
        orgName: [
          { required: true, message: "组织名称不能为空", trigger: "blur" },
        ],
        kind: [{ required: true, message: "请选择组织类型", trigger: "blur" }],
        code: [
          { required: true, message: "组织编码不能为空", trigger: "blur" },
        ],
        cellphone: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
      },
      forgetPassWordRules: {
        loginName: [
          { required: true, trigger: "blur", message: "用户名不能为空" },
        ],
        cellphone: [
          { required: true, trigger: "blur", message: "手机号不能为空" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
        verificationCode: [
          { required: true, trigger: "blur", message: "验证码不能为空" },
        ],
        newPassWord: [
          { required: true, trigger: "blur", message: "新密码不能为空" },
        ],
      },
      userLoginRules: {
        uni_name: [
          { required: true, trigger: "blur", message: "用户名不能为空" },
        ],
        inpk: [{ required: true, trigger: "blur", message: "密码不能为空" }],
        captcha: [{ required: true, message: "请输入验证码", trigger: "blur" }],
      },
      phoneLoginRules: {
        captcha: [
          { required: true, trigger: "blur", message: "验证码不能为空" },
        ],
        phone: [
          { required: true, trigger: "blur", message: "手机号不能为空" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
      },
      loading: false,
      redirect: undefined,
      loginStatus: false,
      loginType: "web",
      smsTime: 0,
      forgetPassWordFormTime: 0,
      timer: null,
      forgetPassWordFormTimer: null,
      switchOrg: false,
      org: [],
      selectOrgId: undefined,
      selectloading: false,
      dialogVisibleOne: false,
      dialogVisible: false,
      lists: {
        tenantSaveType: "settled",
      },
      labelPosition: "left",
      information: false,
      finish: false,
      tenantEnter: true,
      applys: false,
      fillInformation: true,
      // 获取验证码api
      captchaApi: process.env.VUE_APP_BASE_API + "/auth/captcha",
      // 获取验证码url
      captchaUrl: undefined,
      url: "",
      // 路由参数等于号后的值
      url_tenantName: "",
    };
  },
  watch: {
    //监听是否输入正确的租户名称参数
    url: function (newVal, oldVal) {
      if (newVal.indexOf("tenantName") === -1) {
        this.requestError();
        this.$message.error("参数拼错！请输入正确参数(tenantName)");
      }
    },
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
    "loginForm.tenantLoginName": function (newVal, oldVal) {
      this.errorMsg = "";
    },
    "loginForm.uni_name": function (newVal, oldVal) {
      this.checkReset();
      this.errorMsg = "";
    },
    "loginForm.inpk": function (newVal, oldVal) {
      this.checkReset();
      this.errorMsg = "";
    },
    "loginForm.captcha": function (newVal, oldVal) {
      this.errorMsg = "";
    },
  },
  created() {
    this.getCode();
    this.refreshCaptcha();
  },
  beforeMount() {
    this.getTanentIds();
    this.judgeParameter();
  },
  methods: {
    // 切换租户
    changeTenant() {
      document.getElementById("untenantLoginName").style.display = "block";
      this.loginForm.tenantLoginName = "";
    },
    // 获取路由参数等于号后的值
    judgeParameter() {
      var test = this.url.indexOf("=");
      var length = this.url.length;
      this.url_tenantName = this.url.substring(test + 1, length);
    },
    // 获取动态配置数据
    getConfigData() {
      configData({
        clientType: "1",
        tenantId: this.loginForm.tenantId,
      }).then((res) => {
        // this.$nextTick(() => {
        if (res.success === true) {
          res.data.forEach((element) => {
            if (element.configCode === "login_tenant_left_img") {
              this.left_img = element.configValue;
            } else if (element.configCode === "login_tenant_rightBottom_img") {
              this.rightBottom_img = element.configValue;
            } else if (element.configCode === "login_tenant_rightTopLeft_img") {
              this.rightTopLeft_img = element.configValue;
            } else if (
              element.configCode === "login_tenant_rightTopRight_img"
            ) {
              this.rightTopRight_img = element.configValue;
            } else if (element.configCode === "login_tenant_forgetPassword") {
              this.forgetPassword = element.configValue;
            } else if (element.configCode === "login_tenant_tenantApply") {
              this.tenantApply = element.configValue;
            } else if (element.configCode === "tenant_title") {
              this.systemTitle = element.configValue;
            }
          });
        } else {
          this.$nextTick(() => {
            this.requestError();
          });
        }
        // });
      });
    },
    // 请求失败和默认值
    requestError() {
      this.$nextTick(() => {
        // (this.left_img = require("../assets/images/LTImg/login-left.jpg")),
        //   (this.rightTopLeft_img = require("../assets/images/LTImg/Oval-left.png")),
        //   (this.rightTopRight_img = require("../assets/images/LTImg/Oval-left.png")),
        //   (this.rightBottom_img = require("../assets/images/LTImg/bottom.png")),
          (this.forgetPassword = 1),
          (this.tenantApply = 1);
      });
    },
    // 判断路由参数中是否有tentantName
    getTanentIds() {
      this.url = location.search; //获取url中"?"符后的字串
      if (this.url.indexOf("tenantName") !== -1) {
        var tantentData = new Object();
        if (this.url.indexOf("?") !== -1) {
          var str = this.url.substr(1); //substr()方法返回从参数值开始到结束的字符串；
          var strs = str.split("&");
          for (var i = 0; i < strs.length; i++) {
            tantentData[strs[i].split("=")[0]] = strs[i].split("=")[1];
          }
          this.loginForm.tenantLoginName = tantentData.tenantName;
          this.getTenantId();
        }
      }
    },
    // 刷新验证码
    refreshCaptcha() {
      this.captchaUrl = `${this.captchaApi}?token=${
        this.loginForm.token
      }&t=${Math.random()}`;
      this.loginForm.captcha = "";
    },
    submit(submitList) {
      this.$refs["submitList"].validate((valid) => {
        if (valid) {
          this.iconLoading = true;
          if (this.lists !== "") {
            add(this.lists).then((response) => {
              if (response.success === true) {
                this.iconLoading = false;
                this.current++;
                if (this.current === 0) {
                  this.steps[this.current].content = true;
                } else if (this.current === 1) {
                  this.steps[this.current].content = true;
                  (this.steps[0].content = false),
                    (this.steps[2].content = false);
                } else if (this.current === 2) {
                  this.steps[this.current].content = true;
                  (this.steps[0].content = false),
                    (this.steps[1].content = false);
                }
              } else {
                this.$message.error(response.message);
                this, (this.iconLoading = false);
              }
            });
          }
        } else {
          return false;
        }
      });
    },
    prev() {
      this.current--;
      if (this.current === 2) {
        this.steps[this.current].content = true;
      } else if (this.current === 1) {
        this.steps[this.current].content = true;
        (this.steps[0].content = false), (this.steps[2].content = false);
      } else if (this.current === 0) {
        this.steps[this.current].content = true;
        (this.steps[1].content = false), (this.steps[2].content = false);
      }
    },
    //查询用户信息
    findInfo() {
      tenant({ tenant: this.loginNameList.tenantName }).then((res) => {
        if (res.data !== null) {
          this.lists = res.data;
        } else {
          this.$message.error("无此租户，请核实后再输入");
        }
      });
    },
    // 根据路由参数获取租户名 获取该id 表单中（用户输入完毕后去请求tenantId）
    getTenantId() {
      if (this.loginForm.tenantLoginName) {
        this.errorMsgs = "";
        tenant({ tenant: this.loginForm.tenantLoginName }).then(
          (res) => {
            if (res.success === true && res.data !== null) {
              this.loginForm.tenantId = res.data.tenantId;
              this.loginForm.tenantLoginName = res.data.tenantLoginName;
              this.getConfigData();
            } else {
              this.requestError();
              this.loginForm.tenantLoginName = undefined;
              this.loginForm.tenantId = undefined;
              this.$message.error("该租户名有误或已过期，请重新输入");
            }
          }
        );
      }
    },
    // 根据用户输入获取租户名 获取该id 表单中（用户输入完毕后去请求tenantId）
    getUserTenantId() {
      if (this.loginForm.tenantLoginName) {
        this.errorMsgs = "";
        tenant({ tenant: this.loginForm.tenantLoginName }).then(
          (res) => {
            if (res.success === true && res.data !== null) {
              this.loginForm.tenantId = res.data.tenantId;
              this.loginForm.tenantLoginName = res.data.tenantLoginName;
            } else {
              this.$nextTick(() => {
                this.requestError();
              });
              this.loginForm.tenantLoginName = undefined;
              this.loginForm.tenantId = undefined;
              this.errorMsgs = "该租户名有误或已过期，请重新输入";
            }
          }
        );
      }
    },
    // 返回租户申请页面
    returnIndex() {
      (this.lists = {
        tenantName: "",
        tenantLoginName: "",
        displayName: "",
        staffName: "",
        maxStaff: "",
        effectiveDate: "",
      }),
        (this.applys = false),
        (this.fillInformation = true);
    },
    // 进入租户信息展示页面
    applying() {
      (this.lists = {
        tenantName: "",
        tenantLoginName: "",
        displayName: "",
        staffName: "",
        maxStaff: "",
        effectiveDate: "",
        orgName: "",
        code: "",
      }),
        (this.applys = true),
        (this.fillInformation = false);
    },
    // 关闭el-dialog弹窗
    closeDialog() {
      this.dialogVisible = false;
    },
    // 返回上一步
    back() {
      (this.information = false), (this.tenantEnter = true);
    },
    // 新申请
    newApp() {
      this.steps[1].content = true;
      this.steps[0].content = false;
      this.steps[2].content = false;
      this.current = 1;
    },
    // 点击弹窗外的区域关闭弹窗
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          done();
        })
        .catch((_) => {});
    },
    // 租户申请的弹窗
    tenant() {
      this.dialogVisible = true;
      this.current = 0;
      this.dialogVisible = true;
      this.steps[0].content = true;
      this.steps[1].content = false;
      this.steps[2].content = false;
    },
    // 忘记密码弹窗的打开
    handleForget() {
      this.open = true;
    },
    nextStep() {
      this.$refs["forgetPassWordForm"].validateField(
        ["loginName", "cellphone", "verificationCode"],
        (valid) => {
          if (!valid) {
            this.next = true;
            (this.title = "重置密码"), (this.open = false);
          }
        }
      );
    },
    getCode() {
      this.loginForm.token = refreshCaptcha();
      this.codeUrl = `/dev-api/auth/captcha?token=${this.loginForm.token}`;
    },
    handleClick(tab, event) {
      this.errorMsg = "";
    },
    handleLogin() {
      this.$refs.userLoginForm.validate((valid) => {
        if (valid) {
          this.loading = true;

          let max = 12,
            min = 9,
            stra = "",
            arr =
              "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"; // 随机值的长度
          const range = Math.round(Math.random() * (max - min)) + min;

          //随机数值的产生
          for (var i = 0; i < range; i++) {
            const random = Math.round(Math.random() * (arr.length - 1));
            stra = stra + arr[random];
            this.loginForm.pgp = stra;
          }

          this.$store
            .dispatch("Login", this.loginForm)
            .then((data) => {
              if (
                data &&
                data.additionalInformation &&
                data.additionalInformation.userJobDetailVOList &&
                Array.isArray(data.additionalInformation.userJobDetailVOList) &&
                data.additionalInformation.userJobDetailVOList.length > 1
              ) {
                this.switchOrg = true;
                this.org = data.additionalInformation.userJobDetailVOList;
                //排序  按照isMaster的大小排序
                function compare(pro) {
                  return function (a, b) {
                    var topValue = a[pro];
                    var bottomValue = b[pro];
                    return topValue - bottomValue;
                  };
                }
                this.org = this.org.sort(compare("staffOrgType"));
                //如果isMaster全等于0  就拼接字符串
                for (var i = 0; i < this.org.length; i++) {
                  if (this.org[i].staffOrgType === "F") {
                    this.org[i].orgName += "(主岗)";
                  }
                }
              } else {
                location.href = this.redirect || "/show/homePage";
              }
            })
            .catch((msg) => {
              this.$message.error(msg);
              this.refreshCaptcha();
              this.loading = false;
            });
        }
      });
    },
    // 手机号短信验证登录
    handlePhoneLogin() {
      this.$refs.phoneLoginForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.$store
            .dispatch("smscodeLogin", this.loginForm)
            .then((res) => {
              if (
                res &&
                res.additionalInformation &&
                res.additionalInformation.userJobDetailVOList &&
                Array.isArray(res.additionalInformation.userJobDetailVOList) &&
                res.additionalInformation.userJobDetailVOList.length > 1
              ) {
                this.switchOrg = true;
                this.org = res.additionalInformation.userJobDetailVOList;
                //排序  按照isMaster的大小排序
                function compare(pro) {
                  return function (a, b) {
                    var topValue = a[pro];
                    var bottomValue = b[pro];
                    return topValue - bottomValue;
                  };
                }
                this.org = this.org.sort(compare("staffOrgType"));
                //如果isMaster全等于0  就拼接字符串
                for (var i = 0; i < this.org.length; i++) {
                  if (this.org[i].staffOrgType === "0") {
                    this.org[i].orgName += "(主岗)";
                  }
                }
              } else {
                location.href = this.redirect || "/show/homePage";
              }
            })
            .catch((msg) => {
              this.errorMsg = msg;
              this.checkReset();
              this.loading = false;
              this.getCode();
            });
        }
      });
    },
    checkReset() {
      this.loginStatus = false;
    },
    // 发送验证码
    sendSms() {
      this.$refs.phoneLoginForm.validateField("phone", (valid) => {
        if (!valid) {
          this.smsLoading = true;
          sendSms({
            authkey: doEncrypt(`0@bVS46ElU@${this.loginForm.phone}`),
          }).then((r) => {
            this.smsLoading = true;
            if (r.success) {
              if (r.data.resultcode === 0) {
                this.msgSuccess("验证码发送成功！");
                const TIME_COUNT = 60;
                if (!this.timer) {
                  this.smsTime = TIME_COUNT;
                  this.show = false;
                  this.timer = setInterval(() => {
                    if (this.smsTime > 0 && this.smsTime <= TIME_COUNT) {
                      this.smsTime--;
                    } else {
                      this.show = true;
                      clearInterval(this.timer);
                      this.timer = null;
                    }
                  }, 1000);
                }
              } else if (r.data.resultcode === 4) {
                this.$message.error(r.data.resultmsg);
              } else if (r.data.resultcode === 6) {
                this.$message.error(r.data.resultmsg);
              }
            } else {
              this.msgError(r.message);
            }
          });
        }
      });
    },
    // 选择岗位后登陆
    loginByOrgId() {
      if (!this.selectOrgId) {
        this.msgError("请选择岗位");
        return;
      }
      this.selectloading = true;
      const form = new FormData();
      form.append("orgId", this.selectOrgId);
      jobSelect(form).then((r) => {
        this.selectloading = false;
        if (r.success) {
          setToken(r.data.value);
          location.href = this.redirect || "/show/homePage";
        } else {
          this.msgError("数据异常");
        }
      });
    },
    forGetSmsTime() {
      this.$refs["forgetPassWordForm"].validateField(
        ["loginName", "cellphone"],
        (valid) => {
          if (!valid) {
            getVerificationCode({
              ...this.forgetPassWordForm,
              getVerificationCodeType: "1",
            }).then((r) => {
              if (r.success) {
                this.msgSuccess("验证码发送成功");
                const TIME_COUNT = 60;
                if (!this.forgetPassWordFormTimer) {
                  this.forgetPassWordFormTime = TIME_COUNT;
                  this.show = false;
                  this.forgetPassWordFormTimer = setInterval(() => {
                    if (
                      this.forgetPassWordFormTime > 0 &&
                      this.forgetPassWordFormTime <= TIME_COUNT
                    ) {
                      this.forgetPassWordFormTime--;
                    } else {
                      this.show = true;
                      clearInterval(this.forgetPassWordFormTimer);
                      this.forgetPassWordFormTimer = null;
                    }
                  }, 1000);
                }
              } else {
                this.msgError(r.message);
              }
            });
          }
        }
      );
    },
    updatePassWord() {
      this.$refs["forgetPassWordForm"].validateField(
        ["newPassWord"],
        (valid) => {
          if (!valid) {
            forgottenPassword({
              ...this.forgetPassWordForm,
              newPassWord: doEncrypt(this.forgetPassWordForm.newPassWord),
            }).then((r) => {
              if (r.success) {
                this.msgSuccess("密码修改成功");
                this.next = false;
              } else {
                this.msgError(r.message);
              }
            });
          }
        }
      );
    },
    forgetPassWordFormClose() {
      this.resetForm("forgetPassWordForm");
    },
  },
};
</script>
<style scoped lang="scss" src="./assets/index.scss" />
