import request from '@/utils/request'

// 分页查询客户表
export function getPageList(params) {
  return request({
    url: '/customer/customer/findPage',
    method: 'get',
    params,
  })
}

// 查询客户表详细信息
export function getInfo(id) {
  return request({
    url: '/customer/customer/findInfo',
    method: 'get',
    params: {
        id
    }
  })
}

// 新增客户表
export function addCustomer(data) {
  return request({
    url: '/customer/customer/add',
    method: 'post',
    data,
  })
}

// 修改客户表
export function updateCustomer(data) {
  return request({
    url: '/customer/customer/update',
    method: 'post',
    data,
  })
}

// 删除客户表
export function deleteCustomer(id) {
  return request({
    url: '/customer/customer/delete',
    method: 'post',
    params: {
        id
    }
  })
}
