<template>
  <div class="app-container">
    <el-card shadow="never">
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        :inline="true"
      >
        <el-form-item
          label="租户"
          v-if="$store.getters.customParam.userType === 'admin'"
        >
          <el-select
            v-model="queryParams.manageTenantId"
            placeholder="请选择租户"
            size="small"
            style="width: 240px"
            filterable
            remote
            :remote-method="getTenantList"
            :loading="getTenantLoading"
            @change="tenantChange"
          >
            <el-option
              v-for="item in tenantList"
              :key="item.tenantId"
              :label="item.tenantName"
              :value="item.tenantId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="租户名称" prop="tenantName">
          <el-input
            v-model="queryParams.tenantName"
            placeholder="请输入租户名称"
            clearable
            size="small"
            style="width: 240px; margin-right: 20px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="租户登录名" prop="tenantLoginName">
          <el-input
            v-model="queryParams.tenantLoginName"
            placeholder="请输入租户登录名"
            clearable
            size="small"
            style="width: 240px; margin-right: 20px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="租户管理员" prop="displayName">
          <el-input
            v-model="queryParams.displayName"
            placeholder="请输入租户管理员"
            clearable
            size="small"
            style="width: 240px; margin-right: 20px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="dataList">
        <el-table-column
          label="租户名称"
          align="left"
          prop="tenantName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="租户登录名"
          align="left"
          prop="tenantLoginName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="租户管理员"
          align="left"
          prop="displayName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="租户域名"
          align="left"
          prop="tenantDomain"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="租户前台位置"
          align="left"
          prop="tenantHtmlPath"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="业务ID"
          align="left"
          prop="businessId"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="最大用户数"
          align="left"
          prop="maxStaff"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="有效截止时间"
          align="left"
          prop="effectiveDate"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="租户状态"
          align="left"
          prop="tenantStatus"
          :formatter="effectiveStatusFormat"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <el-tag :type="change(scope.row)">
              {{
                selectDictLabel(effectiveStatusOptions, scope.row.tenantStatus)
              }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script>
import { page } from "@/api/system/tenantJurisdiction";
import { list as tenantList } from "@/api/system/tenant";

export default {
  name: "TenantJurisdiction",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      getTenantLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 参数表格数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否有效数据字典
      effectiveStatusOptions: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenantName: undefined,
        tenantLoginName: undefined,
        displayName: undefined,
        effectiveDate: undefined,
        manageTenantId: this.$store.getters.customParam.tenantId,
      },
      tenantId: this.$store.getters.customParam.tenantId,
      defaultOrgId: this.$store.getters.orgId,
      columns: [
        { key: 0, label: `用户编号`, visible: false },
        { key: 1, label: `登录名称`, visible: true },
        { key: 2, label: `用户名称`, visible: true },
        { key: 3, label: `组织`, visible: true },
        { key: 4, label: `手机号码`, visible: true },
        { key: 5, label: `邮箱`, visible: true },
      ],
      loadTenantId: undefined,
      tenantList: [],
    };
  },
  created() {
    this.getTenantList();
    this.getList();
    this.getDicts("tenant_status").then((response) => {
      this.effectiveStatusOptions = response.data;
    });
  },
  methods: {
    getTenantList(tenantName) {
      this.getTenantLoading = true;
      let query = {}
      if (tenantName !== undefined && tenantName !== '') {
        query.tenantName = tenantName
        query.tenantId = undefined
      } else {
        query.tenantId = this.queryParams.manageTenantId
      }
      tenantList(query).then((response) => {
        this.tenantList = response.data;
      }).finally(() => {
        this.getTenantLoading = false;
      });
    },
    change(row) {
      if (row.tenantStatus === "valid") {
        return "";
      } else if (row.tenantStatus === "invalid") {
        return "danger";
      }
    },
    /** 查询参数列表 */
    getList() {
      this.loading = true;
      page({
        ...this.addDateRange({}, this.dateRange).params,
        ...this.queryParams,
      }).then((response) => {
        this.dataList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 参数系统内置字典翻译
    effectiveStatusFormat(row, column) {
      return this.selectDictLabel(
        this.effectiveStatusOptions,
        row.tenantStatus
      );
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    tenantChange() {
      this.handleQuery();
    },
  },
};
</script>
