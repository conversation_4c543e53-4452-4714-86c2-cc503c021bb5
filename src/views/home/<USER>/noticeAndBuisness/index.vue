<template>
  <el-card
    :class="
      this.bgColor === true
        ? 'notice-buisness-card'
        : 'unshow-notice-buisness-card'
    "
    v-loading="noticeLoading"
    shadow="never"
  >
    <div slot="header">
      <span class="card-title">
        <el-menu
          :default-active="activeIndex"
          :class="this.bgColor === true ?'el-menu-demo':'unshow-el-menu-demo'"
          mode="horizontal"
          @select="handleSelect"
          style="width: 100%;"
        >
          <el-menu-item index="system_notice" style="margin-left: 20px">
            <i class="icon iconfont icon-gonggao"></i>通知公告</el-menu-item
          >
          <el-menu-item index="business_news"
            ><i class="icon iconfont icon-yewudongtai"></i
            >新闻动态</el-menu-item
          >
        </el-menu>
        <el-button class="exec" type="text" @click="moreExt()"
          >更多<i class="el-icon-d-arrow-right"></i
        ></el-button>
      </span>
    </div>
    <div class="notice-buisness-body">
      <el-carousel
        v-if="notice.length > 0"
        height="200px"
        style="padding-bottom: 10px"
      >
        <el-carousel-item v-for="item in notice" :key="item.id">
          <a
            :href="`/portal/home/<USER>/${item.programaType}/${item.noticeId}`"
            target="view_window"
          >
            <el-image
              style="width: 100%; height: 100%"
              :src="item.coverUrl"
            ></el-image>
          </a>
        </el-carousel-item>
      </el-carousel>
      <div style="overflow-y: auto; height: calc(100% - 200px)">
        <a
          v-for="item in notice"
          :key="item.id"
          class="notice-buisness"
          :href="`/portal/home/<USER>/${item.programaType}/${item.noticeId}`"
          target="view_window"
        >
          <div class="notice-title">
            「<span style="color: red">{{ item.noticeTypeName }}</span
            >」{{ item.noticeTitle }}
          </div>
          <div class="notice-time">
            {{ parseTime(item.createDate, "{y}-{m}-{d}") }}
          </div>
        </a>
      </div>
      <el-empty
        style="padding: 20px 0"
        v-if="notice.length <= 0"
        :image-size="50"
        :description="text"
        ><el-link
          icon="el-icon-refresh"
          @click="getNotice(activeIndex)"
          :underline="false"
          type="primary"
          >刷新</el-link
        ></el-empty
      >
    </div>
  </el-card>
</template>

<script>
import { selectTop5Page } from "@/api/system/notice";
export default {
  props: ["item"],
  name: "noticeAndBuisness",
  data() {
    return {
      notice: [],
      noticeLoading: false,
      activeIndex: "system_notice",
      text: "暂无通知公告",
      bgColor: "",
    };
  },
  created() {
    this.getNotice(this.activeIndex);
    this.bgColor = this.item.showBgColor;
  },
  watch: {
    "item.showBgColor": function (val) {
      this.bgColor = val;
    },
  },
  methods: {
    moreExt() {
      window.open(`/portal/home/<USER>/${this.activeIndex}`);
    },
    getNotice(activeIndex) {
      this.noticeLoading = true;
      selectTop5Page({
        programaType: activeIndex,
      }).then((res) => {
        this.notice = res.data.records;
        this.noticeLoading = false;
        if (this.activeIndex === "system_notice" && this.notice.length >= 0) {
          this.text = "暂无通知公告";
        } else if (
          this.activeIndex === "business_news" &&
          this.notice.length >= 0
        ) {
          this.text = "暂无新闻动态";
        }
      });
    },
    handleSelect(e) {
      this.activeIndex = e;
      this.getNotice(e);
    },
  },
};
</script>

<style lang="scss" scoped>
.notice-buisness-card {
  height: 100%;
  background: #fff;
  ::v-deep .el-card__header {
    padding: 0px;
    border-bottom: 0px solid #f5f9fa;
  }
  ::v-deep .el-menu--horizontal > .el-menu-item {
    height: 58px;
  }
  ::v-deep .el-card__body {
    padding: 10px 20px 20px 20px;
  }
  .card-title {
    display: flex;
    .icon {
      font-size: 18px;
      color: #419eee;
      margin-right: 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 20px;
      border-bottom: 1px solid #f5f9fa;
      border-radius: 0px;
    }
  }
  .notice-buisness-body {
    height: 100%;
    .notice-buisness {
      display: flex;
      justify-content: space-between;
      flex-direction: row;
      font-size: 14px;
      padding: 4px 0;
      .notice-title {
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        white-space: nowrap;
        flex-basis: 70%;
      }
      .notice-time {
        flex-basis: 20%;
        text-align: right;
      }
    }
  }
}
.unshow-notice-buisness-card {
  height: 100%;
  border: 0px;
  background: #fff;
  ::v-deep .el-card__header {
    padding: 0px;
    border-bottom: 0px solid #f5f9fa;
  }
  ::v-deep .el-menu--horizontal > .el-menu-item {
    height: 58px;
  }
  ::v-deep .el-card__body {
    padding: 10px 20px 20px 20px;
  }
  .el-menu-demo {
     background: #fff;
  }
  .unshow-el-menu-demo {
    background: #fff;
  }
  .card-title {
    display: flex;
    .icon {
      font-size: 18px;
      color: #419eee;
      margin-right: 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 20px;
      border-bottom: 1px solid #f5f9fa;
      border-radius: 0px;
    }
  }
  .notice-buisness-body {
    height: 100%;
    .notice-buisness {
      display: flex;
      justify-content: space-between;
      flex-direction: row;
      font-size: 14px;
      padding: 4px 0;
      .notice-title {
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        white-space: nowrap;
        flex-basis: 70%;
      }
      .notice-time {
        flex-basis: 20%;
        text-align: right;
      }
    }
  }
}
</style>
