<template>
  <div class="app-container">
    <el-card shadow="never">
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="租户" v-if="$store.getters.customParam.userType === 'admin'">
          <el-select
              v-model="queryParams.tenantId"
              placeholder="请选择租户"
              size="small"
              style="width: 240px"
              filterable
              remote
              :remote-method="getTenantList"
              :loading="getTenantLoading"
              @change="tenantChange"
          >
            <el-option
                v-for="item in tenantList"
                :key="item.tenantId"
                :label="item.tenantName"
                :value="item.tenantId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="登录地址" prop="userIp">
            <el-input
                v-model="queryParams.userIp"
                placeholder="请输入IP地址"
                clearable
                size="small"
                style="width: 240px;"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="登录名称" prop="account">
            <el-input
                v-model="queryParams.account"
                placeholder="请输入登录名称"
                clearable
                size="small"
                style="width: 240px;"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
        <el-form-item label="用户名称" prop="accountName">
          <el-input
              v-model="queryParams.accountName"
              placeholder="请输入用户名称"
              clearable
              size="small"
              style="width: 240px;"
              @keyup.enter.native="handleQuery"
          />
        </el-form-item>
          <el-form-item label="状态" prop="logStatus">
            <el-select
                v-model="queryParams.logStatus"
                placeholder="登录状态"
                clearable
                size="small"
                style="width: 240px"
            >
              <el-option label="成功" value="success" />
              <el-option label="失败" value="fail" />
            </el-select>
          </el-form-item>
          <el-form-item label="登录时间">
            <el-date-picker
              v-model="dateRange"
              size="small"
              style="width: 240px"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetimerange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>


      <el-table ref="tables" v-loading="loading" :data="list" :default-sort="defaultSort" @sort-change="handleSortChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="登录名称" align="center" prop="account" :show-overflow-tooltip="true"  />
        <el-table-column label="用户名称" align="center" prop="accountName" :show-overflow-tooltip="true"  />
        <el-table-column label="登录IP" align="center" prop="userIp" width="130" :show-overflow-tooltip="true" />
        <el-table-column label="登录地点" align="center" prop="location" :show-overflow-tooltip="true" />
        <el-table-column label="浏览器" align="center" prop="browser" :show-overflow-tooltip="true" />
        <el-table-column label="操作系统" align="center" prop="os" />
        <el-table-column label="登录状态" align="center" prop="logStatus">
          <template slot-scope="scope">
            <el-tag :type="scope.row.logStatus === 'success' ? '' : 'danger'">
              {{ scope.row.logStatus === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作信息" align="center" prop="exception" />
        <el-table-column label="登录日期" align="center" prop="logTime" sortable="custom"
                         :sort-orders="['descending', 'ascending']" width="180"
        >
        <template slot-scope="scope">
            <span>{{ parseTime(scope.row.logTime) }}</span>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script>
import { list } from "@/api/system/logininfor";
import { list as tenantList } from '@/api/system/tenant'

export default {
  name: "Logininfor",
  data() {
    return {
      // 遮罩层
      loading: true,
      getTenantLoading: false,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      tenantList: [],
      // 日期范围
      dateRange: [],
      // 默认排序
      defaultSort: { prop: 'log_time', order: 'descending' },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userIp: undefined,
        account: undefined,
        accountName: undefined,
        logStatus: undefined,
        orderByColumn: 'log_time',
        isAsc: 'descending',
        tenantId: this.$store.getters.customParam.tenantId,
      }
    };
  },
  created() {
    this.getTenantList();
    this.getList();
  },
  methods: {
    getTenantList(tenantName) {
      this.getTenantLoading = true;
      let query = {}
      if (tenantName !== undefined && tenantName !== '') {
        query.tenantName = tenantName
        query.tenantId = undefined
      } else {
        query.tenantId = this.queryParams.tenantId
      }
      tenantList(query).then((response) => {
        this.tenantList = response.data;
      }).finally(() => {
        this.getTenantLoading = false;
      });
    },
    /** 查询登录日志列表 */
    getList() {
      this.loading = true;
      list({...this.addDateRange({}, this.dateRange).params, ...this.queryParams}).then(response => {
          this.list = response.data.records;
          this.total = response.data.total;
          this.loading = false;
        }
      );
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)
      this.handleQuery();
    },
    /** 排序触发事件 */
    handleSortChange(column, prop, order) {
      this.queryParams.orderByColumn = column.prop;
      this.queryParams.isAsc = column.order;
      this.getList();
    },
    tenantChange(tenantId) {
      if (tenantId !== '') {
        this.queryParams.tenantName = this.tenantList.find(item => item.tenantId === tenantId).tenantName
      }
      this.handleQuery()
    },
  }
};
</script>

