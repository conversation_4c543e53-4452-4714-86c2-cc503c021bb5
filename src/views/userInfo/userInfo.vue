<template>
  <el-form ref="form" :model="user" :rules="rules" label-width="80px">
    <el-form-item label="用户名称" prop="staffName">
      <el-input v-model="user.staffName" />
    </el-form-item>
    <el-form-item label="手机号码" prop="cellphone">
      <el-input v-model="user.cellphone" maxlength="11" />
    </el-form-item>
    <el-form-item label="邮箱" prop="email">
      <el-input v-model="user.email" maxlength="50" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" size="mini" @click="submit">保存</el-button>
      <el-button type="danger" size="mini" @click="close">关闭</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { updateUserProfile } from "@/api/system/user";
import { getInfo} from "@/api/login";
import { getToken } from "@/utils/auth";

export default {
  props: {
    user: {
      type: Object
    }
  },
  data() {
    return {
      // 表单校验
      rules: {
        staffName: [
          { required: true, message: "用户名称不能为空", trigger: "blur" }
        ],
        email: [
          { required: true, message: "邮箱地址不能为空", trigger: "blur" },
          {
            type: "email",
            message: "'请输入正确的邮箱地址",
            trigger: ["blur", "change"]
          }
        ],
        cellphone: [
          { required: true, message: "手机号码不能为空", trigger: "blur" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur"
          }
        ]
      }
    };
  },
  methods: {
    submit() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          const _this = this
          updateUserProfile(_this.user).then(response => {
            if (!response.success) {
              _this.$message.error(response.message);
            } else {
              _this.$message.warning("个人信息修改成功，请重新登录后查看");
              getInfo({ token: getToken() }).then(res => {
                _this.$store.commit("SET_USER_INFO",res)
              })
            }
          });
        }
      });
    },
    close() {
      this.$store.dispatch("tagsView/delView", this.$route);
      window.history.back(1)
    }
  }
};
</script>
