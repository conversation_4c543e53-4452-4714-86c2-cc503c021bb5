<template>
  <el-dialog
    :title="title"
    :visible.sync="openAuxiliaryPoliceDialog"
    width="80%"
    @close="handleClose">
      <div class="table-wrapper box-bg" >

        <div class="table-content">
          <el-table  :data="resultList" style="width: 100%" :row-class-name="tableRowStripeClassName">
            <el-table-column label="市分" align="center" prop="cityCode" />
            <el-table-column label="责任人" align="center" prop="zrr" />
            <el-table-column label="本周新增" align="left" prop="newAdd" />
            <el-table-column label="本年累计发展(户)" align="center" prop="yearSum" />
            <el-table-column label="年度目标(户)" align="center" prop="yearTarget" />
            <el-table-column label="完成率" align="center" prop="finishPer" >
              <template slot-scope="scope">
                <span >{{ (scope.row.finishPer * 100).toFixed(1) }}%</span>
              </template>
            </el-table-column>
            <el-table-column label="排名" align="center" prop="sort" />
            <el-table-column label="累计发展(户)" align="center" prop="totalDevelop" />
            <el-table-column label="辅警总人数(户)" align="center" prop="auxiliaryPoliceNum" />
            <el-table-column label="渗透率" align="center"  >
              <template slot-scope="scope">
                <span >{{ (scope.row.penetrate * 100).toFixed(1) }}%</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="flex-end" style="margin-top: 0.14rem;">
            <el-pagination @current-change="handleCurrentChange"
                           :current-page.sync="queryParams.pageNum" :page-size="queryParams.pageSize" layout="total, prev, pager, next"
                           :total="total">
            </el-pagination>
          </div>
        </div>
      </div>
  </el-dialog>
</template>
<script>
import {resultError} from "@/utils/messageText";
import  { getPageList }  from '@/api/sw/targetMarketing/police'
import UniTable from '@/components/UniTable/UniTable.vue'
export default {
  // import引入的组件需要注入到对象中才能使用
  components: { UniTable },
  name: 'detail',
  props: {
    openAuxiliaryPoliceDialog: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    paramInfo: {
      cityCode: {
        type: String,
        default: ''
      },
      strategyId: {
        type: String,
        default: ''
      },
      contactor: {
        type: String,
        default: '靶向营销'
      },
      beginDate: {
        type: String,
        default: ''
      },
      endDate: {
        type: String,
        default: ''
      }
    },
  },
  data() {
    return {
      resultList: [],//商机或项目列表
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0
    }
  },
  watch: {
    paramInfo: {
      deep: true,
      handler(newValue,oldVal){
        this.queryParams.pageNum = 1;//当重新打开时设置第一页,不然数据多的如果上次翻到第大页,再点击条数少的打开会无数据
        console.info("info："+JSON.stringify(this.paramInfo))
        this.getDetailList()
      }
    },
  },
  created() {

  },
  mounted() {

  },
  // 监听属性类似于data概念
  computed: {},
  // 方法集合
  methods: {
    tableRowStripeClassName({ row, rowIndex }) {
      let rowStyle = "row-bg-high";
      if (rowIndex % 2 === 0) {
        rowStyle = "row-bg-normal";
      }
      return rowStyle;
    },
    getDetailList() {
      let superiorPolicyCode = this.paramInfo.strategyId;
      let cityCode = this.paramInfo.cityCode
      // let dataType = this.paramInfo.dataType;
      this.queryParams = {...this.paramInfo,...this.queryParams,superiorPolicyCode,cityCode}
      let pageNum = this.queryParams.pageNum;
      this.resultList = [];
      getPageList(this.queryParams).then(response => {
        if (response.code === '1') {
          this.resultList = response.data.records;
          this.total = response.data.total;
          this.queryParams.pageNum = pageNum;
        }else{
          this.$message.error(resultError);
        }
      });
    },
    // 当前页码
    handleCurrentChange(page) {
      this.queryParams.pageNum = page;
      this.getDetailList();
    },
    sliceText(text){
      return text.length > 20 ? text.slice(0, 20) + '...' : text;
    },
    // 搜索
    handleQuery() {
      console.log(this.search, 'search')
    },
    handleClose() {
      this.$emit('update-open-auxiliary-police-dialog', false)
    },
  }
}
</script>
<style lang="scss" scoped>
.el-dialog__body {
  padding: 0;
}
::v-deep .el-dialog__body {
  padding: 0;
}
.header {
  font-size: 0.16667rem;

  padding: 0.16667rem;
  background-color: #fff;
  width: 100%;
  margin-top: 0.16667rem;
  border-radius: 0.04167rem;

  i {
    color: #3377ff;
  }
}


.m-con {
  margin: 0.08333rem;
  padding: 0.08333rem;
  background-color: #fff;

  .dict_search {
    width: 30%;
    padding-bottom: 0.08333rem;
  }
}

.table-content{
  padding: 0.1rem 0.2rem;
}

.action-button {
  padding: 0;
  font-size: 0.125rem;
  min-width: 0.125rem;
  background-color: transparent;
  border: none;
  cursor: pointer;
  color: #3377FF;
  font-family: PingFangSC-Regular;
  line-height: 0.18rem;
  font-weight: 400;
}
</style>
