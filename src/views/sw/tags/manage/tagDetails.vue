<template>
  <Container>
    <template slot="header">
<!--        <el-page-header @back="goBack" content="标签详情"></el-page-header>-->
        <div class="header innerLayout" style="padding-top: 10px;padding-bottom: 0px;padding-left: 10px;cursor: pointer" >
          <img src="../../../../assets/images/gotoBack.png" height="32" width="32" @click="goBack" />
          <span style="margin-left: 10px;font-size: 0.135rem;">返回</span>
        </div>
    </template>
    <div class="info">
      <con-main title="基本信息">
        <div class="m-con">
          <el-descriptions :column="4" size="mini">
            <el-descriptions-item label="标签编码">{{
                info.tagCode
              }}
            </el-descriptions-item>
            <el-descriptions-item label="标签名">{{
                info.tagName
              }}
            </el-descriptions-item>
            <el-descriptions-item label="标签类型">{{
                info.tagType === '1' ? '数值' : '字典'
              }}
            </el-descriptions-item>
            <el-descriptions-item label="标签值类型">{{
                info.tagTypeValue
              }}
            </el-descriptions-item>

            <el-descriptions-item label="标签联系人">{{
                info.tagUser
              }}
            </el-descriptions-item>
            <el-descriptions-item label="标签联系人电话">
              {{ info.tagUserPhone }}
            </el-descriptions-item>
            <!--            <el-descriptions-item label="账期类型">{{-->
            <!--                info.acctDateType-->
            <!--              }}-->
            <!--            </el-descriptions-item>-->
            <!--            <el-descriptions-item label="最新账期">{{-->
            <!--                info.acctDate-->
            <!--              }}-->
            <!--            </el-descriptions-item>-->
            <!--            <el-descriptions-item label="账期具备时间">{{-->
            <!--                info.accoPeriodWithTime-->
            <!--              }}-->
            <!--            </el-descriptions-item>-->
            <!--            <el-descriptions-item label="日均调用量">{{-->
            <!--                info.everyDayUseCount-->
            <!--              }}-->
            <!--            </el-descriptions-item>-->
            <!--            <el-descriptions-item label="近90天调用量">{{-->
            <!--                info.ninetyUseCount-->
            <!--              }}-->
            <!--            </el-descriptions-item>-->

            <!--            <el-descriptions-item label="入库时常（分钟）">{{-->
            <!--                info.diffMin-->
            <!--              }}-->
            <!--            </el-descriptions-item>-->
            <!--            <el-descriptions-item label="标签状态">{{-->
            <!--                info.status-->
            <!--              }}-->
            <!--            </el-descriptions-item>-->
            <el-descriptions-item v-if="info.tagType==='2'" label="是否多选值">{{
                info.moreDictValue === '1'
                  ? '是'
                  : info.moreDictValue === '0'
                    ? '否'
                    : ''
              }}
            </el-descriptions-item>

            <el-descriptions-item label="业务口径">{{
                info.businessCaliber
              }}
            </el-descriptions-item>
            <el-descriptions-item label="加工口径">{{
                info.dataCaliber
              }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </con-main>
      <con-main title="字典" v-if="info.tagType === '2'">
        <div class="m-con">
          <div class="dict_search">
            <el-input
              v-model="search"
              size="mini"
              suffix-icon="el-icon-search"
              placeholder="请输入编码或者名称"
              @keyup.enter.native="handleQuery"
            ></el-input>
          </div>
          <div>
            <el-table :data="tableData" style="width: 100%">
              <el-table-column prop="code" label="编码" align="center"/>
              <el-table-column prop="name" label="名称" align="center"/>
            </el-table>
          </div>
        </div>
      </con-main>
      <con-main title="数据来源" v-if="info.tagSource">
        <div class="m-con" style="margin-top:10px;">
          <el-row :gutter="[10,10]" v-for="item in dbSourceList" style="margin-top: 10px">
            <el-col :span="6">表名称：{{ item.dbTableName }}</el-col>
            <el-col :span="6">字段名：{{ item.dbTableColumn }}</el-col>
            <el-col :span="6">字段名描述：{{ item.dbTableDesc }}</el-col>
            <el-col :span="6">字段值描述：{{ item.dbTableColumnDesc }}</el-col>
          </el-row>
        </div>
      </con-main>
    </div>
  </Container>
</template>
<script>
import Container from '../components/Container.vue'
import ConMain from '../components/ConMain.vue'
import { findFormOne, tagFindOne } from '@/api/sw/tags/tagManage'
import { findDictTypeList } from '@/api/sw/tags/dictManage'

export default {
  // import引入的组件需要注入到对象中才能使用
  components: { Container, ConMain },
  name: 'tagsDetails',
  data() {
    return {
      info: {},
      dbSourceList: [],
      search: '',
      tableData: [
        {
          code: '1',
          name: '省'
        },
        {
          code: '1',
          name: '市'
        },
        {
          code: '1',
          name: '县/区'
        }
      ]
    }
  },
  created() {
    this.getData()
  },
  // 监听属性类似于data概念
  computed: {},
  // 方法集合
  methods: {
    labType() {
      if (this.info.tagType === '2') {
        findDictTypeList().then((res) => {
          res.data.forEach((i) => {
            if (i.id === this.info.tagTypeValue) {
              this.$set(this.info, 'labelValueType', i.dictName)
              // this.info.labelValueType = i.dictName;
              // console.log(this.info.labelValueType);
            }
          })
        })
      } else {
        this.info.tagTypeValue =
          this.info.tagTypeValue === '0'
            ? '文本型'
            : '数值型'
      }
    },
    // 获取详情数据
    getData() {
      const id = this.$route.params.id
      findFormOne(id).then((res) => {
        console.log(res, ' 详情')
        this.info = res.data['tag']
        this.dbSourceList = res.data['tagDbs']
        this.labType()
      })
    },
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },
    // 搜索
    handleQuery() {
      console.log(this.search, 'search')
    }
  }
}
</script>
<style lang="scss" scoped>
.header {
  font-size: 0.16667rem;

  padding: 0.16667rem;
  background-color: #fff;
  width: 100%;
  margin-top: 0.16667rem;
  border-radius: 0.04167rem;

  i {
    color: #3377ff;
  }
}

.info {
  margin: 0.16667rem 0;
}

.m-con {
  margin: 0.08333rem;
  padding: 0.08333rem;
  background-color: #fff;

  .dict_search {
    width: 30%;
    padding-bottom: 0.08333rem;
  }
}
.innerLayout{
  align-items: center;
  display: flex;
  justify-content: left;
}
</style>
