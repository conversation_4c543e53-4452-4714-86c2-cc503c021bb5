import Vue from "vue";
import moment from "moment";
import Element from "element-ui";
import "./assets/styles/element-variables.scss";
import "@iconfu/svg-inject"

import "@/assets/styles/index.scss"; // global css
import "@/assets/styles/potal.scss"; // potal css
import App from "./App";
import store from "./store";
import router from "./router";
import permission from "./directive/permission";
import splitPane from 'vue-splitpane';
import VueSimpleVerify from 'vue-simple-verify';
import './utils/diag'

import '../node_modules/vue-simple-verify/dist/vue-simple-verify.css';
Vue.component('vue-simple-verify', VueSimpleVerify);
Vue.component('split-pane', splitPane);

import { download as exportExcel } from "@/utils/request";
import { hasPermission, ownShow } from "@/utils/haspermission";
import "./assets/icons"; // icon
import "./permission"; // permission control
import { getDicts } from "@/api/system/dict/data";
import { getConfigKey } from "@/api/system/config";
import {
  parseTime,
  resetForm,
  addDateRange,
  selectDictLabel,
  selectDictRemark,
  selectDictLabels,
  copyToClipboard,
  download,
  handleTree,
  handlePortalTree
} from "@/utils/utils";
import Pagination from "@/components/Pagination";
// 自定义表格工具扩展
import RightToolbar from "@/components/RightToolbar";
//echarts install

import echarts from 'echarts'
import { sensitiveWords } from '@/api/function'
Vue.prototype.$echarts = echarts
Vue.prototype.$moment = moment;

// import Antd from 'ant-design-vue';
// import 'ant-design-vue/dist/antd.css';
// Vue.use(Antd);


// 全局方法挂载
Vue.prototype.getDicts = getDicts;
Vue.prototype.getConfigKey = getConfigKey;
Vue.prototype.parseTime = parseTime;
Vue.prototype.resetForm = resetForm;
Vue.prototype.addDateRange = addDateRange;
Vue.prototype.selectDictLabel = selectDictLabel;
Vue.prototype.selectDictRemark = selectDictRemark;
Vue.prototype.selectDictLabels = selectDictLabels;
Vue.prototype.copyToClipboard = copyToClipboard;
Vue.prototype.download = download;
Vue.prototype.handleTree = handleTree;
Vue.prototype.handlePortalTree = handlePortalTree;
Vue.prototype.sensitiveWords = sensitiveWords;
Vue.prototype.exportExcel = exportExcel;
Vue.prototype.hasPermission = hasPermission;
Vue.prototype.ownShow = ownShow;

Vue.prototype.msgSuccess = function(msg) {
  this.$message({ showClose: true, message: msg, type: "success" });
};

Vue.prototype.msgError = function(msg) {
  this.$message({ showClose: true, message: msg, type: "error" });
};

Vue.prototype.msgInfo = function(msg) {
  this.$message.info(msg);
};

// 全局组件挂载
Vue.component("Pagination", Pagination);
Vue.component("RightToolbar", RightToolbar);

Vue.use(permission);

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */
//全局配置element 弹窗组件点击空白时不关闭
Element.Dialog.props.closeOnClickModal = false;
Vue.use(Element, {
  size: localStorage.getItem("size") || "medium" // set element-ui default size
});

//全局常量挂载
Vue.prototype.appCode = "portal"

Vue.config.productionTip = false;

new Vue({
  el: "#app",
  router,
  store,
  render: h => h(App)
});
