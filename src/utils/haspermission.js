import store from "@/store";
// 权限判断
export function hasPermission(permission) {
    if (!store.state.user.userInfo || !permission) {
        return false; // 如果没有用户信息或权限，则返回 false
    }
    
    // 使用安全访问防止错误
    const authorities = store.state.user.userInfo.authorities || [];
    const hasPerm = authorities.some(item => (
        item === permission || item.authority === permission
    ));
    
    if (hasPerm) {
        console.log('当前用户拥有权限：' + permission);
    }
    
    return hasPerm; // 返回是否有权限
}
// 自己创建
export function ownShow(createBy) {
    if (!store.state.user.userInfo || !createBy) {
        return false; // 如果没有用户信息或权限，则返回 false
    }
    const hasPerm = store.state.user.userInfo.staffId === createBy;
    return hasPerm; // 返回是否有权限
}