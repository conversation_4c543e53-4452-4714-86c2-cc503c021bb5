## 开发

```bash
# 克隆项目
git clone https://gitee.com/y_project/RuoYi-Vue

# 进入项目目录
cd ruoyi-ui

# 安装依赖
npm install

#用我的中式英语翻译一下就是：
#不能解析依赖树 ，需要先修复上面依赖关系冲突或者重新执行一下npm install命令，
#后面跟–force或者–legacy-peer-deps去接受不正确的(并可能被破坏的)依赖解析。
#根据它的建议，我们去执行
npm install --force
#或者 
npm install --legacy-peer-deps

# 建议不要直接使用 cnpm 安装依赖，会有各种诡异的 bug。可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npm.taobao.org

# 启动服务
npm run dev
```

浏览器访问 http://localhost:80

## 发布

```bash
# 构建测试环境
npm run build:stage

# 构建生产环境
npm run build:prod
```