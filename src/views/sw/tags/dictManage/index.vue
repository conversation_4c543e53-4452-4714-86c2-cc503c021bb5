<template>
  <Container>
    <template slot="header">
      <con-header>
        <el-form
          :model="queryParams"
          @submit.native.prevent
          ref="queryForm"
          label-width="1rem"
          label-position="left"
        >
          <el-row>
            <el-col :span="6">
              <el-form-item label="字典名称" prop="classificationName">
                <el-input
                  v-model="queryParams.dictName"
                  placeholder="请输入"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <div class="btn-group" style="right: 0;">
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
          </div>
        </el-form>
      </con-header>
    </template>
    <con-main title="字典列表">
      <template slot="btn">
        <div class="btn">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-plus"
            @click="handleEdit"
          >新增
          </el-button
          >
          <el-button
            type="primary"
            size="small"
            icon="el-icon-delete"
            :disabled="!selectData.length"
            @click="handleDel"
          >删除
          </el-button
          >
        </div>
      </template>
      <uni-table
        ref="uniTable"
        action="/sanquan/dict/queryDictTypeList/"
        :columns="tableColumns"
        :params="queryParams"
      >
        <template v-slot:opt="scope">
          <div class="action-buttons flex-center">
            <el-button
              size="mini"
              class="action-button"
              type="button"
              @click="handleEdit(scope.row)"
            >修改
            </el-button>
            <span class="separator"></span>
            <el-button
              size="mini"
              class="action-button"
              type="button"
              @click="handleDel(scope.row.id)"
            >删除
            </el-button>
            <span class="separator"></span>
            <el-button
              size="mini"
              class="action-button"
              type="button"
              @click="handleView(scope.row)"
            >详情
            </el-button>
          </div>
        </template>
      </uni-table>
    </con-main>
    <edit-dict-dialog
      :title="titleDialog"
      :visibleDialog.sync="openTabDialog"
      :formData="columnData"
      @getData="getList"
    />
  </Container>
</template>
<script>
import Container from '../components/Container.vue'
import ConHeader from '../components/ConHeader.vue'
import ConMain from '../components/ConMain.vue'
import EditDictDialog from './EditDictDialog.vue'
import { queryDictTypeList, dictTypeDelete } from '@/api/sw/tags/dictManage'
import UniTable from '@/components/UniTable/UniTable.vue'

export default {
  // import引入的组件需要注入到对象中才能使用
  components: { UniTable, Container, ConHeader, ConMain, EditDictDialog },
  data() {
    return {
      tableColumns: [
        { props: 'dictCode', label: '字典编码' },
        { props: 'dictName', label: '字典名称' },
        { props: 'opt', label: '操作', slot: true ,align: 'center' }
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dictName: undefined
      },
      // 表单校验
      rules: {
        tabName: [
          { required: true, message: '请输入标签名称', trigger: 'blur' }
        ]
      },
      // 表格数据
      tableData: [],
      // 传递列表数据
      columnData: {},
      // 弹出层标题
      titleDialog: '',
      // 表格loading
      tableLoading: false,
      // 是否显示弹出层
      openTabDialog: false,
      // 表格总数
      total: 0,
      //当前页
      currentPage: 1,
      // 批量删除的数据
      selectData: []
    }
  },
  // 初始化
  created() {
    // this.getList()
  },
  // 监听属性类似于data概念
  computed: {},
  // 方法集合
  methods: {
    // 多选表格
    handleSelectionChange(val) {
      this.selectData = val.map((item) => item.id)
    },
    /** 查询列表 */
    getList() {
      // queryDictTypeList(this.queryParams).then((res) => {
      //   this.tableData = res.data.records;
      //   this.total = res.data.total;
      // });
      this.$refs.uniTable.loadData(this.queryParams)
    },
    // 修改
    handleEdit(row) {
      // this.$router.push({
      //   name: 'strategyResultDetail',
      //   params: row
      // })
      if (row.id) {
        this.titleDialog = '修改字典'
        this.columnData = row
        // console.log(this.columnData, "this.columnData");
      } else {
        this.columnData = {}
        this.titleDialog = '新增字典'
      }
      this.openTabDialog = true
    },
    // 删除
    handleDel(id) {
      this.$confirm('此操作将删除该数据项，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let list = []
        if (this.selectData.length) {
          list = [...this.selectData]
        } else {
          list = [id]
        }
        dictTypeDelete(list).then((response) => {
          this.$message.success('删除成功！')
          this.getList()
        })
      })
    },
    // 详情
    handleView(row) {
      this.titleDialog = '字典详情'
      this.columnData = {
        types: 1,
        ...row
      }
      this.openTabDialog = true
    },
    // 当前页码
    handleCurrentChange(page) {
      this.currentPage = page
      this.getList()
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.uniTable.resetPage()
      this.resetForm('queryForm')
      this.queryParams={}
      this.$nextTick(() => {
        this.handleQuery()
      })
    }
  }
}
</script>
<style lang="scss" scoped></style>
