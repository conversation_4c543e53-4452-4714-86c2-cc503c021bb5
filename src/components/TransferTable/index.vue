<template>
  <el-dialog
    width="70%"
    :title="modelName"
    :visible.sync="visible"
    append-to-body
    destroy-on-close
  >
    <el-row :gutter="10">
      <el-col :span="12"
        ><el-card class="select-dep-card" shadow="never" destroy-on-close>
          <div slot="header">
            <span>{{ leftTitle }}</span>
            <div style="float: right; padding: 3px 0">
              <el-input
                placeholder="请输入角色名称"
                v-model="queryParams.roleName"
                size="small"
                style="width: 240px"
                @keyup.enter.native="reload"
                clearable
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="reload"
                  :loading="unselectloading"
                ></el-button>
              </el-input>
            </div>
          </div>
          <el-table
            v-if="list.length > 0"
            :data="list"
            style="width: 100%"
            height="500"
            @selection-change="handleSelectionChange"
            v-loading="unselectloading"
            row-key="roleId"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column :prop="listProp.key" :label="listProp.name">
            </el-table-column>
            <el-table-column fixed="right" width="100" label="操作">
              <template slot-scope="scope" v-if="!scope.row.isOp">
                <el-button
                  @click="add(scope.row)"
                  type="text"
                  size="small"
                  icon="el-icon-d-arrow-right"
                ></el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-empty
            v-if="list.length <= 0"
            description="暂无数据"
          ></el-empty> </el-card
      ></el-col>
      <el-col :span="12"
        ><el-card class="select-dep-card" shadow="never" destroy-on-close>
          <div slot="header">
            <span>{{ rightTitle }}</span>
            <el-button
              style="float: right; padding: 3px 0"
              type="text"
              icon="el-icon-refresh"
              >刷新</el-button
            >
          </div>
          <el-table
            v-if="selected.length > 0"
            :data="selected"
            style="width: 100%"
            height="500"
            v-loading="selectloading"
          >
            <el-table-column :label="selectedProp.name">
              <template slot-scope="scope">
                <div>
                  <i class="el-icon-role"></i>
                  {{ scope.row.roleName }}
                </div>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="100">
              <template slot-scope="scope">
                <a @click="remove(scope.row)" type="text" size="small" style="margin:0"
                  >删除</a
                >
              </template>
            </el-table-column>
          </el-table>
          <el-empty
            v-if="selected.length <= 0"
            description="暂未选择数据"
          ></el-empty> </el-card
      ></el-col>
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    modelName: {
      type: String,
      default: "穿梭框",
    },
    leftTitle: {
      type: String,
      default: "全部",
    },
    rightTitle: {
      type: String,
      default: "已选择",
    },
    selectedProp: {
      type: Object,
      default: {
        key: "name",
        name: "列1",
      },
    },
    selectedId: {
      type: String,
      default: "id",
    },
    listId: {
      type: String,
      default: "id",
    },
    listProp: {
      type: Object,
      default: {
        key: "name",
        name: "列1",
      },
    },
  },
  data() {
    return {
      unselectloading: false,
      visible: false,
      queryParams: {
        roleName: undefined,
      },
      selected: [],
      list: [],
      selectloading: false,
    };
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    getList() {
      this.unselectloading = true;
      this.selectloading = true;
      this.$emit("loadData", this.queryParams);
    },
    setData(data) {
      this.list = Array.isArray(data) ? data : [];
      this.unselectloading = false;
    },
    setRightData(response) {
      this.selected = Array.isArray(response.data) ? response.data : [];
      this.selectloading = false;
    },
    reload() {
      this.getList();
    },
    open() {
      this.getList();
      this.visible = true;
    },
    close() {
      this.visible = false;
    },
    add(row) {
      this.unselectloading = true;
      this.selectloading = true;
      this.$emit("add", row);
    },
    remove(row) {
      this.unselectloading = true;
      this.selectloading = true;
      this.$emit("remove", row);
    },
    roleNameRenderHeader(h,{ column }) {
      return h(
        'div',[
          h('span', column.name),
          h('i', {
            class:'el-icon-location',
            style:'color:#409eff;margin-left:5px;'
          })
        ],
      );
    },
  },
};
</script>

<style scoped lang="scss">
.select-dep-card {
  height: 100%;
  overflow-y: auto;
}
</style>
<style>
  .el-icon-role {
    background: url('~@/assets/images/role_mini.png') no-repeat;
  }
  .el-icon-role:before{
    content: "\8d3a";
    font-size: 14px;
    visibility: hidden;
  }
</style>
<style scoped>
  /*有子节点 且未展开*/
  .el-table /deep/ .el-table__expand-icon .el-icon-arrow-right:before {
    background: url('~@/assets/images/expand.png') no-repeat;
    content: '';
    display: block;
    width: 16px;
    height: 16px;
    font-size: 16px;
    background-size: 16px;
    margin-bottom: -3px;
  }

  /*有子节点 且已展开*/
  .el-table /deep/ .el-table__expand-icon--expanded{
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  .el-table /deep/ .el-table__expand-icon--expanded .el-icon-arrow-right:before {
    background: url('~@/assets/images/putitaway.png') no-repeat;
    content: '';
    display: block;
    width: 16px;
    height: 16px;
    font-size: 16px;
    background-size: 16px;
    margin-bottom: -3px;
  }

  /*没有子节点*/
  .el-table /deep/ .el-table__placeholder::before {
    background: url('~@/assets/images/role.png') no-repeat;
    content: '';
    display: block;
    width: 16px;
    height: 16px;
    font-size: 16px;
    background-size: 16px;
    margin-bottom: -2px;
  }
</style>
