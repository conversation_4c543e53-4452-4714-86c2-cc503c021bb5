import request from '@/utils/request'
import loginrequest from '@/utils/checkloginRequest'

// 整体转化率分析-按策略(新)
export function strategyConverRatePage(data) {
  return request({
    url: '/sanquan/strategy/transAnalysis/strategyConverRatePage',
    method: 'post',
    data
  })
}

// 整体转化率分析
export function allConverRate(data) {
  return request({
    url: '/sanquan/strategy/transAnalysis/allConverRate',
    method: 'post',
    data
  })
}

// 整体转化率分析-按地市
export function allConverRateByCity(data) {
  return request({
    url: '/sanquan/strategy/transAnalysis/allConverRateByCity',
    method: 'post',
    data
  })
}

// 产品推荐成功排行
export function productRecommendation(data) {
  return request({
    url: '/sanquan/strategy/transAnalysis/productRecommendation',
    method: 'post',
    data
  })
}

// 地图地市商机分布
export function cityOpportunityArea(data) {
  return request({
    url: '/sanquan/strategy/transAnalysis/cityOpportunityArea',
    method: 'post',
    data
  })
}

// 商机转化客户排行榜
export function oportunityConvCustTop(data) {
  return request({
    url: '/sanquan/strategy/transAnalysis/oportunityConvCustTop',
    method: 'post',
    data
  })
}

// 下钻一级（策略数）
export function strategyDownOne(data) {
  return request({
    url: '/sanquan/strategy/transAnalysis/strategyDownOne',
    method: 'post',
    data
  })
}

// 下钻一级（潜在机会数）
export function potentialOpportunityDownOne(data) {
  return request({
    url: '/sanquan/strategy/transAnalysis/potentialOpportunityDownOne',
    method: 'post',
    data
  })
}

// 下钻一级（工单数）
export function orderDownOne(data) {
  return request({
    url: '/sanquan/strategy/transAnalysis/orderDownOne',
    method: 'post',
    data
  })
}

// 下钻一级（商机数） 商机明细
export function businessOpportunityDownOne(data) {
  return request({
    url: '/sanquan/strategy/transAnalysis/businessOpportunityDownOne',
    method: 'post',
    data
  })
}

// 下钻一级（商机数） 商机明细
export function businessOpportunityDownOnePage(params) {
  return request({
    url: '/sanquan/strategy/transAnalysis/businessOpportunityDownOnePage',
    method: 'get',
    params
  })
}

// 下钻二级（工单数-执行中）
export function executingDownTwo(data) {
  return request({
    url: '/sanquan/strategy/transAnalysis/executingDownTwo',
    method: 'post',
    data
  })
}

// 获取当前用户及角色编码, 行业信息
export function getCurrentUser() {
  return request({
    url: '/sanquan/getCurrentUser',
    method: 'get'
  })
}

// 获取当前用户及角色编码, 行业信息
export function getTokenBySession(params) {
  return request({
    url: '/auth/sso/oauth/session/token',
    method: 'get',
    params
  })
}

// checklogin
export function checkLoginSes() {
  return loginrequest({
    url: '/auth-web/checkLogin',
    method: 'post'
  })
}



