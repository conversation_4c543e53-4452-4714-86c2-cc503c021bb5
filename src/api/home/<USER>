
import request from '@/utils/request'
//首页请求
//法律法规列表
export function getLowList(p) {
  return request({
    url: "/gh/tu-policy/page",
    method: "get",
    params:p
  });
}
//工会新闻列表
export function getGhNewList(p) {
  return request({
    url: "/gh/tu-mn-activity/home/<USER>",
    method: "get",
    params:p
  });
}
//书香中国
export function getBookList(data) {
  return request({
    url: "/dingding/scholarly/selectSFByDataAndTitle",
    method: "post",
    data: data
  });
}

//首页请求
//健步走echart图部门排行和个人排行
export function getEchartData() {
  return request({
    url: "/dingding/statistics/fronPageChart",
    method: "post"
  });
}
//公司近6月趋势和环比
export function getEchartDataHb() {
  return request({
    url: "/dingding/statistics/fronPageChartForCompany",
    method: "post"
  });
}

//待办活动
export function getDbHd(p) {
  return request({
    url: "/gh/tu-mn-activity/home/<USER>",
    method: "get",
    params:p
  });
}
//待办书香中国
export function getDbBook(p) {
  return request({
    url: "/dingding/scholarly/selectGroupBy",
    method: "get",
    params: p
  });
}

//待办书香中国评论审核
export function getDbBookComment() {
  return request({
    url: "/dingding/scholarly/comment/db",
    method: "get"
  });
}
  //待办暖心工程
  export function getDbCare(p) {
    return request({
      url: "/gh/tu-care/dbcount",
      method: "get",
      params:p
    });
}

//待办邮箱
export function getDbEmail(p) {
  return request({
    url: "/gh/tu-aspiration/dbcount",
    method: "get",
    params:p
  });
}

//获取所有的待办列表
export function getAllCount() {
  return request({
    url: "/gh/my/getAllCount",
    method: "post"
  });
}

//获取所有的待办列表
export function getDbPage(data) {
  return request({
    url: "/gh/my/getPage",
    method: "post",
    data: data
  });
}

//读书排行
export function getReadList() {
  return request({
    url: "/dingding/scholarly/selectTSFGroupOrg",
    method: "get"
  });
}




