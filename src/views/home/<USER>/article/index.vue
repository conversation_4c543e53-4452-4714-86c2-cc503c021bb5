<template>
  <el-card
    :class="this.bgColor === true? 'notice-card':'unshow-notice-card'"
    v-loading="dataLoading"
    shadow="never"
  >
    <div slot="header" v-if="show">
      <span class="card-title">
        <div class="title">
          <span v-if="!editLayout"
            ><i class="icon iconfont icon-gonggao"></i>{{ item.title }}</span
          >
          <el-select
            v-else
            v-model="item.programaType"
            placeholder="请选择栏目类型"
            clearable
            filterable
            @change="setTitle"
          >
            <el-option
              v-for="item in programaList"
              :key="item.programaTypeId"
              :label="item.programaTypeName"
              :value="item.programaTypeCode"
            />
          </el-select>
        </div>
        <div class="moreButtom">
          <el-button
            class="exec"
            type="text"
            style="font-size: 12px"
            @click="moreExt(item.programaType)"
            >更多<i class="el-icon-d-arrow-right"></i
          ></el-button>
        </div>
      </span>
    </div>
    <div class="home-card-body">
      <a class="notice" v-for="item in list" :key="item.noticeId"
         :href="`/portal/home/<USER>/${item.programaType}/${item.noticeId}`" target="view_window">
        <div class="notice-title">
          「<span style="color: red">{{ item.noticeTypeName }}</span
          >」{{ item.noticeTitle }}
        </div>
        <div class="notice-time">
          {{ parseTime(item.createDate, "{y}-{m}-{d}") }}
        </div>
      </a>
      <el-empty
        style="padding: 20px 0"
        v-if="list.length <= 0"
        :image-size="30"
        description="暂无数据"
      >
        <el-link
          icon="el-icon-refresh"
          @click="getNotice"
          :underline="false"
          type="primary"
          >刷新</el-link
        ></el-empty
      >
    </div>
  </el-card>
</template>

<script>
import { mapState } from "vuex";
import { selectTopPage } from "@/api/system/notice";
import { findList } from "/src/api/system/programaType";

export default {
  name: "Article",
  props: {
    editLayout: {
      type: Boolean,
      default: false,
    },
    item: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      bgColor: "",
      show: "",
      list: [],
      programaList: [],
      dataLoading: false,
      queryParams: {
        tenantId: this.$store.getters.customParam.tenantId,
        programaTypeCode: undefined,
        programaTypeName: undefined,
      },
    };
  },
  watch: {
    "item.programaType": function (newVal, oldVal) {
      if (newVal && newVal !== "") {
        this.getNotice();
      } else {
        console.log("请选择栏目");
      }
    },
    "item.showTitle": function (val) {
      this.show = val;
    },
    "item.showBgColor": function (val) {
      this.bgColor = val;
    },
  },
  created() {
    this.getNotice();
    this.getProgramaTypeList();
    this.show = this.item.showTitle,
    this.bgColor = this.item.showBgColor;
  },
  methods: {
    setTitle() {
      this.item.title = this.programaList.find(
        (item) => item.programaTypeCode === this.item.programaType
      ).programaTypeName;
      this.$emit("setTitle", this.item);
    },
    moreExt(type) {
      window.open(`/portal/home/<USER>/${type}`);
    },
    getProgramaTypeList() {
      this.loading = true;
      findList({ ...this.queryParams }).then((response) => {
        this.programaList = response;
      });
    },
    getNotice() {
      if (
        typeof this.item.programaType !== "undefined" &&
        this.item.programaType !== ""
      ) {
        this.dataLoading = true;
        selectTopPage({
          programaType: this.item.programaType,
          queryNum:10,
        }).then((res) => {
          this.list = res.data.records;
          this.dataLoading = false;
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.home-card-body {
  padding-top: 20px;
}
.notice-card {
  height: 100%;
  background: #fff;
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 0;
    }
  }
  .notice {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    padding: 4px 0;
    font-size: 14px;
    .notice-title {
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: nowrap;
      flex-basis: 70%;
    }
    .notice-time {
      flex-basis: 20%;
      text-align: right;
    }
  }
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 0;
    }
  }
  .notice {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    padding: 4px 0;
    font-size: 14px;
    .notice-title {
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: nowrap;
      flex-basis: 70%;
    }
    .notice-time {
      flex-basis: 20%;
      text-align: right;
    }
  }
}
.unshow-notice-card {
  border: 0px;
  height: 100%;
  background: #fff;
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 0;
    }
  }
  .notice {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    padding: 4px 0;
    font-size: 14px;
    .notice-title {
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: nowrap;
      flex-basis: 70%;
    }
    .notice-time {
      flex-basis: 20%;
      text-align: right;
    }
  }
}
.moreButtom {
  display: flex;
  justify-content: flex-end;
}
</style>
