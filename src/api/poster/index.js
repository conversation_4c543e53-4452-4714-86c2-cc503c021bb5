import request from '@/utils/request';

//新增广告接口
export function addPoster(data) {
    return request({
        url: "/cms/v1.0/advertising/insertAdvertising",
        method: "post",
        data
    });
}

//编辑广告接口
export function editPoster(data) {
    return request({
        url: "/cms/v1.0/advertising/updateAdvertisingById",
        method: "post",
        data
    });
}

//查看广告详情
export function lookPoster(params) {
    return request({
        url: "/cms/v1.0/advertising/selectAdvertisingById",
        method: "post",
        params
    });
}

//删除广告
export function delPoster(params) {
    return request({
        url:"/cms/v1.0/advertising/deleteById",
        method:"post",
        params
    })
}

//更改广告状态
export function changeStatusPoster(data) {
    return request ({
        url:"/cms/v1.0/advertising/updateStatusById",
        method:"post",
        data
    })
}

//分页查询广告列表
export function posterList(data) {
    return request({
        url:"/cms/v1.0/advertising/selectForPage",
        method:"post",
        data
    })
}

//查询首页广告数据
export function  indexData(data) {
    return request ({
        url:"/cms/v1.0//advertising/selectTopPage",
        method:"post",
        data
    })
}