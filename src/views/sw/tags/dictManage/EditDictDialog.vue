<template>
  <div>
    <!-- 添加或修改标签弹框 -->
    <el-dialog
      :title="title"
      :visible.sync="openTabDialog"
      @open="onOpen"
      @close="onCancel"
      width="1000px"
    >
      <template slot="title">
        <span class="dialog_title">{{ title }}</span>
      </template>
      <div class="title">字典基本信息</div>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <template v-if="form.id">
          <el-row :gutter="10" class="info">
            <el-col :span="12">
              <span class="info-tabel">字典编码：</span>
              {{ form.dictCode }}
            </el-col>
            <el-col :span="12">
              <span class="info-tabel">字典名称：</span>
              {{ form.dictName }}
            </el-col>
          </el-row>
          <el-row :gutter="10" class="info">
            <el-col :span="24">
              <span class="info-tabel">描述：</span>
              {{ form.describes }}
            </el-col>
          </el-row>
        </template>
        <template v-else>
          <el-form-item label="字典编码" prop="dictCode">
            <el-input v-model="form.dictCode" placeholder="请输入字典编码" />
          </el-form-item>
          <el-form-item label="字典名称" prop="dictName">
            <el-input v-model="form.dictName" placeholder="请输入字典名称" />
          </el-form-item>
          <el-form-item label="描述" prop="describe">
            <el-input
              v-model="form.describes"
              type="textarea"
              placeholder="请输入"
            />
          </el-form-item>
        </template>
      </el-form>
      <div class="title flex-between">
        <div>字典值</div>
        <div v-if="!form.types" class="btn">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-plus"
            plain
            @click="handleEdit"
            >新增</el-button
          >
        </div>
      </div>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        max-height="400"
        style="width: 100%"
      >
        <el-table-column label="字典键" align="center" prop="dictKey">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.dictKey"
              :disabled="!!form.types"
              placeholder="请输入"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="字典值" align="center" prop="dictValue">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.dictValue"
              :disabled="!!form.types"
              placeholder="请输入"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="排序" align="center" prop="sort">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.sort"
              :disabled="!!form.types"
              placeholder="请输入"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column v-if="!form.types" label="操作" align="center">
          <template slot-scope="scope">
            <div class="action-buttons flex-center">
              <!-- <el-button  type="text" @click="handleEdit(scope.row)"
                >修改
              </el-button> -->
              <el-button
                type="text"
                @click="handleDel(scope.row.id, scope.$index)"
                >删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="onCancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  addDictType,
  addDictValue,
  dictDetails,
  dictValueDelete,
  dictTypeModify,
} from "@/api/sw/tags/dictManage";
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    title: "",
    visibleDialog: {
      type: Boolean,
      required: true,
      default: false,
    },
    formData: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      openTabDialog: false,
      open: false,
      // 表单参数
      form: {},

      // 表格loading
      tableLoading: false,
      titles: "",
      // 列表数据
      infoData: {},
      // 表格数据
      tableData: [],
      // 表单校验
      rules: {
        dictCode: [
          { required: true, message: "请输入字典编码", trigger: "blur" },
        ],
        dictName: [
          { required: true, message: "请输入字典名称", trigger: "blur" },
        ],
      },
    };
  },
  // 监听属性类似于data概念
  watch: {
    visibleDialog(val) {
      this.openTabDialog = val;
    },
  },
  // 方法集合
  methods: {
    // 打开窗口
    onOpen() {
      this.form = { ...this.formData };
      const id = this.formData.id;
      this.tableLoading = true;
      dictDetails({ id }).then((res) => {
        // this.formData
        this.tableData = res.data.sort((a, b) => {
          return a.sort - b.sort;
        });
        this.tableLoading = false;
      });
    },
    editData(val) {
      for (let i = 0; i < this.tableData.length; i++) {
        if (this.tableData[i].id === val.id) {
          this.tableData.splice(i, 1, { ...val }); // 使用splice方法替换元素
          break; // 找到并替换后退出循环
        }
      }
    },
    // 字典值
    handleEdit(row) {
      this.tableData.unshift({
        dictKey: "",
        dictValue: "",
        sort: "",
        type_id: this.formData.id,
      });
    },
    // 删除字典值
    handleDel(id, i) {
      this.$confirm("此操作将删除该数据项，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        if (id) {
          await dictValueDelete([id]);
        }
        this.tableData.splice(i, 1);
        this.$message.success("操作成功");
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(async (valid) => {
        if (valid) {
          if (this.form.id) {
            dictTypeModify(this.tableData).then((response) => {
              this.$message.success("修改成功");
              this.onCancel();
              this.$emit("getData");
            });
          } else {
            const list = this.tableData.map((item) => {
              return {
                ...item,
                dictName: this.form.dictName,
              };
            });
            await addDictType(this.form);
            await addDictValue(list);
            this.$message.success("新增成功");
            this.onCancel();
            this.$emit("getData");
          }
        }
      });
    },

    // 取消按钮
    onCancel() {
      this.$emit("update:visibleDialog", false);
      // this.openTabDialog = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {};
      // this.resetForm("form");
    },
  },
};
</script>
<style lang="scss" scoped>
.title {
  padding: 0.08333rem;
  background-color: #f0f2f5;
  margin-bottom: 0.08333rem;
  font-weight: bold;
  font-size: 0.125rem;
}
.el-button--text {
  color: #3377ff !important;
}
.info {
  padding: 2px 16px;
  margin: 6px 0px;
}
</style>
