import Vue from 'vue'
import Vuex from 'vuex'
import app from './modules/app'
import user from './modules/user'
import tagsView from './modules/tagsView'
import permission from './modules/permission'
import settings from './modules/settings'
import getters from './getters'
import inquiryCenter from './modules/inquiryCenter'
import monitor from './modules/monitor'
import dtlSidebar from './modules/dtlSidebar'
import customerManager from './modules/customerManager'

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    user,
    tagsView,
    permission,
    settings,
    inquiryCenter,
    monitor,
    dtlSidebar,
    customerManager
  },
  getters
})

export default store
