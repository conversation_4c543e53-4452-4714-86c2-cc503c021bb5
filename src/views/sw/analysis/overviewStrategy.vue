<template>
  <div class="portal-body">
    <div class="header innerLayout" style="padding-top: 10px;padding-bottom: 0px;padding-left: 10px;cursor: pointer" v-if="showCity" @click="backQuery">
      <img src="../../../assets/images/gotoBack.png" height="32" width="32"  />
      <span style="margin-left: 10px">返回</span>
    </div>
    <div class="analysis" >
      <div class="search-wrapper box-bg">
        <el-form :model="queryParams" @submit.native.prevent ref="queryForm" label-width="1rem">
          <el-row>
            <el-col :span="6">
              <el-form-item label="策略名称" prop="strategyName">
                <el-input
                  v-model="queryParams.strategyName"
                  placeholder="请输入策略名称"
                  clearable
                  :disabled="showCity"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="场景" prop="contactor" v-if='!showCity'>
                <el-select
                  v-model="queryParams.contactor"
                  placeholder="请选择"
                  filterable
                  clearable
                >
                  <el-option label="全部" value="全部"></el-option>
                  <el-option
                    label="靶向营销"
                    value="靶向营销"
                  >
                  </el-option>
                  <el-option
                    label="要客管家"
                    value="要客管家"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="策略执行时间" prop="executeTime" style="width: 99%">
                <el-date-picker
                  v-model="dateRange"
                  size="small"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 98.5%;"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="btn-group" >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery" v-if="!showCity">重置</el-button>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
          </div>
        </el-form>
      </div>
      <div class="container">
        <div class="con-main table-wrapper box-bg" style="flex:4;margin-left: 10px">
          <div class="flex-between" style="padding: 0 0.2rem">
            <div class="table-title">任务跟踪评价</div>
            <div>
              <el-button type="success" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
            </div>
          </div>
          <el-divider />
          <div class="table-con" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
            <el-table  ref="multipleTable" v-loading="tableLoading" :data="tableData" style="width: 100%;" :row-class-name="tableRowStripeClassName" max-height="600">
              <el-table-column label="策略名称" align="left" prop="strategyName" min-width="220" fixed>
                <template slot-scope="scope">
                  <div v-if="showCity">
                    {{ scope.row.strategyName }}
                  </div>
                  <div v-else-if="scope.row.strategyName==='合计'">
                    {{ scope.row.strategyName }}
                  </div>
                  <div v-else style="cursor: pointer;color: #2266FD" @click="goToGroupCity(scope.row.strategyName,'1',scope.row.contactor)">
                    {{ scope.row.strategyName }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="场景" align="center" prop="contactor" fixed/>
              <el-table-column label="地市" align="center" prop="cityName" v-if="showCity" fixed/>
              <el-table-column label="转化率" align="center" prop="transfPercent" :formatter="formatPercentage"/>
              <el-table-column label="执行率" align="center" prop="execPercent" :formatter="formatPercentage"/>
<!--              <el-table-column label="策略任务数" align="center" prop="totalNum" />-->
              <el-table-column label="工单数" align="center" prop="orderNum">
                <template slot-scope="scope">
                  <span v-html="scope.row.orderNum" :class="[scope.row.orderNum>0 ? 'blue-text' : '']"  @click="openOrderData(scope.row.strategyId,scope.row.cityName,scope.row.orderNum,'',scope.row.contactor)"></span>
                </template>
              </el-table-column>
              <el-table-column label="待执行" align="center" prop="daiExecNum"/>
              <el-table-column label="待改派" align="center" prop="daiChangeNum"/>
              <el-table-column label="执行中" align="center" prop="doingNum"/>
              <el-table-column label="已关单" align="center" prop="closeNum">
                <template slot-scope="scope">
                  <span v-html="scope.row.closeNum" :class="[scope.row.closeNum>0 ? 'blue-text' : '']"  @click="goOrderDetail(scope.row)"></span>
                </template>
              </el-table-column>
              <el-table-column label="预警数量" align="center" prop="warningCount">
                <template slot-scope="scope">
                  <span :class="[scope.row.warningCount>0 ? 'blue-text' : '']" @click="scope.row.warningCount > 0 ? goPage('workMonitorOneStatus', scope.row) : null" style="cursor: pointer;">
                    {{ formatZero(scope.row.warningCount) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="催办数量" align="center" prop="reminderCount">
                <template slot-scope="scope">
                  <span :class="[scope.row.reminderCount>0 ? 'blue-text' : '']" @click="scope.row.reminderCount > 0 ? goPage('workMonitorOneStatus', scope.row) : null" style="cursor: pointer;">
                    {{ formatZero(scope.row.reminderCount) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="督办数量" align="center" prop="supervisionCsount">
                <template slot-scope="scope">
                  <span :class="[scope.row.supervisionCsount>0 ? 'blue-text' : '']" @click="scope.row.supervisionCsount > 0 ? goPage('workMonitorOneStatus', scope.row) : null" style="cursor: pointer;">
                    {{ formatZero(scope.row.supervisionCsount) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="到期未执行" align="center" prop="noExecNum"/>
              <el-table-column label="商机情况" align="center">
                <el-table-column label="关联商机数量" align="center" prop="assoOpportunityNum">
                  <template slot-scope="scope">
                    <span v-if="scope.row.strategyName==='移网优惠到期提醒（当月到期）'||scope.row.strategyName==='移网终端到期提醒（当月到期）'">-</span>
                    <span v-else v-html="scope.row.assoOpportunityNum" :class="[scope.row.assoOpportunityNum>0 ? 'blue-text' : '']"  @click="openDetailData('assoOpportunity',scope.row.strategyId,scope.row.cityName,scope.row.assoOpportunityNum,scope.row.contactor)"></span>
                  </template>
                </el-table-column>
                <el-table-column label="关联商机金额(万元)" align="center" prop="assoOppMoney">
                  <template slot-scope="scope">
                    {{scope.row.strategyName==='移网优惠到期提醒（当月到期）'||scope.row.strategyName==='移网终端到期提醒（当月到期）' ? '-' : scope.row.assoOppMoney}}
                  </template>
                </el-table-column>
                <el-table-column label="匹配商机数量" align="center" prop="opportunityNum">
                  <template slot-scope="scope">
                    <span v-if="scope.row.strategyName==='移网优惠到期提醒（当月到期）'||scope.row.strategyName==='移网终端到期提醒（当月到期）'">-</span>
                    <span v-else v-html="scope.row.opportunityNum" :class="[scope.row.opportunityNum>0 ? 'blue-text' : '']"  @click="openDetailData('opportunity',scope.row.strategyId,scope.row.cityName,scope.row.opportunityNum,scope.row.contactor)"></span>
                  </template>
                </el-table-column>
                <el-table-column label="匹配商机金额(万元)" align="center" prop="oppMoney">
                  <template slot-scope="scope">
                    {{scope.row.strategyName==='移网优惠到期提醒（当月到期）'||scope.row.strategyName==='移网终端到期提醒（当月到期）' ? '-' : scope.row.oppMoney}}
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column label="项目情况" align="center">
                <el-table-column label="关联项目数量" align="center" prop="assoProjectNum">
                  <template slot-scope="scope">
                    <span v-if="scope.row.strategyName==='移网优惠到期提醒（当月到期）'||scope.row.strategyName==='移网终端到期提醒（当月到期）'">-</span>
                    <span v-else v-html="scope.row.assoProjectNum" :class="[scope.row.assoProjectNum>0 ? 'blue-text' : '']"  @click="openDetailData('assoProject',scope.row.strategyId,scope.row.cityName,scope.row.assoProjectNum,scope.row.contactor)"></span>
                  </template>
                </el-table-column>
                <el-table-column label="关联项目金额(万元)" align="center" prop="assoProjectMoney">
                  <template slot-scope="scope">
                    {{scope.row.strategyName==='移网优惠到期提醒（当月到期）'||scope.row.strategyName==='移网终端到期提醒（当月到期）' ? '-' : scope.row.assoProjectMoney}}
                  </template>
                </el-table-column>
                <el-table-column label="匹配项目数量" align="center" prop="projectNum">
                  <template slot-scope="scope">
                    <span v-if="scope.row.strategyName==='移网优惠到期提醒（当月到期）'||scope.row.strategyName==='移网终端到期提醒（当月到期）'">-</span>
                    <span v-else v-html="scope.row.projectNum" :class="[scope.row.projectNum>0 ? 'blue-text' : '']"  @click="openDetailData('project',scope.row.strategyId,scope.row.cityName,scope.row.projectNum,scope.row.contactor)"></span>
                  </template>
                </el-table-column>
                <el-table-column label="匹配项目金额(万元)" align="center" prop="projectMoney">
                  <template slot-scope="scope">
                    {{scope.row.strategyName==='移网优惠到期提醒（当月到期）'||scope.row.strategyName==='移网终端到期提醒（当月到期）' ? '-' : scope.row.projectMoney}}
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column label="命中业务数量" align="center" prop="addDevNum">
                <template slot-scope="scope">
                  <span v-if="scope.row.strategyName==='移网优惠到期提醒（当月到期）'||scope.row.strategyName==='移网终端到期提醒（当月到期）'">-</span>
                  <span v-else v-html="scope.row.addDevNum" :class="[scope.row.addDevNum>0 ? 'blue-text' : '']"  @click="openNetWorkData('1',scope.row.strategyId,scope.row.cityName,scope.row.addDevNum)"></span>
                </template>
              </el-table-column>
              <el-table-column label="命中业务收入(元)" align="center" prop="addDevFee" >
                <template slot-scope="scope">
                  {{scope.row.strategyName==='移网优惠到期提醒（当月到期）'||scope.row.strategyName==='移网终端到期提醒（当月到期）' ? '-' : scope.row.addDevFee}}
                </template>
              </el-table-column>
              <el-table-column label="拉动业务数量" align="center" prop="newDevNum">
                <template slot-scope="scope">
                  <span v-if="scope.row.strategyName==='移网优惠到期提醒（当月到期）'||scope.row.strategyName==='移网终端到期提醒（当月到期）'">-</span>
                  <span v-else v-html="scope.row.newDevNum" :class="[scope.row.newDevNum>0 ? 'blue-text' : '']"  @click="openNetWorkData('2',scope.row.strategyId,scope.row.cityName,scope.row.newDevNum)"></span>
                </template>
              </el-table-column>
              <el-table-column label="拉动业务收入(元)" align="center" prop="newDevFee" >
                <template slot-scope="scope">
                  {{scope.row.strategyName==='移网优惠到期提醒（当月到期）'||scope.row.strategyName==='移网终端到期提醒（当月到期）' ? '-' : scope.row.newDevFee}}
                </template>
              </el-table-column>

              <el-table-column label="到期续约数" align="center" prop="renewalNum">
                <template slot-scope="scope">
                  {{scope.row.strategyName!=='移网优惠到期提醒（当月到期）' && scope.row.strategyName!=='移网终端到期提醒（当月到期）' ? '-' : scope.row.renewalNum}}
                </template>
              </el-table-column>
              <el-table-column label="到期合约数" align="center" prop="expirationNum">
                <template slot-scope="scope">
                  {{scope.row.strategyName!=='移网优惠到期提醒（当月到期）' && scope.row.strategyName!=='移网终端到期提醒（当月到期）' ? '-' : scope.row.expirationNum}}
                </template>
              </el-table-column>

            </el-table>
            <div class="flex-end" style="margin-top: 0.14rem">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page.sync="queryParams.pageNum"
                :page-sizes="[10, 20, 30, 40, 50]"
                :page-size="queryParams.pageSize"
                layout="prev, pager, next, sizes, jumper, total "
                :total="total"
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>

    <OrderDetail :paramInfo="paramInfoOrder"  :openOrderDialog.sync="openOrderDialog" :title="openTitle" @update-open-order-dialog="updateOpenOrderDialog"/>

    <OpportDetail :paramInfo="paramInfo"  :openDetailDialog.sync="openDetailDialog" :title="openTitle" @update-open-detail-dialog="updateOpenDetailDialog"/>

    <NetWorkDetail :paramInfo="paramInfoNetWork"  :openNetWorkDialog.sync="openNetWorkDialog" :title="openTitle" @update-open-network-dialog="updateOpenNetWorkDialog"/>

    <AuxiliaryPoliceDetail :paramInfo="paramAuxiliaryPolice"  :openAuxiliaryPoliceDialog.sync="openAuxiliaryPoliceDialog" :title="openTitle" @update-open-auxiliary-police-dialog="updateOpenAuxiliaryPoliceDialog"/>
  </div>

</template>
<script>
import { strategyConverRatePage} from '@/api/sw/analysis/analysis'
import OpportDetail from "@/views/sw/analysis/components/OpportDetail.vue";
import NetWorkDetail from '@/views/sw/analysis/components/NetWorkDetail.vue'
import OrderDetail from '@/views/sw/analysis/components/OrderDetail.vue'
import AuxiliaryPoliceDetail from '@/views/sw/analysis/components/AuxiliaryPoliceDetail.vue'
import {addError, addSuccess, deleteError, deleteSuccess, editError, editSuccess} from "@/utils/messageText";
import UniTable from '@/components/UniTable/UniTable.vue'
export default {
  // import引入的组件需要注入到对象中才能使用
  components: { UniTable,OpportDetail,NetWorkDetail,OrderDetail,AuxiliaryPoliceDetail},
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        contactor: "全部",
        beginDate: '',
        endDate: ''
      },
      tableParams: {
        pageNum: 1,
        pageSize: 10,
        modelName: "",
      },
      // 日期范围
      dateRange: [],
      // 表格数据
      tableData: [],

      // 表单参数
      form: {},
      // 表格loading
      tableLoading: false,
      // 表格总数
      total: 0,
      showCity: false,
      openOrderDialog: false,//工单详情标示
      openDetailDialog: false,
      openNetWorkDialog: false,//联网通信详情
      openAuxiliaryPoliceDialog: false,//交辅警详情展示
      // 弹出层标题
      title: '',
      openTitle: '',
      paramInfoOrder: {
        city: '',
        dataType: '',
        strategyId: '',
        contactor: "靶向营销",
        status:'',
        beginDate: '',
        endDate: ''
      },
      paramInfo: {
        city: '',
        dataType: '',
        strategyId: '',
        contactor: "靶向营销",
        beginDate: '',
        endDate: ''
      },
      paramInfoNetWork: {
        city: '',
        dataType: '',
        strategyId: '',
        contactor: "靶向营销",
        beginDate: '',
        endDate: ''
      },
      paramAuxiliaryPolice: {
        cityCode: '',
        strategyId: '',
        contactor: "靶向营销",
        beginDate: '',
        endDate: ''
      },
      hideScrollbarTimeout: null,
    };
  },
  created() {
    this.init();
    this.getList();

  },
  // 监听属性类似于data概念
  computed: {},
  watch: {
  },
  // 方法集合
  methods: {
    init(){
      var pas = this.$route.query.matchModel;
      console.log("首页跳转过来,",pas);
      if(pas == '要客管家'){
        this.queryParams.contactor = '要客管家'
      }
    },
    // 当前页码
    handleCurrentChange(page) {
      this.queryParams.pageNum = page;
      this.getList();
    },
    formatPercentage(row, column, cellValue) {
      return (cellValue * 100).toFixed(2) + '%';
    },
    // 右侧表格数据
    getList() {
      this.queryParams.beginDate = this.dateRange[0];
      this.queryParams.endDate = this.dateRange[1];
      // this.$refs.tableModelRef.loadData(this.queryParams);
      const params = { ...this.queryParams };
      this.tableLoading = true;
      strategyConverRatePage(params).then((res) => {
        this.tableLoading = false;
        this.tableData = res.data.records;
        this.total = res.data.total;
      })
    },
    //某个策略按地市统计
    goToGroupCity(strategyName,state,contactor) {
      this.queryParams.contactor = contactor;
      this.queryParams = {...this.queryParams,strategyName,state}
      this.getList();
      this.showCity = true;
      // this.$message.info("策略名称："+strategyName)
      // if (name != "全行业") {
      //   this.$router.push({
      //     name: "analysisOverview",
      //     params: { stragegyId: stragegyId }
      //   });
      // }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 导出按钮操作 */
    handleExport() {
      this.exportExcel(
        '/sanquan/strategy/transAnalysis/export',
        {
          ...this.queryParams,
          // 'monthId': this.formQuery['monthId'].replace('-', '')
        },
        `策略转化率分析导出.xlsx`
      )
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = { pageNum: 1,
        pageSize: 10,
        contactor: "靶向营销"}
      this.dateRange = [];
      this.showCity = false;
      this.resetForm("queryForm");
      this.handleQuery();
    },
    backQuery(){
      this.queryParams = { pageNum: 1,
        pageSize: 10,
        contactor: "靶向营销",
        beginDate: '',
        endDate: ''
      }
      this.dateRange = [];
      this.showCity = false;
      this.resetForm("queryForm");
      this.handleQuery();
    },
    tableRowStripeClassName({ row, rowIndex }) {
      let rowStyle = "row-bg-high";
      if(row.strategyName==='合计'){
        rowStyle = "row-bg-total";
      }else{
        rowStyle = "row-bg-high";
        if (rowIndex % 2 === 0) {
          rowStyle = "row-bg-normal";
        }
      }

      return rowStyle;
    },
    openOrderData(strategyId,city,num,status,contactor){
      if(num>0){
        if(this.showCity){
          this.paramInfoOrder.city = city;
        }else{
          this.paramInfoOrder.city = '';
        }
        this.paramInfoOrder.strategyId = strategyId;
        this.paramInfoOrder.status = status;
        // this.paramInfoOrder.contactor = this.queryParams.contactor
        this.paramInfoOrder = {...this.paramInfoOrder,...this.queryParams}
        this.paramInfoOrder.contactor = contactor
        this.openOrderDialog = true;
        this.openTitle = '查看工单明细';
      }
    },
    openDetailData(type,strategyId,city,num,contactor){
      if(num>0){
        if(this.showCity){
          this.paramInfo.city = city;
        }else{
          this.paramInfo.city = '';
        }
        this.paramInfo.strategyId = strategyId;
        this.paramInfo = {...this.paramInfo,...this.queryParams}
        this.paramInfo.contactor = contactor
        this.openDetailDialog = true;
        if(type==='opportunity'){
          this.paramInfo.dataType = 'opportunity';
          this.openTitle = '查看商机详情';
        }else if(type==='project'){
          this.paramInfo.dataType = 'project';
          this.openTitle = '查看项目详情';
        }else if (type==='assoOpportunity'){
          this.paramInfo.dataType = 'assoOpportunity';
          this.openTitle = '查看关联商机详情';
        }else if (type==='assoProject'){
          this.paramInfo.dataType = 'assoProject';
          this.openTitle = '查看关联项目详情';
        }
      }
    },
    openNetWorkData(type,strategyId,city,num){
      if(num>0){
        if("P17202410210023"===strategyId){
          if(this.showCity){
            this.paramAuxiliaryPolice.cityCode = city;
          }else{
            this.paramAuxiliaryPolice.cityCode = '';
          }
          this.paramAuxiliaryPolice.strategyId = strategyId;
          this.paramAuxiliaryPolice = {...this.paramAuxiliaryPolice,...this.queryParams}
          this.openTitle = '带动其他业务的提升情况'
          this.openAuxiliaryPoliceDialog = true;
        }else{
          if(this.showCity){
            this.paramInfoNetWork.city = city;
          }else{
            this.paramInfoNetWork.city = '';
          }
          this.paramInfoNetWork.strategyId = strategyId;
          this.paramInfoNetWork = {...this.paramInfoNetWork,...this.queryParams}
          if(type==='1'){
            this.paramInfoNetWork.dataType = '1'
            this.openTitle = '推荐的本业务的提升情况'
            this.openNetWorkDialog = true;
          }else if(type==='2'){
            this.paramInfoNetWork.dataType = '2'
            this.openTitle = '带动其他业务的提升情况'
            this.openNetWorkDialog = true;
          }
        }
      }
    },
    updateOpenOrderDialog(flag){
      this.openOrderDialog = flag;
    },
    updateOpenDetailDialog(flag){
      this.openDetailDialog = flag;
    },
    updateOpenNetWorkDialog(flag){
      this.openNetWorkDialog = flag;
    },
    updateOpenAuxiliaryPoliceDialog(flag){
      this.openAuxiliaryPoliceDialog = flag;
    },
    getOrderStatus(status){
      let result = '';
      if(status==='1'){
        result = '待执行'
      }else if(status==='2'){
        result = '已关单'
      }else if(status==='3'){
        result = '待改派'
      }else if(status==='4'){
        result = '跟进中'
      }else if(status==='5'){
        result = '到期未执行'
      }
      return result;
    },
    formatZero(cellValue) {
      return cellValue || cellValue === 0 ? cellValue : 0;
    },
    // 页面跳转
    goPage(path, params) {
      this.$router.push({
        name: path,
        query: params
      })
    },
    // 跳转工单详情
    goOrderDetail(row) {
      // 根据 contactor 决定路径和查询参数
      const contactor = row.contactor;
      let query = {
        superiorPolicyName: row.strategyName,
        status: '2'
      };
      let path = '';
      if (contactor === '靶向营销') {
        path = '/sw/targetOrder';
        query.city = row.cityName
      } else if (contactor === '要客管家') {
        query.cityCode = row.cityName
        path = '/sw/strategyFeedbackNew';
      }
      // 如果有对应的 path，进行跳转
      if (path) {
        this.$router.push({ path, query });
      }
    },
    // 查询的条数修改
    handleSizeChange(val) {
      this.queryParams.pageSize = val;
      this.getList();
    },
    handleMouseEnter() {
      clearTimeout(this.hideScrollbarTimeout); // 取消任何未完成的隐藏操作
      this.$refs.multipleTable.bodyWrapper.style.overflowX = 'auto';
    },
    handleMouseLeave() {
      this.hideScrollbarTimeout = setTimeout(() => {
        this.$refs.multipleTable.bodyWrapper.style.overflowX = 'hidden';
      }, 200); // 200ms 的延迟
    }
  },
};
</script>
<style lang="scss" scoped>
::v-deep .row-bg-total{
  background-color: #FFEDE5;
}
.container {
  display: flex;
  .con-left {
    width: 30%;
    margin-right: 0.16667rem;

    .flex-between {
      display: block;
      padding: 0.16667rem 0.08333rem;
      border-bottom: 0.00833rem solid;
      border-bottom: 0.00833rem solid #f1f1f1;
    }
    .tab-tree {
      height: calc(60vh - 10px);
      overflow-y: auto;
    }
    .tab-icon {
      padding-right: 0.08333rem;
    }
  }
  .con-main {
    width: calc(70% - 0.16667rem);
    .blue-text{
      color:blue;
      cursor: pointer;
    }
  }
}
.innerLayout{
  align-items: center;
  display: flex;
  justify-content: left;
}

.btn-group {
  position: absolute;
  top: 24px;
  right: 0.2rem;
}

// 隐藏滚动条
::v-deep .el-table__body-wrapper {
  overflow-x: hidden;
}

::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  width: 0px;
  height: 10px !important;
}
</style>
