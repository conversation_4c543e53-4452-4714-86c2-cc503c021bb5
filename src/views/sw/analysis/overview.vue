<template>
    <div class="portal-body">
        <div class="analysis">
            <div class="flex-between  title-wrapper">
                <div class="flex-start" @click="goBack()" style="cursor: pointer;">
                    <img v-show="industry" src="../../../assets/images/sw/back.png" alt="">
                    <p class="title">{{ industry ? industry : '工业互联网' }}</p>
                </div>
                <el-date-picker v-model="queryParams.monthId" type="month" placeholder="选择月" format="yyyyMM"
                    value-format="yyyyMM" @change="search">
                </el-date-picker>
            </div>
            <div class="flex-between">
                <div style="width: 61%;">
                    <div class="table-wrapper box-bg cl">
                        <div class="flex-start p-2">
                            <div class="table-title">策略转化分析</div>
                        </div>
                        <el-divider></el-divider>
                        <div class="table-con">
                            <div class="flex-between" style="margin-bottom: 32px;">
                                <div class="cl-item">
                                    <p>策略数</p>
                                    <p class="cl-num pointer" @click="goDetail(0, '策略转化分析列表')">{{ industryData.configNum }}</p>
                                </div>
                                <div class="cl-item">
                                    <p>策略执行次数</p>
                                    <p class="cl-num">{{ industryData.resultExecNum }}</p>
                                </div>
                                <div class="cl-item">
                                    <p>生成潜在机会数</p>
                                    <p class="cl-num pointer" @click="goDetail(1, '生成潜在机会数分析列表')">{{
                    industryData.potentialOpportunityNum }}</p>
                                </div>
                            </div>
                            <div class="flex-between">
                                <div class="cl-item">
                                    <p>工单数</p>
                                    <p class="cl-num pointer" @click="goDetail(2, '工单数分析列表')">{{ industryData.orderNum }}</p>
                                </div>
                                <div class="cl-item">
                                    <p>商机数</p>
                                    <p class="cl-num pointer" @click="goDetail(3, '商机数分析列表')">{{
                    industryData.businessOpportunityNum }}</p>
                                </div>
                                <div class="cl-item">
                                    <p>商机转化率</p>
                                    <p class="cl-num">{{ perc }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex-between">
                        <div class="table-wrapper box-bg model">
                            <div class="flex-start p-2">
                                <div class="table-title">模型使用次数分析</div>
                            </div>
                            <el-divider></el-divider>
                            <div class="table-con model-chart" ref="modelChart"></div>
                        </div>
                        <div class="table-wrapper box-bg model">
                            <div class="flex-start p-2">
                                <div class="table-title">标签打标数量统计</div>
                            </div>
                            <el-divider></el-divider>
                            <div class="table-con model-chart" ref="tagChart"></div>
                        </div>
                    </div>
                </div>

                <div class="table-wrapper box-bg" style="width: 35%;height: 573px">
                    <div class="flex-start" style="padding: 0 0.2rem;">
                        <div class="table-title">地市商机分布图</div>
                    </div>
                    <el-divider></el-divider>
                    <div class="map-chart" ref="mapChart"></div>
                </div>
            </div>
            <div class="table-wrapper box-bg">
                <div class="flex-start p-2">
                    <div class="table-title">产品推荐成功排行</div>
                </div>
                <el-divider></el-divider>
                <div class="table-con">
                    <div ref="barChart" class="bar-chart"></div>
                </div>
            </div>
            <div class="table-wrapper box-bg">
                <div class="flex-start p-2">
                    <div class="table-title">商机转化客户排行</div>
                </div>
                <el-divider></el-divider>
                <div class="p-2">
                    <ul class="customer-top">
                        <li v-for="(item, index) in customerTop">
                            <div class="flex-start">
                                <span class="no">{{ index + 1 }}</span>
                                <span class="customer">{{ item.name }}</span>
                                <span :class="item.level == '世界500强' ? 'world-level' : 'country-level'">{{ item.level
                                    }}</span>
                            </div>
                            <div class="flex-start">
                                <p class="c-dtl-item flex-between">
                                    <span>挖掘商机数</span>
                                    <span class="num">{{ item.sj }}</span>
                                </p>
                                <p class="c-dtl-item flex-between">
                                    <span>商机转化数</span>
                                    <span class="num">{{ item.zh }}</span>
                                </p>
                                <p class="c-dtl-item flex-between">
                                    <span>客户标签数</span>
                                    <span class="num">{{ item.tag }}</span>
                                </p>
                                <p class="c-dtl-item flex-between">
                                    <span>产品匹配数</span>
                                    <span class="num">{{ item.product }}</span>
                                </p>
                                <p class="c-dtl-item flex-between">
                                    <span>地市</span>
                                    <span class="num city">{{ item.city }}</span>
                                </p>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

    </div>
</template>

<style scoped lang="less">
p {
    margin: 0;
}

ul,
li {
    list-style: none;
}

.analysis {
    padding: 20px 0;
}

.table-wrapper {
    margin-bottom: 0.2rem;
}

.title-wrapper {
    margin: 5px 0 24px 0;
}

.title {
    margin-left: 5px;
    font-family: PingFangSC-Regular;
    font-size: 0.2rem;
    color: #181818;
    letter-spacing: 0;
    line-height: 0.26rem;
    font-weight: 600;
}

.p-2 {
    padding: 0 0.2rem;
}

.table-con {
    padding: 12px 0 12px 0.33rem !important;
}

.cl {
    height: 278px;
}

.model {
    width: 48.6%;
    height: 270px;
}

.cl-item {
    width: 2rem;
    text-align: left;
    font-size: 0.125rem;
    color: #373D41;
    letter-spacing: 0;
    line-height: 0.2rem;
    font-weight: 400;

    .cl-num {
        font-family: AlibabaSans102Dec10-Bold;
        font-size: 0.25rem;
        color: #181818;
        letter-spacing: 0;
        font-weight: 700;
        line-height: 0.35rem;
    }
}

.model-chart {
    width: 97%;
    height: 210px;
}

.bar-chart {
    width: 97%;
    height: 380px;
}

.map-chart {
    width: 97%;
    height: 480px;
}

.customer-top {
    padding: 0;

    li {
        padding: 16px 0 19px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.06);
        ;
    }

    span {
        display: inline-block;
    }

    .no {
        width: 0.18rem;
        height: 0.18rem;
        color: #fff;
        background: rgba(169, 176, 180, 0.60);
        border: 1px solid rgba(169, 176, 180, 1);
        border-radius: 4px;
        font-size: 0.135rem;
        line-height: 0.18rem;
        text-align: center;
    }

    li:first-child {
        .no {
            background: rgba(224, 32, 32, 0.60);
            border: 1px solid rgba(224, 32, 32, 1);
        }
    }

    li:nth-child(2) {
        .no {
            background: rgba(250, 100, 0, 0.60);
            border: 1px solid rgba(250, 100, 0, 1);
        }
    }

    li:nth-child(3) {
        .no {
            background: rgba(247, 181, 0, 0.60);
            border: 1px solid rgba(247, 181, 0, 1);
        }
    }

    li:nth-child(4) {
        .no {
            background: rgba(51, 119, 255, 0.60);
            border: 1px solid rgba(51, 119, 255, 1);
        }
    }

    .customer {
        margin: 0 16px;
        font-family: PingFangSC-Medium;
        font-size: 0.125rem;
        color: rgba(0, 0, 0, 0.85);
        line-height: 0.2rem;
        font-weight: 500;
    }

    .world-level {
        padding: 0 8px;
        background: #FFF1F0;
        border: 1px solid rgba(255, 204, 199, 1);
        border-radius: 4px;
        font-family: PingFangSC-Regular;
        font-size: 0.125rem;
        color: #FF4D4F;
        line-height: 0.2rem;
        font-weight: 400;
    }

    .country-level {
        padding: 0 8px;
        background: #FFFBE6;
        border: 1px solid rgba(255, 241, 184, 1);
        border-radius: 4px;
        font-family: PingFangSC-Regular;
        font-size: 0.125rem;
        color: #FAAD14;
        line-height: 0.2rem;
        font-weight: 400;
    }

    .c-dtl-item {
        margin: 0.13rem 0.27rem 0;
        padding: 0 0.2rem;
        width: 1.52rem;
        height: 0.25rem;
        background: rgba(51, 119, 255, 0.1);
        border-radius: 2px;
        font-family: PingFangSC-Regular;
        font-size: 0.125rem;
        color: rgba(0, 0, 0, 0.45);
        line-height: 0.25rem;
        font-weight: 400;

        .num {
            margin-left: 0.2rem;
            font-family: AlibabaSans102Dec10-Bold;
            font-size: 0.135rem;
            color: #181818;
            letter-spacing: 0;
            line-height: 0.25rem;
            font-weight: 700;

        }

        .city {
            font-size: 0.125rem;
        }
    }

}
.pointer{
    cursor: pointer;
}
</style>

<script>
import echarts from 'echarts'
import wordcloud from 'echarts-wordcloud'
import { allConverRate, productRecommendation, cityOpportunityArea, oportunityConvCustTop } from '@/api/sw/analysis/analysis'
import mapjson from '@/assets/map/shandong.json'
export default {
    name: 'index',
    data() {
        return {
            barChart: null,
            modelChart: null,
            tagChart: null,
            mapChart: null,
            queryParams: {
                monthId: '202404',
                industry: ''
            },
            customerTop: [],
            analysisData: [],
            industry: '',
            industryData: {
                businessOpportunityNum: 0,
                configNum: 0,
                orderNum: 0,
                potentialOpportunityNum: 0,
                resultExecNum: 0
            },
            tableColumns: [],
            tableData: [],
            cltableColumns: [
                {
                    name: '策略id',
                    value: 'id',
                }, {
                    name: '策略名称',
                    value: 'name',
                }, {
                    name: '策略执行次数',
                    value: 'times',
                }, {
                    name: '生成潜在机会数',
                    value: 'oppotunity',
                }, {
                    name: '工单数',
                    value: 'gd',
                }, {
                    name: '商机数',
                    value: 'sj',
                }, {
                    name: '商机转化率',
                    value: 'oerc',
                },
            ],
            cltableData: [
                {
                    id: '1111',
                    name: '',
                    times: '',
                    oppotunity: '',
                    gd: '',
                    sj: '',
                    perc: ''
                },
                {
                    id: '1111',
                    name: '',
                    times: '',
                    oppotunity: '',
                    gd: '',
                    sj: '',
                    perc: ''
                },
            ],
            optableColumns: [
                {
                    name: '地市',
                    value: 'name',
                }, {
                    name: '生成潜在机会数',
                    value: 'oppotunity',
                }, {
                    name: '工单数',
                    value: 'gd',
                }, {
                    name: '商机数',
                    value: 'sj',
                }, {
                    name: '商机转化率',
                    value: 'oerc',
                },
            ],
            optableData: [
                {
                    id: '1111',
                    name: '济南',
                    times: '',
                    oppotunity: '',
                    gd: '',
                    sj: '',
                    perc: ''
                },
                {
                    id: '1111',
                    name: '青岛',
                    times: '',
                    oppotunity: '',
                    gd: '',
                    sj: '',
                    perc: ''
                },
            ],
            gdtableColumns: [
                {
                    name: '地市',
                    value: 'name',
                }, {
                    name: '工单数',
                    value: 'gd',
                    hasDtl:true,
                }, {
                    name: '已查看',
                    value: 'seeo',
                }, {
                    name: '已关单',
                    value: 'closeo',
                }, {
                    name: '平均执行天数',
                    value: 'averg',
                },
            ],
            gdtableData: [
                {
                    name: '济南',
                    gd: '122',
                    seeo: '109',
                    closeo: '20',
                    averg: '17'
                },
                {
                    name: '济南',
                    gd: '122',
                    seeo: '109',
                    closeo: '20',
                    averg: '17'
                },
            ],
            sjtableColumns: [
                {
                    name: '地市',
                    value: 'name',
                }, {
                    name: '客户名称',
                    value: 'customer',
                }, {
                    name: '客户经理',
                    value: 'manager',
                }, {
                    name: '商机提交时间',
                    value: 'datetime',
                }
            ],
            sjtableData: [
                {
                    name: '济南',
                    customer: '有限公司',
                    manager: '张三',
                    datetime: '2024-03-23'
                },
                {
                    name: '济南',
                    customer: '有限公司',
                    manager: '张三',
                    datetime: '2024-03-23'
                },
            ],
        }
    },
    computed: {
        perc: function () {
            let percent = this.industryData.orderNum > 0 ? (100 * this.industryData.businessOpportunityNum / this.industryData.orderNum).toFixed(2) : 0;
            return percent + '%'
        }
    },
    mounted() {
        this.industry = this.$route.params.industry
        this.queryParams.industry = this.$route.params.industry
        this.barChart = echarts.init(this.$refs.barChart)
        this.mapChart = echarts.init(this.$refs.mapChart)
        this.modelChart = echarts.init(this.$refs.modelChart)
        this.tagChart = echarts.init(this.$refs.tagChart)
        console.log('router:' + this.$route.params.industry)
        let _this = this
        console.log('overview')
        this.getData()
        setTimeout(function () {
            _this.initModelChart()
            _this.initTagChart()
        }, 1000)

        window.onresize = function () {
            _this.modelChart.resize()
            _this.barChart.resize()
            _this.mapChart.resize()
            _this.tagChart.resize()
        }
    },
    activated() {
        if (this.$route.params.industry && this.$route.params.industry != this.industry) {
            this.industry = this.$route.params.industry
            this.queryParams.industry = this.$route.params.industry
            this.getData()
        }
        console.log('keep alive analysis detail')
    },
    methods: {
        search() {
            this.getData()
        },
        getData() {
            this.getAllConverRate()
            this.getProductRecommendation()
            this.getCityOpportunityArea()
            this.getOportunityConvCustTop()
        },
        // 整体转化率分析
        getAllConverRate() {
            allConverRate(this.queryParams).then(res => {
                if (res.success && res.data.length > 0) {
                    this.industryData = res.data[0]
                } else {
                    this.industryData = {
                        businessOpportunityNum: 0,
                        configNum: 0,
                        orderNum: 0,
                        potentialOpportunityNum: 0,
                        resultExecNum: 0
                    }
                }
            })
        },
        // 产品推荐成功排行
        getProductRecommendation() {
            productRecommendation(this.queryParams).then(res => {
                let _this = this
                if (res.success) {
                    let xarr = [], sdata = [];
                    for (let i = 0, len = res.data.length; i < len; i++) {
                        xarr.push(res.data[i].product_name)
                        sdata.push({
                            name: res.data[i].product_name,
                            value: res.data[i].num,
                            itemStyle: {
                                color: i % 2 == 0 ? '#5B8FF9' : '#5AD8A6'
                            }
                        })
                    }
                    setTimeout(function () {
                        _this.initBarChart(xarr, sdata)
                    }, 1500)

                }
            })
        },
        initBarChart(xarr, sdata) {
            this.barChart.setOption({
                tooltip: {
                    trigger: 'axis',
                    axisPointer: { // 坐标轴指示器，坐标轴触发有效
                        type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
                    }
                },
                grid: {
                    top: 10,
                    left: '2%',
                    right: '2%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: [{
                    type: 'category',
                    data: xarr,
                    axisTick: {
                        alignWithLabel: true
                    }
                }],
                yAxis: [{
                    type: 'value',
                    axisTick: {
                        show: false
                    }
                }],
                series: [{
                    name: '数量',
                    type: 'bar',
                    stack: 'vistors',
                    barWidth: '50',
                    data: sdata
                }]
            })
        },
        // 地市商机分布
        getCityOpportunityArea() {
            let _this = this
            cityOpportunityArea(this.queryParams).then(res => {
                if (res.success) {
                    let mapData = []
                    res.data.forEach(item => {
                        mapData.push({
                            name: item.city_code + "市",
                            value: item.opportunityNum
                        })
                    })
                    this.initMapChart(mapData)
                }
            })
        },
        initMapChart(mapData) {
            this.mapChart = echarts.init(this.$refs.mapChart)
            echarts.registerMap('山东', mapjson);

            let option = {
                tooltip: {
                    show: true
                },
                visualMap: {
                    show: false,
                    borderColor: 'white',
                    min: 0,
                    max: 150,
                    y: 'center',
                    splitNumber: 10,
                    calculable: true,
                    textGap: 15,
                    text: [''],
                    seriesIndex: [0],
                    inRange: {
                        color: ['#e0ffff', '#006edd']
                    },
                    outOfRange: {
                        color: [
                            '#8c8c8c'
                        ]
                    },
                    textStyle: {
                        color: '#fff'
                    }
                },
                geo: {
                    map: '山东',
                    zoom: 1.1,
                    // itemStyle: {
                    //     shadowColor: 'black',
                    //     shadowBlur: 15,
                    //     shadowOffsetX: -5,
                    //     shadowOffsetY: 15,
                    // }
                },
                series: [
                    {
                        type: 'map',
                        map: '山东',
                        name: '商机数',
                        zoom: 1.1,
                        roam: false, // 是否开启鼠标缩放和平移漫游
                        label: {
                            normal: {
                                show: true,
                                textStyle: {
                                    fontSize: 12,
                                    color: 'black'
                                }
                            },
                            emphasis: {
                                textStyle: {
                                    fontSize: 12,
                                    color: '#fff'
                                }
                            }
                        },
                        itemStyle: { // 地图区域的多边形 图形样式
                            normal: { // 是图形在默认状态下的样式
                                label: {
                                    show: true, // 是否显示标签
                                    textStyle: {
                                        color: 'transparent'
                                    },
                                },
                                borderWidth: 1,
                                borderColor: '#3c64ff',
                                areaColor: '#2298ff'
                            },
                            emphasis: { // 是图形在高亮状态下的样式,比如在鼠标悬浮或者图例联动高亮时
                                label: {
                                    show: false,
                                    textStyle: {
                                        color: 'transparent'
                                    },
                                },
                                borderColor: 'white',
                                areaColor: '#e2cd00',
                            },
                        },
                        showLegendSymbol: false,
                        data: mapData
                    },

                ]
            }
            this.mapChart.setOption(option);
            let mapIndex = 1;
            // setInterval(() => {
            //     this.mapChart.dispatchAction({
            //         type: 'downplay',
            //         seriesIndex: 0,
            //     });
            //     this.mapChart.dispatchAction({
            //         type: 'highlight',
            //         seriesIndex: 0,
            //         dataIndex: mapIndex %= mapData.length
            //     });
            //     mapIndex++;
            // }, 3000);
        },
        // 模型
        initModelChart() {
            let option = {
                // backgroundColor: '#fff',
                tooltip: {
                    trigger: 'item',
                    formatter: '{b}:({d}%)',
                },
                legend: [
                    {
                        top: 'center',
                        orient: 'vertical',
                        right: '3%',
                        itemWidth: 9,
                        itemHeight: 9,
                        icon: 'circle',
                        itemGap: 20,
                        textStyle: {
                            color: '#4E5969',
                            fontSize: 12,
                            padding: [0, 0, 0, 10],
                        }
                    }
                ],
                grid: {
                    containLabel: true,
                },
                title: {
                    show: false
                },
                series: [
                    {
                        type: 'pie',
                        radius: ['50%', '75%'],
                        center: ['27%', '50%'],
                        color: [
                            '#3377FF',
                            '#5AD8A6',
                            '#5D7092',
                            '#846BCE',
                            '#21CCFF',
                        ],
                        label: {
                            position: 'outside',
                            show: true,
                            color: '#181818',
                            fontSize: 12,
                            formatter: function (params) {
                                if (params.name !== '') {
                                    return params.value;
                                    // return params.percent + '%';
                                }
                            },
                        },
                        labelLine: {
                            show: true,
                            length2: 10,
                            length: 10,
                        },
                        itemStyle: {
                            normal: {
                                borderWidth: 2,
                                borderColor: '#fff',
                            },
                        },
                        data: [
                            { value: '36', name: '标签匹配模型' },
                            { value: '48', name: '热销模型' },
                            { value: '31', name: '标杆模型' },
                        ],
                    },
                ],
            }
            this.modelChart.setOption(option)
        },
        // 标签
        initTagChart() {

            let list = [
                { value: '66', name: '等保测评' },
                { value: '49', name: '工业互联网' },
                { value: '28', name: '规模' },
                { value: '37', name: '渗透率' },
                { value: '26', name: '行业' },
                { value: '25', name: '客户价值' },
                { value: '24', name: '大数据' },
                { value: '33', name: '血库' },
                { value: '22', name: '业务场景' },

            ]
            let option = {
                tooltip: {
                    show: true,
                    borderColor: '#ffffff',
                    borderWidth: 1,
                    padding: [10, 15, 10, 15],
                    confine: true,
                    backgroundColor: 'rgba(255, 255, 255, .9)',
                    textStyle: {
                        color: '#000',
                        lineHeight: 22
                    },
                    extraCssText: 'box-shadow: 0 4px 20px -4px rgba(199, 206, 215, .7);border-radius: 4px;'
                },
                color: ['#5087ec', '#67bbc4', '#f3bd43', '#ef752f', '#a1cca3', '#5087ec', '#67bbc4', '#f3bd43', '#ef752f', '#a1cca3'],
                series: [
                    {
                        type: 'wordCloud',
                        // The shape of the "cloud" to draw. Can be any polar equation represented as a
                        // callback function, or a keyword present. Available presents are circle (default),
                        // cardioid (apple or heart shape curve, the most known polar equation), diamond (
                        // alias of square), triangle-forward, triangle, (alias of triangle-upright, pentagon, and star.

                        shape: 'pentagon',

                        // A silhouette image which the white area will be excluded from drawing texts.
                        // The shape option will continue to apply as the shape of the cloud to grow.

                        // Folllowing left/top/width/height/right/bottom are used for positioning the word cloud
                        // Default to be put in the center and has 75% x 80% size.

                        left: 'center',
                        top: 'center',
                        width: '100%',
                        height: '100%',
                        right: null,
                        bottom: null,


                        // Text size range which the value in data will be mapped to.
                        // Default to have minimum 12px and maximum 60px size.

                        sizeRange: [14, 40],

                        // Text rotation range and step in degree. Text will be rotated randomly in range [-90, 90] by rotationStep 45

                        rotationRange: [0, 0],
                        rotationStep: 0,

                        // size of the grid in pixels for marking the availability of the canvas
                        // the larger the grid size, the bigger the gap between words.

                        gridSize: 25,

                        // set to true to allow word being draw partly outside of the canvas.
                        // Allow word bigger than the size of the canvas to be drawn
                        drawOutOfBound: false,

                        // If perform layout animation.
                        // NOTE disable it will lead to UI blocking when there is lots of words.
                        layoutAnimation: true,

                        // Global text style
                        textStyle: {
                            // fontFamily: 'PingFangSC-Regular',
                            fontWeight: 600,
                            normal: {
                                color: function (params) {
                                    let colors = ['#5087ec', '#67bbc4', '#f3bd43', '#ef752f', '#a1cca3', '#5087ec', '#67bbc4', '#f3bd43', '#ef752f', '#a1cca3', '#5087ec', '#67bbc4', '#f3bd43', '#ef752f', '#a1cca3']
                                    return colors[params.dataIndex]
                                    // return colors[parseInt(Math.random() * 10)];
                                },
                            },

                        },

                        // textStyle: {
                        //     normal: {
                        //         color: function () {
                        //             return (
                        //                 'rgb(' + [
                        //                     Math.round(Math.random() * 500),
                        //                     Math.round(Math.random() * 300),
                        //                     Math.round(Math.random() * 200)
                        //                 ].join(',') +
                        //                 ')'
                        //             )
                        //         }
                        //     },
                        //     emphasis: {
                        //         shadowBlur: 10,
                        //         shadowColor: '#ffffff'
                        //     }
                        // },
                        emphasis: {
                            focus: 'none',
                        },

                        // Data is an array. Each array item must have name and value property.
                        data: list,
                    },
                ],
            };
            this.tagChart.setOption(option)

        },
        // 商机转化客户排行榜
        getOportunityConvCustTop() {
            oportunityConvCustTop(this.queryParams).then(res => {
                if (res.success) {
                    this.customerTop = []
                    res.data.forEach(item => {
                        this.customerTop.push({
                            name: item.customer_name,
                            sj: item.potentialOpportunityNum,
                            zh: item.opportunityNum,
                            tag: 0,
                            product: item.productNum,
                            city: item.city_code,
                            // level: '全国500强'
                        })
                    })
                }
            })
        },
        //详情页
        goBack() {
            if (this.$route.params.industry) {
                this.$router.push("/sw/analysisOverviewAll");
            }
        },
        //详情页
        goDetail(t, n) {
            // this.handleTable(t)
            this.$router.push({
                name: "analysisDetail",
                params: {
                    title: n,
                    industry: this.industry,
                    type:t
                    // tableColumns: this.tableColumns,
                    // tableData: this.tableData
                }
            });
        },
        handleTable(t) {
            switch (t) {
                case 0: this.tableColumns = this.cltableColumns;
                    this.tableData = this.cltableData;
                    break;
                case 1: this.tableColumns = this.optableColumns;
                    this.tableData = this.optableData;
                    break;
                case 2: this.tableColumns = this.gdtableColumns;
                    this.tableData = this.gdtableData;
                    break;
                case 3: this.tableColumns = this.sjtableColumns;
                    this.tableData = this.sjtableData;
                    break;
            }
        },
        //弹窗
        handleClose(done) {
            this.$confirm('确认关闭？')
                .then(_ => {
                    done();
                    this.dialogList = ''
                })
                .catch(_ => { });
        },
    }
}
</script>
