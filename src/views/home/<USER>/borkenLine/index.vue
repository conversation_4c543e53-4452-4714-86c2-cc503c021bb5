<template>
  <el-card
    :class="this.bgColor === true ? 'line-card':'unshow-line-card'"
    v-loading="line"
    shadow="never"
  >
    <div slot="header" v-if="showTitle">
      <div class="card-title">
        <div class="title">
          <span>
            <i class="icon iconfont icon-gonggao"></i>云监控-折线面积图
          </span>
        </div>
        <div class="top">
          <el-button icon="el-icon-d-arrow-right" class="el-button" circle @click="cancel"></el-button>
          <transition name="forms">
            <el-form
              :inline="true"
              class="demo-form-inline"
              :data="usefulVal"
              v-if="show"
              style="line-height:40px;margin-left:10px;width:100%"
            >
              <el-form-item label="监控类型">
                <el-select v-model="key" placeholder="请选择">
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <!-- <el-input
                  placeholder="请输入查询的设备"
                  v-model="key"
                  size="mini"
                  style="width: 150px"
                  @change="useChart"
                ></el-input>-->
              </el-form-item>
            </el-form>
          </transition>
        </div>
      </div>
    </div>
    <div class="home-card-body">
      <div class="borkenLine" v-if="borkenLineVal">
        <div id="borkenLines"></div>
      </div>
    </div>
  </el-card>
</template>

<script>
// import { utilization } from "@/api/monitor/monitor";
import { Chart } from "@antv/g2";
export default {
  name: "borkenLine",
  props: ["item"],
  data() {
    return {
      line: false,
      borkenLineVal: true,
      barChart: false,
      peiChartVal: false,
      usefulVal: {},
      showTitle: "",
      show: "",
      bgColor: "",
    };
  },
  mounted() {
    this.brokenLine();
  },
  created() {
    this.showTitle = this.item.showTitle;
    this.bgColor = this.item.showBgColor;
  },
  watch: {
    "item.showTitle": function (val) {
      this.showTitle = val;
    },
    "item.showBgColor": function (val) {
      this.bgColor = val;
    },
  },
  methods: {
    cancel() {
      this.show = !this.show;
    },
    // // 接口请求走势图
    brokenLine() {
      const data = [
        { country: "Asia", year: "1750", value: 502 },
        { country: "Asia", year: "1800", value: 635 },
        { country: "Asia", year: "1850", value: 809 },
        { country: "Asia", year: "1900", value: 5268 },
        { country: "Asia", year: "1950", value: 4400 },
        { country: "Asia", year: "1999", value: 3634 },
        { country: "Asia", year: "2050", value: 947 },
        { country: "Africa", year: "1750", value: 106 },
        { country: "Africa", year: "1800", value: 107 },
        { country: "Africa", year: "1850", value: 111 },
        { country: "Africa", year: "1900", value: 1766 },
        { country: "Africa", year: "1950", value: 221 },
        { country: "Africa", year: "1999", value: 767 },
        { country: "Africa", year: "2050", value: 133 },
        { country: "Europe", year: "1750", value: 163 },
        { country: "Europe", year: "1800", value: 203 },
        { country: "Europe", year: "1850", value: 276 },
        { country: "Europe", year: "1900", value: 628 },
        { country: "Europe", year: "1950", value: 547 },
        { country: "Europe", year: "1999", value: 729 },
        { country: "Europe", year: "2050", value: 408 },
        { country: "Oceania", year: "1750", value: 200 },
        { country: "Oceania", year: "1800", value: 200 },
        { country: "Oceania", year: "1850", value: 200 },
        { country: "Oceania", year: "1900", value: 460 },
        { country: "Oceania", year: "1950", value: 230 },
        { country: "Oceania", year: "1999", value: 300 },
        { country: "Oceania", year: "2050", value: 300 },
      ];
      const chart = new Chart({
        container: "borkenLines",
        autoFit: true,
        height: 500,
      });

      chart.data(data);
      chart.scale("year", {
        type: "linear",
        tickInterval: 50,
      });
      chart.scale("value", {
        nice: true,
      });

      chart.tooltip({
        showCrosshairs: true,
        shared: true,
      });

      chart.area().adjust("stack").position("year*value").color("country");
      chart.line().adjust("stack").position("year*value").color("country");

      chart.interaction("element-highlight");

      chart.render();
    },
  },
};
</script>

<style lang="scss" scoped>
.line-card {
  height: 100%;
  background: #fff;
  .center {
    position: relative;
  }
  .card-title {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  #titleTop {
    position: absolute;
    top: 50%;
    right: 50%;
    transform: translate(50%, -50%);
    margin: 0 auto;
    font-size: 16px;
    text-align: center;
  }

  .veRing {
    margin: 0 auto;
  }
  .top {
    display: flex;
    margin-left: 20px;
  }
  .el-button {
    display: inline-block;
    height: 40px;
    width: 40px;
    margin-right: 20px;
  }
  .borkenLine {
    width: 100%;
    height: 300px;
  }
  #borkenLines {
    width: 100%;
    height: 100%;
  }
  .demo-form-inline {
    width: 85%;
    margin: 0 auto;
    height: 40px;
  }
  .el-form-item__label {
    color: black;
    font-size: 12px;
  }
  .Box-enter-active,
  .Box-leave-active {
    transition: all 2s;
  }
  .Box-enter,
  .Box-leave-to {
    width: 0px;
    height: 0px;
  }
}
.unshow-line-card {
  border: 0px;
  height: 100%;
  background: #f5f9fa;
  .center {
    position: relative;
  }
  .card-title {
    width: 100%;
    display: flex;
    align-items: center;
  }
  #titleTop {
    position: absolute;
    top: 50%;
    right: 50%;
    transform: translate(50%, -50%);
    margin: 0 auto;
    font-size: 16px;
    text-align: center;
  }

  .veRing {
    margin: 0 auto;
  }
  .top {
    margin-left: 20px;
    width: 60%;
    display: flex;
    justify-content: flex-start;
  }
  .el-button {
    display: inline-block;
    height: 40px;
    width: 40px;
    margin-right: 20px;
  }
  .borkenLine {
    width: 100%;
    height: 300px;
  }
  #borkenLines {
    width: 100%;
    height: 100%;
  }
  .demo-form-inline {
    width: 85%;
    margin: 0 auto;
    height: 40px;
  }
  .el-form-item__label {
    color: black;
    font-size: 12px;
  }
  .Box-enter-active,
  .Box-leave-active {
    transition: all 2s;
  }
  .Box-enter,
  .Box-leave-to {
    width: 0px;
    height: 0px;
  }
}
</style>
