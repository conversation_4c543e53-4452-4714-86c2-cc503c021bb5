import {
  login,
  smscode as smscodeLogin,
  logout,
  getInfo,
  selectByConfigCode
} from "@/api/login";
import { findEnableTheme } from "@/api/system/theme";
import {
  getToken,
  setToken,
  removeToken
} from "@/utils/auth";
import {cmdSM2Encrypt} from "@/utils/sm2Encryptor";
import updateTheme from "@/utils/updateTheme";
import store from "../../store";
const user = {
  state: {
    token: getToken(),
    name: "",
    orgId: "",
    avatar: "",
    roles: [],
    permissions: [],
    userInfo: {},
    customParam: {},  //行业
    authorities:[]    //角色
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token;
    },
    SET_NAME: (state, name) => {
      state.name = name;
    },
    SET_ORG_ID: (state, orgId) => {
      state.orgId = orgId;
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar;
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles;
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions;
    },
    SET_USER_INFO: (state, userInfo) => {
      state.userInfo = userInfo;
    },
    SET_CUSTOM_PARAM: (state, customParam) => {
      state.customParam = customParam;
    },
    SET_AUTHORITIES: (state, authorities) => {
      state.authorities = authorities;
    }
  },

  actions: {
    // 登录
    Login({
      commit
    }, userInfo) {
      const uni_name = userInfo.uni_name.trim();
      const inpk = userInfo.inpk;
      const captcha = userInfo.captcha;
      const token= userInfo.token;
      // const uid= userInfo.uid;
      const tenantId = userInfo.tenantId;
      const loginType = userInfo.loginType;
      const pgp = userInfo.pgp;

      return new Promise((resolve, reject) => {
        cmdSM2Encrypt(inpk).then((r)=>{
          if(r != "" && r != undefined){
            const loginForm = new FormData();
            loginForm.append("username", uni_name);
            loginForm.append("password", r);
            loginForm.append("captcha", captcha);
            loginForm.append("token", token);
            // loginForm.append("uid", uid);
            loginForm.append("tenantId",tenantId);
            loginForm.append("loginType",loginType);
            //loginForm.append("pgp",pgp);
            sessionStorage.setItem("pgp",pgp)
            login(loginForm)
              .then(res => {
                if (res.success) {
                  setToken(res.data.value);
                  commit("SET_TOKEN", res.data.value);
                  resolve(res.data);
                } else {
                  reject(res.message);
                }
              })
              .catch(error => {
                reject("用户名或密码错误！");
              });
          }
        });
      });

    },
    smscodeLogin({
      commit
    }, userInfo) {
      const mobile = userInfo.phone.trim();
      const smscode = userInfo.captcha.trim();
      const tenantId = userInfo.tenantId
      return new Promise((resolve, reject) => {
        const loginForm = new FormData();
        loginForm.append("mobile", mobile);
        loginForm.append("smscode", smscode);
        loginForm.append("tenantId",tenantId)
        smscodeLogin(loginForm)
          .then(res => {
            if (res.success) {
              setToken(res.data.value);
              commit("SET_TOKEN", res.data.value);
              resolve(res.data);
            } else {
              reject(res.message);
            }
          })
          .catch(error => {
            reject("系统内部异常，请联系管理员处理！");
          });
      });
    },
    // 获取用户信息
    GetInfo({
      commit,
      state
    }) {
      return new Promise((resolve, reject) => {
        getInfo({
          uid: getToken() || ''
        })
          .then(res => {
            // 先根据租户获取可用的主题，如果没有从系统默认里面获取
            findEnableTheme({ tenantId: res.customParam.tenantId }).then(r => {
              const themeOption = r.data
              Object.keys(themeOption).forEach(i => {
                store
                  .dispatch("settings/changeSetting", {
                    key: i,
                    value: themeOption[i]
                  })
                if (i === 'theme') {
                  updateTheme(themeOption[i])
                }
              })
            })
            commit("SET_USER_INFO", res);
            commit("SET_PERMISSIONS", res.authorities);
            if (res.authorityList && res.authorityList.length > 0) {
              // 验证返回的roles是否是一个非空数组
              commit("SET_ROLES", res.authorityList);
            } else {
              commit("SET_ROLES", ["ROLE_DEFAULT"]);
            }
            commit("SET_NAME", res.staffName);
            commit("SET_ORG_ID", res.orgId);
            commit("SET_CUSTOM_PARAM", res.customParam);
            commit("SET_AVATAR", require("@/assets/images/profile.jpg"));
            resolve(res);
          })
          .catch(error => {
            reject(error);
          });
      });
    },

    // 退出系统
    LogOut({
      commit,
      state
    }) {
      return new Promise((resolve, reject) => {
        logout(state.token)
          .then(() => {
            commit("SET_TOKEN", "");
            commit("SET_ROLES", []);
            commit("SET_PERMISSIONS", []);
            removeToken();
            resolve();
          })
          .catch(error => {
            reject(error);
          });
      });
    },

    // 前端 登出
    FedLogOut({
      commit
    }) {
      return new Promise(resolve => {
        commit("SET_TOKEN", "");
        removeToken();
        resolve();
      });
    }
  }
};

export default user;
