<template>
  <div class="formitem">
    <uni-section :title="datas.config.shopName" subTitle="元素可自定义input/select/radio/check等表单组件" type="line" padding>
      <el-input
        v-model="datas.config.copywriting"
        placeholder="请输入右侧内容"
        maxlength="8"
      />
    </uni-section>
    <!-- 删除组件 -->
    <slot name="deles" />
  </div>
</template>

<script>
import uniSection from "@/components/UniUI/uni-section"
export default {
  name: 'formitem',
  components: { uniSection },
  props: {
    datas: Object,
  },
  data() {
    return {
      value: '',
    }
  },
}
</script>

<style scoped lang="less">
.formitem {
  position: relative;
}
</style>
