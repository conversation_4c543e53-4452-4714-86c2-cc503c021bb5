<template>
  <el-card
    :class="this.bgColor === true ? 'hotSearch-card' : 'unshow-hotSearch-card'"
    v-loading="Loading"
    shadow="never"
  >
    <div slot="header" v-if="show">
      <span class="card-title">
        <span> <i class="icon iconfont"></i>热搜 </span>
      </span>
    </div>
    <div class="hot home-card-body-news">
      <div v-for="(item, index) in newList" :key="index">
        <a
          :href="
            'https://www.toutiao.com/amos_land_page/?category_name=topic_innerflow&event_type=hot_board&topic_id=' +
            item.id
          "
          target="
          _blank"
          style="display: flex; margin-bottom: 10px"
        >
          <div
            :class="
              index == 0
                ? 'eIndex'
                : '' || index == 1
                ? 'e2Index'
                : '' || index == 2
                ? 'e3Index'
                : ''
            "
            style="margin-right: 20px"
          >
            {{ index + 1 }}
          </div>
          <div>{{ item.word }}</div>
          <div class="hotFont" v-if="item.words_type">热</div>
        </a>
      </div>
    </div>
  </el-card>
</template>

<script>
import { getDate } from "@/api/hotSearch/index";
export default {
  name:"hotSearch",
  props: ["item"],
  data() {
    return {
      show: "",
      Loading: false,
      list: [],
      newList: [],
      category_name: "topic_innerflow",
      event_type: "hot_board",
      bgColor: "",
    };
  },
  mounted() {
    this.getDates();
  },
  created() {
    this.show = this.item.showTitle;
    this.bgColor = this.item.showBgColor;
  },
  watch: {
    "item.showTitle": function (val) {
      this.show = val;
    },
    "item.showBgColor": function (val) {
      this.bgColor = val;
    },
  },
  methods: {
    getDates() {
      getDate().then((res) => {
        this.list = res.data.data[0].words;
        this.newList = this.list.slice(0, 10);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.hotSearch-card {
  height: 100%;
  background: #fff;
}
.unshow-hotSearch-card {
  height: 1005;
  background: #fff;
  border: 0px;
}
.eIndex {
  color: red;
}
.e2Index {
  color: blue;
}
.e3Index {
  color: burlywood;
}
.home-card-body {
  margin-top: -20px;
}
.hotFont {
  width: 20px;
  height: 20px;
  background: red;
  margin: 2px 0 0 5px;
  text-align: center;
  line-height: 20px;
  color: white;
  border-radius: 5px;
  font-size: 14px;
}
</style>
