<template>
  <div class="investigate" @click="guanbi">
    <!-- 内容 -->
    <div class="title">{{ datas.config.title }}</div>
    <uni-section :title="datas.config.moduleTitle" type="line">
      <div
        class="rescon"
        v-for="(item1, index1) in datas.data"
        :key="index1"
        @mouseleave="leave()"
      >
        <!-- 输入框 -->
        <div v-if="item1.type === 0">
          <uni-forms-item :label="item1.name" required>
            <el-input
              :value="item1.value2"
              :placeholder="item1.value"
              maxlength="8"
            />
          </uni-forms-item>
        </div>

        <!-- 下拉框 -->
        <div v-if="item1.type === 1" class="xiala">
          <uni-forms-item :label="item1.name" required>
            <el-select placeholder="请选择" v-model="selectValue">
              <el-option
                v-for="(item, index) in item1.value1"
                :key="index"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
          </uni-forms-item>
        </div>

        <!-- 单选框 -->
        <div v-if="item1.type === 2" class="xiala">
          <uni-forms-item :label="item1.name" required>
            <el-radio-group v-model="radioValue" style="margin-top: 5px;">
              <el-radio
                v-for="(item, index) in item1.value1"
                :key="index"
                :label="item"
                :value="item">
              </el-radio>
            </el-radio-group>
          </uni-forms-item>
        </div>

        <!-- 复选框 -->
        <div v-if="item1.type === 3" class="xiala">
          <uni-forms-item :label="item1.name" required>
            <el-checkbox-group v-model="checkboxValue" style="margin-top: 5px;">
              <el-checkbox
                v-for="(item, index) in item1.value1"
                :key="index"
                :label="item"
                :value="item">
              </el-checkbox>
            </el-checkbox-group>
          </uni-forms-item>
        </div>
      </div>
    </uni-section>
    <!-- 删除组件 -->
    <slot name="deles" />
  </div>
</template>

<script>
import uniSection from "@/components/UniUI/uni-section";
import uniForms from "@/components/UniUI/uni-forms/uni-forms";
import uniFormsItem from "@/components/UniUI/uni-forms/uni-forms-item";
export default {
  name: 'investigate',
  components: {
    uniSection,
    uniForms,
    uniFormsItem,
  },
  data() {
    return {
      data: [],
      selectValue: '',
      radioValue: '',
      checkboxValue: '',
      range: [
        { value: 0, text: "选项1" },
        { value: 1, text: "选项2" },
      ],
    }
  },
  props: {
    datas: Object,
  },
  created() {},
  mounted() {},
  methods: {
    //点击显示下拉框
    showpic(index1) {
      event.stopPropagation()
      this.datas.data.forEach((el) => {
        el.showPicker = false
      })
      this.datas.data[index1].showPicker = !this.datas.data[index1]
        .showPicker
    },

    // //下拉选择
    xuanze(index1) {
      this.datas.data[index1].showPicker = false
    },

    //关闭下拉选项
    guanbi() {
      this.datas.data.forEach((el) => {
        el.showPicker = false
      })
    },
    leave() {
      this.datas.data.forEach((el) => {
        el.showPicker = false
      })
    },
    //
  },
  watch: {},
}
</script>

<style scoped lang="less">
.investigate {
  position: relative;
  padding: 0 6px;
}
form select {
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
}
.xiala {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  box-sizing: border-box;
  width: 100%;
  padding: 10px 16px;
  // overflow: hidden;
  color: #323233;
  font-size: 14px;
  line-height: 24px;
  background-color: #fff;
  .titlename {
    width: 5.6em;
    margin-right: 12px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
select {
  border: none;
  outline: none;
}
.title {
  text-align: center;
  padding: 10px;
  font-size: 18px;
  font-weight: bold;
}
/deep/.el-form-item__label {
  text-align: center;
  width: 100% !important;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
/deep/.el-form-item__content {
  margin-left: 100% !important;
}
/* 上传图片按钮 */
.uploadImg {
  width: 200px;
  height: 40px;
  margin-top: 20px;
}
.button {
  padding: 12px 24px;
  button {
    width: 100%;
    background: rgb(48, 116, 243);
    color: #fff;
    padding: 8px;
    border-radius: 20px;
    text-align: center;
    font-size: 14px;
    border: none;
  }
}
.select {
  position: relative;
  width: 100%;
  .readinput {
    display: block;
    box-sizing: border-box;
    width: 100%;
    min-width: 0;
    margin: 0;
    padding: 0;
    color: #323233;
    line-height: inherit;
    text-align: left;
    background-color: transparent;
    border: 0;
    resize: none;
    cursor: default;
  }
}
.ultext {
  display: none;
  height: 0;
  overflow: hidden;
  transition: all linear 1s;
  background: #fff;
  z-index: 100;
  border-radius: 6px;
  box-shadow: 0 0 16px 1px rgba(200, 200, 200, 0.5);
  li {
    padding: 4px 16px;
    border-bottom: 1px solid #eeeeee;
    &:hover {
      background: #c3d4f5;
    }
  }
}
.ulshow {
  display: block;
  height: auto;
  max-height: 200px;
  overflow-y: auto;
  margin-top: 6px;
  position: absolute;
}
</style>
