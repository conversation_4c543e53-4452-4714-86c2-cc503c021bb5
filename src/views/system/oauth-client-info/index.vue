<template>
  <div class="app-container">
    <el-card shadow="never">

      <!-- 查询条件表单 -->
      <el-form :model="queryParams" ref="queryParams" v-show="showSearch" :inline="true">
        <el-row>
          <el-form-item label="系统入驻名" prop="clientApp">
            <el-input
              v-model="queryParams.clientApp"
              placeholder="请输入系统入驻名"
              clearable
              size="small"
              style="width: 200px;margin-right:20px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="系统负责单位" prop="clientUnit">
            <el-input
              v-model="queryParams.clientUnit"
              placeholder="请输入系统负责单位"
              clearable
              size="small"
              style="width: 200px;margin-right:20px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="联系人" prop="clientContact">
            <el-input
              v-model="queryParams.clientContact"
              placeholder="请输入联系人"
              clearable
              size="small"
              style="width: 200px;margin-right:20px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="联系人电话" prop="clientTel">
            <el-input
              v-model="queryParams.clientTel"
              placeholder="请输入联系人电话"
              clearable
              size="small"
              style="width: 200px;margin-right:20px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-row>
          
      </el-form>

      <!-- 搜索扩展工具栏 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
          >新增
          </el-button>
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <!-- 列表内容 -->
      <el-table v-loading="loading" :data="dataList">
        <el-table-column label="系统入驻名" align="left" prop="clientApp" :show-overflow-tooltip="true"/>
        <el-table-column label="系统入驻Key" align="left" prop="clientKey" :show-overflow-tooltip="true"/>
        <el-table-column label="系统负责单位" align="left" prop="clientUnit" :show-overflow-tooltip="true"/>
        <el-table-column label="联系人" align="left" prop="clientContact" :show-overflow-tooltip="true"/>
        <el-table-column label="联系人电话" align="left" prop="clientTel" :show-overflow-tooltip="true"/>
        <el-table-column label="备注" align="left" prop="clientRemark" :show-overflow-tooltip="true"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              v-hasPermi="['sys:oauth-client-info:update']"
              @click="handleUpdate(scope.row)"
              :loading="reloadId === scope.row.clientId && reloadType === 'update'"
            >编辑
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleGetSecret(scope.row)"
              :loading="reloadId === scope.row.clientId && reloadType === 'getSecret'"
            >查看密钥
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleDebug(scope.row)"
              :loading="reloadId === scope.row.clientId && reloadType === 'debug'"
            >调试
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              v-hasPermi="['sys:oauth-client-info:delete']"
              @click="handleDelete(scope.row)"
              :loading="reloadId === scope.row.clientId && reloadType === 'delete'"
            >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 新增或编辑对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="open"
        width="1000px"
        append-to-body
        :close-on-press-escape="false"
        @close="cancel"
        v-dialogDrag
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="150px">
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="系统入驻名" prop="clientApp">
                <el-input
                  v-model="form.clientApp"
                  placeholder="请输入系统入驻名"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="系统负责单位" prop="clientUnit">
                <el-input
                  v-model="form.clientUnit"
                  placeholder="请输入系统负责单位"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系人" prop="clientContact">
                <el-input
                  v-model="form.clientContact"
                  placeholder="请输入联系人"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系人电话" prop="clientTel">
                <el-input
                  v-model="form.clientTel"
                  placeholder="请输入联系人电话"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="系统访问地址" prop="clientUrl">
                <el-input
                  v-model="form.clientUrl"
                  placeholder="请输入系统访问地址"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="系统回调地址" prop="clientRedirect">
                <el-input
                  v-model="form.clientRedirect"
                  placeholder="请输入系统回调地址"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系人邮箱" prop="clientEmail">
                <el-input
                  v-model="form.clientEmail"
                  placeholder="联系人邮箱将作为查看密钥的凭证,请妥善输入"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="备注" prop="clientRemark">
                <el-input
                  v-model="form.clientRemark"
                  type="textarea"
                  placeholder="请输入备注"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="saveLoading">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>

      <!-- 查看密钥对话框 -->
      <el-dialog
        title="查看密钥"
        :visible.sync="openGetSecret"
        width="600px"
        append-to-body
        :close-on-press-escape="false"
        @close="cancelGetSecret"
        v-dialogDrag
      >
        <el-form ref="formGetSecret" :model="formGetSecret" :rules="rulesGetSecret" label-width="150px">
          <el-row :gutter="24">
            <el-col :span="22">
              <el-form-item label="联系人邮箱" prop="clientEmail">
                <el-input
                  v-model="formGetSecret.clientEmail"
                  placeholder="请输入当前入驻系统的联系人邮箱"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitFormGetSecret" :loading="saveLoading">查 询</el-button>
          <el-button @click="cancelGetSecret">取 消</el-button>
        </div>
      </el-dialog>

      <!-- 单点登录调试对话框 -->
      <el-dialog
        title="单点登录调试"
        :visible.sync="openDebug"
        width="700px"
        append-to-body
        :close-on-press-escape="false"
        @close="cancelDebug"
        v-dialogDrag
      >
        <el-form ref="debugForm" :model="debugForm" label-width="150px">
          <!-- 第一步 -->
          <el-row :gutter="24">
            <el-col :span="24">
              <p style="font-weight: bold;color: red;">一、根据当前登录用户动态生成 mmy 参数;</p>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="跳转参数 [mmy]" prop="mmy">
                <el-input
                  v-model="debugForm.mmy"
                  type="textarea"
                  width="800px"
                  placeholder="点击右侧按钮,系统会根据当前登录用户的登录账号,动态生成跳转参数 [mmy]"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" plain @click="createMmy" :loading="createMmyLoading">点我生成参数</el-button>
            </el-col>
          </el-row>
          <!-- 第二步 -->
          <el-row :gutter="24">
            <el-col :span="24">
              <p style="font-weight: bold;color: red;">二、输入接入系统的跳转地址;</p>
              <p style="color: darkgray;margin-left: 28px;">门户应用打开接入系统的跳转地址时,会默认携带 [mmy] 参数进行跳转</p>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="跳转地址" prop="clientUrl">
                <el-input
                  v-model="debugForm.clientUrl"
                  type="textarea"
                  width="800px"
                  placeholder="输入目标系统的跳转地址"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" plain @click="createClientUrl">拼接跳转参数</el-button>
            </el-col>
          </el-row>
          <!-- 第三步 -->
          <!-- 第三步 - (1) -->
          <el-row :gutter="24">
            <el-col :span="24">
              <p style="font-weight: bold;color: red;">三、此时接入目标系统会解析 [mmy] ,并请求门户认证中心接口,获取授权码 [code];</p>
              <p style="color: darkgray;margin-left: 28px;">(1) 此处我们模拟接入系统,向门户认证中心 [获取授权码接口] 发起请求,首先填入接入系统对应参数</p>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="10">
              <el-form-item label="系统入驻Key" prop="clientKey">
                <el-input
                  v-model="debugForm.clientKey"
                  placeholder="请输入系统入驻Key"
                  style="width: 180px;"
                />
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="重定向地址" prop="clientRedirect">
                <el-input
                  v-model="debugForm.clientRedirect"
                  placeholder="请输入重定向地址"
                  style="width: 180px;"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="跳转参数 [mmy]" prop="mmySource">
                <el-input
                  v-model="debugForm.mmySource"
                  type="textarea"
                  width="800px"
                  placeholder="可使用 [第一步] 操作生成"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 第三步 - (2) -->
          <el-row :gutter="24">
            <el-col :span="24">
              <p style="color: darkgray;margin-left: 28px;">(2) 根据上方的配置,生成 [获取授权码接口] 的接口所需要的参数</p>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="接口所需参数" prop="decideParams">
                <el-input
                  v-model="debugForm.decideParams"
                  type="textarea"
                  width="800px"
                  placeholder="点击右侧按钮,系统会根据上面输入的系统参数,生成 [获取授权码接口] 所需要的请求参数"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" plain @click="createDecideParams" :loading="createDecideParamsLoading">生成请求参数</el-button>
            </el-col>
          </el-row>
          <!-- 第三步 - (3) -->
          <el-row :gutter="24">
            <el-col :span="24">
              <p style="color: darkgray;margin-left: 28px;">(3) 拼接上第(2)步生成的请求参数,调用 [获取授权码] 接口</p>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="[获取授权码] URL" prop="decideApiUrl">
                <el-input
                  v-model="debugForm.decideApiUrl"
                  type="textarea"
                  width="800px"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="[获取授权码] 响应" prop="code">
                <el-input
                  v-model="debugForm.code"
                  type="textarea"
                  width="800px"
                  placeholder="点击右侧按钮,调用 [获取授权码] 接口,获取响应结果,解析 [code] 授权码参数"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" plain @click="getDecideCode" :loading="getDecideCodeLoading">调用接口</el-button>
            </el-col>
          </el-row>
          <!-- 第三步 - (4) -->
          <el-row :gutter="24">
            <el-col :span="24">
              <p style="color: darkgray;margin-left: 28px;">(4) 使用第(3)步获取的授权码,调用 [获取令牌] 接口</p>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="10">
              <el-form-item label="授权码 [code]" prop="code">
                <el-input
                  v-model="debugForm.code"
                  placeholder="请输入授权码"
                  style="width: 180px;"
                />
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="[client_id]" prop="clientKey">
                <el-input
                  v-model="debugForm.clientKey"
                  placeholder="请输入系统入驻Key"
                  style="width: 180px;"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="10">
              <el-form-item label="[client_secret]" prop="clientSecret">
                <el-input
                  v-model="debugForm.clientSecret"
                  placeholder="请输入系统入驻密钥"
                  style="width: 180px;"
                />
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="[redirect_uri]" prop="clientRedirect">
                <el-input
                  v-model="debugForm.clientRedirect"
                  placeholder="请输入重定向地址"
                  style="width: 180px;"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="[获取令牌] URL" prop="checkTokenApiUrl">
                <el-input
                  v-model="debugForm.checkTokenApiUrl"
                  type="textarea"
                  width="800px"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="[获取令牌] 响应" prop="checkTokenResponse">
                <el-input
                  v-model="debugForm.checkTokenResponse"
                  type="textarea"
                  width="800px"
                  placeholder="点击右侧按钮,调用 [获取令牌] 接口,获取响应结果,解析 [token] 获取令牌"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" plain @click="getCheckToken" :loading="getCheckTokenLoading">调用接口</el-button>
            </el-col>
          </el-row>
          <!-- 第四步 -->
          <el-row :gutter="24">
            <el-col :span="24">
              <p style="font-weight: bold;color: red;">四、至此,我们便获取到了门户认证中心统一令牌 [token] ;</p>
              <p style="color: darkgray;margin-left: 28px;">当请求携带统一令牌 [token] 时,即可调用门户接口;</p>
              <p style="color: darkgray;margin-left: 28px;">建议将请求头 Cookie 中新增 unifast_token 的Key (value为统一令牌) 作为标识尝试其他接口的调用;</p>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="10">
              <el-form-item label="统一令牌 [token]" prop="token">
                <el-input
                  v-model="debugForm.token"
                  placeholder="请输入系统入驻Key"
                  style="width: 180px;"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="resetDebug">重 置</el-button>
          <el-button @click="cancelDebug">取 消</el-button>
        </div>
      </el-dialog>

    </el-card>
  </div>
</template>

<script>
import {add, del, findList, getParam, getSecret} from "/src/api/system/oauthClientInfo";
import {getInfo} from "/src/api/login";
import {getToken} from "/src/utils/auth";
import ssoCrypto from "/src/utils/ssoCrypto";
import axios from "axios";

export default {
  name: "OauthClientInfo",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 提交遮罩层
      saveLoading: false,
      // 遮罩层
      createMmyLoading: false,
      // 遮罩层
      createDecideParamsLoading: false,
      // 遮罩层
      getDecideCodeLoading: false,
      // 遮罩层
      getCheckTokenLoading: false,
      // decide 接口无参数时的原始地址
      decideApiUrlRaw: "",
      // 数据行锁ID
      reloadId: undefined,
      // 数据行锁操作类型
      reloadType: undefined,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 参数表格数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示单点登录调试弹出层
      openDebug: false,
      // 是否显示查看密钥弹出层
      openGetSecret: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        clientApp: undefined,
        clientUnit: undefined,
        clientContact: undefined,
        clientTel: undefined
      },
      // 表单参数
      form: {},
      // 调试表单参数
      debugForm: {},
      // 查看密钥表单参数
      formGetSecret: {},
      // 表单校验
      rules: {
        clientApp: [
          {required: true, message: "系统入驻名不能为空", trigger: "blur"},
          {max: 30, message: "长度需要小于 30 个字符", trigger: "blur"},
        ],
        clientUnit: [
          {required: true, message: "系统负责单位不能为空", trigger: "blur"},
          {max: 50, message: "长度需要小于 50 个字符", trigger: "blur"},
        ],
        clientContact: [
          {required: true, message: "联系人不能为空", trigger: "blur"},
          {max: 20, message: "长度需要小于 50 个字符", trigger: "blur"},
        ],
        clientTel: [
          {required: true, message: "联系人电话不能为空", trigger: "blur"},
          {pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur"},
        ],
        clientEmail: [
          {required: true, message: "联系人邮箱不能为空", trigger: "blur"},
        ],
      },
      // 查看密钥表单校验
      rulesGetSecret: {
        clientEmail: [
          {required: true, message: "联系人邮箱不能为空", trigger: "blur"},
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 数据行上锁
    setLoad(id, type) {
      this.reloadId = id;
      this.reloadType = type;
    },
    // 数据行锁重置
    resetLoad() {
      this.reloadId = undefined;
      this.reloadType = undefined;
    },
    // 重置按钮事件
    resetQuery() {
      this.resetForm("queryParams");
      this.handleQuery();
    },
    // 新增\修改对话框取消按钮事件
    cancel() {
      this.resetForm("form");
      this.resetLoad();
      this.open = false;
    },
    // 取消查看密钥
    cancelGetSecret() {
      this.resetForm("formGetSecret");
      this.resetLoad();
      this.openGetSecret = false;
    },
    // 查看密钥按钮事件
    handleGetSecret(row) {
      this.resetForm("formGetSecret");
      this.openGetSecret = true;
      this.reloadType = "getSecret";
      this.formGetSecret.clientId = row.clientId;
    },
    // 提交查看密钥
    submitFormGetSecret() {
      this.$refs["formGetSecret"].validate((valid) => {
        if (valid) {
          this.saveLoading = true;
          getSecret({
            clientId: this.formGetSecret.clientId,
            clientEmail: this.formGetSecret.clientEmail,
          }).then((res) => {
            this.saveLoading = false;
            if (!res.success) {
              this.$message.error(res.message);
            } else {
              this.openGetSecret = false;
              this.getList();
              this.$confirm(
                '请妥善保管好您的系统密钥 [' + res.data + ']',
                "系统密钥",
                {
                  confirmButtonText: "确定",
                  type: "warning",
                }
              ).catch(() => {});
            }
          });
        }
      });
    },
    // [USUALLY]
    // @param variable : 期望获取的路径参数Key;
    // @param url : 进行操作的url地址,不传递时默认为当前页面路径;
    // @describe 获取当前页面路径上传入的路径参数值.
    getQueryVariable (variable, url) {
      var query;
      if (!this.isNull(url)) {
        if (url.split("?")[1]) {
          query = url.split('?')[1];
        } else {
          return false;
        }
      } else {
        query = window.location.search.substring(1);
      }
      var vars = query.split("&");
      for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] === variable) {
          return pair[1];
        }
      }
      return false;
    },
    // [OFTEN]
    // @param key : 期望新增或更新的路径参数Key;
    // @param value : 期望新增或更新的路径参数Value;
    // @param url : 进行操作的url地址,不传递时默认为当前页面路径;
    // @describe 将入参 url 中路径参数 key 对应的值替换为 value,没有参数则添加此参数,返回替换后的url字符串.
    addOrUpdateQueryVariable (key, value, url) {
      var query = "";
      if (!this.isNull(url)) {
        if (url.split("?")[1]) {
          query = url.split('?')[1];
        }
      } else {
        url = window.location.href;
        query = window.location.search.substring(1);
      }
      var vars = query.split("&");
      for (var i = 0; i < vars.length; i++) {
        if (vars[i] !== "") {
          var pair = vars[i].split("=");
          if (pair[0] === key) {
            var target = pair[0] + "=" + pair[1];
            return url.replace(target, pair[0] + "=" + value);
          }
        }
      }
      var param = key + "=" + value;
      return url.indexOf("?") !== -1 ? url + "&" + param : url + "?" + param;
    },
    // [OFTEN]
    // @param val : 判断对象;
    // @describe 根据入参对象的类型判断是否为逻辑意义上的空值.
    isNull (val) {
      if (typeof(val) == "undefined" || !val) {
        return true;
      }
      switch (typeof(val)) {
        case "string":
          return val.trim() === "";
        case "number":
          return false;
        case "object":
          return val.length === 0;
        default:
          return false;
      }
    },

    // ┏ ━ ━ ━ ━ 单点登录调试 ━ ━ ━ ━ ┓
    // 重置调试
    resetDebug() {
      this.resetForm("debugForm");
    },
    // 取消调试
    cancelDebug() {
      this.resetForm("debugForm");
      this.resetLoad();
      this.openDebug = false;
    },
    // 开始调试
    handleDebug(row) {
      this.debugForm = row;
      let gateway = window.location.origin + process.env.VUE_APP_BASE_API;
      this.decideApiUrlRaw =  gateway + "/auth/decide";
      this.$set(this.debugForm, 'decideApiUrl', this.decideApiUrlRaw);
      this.$set(this.debugForm, 'checkTokenApiUrl', gateway + "/auth/oauth/token");
      this.openDebug = true;
    },
    // 生成 mmy 参数
    createMmy() {
      this.createMmyLoading = true;
      getInfo({token: getToken()}).then((res) => {
        this.createMmyLoading = false;
        let mmy = ssoCrypto(`${res.loginName}:${res.tenantId}`);
        this.$delete(this.debugForm, 'mmy');
        this.$set(this.debugForm, 'mmy', mmy);
        this.$delete(this.debugForm, 'mmySource');
        this.$set(this.debugForm, 'mmySource', mmy);
      }).catch((error) => {
        this.createMmyLoading = false;
        this.$message.error('当前登录用户信息查询失败');
      });
    },
    // 拼接跳转参数
    createClientUrl() {
      if (this.debugForm.clientUrl) {
        let clientUrl = this.addOrUpdateQueryVariable("mmy", this.debugForm.mmy, this.debugForm.clientUrl);
        this.$delete(this.debugForm, 'clientUrl');
        this.$set(this.debugForm, 'clientUrl', clientUrl);
      } else {
        this.$message.error("请先输入跳转地址");
      }
    },
    // 生成 decide 接口请求参数
    createDecideParams() {
      this.createDecideParamsLoading = true;
      getParam({
        "clientKey": this.debugForm.clientKey,
        "clientRedirect": this.debugForm.clientRedirect,
        "mmySource": this.debugForm.mmySource
      }).then((res) => {
        this.createDecideParamsLoading = false;
        this.$delete(this.debugForm, 'decideParams');
        this.$set(this.debugForm, 'decideParams', res.data);
        // 将参数拼接至接口地址后面
        this.$delete(this.debugForm, 'decideApiUrl');
        this.$set(this.debugForm, 'decideApiUrl', this.decideApiUrlRaw + "?" + res.data);
      }).catch((error) => {
        this.createDecideParamsLoading = false;
        this.$message.error('当前登录用户信息查询失败');
      });
    },
    // 调用 decide 接口
    getDecideCode() {
      this.getDecideCodeLoading = true;
      let _this = this;
      axios.get(this.debugForm.decideApiUrl,{
      }).then(function (res) {
        _this.getDecideCodeLoading = false;
        if(res.request.responseURL) {
          let url =  res.request.responseURL;
          let code = _this.getQueryVariable("code", url);
          _this.$delete(_this.debugForm, 'code');
          _this.$set(_this.debugForm, 'code', code);
        }
      }).catch(err => {
        _this.getDecideCodeLoading = false;
        // 如果跳转地址无法访问或者跨域,将进入 catch
        this.$confirm(
          '请确保重定向地址能正常访问!<p>若重定向地址有网络问题或跨域问题无法访问,可将生成的<b> [获取授权码] URL</b>复制到浏览器中进行查看;<p>或使用开发者工具F12查看携带<b> [code] </b>参数的重定向地址;</p></p>',
          "请求失败",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            dangerouslyUseHTMLString: true,
          }
        ).catch(() => {});
      });
    },
    // 调用 check_token 接口
    getCheckToken() {
      this.getCheckTokenLoading = true;
      let _this = this;
      axios.post(this.debugForm.checkTokenApiUrl, {}, {
        params: {
          "grant_type": "authorization_code",
          "client_id": this.debugForm.clientKey,
          "client_secret": this.debugForm.clientSecret,
          "redirect_uri": this.debugForm.clientRedirect,
          "code": this.debugForm.code,
        }
      }).then(function (res) {
        _this.getCheckTokenLoading = false;
        _this.$delete(_this.debugForm, 'checkTokenResponse');
        _this.$set(_this.debugForm, 'checkTokenResponse', res.data.data.value);
        _this.$delete(_this.debugForm, 'token');
        _this.$set(_this.debugForm, 'token', res.data.data.value);
      }).catch(err => {
        // 异常
        console.log(err)
        _this.getCheckTokenLoading = false;
        this.$message.error('请求失败,请确认参数是否正确');
      });
    },
    // ┗ ━ ━ ━ ━ 单点登录调试 ━ ━ ━ ━ ┛

    // ┏ ━ ━ ━ ━   ━ ━ ━ ━ CRUD基础操作方法 ━ ━ ━ ━   ━ ━ ━ ━ ┓
    // 新增按钮事件
    handleAdd() {
      this.resetForm("form");
      this.open = true;
      this.title = "新增";
      this.reloadType = "add";
    },
    // 提交按钮事件
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.saveLoading = true;
          add(this.form).then((res) => {
            if (!res.success) {
              this.$message.error(res.message);
              this.saveLoading = false;
            } else {
              this.$message.success('保存成功');
              this.open = false;
              this.saveLoading = false;
              this.getList();
              this.$confirm(
                '请妥善保管好您的系统密钥 [' + res.data + ']',
                "系统密钥",
                {
                  confirmButtonText: "确定",
                  type: "warning",
                }
              ).catch(() => {});
            }
          });
        }
      });
    },
    // 删除按钮事件
    handleDelete(row) {
      this.setLoad(row.clientId, "delete");
      this.$confirm(
        '是否确认删除"' + row.clientApp + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(function () {
        return del({"clientId": row.clientId});
      }).then(() => {
        this.resetLoad();
        this.getList();
        this.$message.success('删除成功');
      }).catch(() => {
        this.resetLoad();
      });
    },
    // 编辑按钮事件
    handleUpdate(row) {
      // this.setLoad(row.clientId, "update");
      // update({
      //   tenantId: row.tenantId,
      //   tenantStatus: tenantStatus,
      // }).then((res) => {
      //   if (res.data) {
      //     this.$message.success("操作成功");
      //   } else {
      //     this.$message.error("数据异常！");
      //   }
      //   this.reloadId = undefined;
      //   this.reloadType = undefined;
      //   this.getList();
      // });
    },
    // 搜索按钮事件
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 详情查询
    getOne(row) {
      // this.reset();
      // this.reloadId = row.tenantId;
      // this.reloadType = "select";
      // getById(tenantId).then((res) => {
      //   if (res.data) {
      //     this.form = res.data;
      //     this.open = true;
      //     this.title = "修改租户";
      //   } else {
      //     this.$message.error("数据异常！");
      //   }
      // });
    },
    // 列表查询
    getList() {
      this.loading = true;
      findList({
        ...this.queryParams
      }).then((res) => {
        this.dataList = res.data.records;
        this.total = res.data.total;
        this.loading = false;
      });
    },
    // ┗ ━ ━ ━ ━   ━ ━ ━ ━ CRUD基础操作方法 ━ ━ ━ ━   ━ ━ ━ ━ ┛
  },
};
</script>
