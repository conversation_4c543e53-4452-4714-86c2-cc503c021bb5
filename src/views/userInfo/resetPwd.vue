<template>
  <el-form ref="form" :model="user" :rules="rules" label-width="80px" v-loading="saveLoading">
    <el-form-item label="旧密码" prop="oldPassword">
      <el-input v-model="user.oldPassword" placeholder="请输入旧密码" type="password" :show-password="true" />
    </el-form-item>
    <el-form-item label="新密码" prop="newPassword">
      <el-input v-model="user.newPassword" placeholder="请输入新密码" type="password" :show-password="true" />
    </el-form-item>
    <el-form-item label="确认密码" prop="confirmPassword">
      <el-input v-model="user.confirmPassword" placeholder="请确认密码" type="password" :show-password="true" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" size="mini" @click="submit">保存</el-button>
      <el-button type="danger" size="mini" @click="close">关闭</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { updateUserPwd } from "@/api/system/user";
import {mapState} from "vuex";
import doEncrypt from "@/utils/crypto";

export default {
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.user.newPassword !== value) {
        callback(new Error("两次输入的密码不一致"));
      } else {
        callback();
      }
    };
    return {
      saveLoading: false,
      test: "1test",
      user: {
        oldPassword: undefined,
        newPassword: undefined,
        confirmPassword: undefined,
      },
      // 表单校验
      rules: {
        oldPassword: [
          { required: true, message: "旧密码不能为空", trigger: "blur" }
        ],
        newPassword: [
          { required: true, message: "新密码不能为空", trigger: "blur" },
          { min: 6, max: 20, message: "长度在 6 到 20 个字符", trigger: "blur" }
        ],
        confirmPassword: [
          { required: true, message: "确认密码不能为空", trigger: "blur" },
          { required: true, validator: equalToPassword, trigger: "blur" }
        ]
      }
    };
  },
  computed: {
    ...mapState({
      userInfo : state => state.user.userInfo
    })
  },
  methods: {
    submit() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.saveLoading = true;
          updateUserPwd({
            newPassword: doEncrypt(this.user.newPassword),
            oldPassword: doEncrypt(this.user.oldPassword),
            confirmPassword: doEncrypt(this.user.confirmPassword),
            //userId: this.userInfo.userid,
          }).then(
            response => {
              this.saveLoading = false;
              if (response.success) {
                this.$alert("密码更新成功，请重新登录！", "密码更新成功", {
                  confirmButtonText: "去登陆",
                  showClose: false,
                  callback: (action) => {
                    this.$store.dispatch("LogOut").then(() => {
                      location.href = `/#/login?redirect=${location.pathname}`;
                    });
                    this.reset();
                  },
                });
              } else {
                this.$message.error(response.message);
              }
            }
          );
        }
      });
    },
    // 重置
    reset() {
      this.user = {
        oldPassword: undefined,
        newPassword: undefined,
        confirmPassword: undefined
      };
    },
    close() {
      this.$store.dispatch("tagsView/delView", this.$route);
      window.history.back()
    }
  }
};
</script>
