<template>
  <Container>
    <template slot="header">
<!--        <el-page-header @back="goBack" :content="pageTitle"></el-page-header>-->
        <div class="header innerLayout" style="padding-top: 10px;padding-bottom: 0px;padding-left: 10px;cursor: pointer" >
          <img src="../../../../assets/images/gotoBack.png" height="32" width="32" @click="goBack" />
          <span style="margin-left: 10px;font-size: 0.135rem" :content="pageTitle">返回</span>
        </div>
    </template>

    <div class="info">
      <el-form
        ref="form"
        :model="form"
        :rules="formRules"
        label-width="auto"
      >
        <con-main title="基本信息">
          <div class="m-con">
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="标签编码" prop="tagCode">
                  <el-input
                    v-model="form.tagCode"
                    placeholder="请输入"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="标签名" prop="tagName">
                  <el-input
                    v-model="form.tagName"
                    placeholder="请输入"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="标签类型"
                  prop="tagType"
                  :rules="{
                    required: true,
                    message: '请选择',
                    trigger: 'change',
                  }"
                >
                  <el-select
                    v-model="form.tagType"
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in tagTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="form.tagType" :span="8">
                <el-form-item
                  label="标签值类型"
                  prop="tagTypeValue"
                  :rules="{
                    required: true,
                    message: '请选择',
                    trigger: 'change',
                  }"
                >
                  <el-select
                    v-model="form.tagTypeValue"
                    placeholder="请选择"
                    style="width: 100%"
                    filterable
                    @change="handleChange"
                  >
                    <el-option
                      v-for="item in valueList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <!--              <el-col :span="8" v-if="form.tagType==='1'">-->
              <!--                <el-form-item label="匹配条件" prop="dbMatchType">-->
              <!--                  <el-select-->
              <!--                    v-model="form.dbMatchType"-->
              <!--                    placeholder="请选择"-->
              <!--                    style="width: 100%"-->
              <!--                    filterable-->
              <!--                  >-->
              <!--                    <el-option-->
              <!--                      v-for="item in dbMatchTypeList"-->
              <!--                      :key="item.value"-->
              <!--                      :label="item.label"-->
              <!--                      :value="item.value"-->
              <!--                    >-->
              <!--                    </el-option>-->
              <!--                  </el-select>-->
              <!--                </el-form-item>-->
              <!--              </el-col>-->
              <el-col :span="8">
                <el-form-item label="标签联系人" prop="tagUser">
                  <el-input
                    v-model="form.tagUser"
                    placeholder="请输入"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="标签联系人电话" prop="tagUserPhone">
                  <el-input
                    v-model="form.tagUserPhone"
                    placeholder="请输入"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col v-if="form.tagType === '2'" :span="8">
                <el-form-item label="是否多选值" prop="moreDictValue">
                  <el-select
                    v-model="form.moreDictValue"
                    placeholder="请选择"
                    style="width: 100%"
                    clearable
                  >
                    <el-option label="是" value="1"/>
                    <el-option label="否" value="0"/>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="采集类型" prop="collectionType"
                  :rules="{
                    required: true,
                    message: '请选择',
                    trigger: 'change',
                  }"
                >
                  <el-select
                    v-model="form.collectionType"
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in collectionTypeList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </con-main>
      </el-form>
      <con-main title="数据来源">
        <template #btn>
          <el-button @click="addDbSource">添加数据源</el-button>
        </template>
        <div v-for="(item,index) in dbSourceList">
          <el-form
            :ref="getFormRef(index)"
            :model="item"
            label-width="auto"
          >
            <div class="m-con">
              <el-row :gutter="10">
                <el-col :span="8">
                  <el-form-item
                    label="表名称"
                    prop="dbTableName"
                    placeholder="请输入"
                    :rules="{
                      required: true,
                      message: '请输入',
                      trigger: 'blur',
                    }"
                  >
                    <el-input v-model="item.dbTableName"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item
                    label="字段名"
                    prop="dbTableColumn"
                    placeholder="请输入"
                    :rules="{
                      required: true,
                      message: '请输入',
                      trigger: 'blur',
                    }"
                  >
                    <el-input v-model="item.dbTableColumn"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="字段描述" prop="dbTableDesc">
                    <el-input
                      v-model="item.dbTableDesc"
                      placeholder="请输入"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="字段值描述" prop="dbTableColumnDesc">
                    <el-input
                      v-model="item.dbTableColumnDesc"
                      placeholder="请输入"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-button type="text" @click="removeDb(item)" style="color:red">移出</el-button>
                </el-col>
                <div v-show="false">{{ item.tagName = form.tagName }}--{{ item.tagName }}</div>
                <div v-show="false">{{ item.collectionType = form.collectionType }}--{{ item.collectionType }}</div>
              </el-row>
            </div>
          </el-form>
        </div>
      </con-main>

      <con-main title="字典" v-if="form.tagType === '2'">
        <div class="m-con">
          <div class="dict_search">
            <el-input
              v-model="search"
              size="mini"
              suffix-icon="el-icon-search"
              placeholder="请输入编码或者名称"
              @keyup.enter.native="handleQuery"
            ></el-input>
          </div>
          <div>
            <el-table :data="tableData" style="width: 100%">
              <el-table-column prop="dictKey" label="编码" align="center"/>
              <el-table-column prop="dictValue" label="名称" align="center"/>
            </el-table>
          </div>
        </div>
      </con-main>

      <con-main title="口径">
        <el-form
          ref="form_end"
          :model="form"
          label-width="auto"
        >
          <div class="m-con">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="业务口径" prop="businessCaliber">
                  <el-input
                    v-model="form.businessCaliber"
                    type="textarea"
                    placeholder="请输入"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="加工口径" prop="dataCaliber">
                  <el-input
                    v-model="form.dataCaliber"
                    type="textarea"
                    placeholder="请输入"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </con-main>
    </div>
    <template #footer>
      <div class="footer">
        <el-button @click="goBack" :disabled="submitLoading">取消</el-button>
        <el-button type="primary" @click="submit" :loading="submitLoading">保存</el-button>
      </div>
    </template>
  </Container>
</template>
<script>
import Container from '../components/Container.vue'
import ConMain from '../components/ConMain.vue'
import { checkExist, findFormOne, tagFindOne, tagSave, testDb } from '@/api/sw/tags/tagManage'
import {
  findDictTypeList,
  findDictValueList,
  findDetails
} from '@/api/sw/tags/dictManage'
import message from 'element-ui/packages/message'

const numberMatchList = [{ label: '相等', value: 'eq' }, { label: '大于', value: 'gt' }, { label: '小于', value: 'lt' }
  , { label: '大于等于', value: 'ge' }, { label: '小于等于', value: 'le' }, { label: '介于', value: 'between' }]

const charMatchList = [{ label: '相等', value: 'eq' }, { label: '包含', value: 'like' }, {
  label: '不包含',
  value: 'n_like'
}]
export default {
  // import引入的组件需要注入到对象中才能使用
  components: { Container, ConMain },
  name: 'tagsDetails',
  data() {
    var validateExistName = (rule, value, callback) => {
      console.log(value, '***', this.form)
      if (!value) {
        callback(new Error('请输入标签名'))
      } else {
        checkExist({
          existType: 'name',
          tagName: value,
          id: this.form.id
        }).then((res) => {
          if (res['data'] === true) {
            callback()
          } else {
            callback(new Error(value + '标签名已存在'))
          }
          console.log(res, '这里是判断返回的', this.formData)
        }).catch(() => {
          callback(new Error('判断唯一值异常'))
        })
      }
    }
    var validateExistCode = (rule, value, callback) => {
      console.log(value, '***', this.form)
      if (!value) {
        callback(new Error('请输入标签编码'))
      } else {
        checkExist({
          existType: 'code',
          tagCode: value,
          id: this.form.id
        }).then((res) => {
          if (res['data'] === true) {
            callback()
          } else {
            callback(new Error(value + '编码已存在'))
          }
          console.log(res, '这里是判断返回的', this.formData)
        }).catch(() => {
          callback(new Error('判断唯一值异常'))
        })
      }
    }
    return {
      info: {},
      submitLoading: false,
      // 搜索
      search: '',
      // 标题
      pageTitle: '',
      // 表单数据
      form: {
        moreDictValue: undefined,
        tagType: '1',
        tabValue: undefined,
        tagSource: 1
      },
      dbMatchTypeList: charMatchList,
      // 标签类型枚举
      tagTypeOptions: [
        {
          label: '数值',
          value: '1'
        },
        {
          label: '字典',
          value: '2'
        }
      ],
      dbSourceList: [{ dbTableColumn: '', dbTableName: '', dbTableDesc: '', dbTableColumnDesc: '', tagName: '',collectionType: '' }],
      // 标签值类型
      valueTypeOptions: [
        {
          label: '文本型',
          value: '0'
        },
        {
          label: '数值型',
          value: '2'
        }
      ],
      dictValueTypeOptions: [],
      // 数据是否来源数据库
      databaseOptions: [
        {
          label: '是',
          value: '1'
        },
        {
          label: '否',
          value: '0'
        }
      ],
      // 账期类型
      accountPeriodOptions: [
        {
          label: '日标签',
          value: '1'
        }
      ],
      // 数据来源
      formData: {},
      // 表格数据
      tableData: [],
      // 表单校验
      formRules: {
        tagCode: [
          { validator: validateExistCode, required: true, trigger: 'blur' }
        ],
        tagName: [
          { validator: validateExistName, required: true, trigger: 'blur' }]
      },
      dictTypeId: '',
       // 数据是否来源数据库
      collectionTypeList: [
        {
          label: '客户类型',
          value: '1'
        },
        {
          label: '产品类型',
          value: '2'
        }
      ],
    }
  },
  created() {
    this.getInfo()

    // console.log(this.$route);
  },
  // 监听属性类似于data概念
  computed: {
    valueList() {
      return this.form.tagType === '2'
        ? this.dictValueTypeOptions
        : this.valueTypeOptions
    }
  },
  watch: {
    'form.tagType': {
      handler(newVal, oldVal) {
        console.log(newVal, oldVal, 'form.tagType')
        if (typeof oldVal === 'string') {
          if (oldVal === '1') {
            // 当tagType从1变为其他值时的处理逻辑
            this.resetFormFields()
          } else {
            // 当tagType从非1变为其他值时的处理逻辑
            this.resetOtherFields()
          }
        }
      }
    }
  },
  // 方法集合
  methods: {
    addDbSource() {
      console.log(this.$refs, this.getFormRef(this.dbSourceList.length - 1))
      this.$refs[this.getFormRef(this.dbSourceList.length - 1)][0].validate((valid) => {
        if (valid) {
          testDb(this.dbSourceList[this.dbSourceList.length - 1]).then((res) => {
            if (res['success'] === true) {
              this.dbSourceList.push({
                dbTableColumn: '',
                dbTableName: '',
                dbTableDesc: '',
                dbTableColumnDesc: '',
                tagName: '',
                collectionType: ''
              })
            } else {
              this.$message({
                message: '无效的数据源链接',
                type: 'error'
              })
            }
          }).catch(() => {
            this.$message({
              message: '无效的数据源链接',
              type: 'error'
            })
          })

        }
      })

    },
    removeDb(dbItem) {
      this.dbSourceList = this.dbSourceList.filter(item => {
        return item !== dbItem
      })
    },
    getFormRef(index) {
      return 'formDb' + index
    },
    // 获取基本信息
    getInfo() {
      const id = this.$route.query.id
      if (id) {
        this.pageTitle = '编辑标签'

        findFormOne(id).then((res) => {
          console.log(res, '详情')
          this.form = res.data.tag
          this.dbSourceList = res.data.tagDbs
          if (this.form.tagType === '2') {
            this.dictTypeId = this.form.tagTypeValue
          }
          this.findTypeValueSelect()

        })
      } else {
        this.pageTitle = '新增标签'
        this.findTypeValueSelect()
      }

    },
    findTypeValueSelect() {
      findDictTypeList().then((res) => {
        console.log(res, 'res')
        const list = res.data
        this.dictValueTypeOptions = list.map((item) => {
          return {
            label: item.dictName,
            value: item.id,
            dictCode: item.dictCode
          }
        })
        if (this.dictTypeId) {
          this.handleChange(this.dictTypeId)
        }
        console.log('我是测dictValueTypeOptions', this.dictValueTypeOptions)
      })
    },
    // 重置表单字段的方法，提高代码复用性
    resetFormFields() {
      this.$set(this.form,'moreDictValue',undefined)
      this.$set(this.form,'tagTypeValue',undefined)
    },
    // 重置其他字段的方法，提高代码复用性
    resetOtherFields() {
      this.form.tagSource = 1
      this.$set(this.form,'tagNumValue',undefined)
    },
    // 获取字典数据
    handleChange(row) {
      console.log(row, 'row----')
      this.form.tagTypeValue = row
      if (this.form.tagType === '2') {
        findDictValueList(row).then((res) => {
          // console.log(res, "res");
          this.tableData = res.data
        })
      } else if (this.form.tagType === '1') {
        if (this.form.tagTypeValue === '2') {
          this.dbMatchTypeList = numberMatchList
        } else if (this.form.tagTypeValue === '0') {
          this.dbMatchTypeList = charMatchList
        }

      }
    },
    // 返回上一页
    goBack() {
      // this.$router.go(-1);
      this.$router.replace('/sw/tagsManage')
    },
    // 搜索
    handleQuery() {
      const obj = {
        id: this.form.tagTypeValue,
        search: this.search
      }
      // console.log(val, "val");
      findDetails(obj).then((res) => {
        console.log(res, 'res')
        this.tableData = res.data
      })
      console.log(this.search, 'search')
    },
    // 确定
    submit() {
      this.submitLoading = true
      console.log(this.$refs['form'], '上传的form对象')
      this.$refs['form'].validate((valid) => {
        console.log(valid, '这个验证的哦')

        if (valid) {
          const baseInfo = this.form
          baseInfo.tagTypeId = this.$route.query.tag_type_id
          delete baseInfo.diffMin
          delete baseInfo.tagTypeName
          delete baseInfo.updateDate
          delete baseInfo.createDate

          const info = {
            tag: baseInfo
          }
          let ok = true
          // 判断数据源是否可以链接成功
          this.$refs[this.getFormRef(this.dbSourceList.length - 1)][0].validate((valid) => {
            ok = valid
            if (valid) {
              info.tagDbs = this.dbSourceList
              testDb(this.dbSourceList[this.dbSourceList.length - 1]).then((res) => {
                ok = res['success'] === true
                if (!ok) {
                  this.submitLoading = false

                  this.$message({
                    message: '无效的数据源链接',
                    type: 'error'
                  })
                }
              }).catch(() => {
                ok = false
                this.submitLoading = false
                this.$message({
                  message: '无效的数据源链接',
                  type: 'error'
                })
              })
            }else{
              this.submitLoading = false
            }
          })
          setTimeout(() => {
            if (ok) {
              tagSave(info).then((res) => {
                if (res['success'] === true) {
                  this.$message({
                    message: '提交成功',
                    type: 'success'
                  })
                  this.submitLoading = false
                  this.goBack()
                } else {
                  this.$message({
                    message: res['message'],
                    type: 'error'
                  })
                  this.submitLoading = false
                }
              })
            }
          }, 1000)
        }else{
          this.submitLoading = false
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.header {
  font-size: 0.16667rem;

  padding: 0.16667rem;
  background-color: #fff;
  width: 100%;
  margin-top: 0.16667rem;
  border-radius: 0.04167rem;
  box-shadow: 0px 4px 12px 0px rgba(11, 38, 110, 0.08);
  border-radius: 6px;

  i {
    color: #3377ff;
  }
}

.info {
  margin-top: 0.16667rem;
}

.con-main {
  margin-bottom: 0.16667rem;
}

.m-con {
  margin: 0.08333rem;
  padding: 0.08333rem;
  background-color: #fff;

  .dict_search {
    width: 30%;
    padding-bottom: 0.08333rem;
  }
}

.footer {
  display: flex;
  justify-content: center;
  background-color: #fff;
  box-shadow: 0px 4px 12px 0px rgba(11, 38, 110, 0.08);
  border-radius: 0.04167rem;
  padding: 0.08333rem;
}
.innerLayout{
  align-items: center;
  display: flex;
  justify-content: left;
}
</style>
