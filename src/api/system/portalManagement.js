import request from "@/utils/request";

// 查询菜单列表
// export function listMenu(params) {
//   return request({
//     url: "/user/permissions/findAllMenu",
//     method: "get",
//     params: params
//   });
// }

// 查询菜单详细
// export function getMenu(permissionId) {
//   return request({
//     url: "/user/permissions/" + permissionId,
//     method: "get"
//   });
// }

// 查询用户组下菜单
// export function getOrgMenu(data) {
//   return request({
//     url: "/user/roles/orgId/permissions",
//     method: "post",
//     data: data
//   });
// }

// 列表查询
export function getAllPortal(data) {
  return request({
    url: "/cms/eip/list",
    method: "post",
    data: data
  });
}

// 修改操作-查询详情
export function getPortalDetails(data) {
  return request({
    url: "/cms/eip/" + data,
    method: "get"
  });
}

// 修改操作
export function portalDetailsUpdate(data) {
  return request({
    url: "/cms/eip/update",
    method: "post",
    data: data
  });
}

// 门户新增
export function addPortal(data) {
  return request({
    url: "/cms/eip",
    method: "post",
    data: data
  });
}

// 删除操作
export function deletePortal(data) {
  return request({
    url: "/cms/eip/delete",
    method: "post",
    data: data
  });
}

// 单一门户菜单查询
export function getPortalMenu(data) {
  return request({
    url: "/cms/eipMenu/list",
    method: "post",
    data: data
  });
}

// 单一门户主类目下新增
export function addMainCategory(data) {
  return request({
    url: "/cms/eipMenu",
    method: "post",
    data: data
  });
}

// 单一门户列表新增
export function addTableMenu(data) {
  return request({
    url: "/cms/eipMenu",
    method: "post",
    data: data
  });
}

// 单一门户列表修改-查询详情
export function editTableMenu(data) {
  return request({
    url: "/cms/eipMenu/" + data,
    method: "get"
  });
}

// 单一门户列表修改
export function editTableMenuUpdate(data) {
  return request({
    url: "/cms/eipMenu/update",
    method: "post",
    data: data
  });
}

// 单一门户列表删除
export function deleteTableMenu(data) {
  return request({
    url: "/cms/eipMenu/delete",
    method: "post",
    data: data
  });
}
