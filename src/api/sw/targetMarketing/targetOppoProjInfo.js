import request from '@/utils/request'

// 分页查询靶向营销算网数值商机项目详情表
export function getPageList(params) {
  return request({
    url: '/sanquan/targetMarketing/oppoproInfo/findPage',
    method: 'get',
    params,
  })
}

// 查询靶向营销算网数值商机项目详情表详细信息
export function getInfo(taskCode) {
  return request({
    url: '/sanquan/targetMarketing/oppoproInfo/findInfo',
    method: 'get',
    params: {
        taskCode
    }
  })
}

// 新增靶向营销算网数值商机项目详情表
export function addInfo(data) {
  return request({
    url: '/sanquan/targetMarketing/oppoproInfo/add',
    method: 'post',
    data,
  })
}

// 修改靶向营销算网数值商机项目详情表
export function updateInfo(data) {
  return request({
    url: '/sanquan/targetMarketing/oppoproInfo/update',
    method: 'post',
    data,
  })
}

// 删除靶向营销算网数值商机项目详情表
export function deleteInfo(taskCode) {
  return request({
    url: '/sanquan/targetMarketing/oppoproInfo/delete',
    method: 'post',
    params: {
        taskCode
    }
  })
}
