# 环境配置标识 - 开发环境
ENV = 'development'
#port = 9091
# 前端请求统一接口路径
VUE_APP_BASE_API = '/dev-api'
# 网关地址
#VUE_APP_GATEWAY_URL = 'http://172.16.4.101:8080'
#VUE_APP_GATEWAY_URL = 'http://172.16.23.108:8080'
VUE_APP_GATEWAY_URL = 'http://127.0.0.1:8080'
#VUE_APP_GATEWAY_URL = 'http://10.238.25.38:18081'

#VUE_APP_GATEWAY_URL = 'http://172.16.6.27:8080'
#VUE_APP_GATEWAY_URL = 'http://10.238.52.166:8080'
#VUE_APP_GATEWAY_URL = 'http://172.16.11.56:8080'
# 路由懒加载
VUE_CLI_BABEL_TRANSPILE_MODULES = true
# 创建商机跳转页面
VUE_APP_CREATE_BUSINESS_OPPORTUNITIES = 'https://www.baidu.com'

# 跳转客户详情
VUE_APP_YAOKE_CUSTOMER_DETAILS = 'http://10.238.25.38:8892/ykManager/#/ykycInfoList?'
# 加载菜单
VUE_APP_SHOW_MENU='true'

VUE_APP_PREFIX=''
