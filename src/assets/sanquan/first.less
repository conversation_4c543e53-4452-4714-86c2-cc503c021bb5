.home-container {
  font-size: .125rem;
  width: 100%;
  // min-width: 1680px;
  margin-top: 0;

  .first-flood {
    margin: 0 .1867rem;
    background: url(images/mxbg.png) center center no-repeat;
    width: calc(100% - .3333rem);
    height: 1.1278rem;
    background-size: 100% 100%;
    display: flex;

    .first-flood-pie {
      width: calc(20% - .0083rem);
      height: 1.1833rem;
      // float: left;
      position: relative;

      .first-flood-pie-data {
        width: .8833rem;
        height: .8833rem;
        background: url("images/y1.png") center center no-repeat;
        background-size: cover;
        position: absolute;
        padding: .1833rem 0;
        left: -0.0833rem;
        right: 0;
        top: 0;
        bottom: 0;
        margin: 0 auto;

        .num {
          width: 100%;
          height: .2rem;
          line-height: .2rem;
          font-family: PingFangSC-Medium;
          font-size: .25rem;
          color: #FFFFFF;
          letter-spacing: 0;
          font-weight: 500;
          text-align: center;
          cursor: pointer;
          margin-bottom: .05rem;
        }

        .name {
          width: 100%;
          height: .2083rem;
          line-height: .2083rem;
          font-family: PingFangSC-Semibold;
          font-size: .15rem;
          color: #FFFFFF;
          letter-spacing: 0;
          font-weight: 600;
          text-align: center;
        }
      }

      .pie-2 {
        width: 1.0667rem;
        height: 1.0667rem;
        padding: .3333rem 0;
      }

      .pie-1{
        width: 1rem;
        height: 1rem;
        padding: .3333rem 0;
      }
    }

  }

  .first-flood-arrow {
    width: calc(100% - .2333rem);
    height: .55rem;
    background: url(images/jt.png) center center no-repeat;
    margin-top: -0.275rem;
    background-size: contain;
  }

  .second-flood {
    width: 100%;
    background: #ffffff;
    border-radius: .0667rem;

    .second-flood-title {
     /* background: url(images/bt.png) center center no-repeat;*/
      background: url(images/bt282.png) center center no-repeat;
      height: .475rem;
      text-align: center;
      font-family: PingFang-SC-Semibold;
      font-size: .1667rem;
      color: #333E50;
      letter-spacing: .0014rem;
      line-height: .4167rem;
      font-weight: 600;

      .t1 {
        color: #237AFB;
      }
    }

    .second-flood-first {
      margin: 0 .25rem;
      // width: calc(100% - .5rem);
      // height: 1.25rem;
      background: url("images/celbg.png") center center no-repeat;
      background-size: 100% 100%;
      // padding: 0 .25rem;
      display: flex;
      justify-content: center;

      .item {
        float: left;
      }

      .item-bg {
        width: 6.6rem;
        background: url("images/clscbg.png") center center no-repeat;
        background-size: 100% 140%;
        position: relative;

        .item-title {
          font-family: PingFang-SC-Semibold;
          font-size: .15rem;
          letter-spacing: 0.14px;
          line-height: .2083rem;
          font-weight: 600;
          position: absolute;
          top: .2167rem;
          left: .137rem;


          .t1 {
            color: #2364FB;
          }
        }

        .detail {
          width: calc(100% - 1.5833rem);
          height: .9167rem;
          position: absolute;
          top: .3667rem;
          left: 1.5rem;

          .item-box {
            float: left;
            width: calc(33% - .0833rem);
            height: .6667rem;

            .hand-num{
              cursor: pointer;
            }

            .item-num {
              width: 100%;
              font-family: PingFangSC-Medium;
              font-size: .15rem;
              color: #2364FB;
              letter-spacing: 0;
              font-weight: 500;
              text-align: center;
            }

            .item-box-title {
              width: 100%;
              margin-top: .0833rem;
              text-align: center;
              font-family: PingFang-SC-Regular;
              font-size: .1433rem;
              color: #333E50;
              letter-spacing: 0;
              font-weight: 400;
            }
          }
        }

        .down-arrow {
          width: .25rem;
          height: .25rem;
          position: absolute;
          bottom: -0.25rem;
          left: 0;
          right: 0;
          margin: 0 auto;
          background: url("images/arrow.png") center center no-repeat;
          background-size: 100% 100%;
        }
      }

      .left-1 {
        margin: .1167rem;

        .product-num-area {
          display: flex;
          width: 100%;

          .bg {
            margin: .125rem .1667rem;
            width: .35rem;
            height: 0.4583rem;
            background: url("images/tb1.png") center center no-repeat;
          }
        }

      }

      .center-1 {
        width: 1.6rem;
        padding: .1167rem .0833rem;
        .num {
          // height: 45px;
          // line-height: 45px;
          width: 100%;
          text-align: center;
          font-family: PingFangSC-Semibold;
          font-size: .2333rem;
          color: #2364FB;
          letter-spacing: 0.23px;
          font-weight: 600;
        }

        .title-num {
          // height: 45px;
          // line-height: 45px;
          padding-top: .0417rem !important;
          width: 100%;
          text-align: center;
          font-family: PingFangSC-Semibold;
          font-size: .1333rem;
          letter-spacing: 0.23px;
          font-weight: 600;
        }

        .bottom {
          margin-top: .1417rem;
          width: 100%;
          height: .5rem;
          background: url("images/clsbg.png") center center no-repeat;
        }
      }

      .right-1 {
        margin: .1167rem;

        .product-num-area {
          display: flex;

          .bg {
            margin: .125rem .1667rem;
            width: .35rem;
            height: 0.4583rem;
            background: url("images/tb2.png") center center no-repeat;
          }
        }
      }
    }

    .second-flood-second-title {
      margin: .0667rem auto;
      width: calc(100% - .5rem);
      height: .2083rem;
      line-height: .2083rem;
      font-family: PingFang-SC-Semibold;
      font-size: .15rem;
      letter-spacing: 0.14px;
      font-weight: 600;

      .t1 {
        color: #237AFB;
      }
    }

    .second-flood-second-content {
      width: calc(100% - .1667rem);
      margin: 0 .0833rem;
      height: 1.0333rem;

      .second-row {
        float: right;
        width: calc(50% - .1067rem);
        height: 1.1667rem;
        margin: 0 0 0 .0833rem;

        .f-row {
          width: 100%;
          height: .3833rem;
          line-height: .3833rem;

          .f-row-content {
            float: left;
            width: calc(33% - 0.1667rem);
            height: 100%;
            background: url("images/clpjbg1.png") center center no-repeat;
            background-size: 100% 100%;
            margin: 0 .0833rem .0833rem .0833rem;
            display: flex;

            .c-title {
              font-family: PingFang-SC-Regular;
              font-size: .1433rem;
              color: #333E50;
              letter-spacing: 0;
              font-weight: 400;
              padding: 0 .1067rem;
            }

            .c-num {
              flex: 1;
              text-align: right;
              padding-right: .1067rem;
              font-family: PingFangSC-Medium;
              font-size: .1433rem;
              color: #333E50;
              letter-spacing: 0;
              font-weight: 500;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }

          .f-row-content:last-child{
            // float:right;
            margin-right:0;
          }
        }

        .s-row {
          width: 100%;
          height: .3833rem;
          line-height: .3833rem;
          display: flex;

          .f-row-content {
            // float: left;
            width: calc(50% - .2083rem);
            height: 100%;
            background: url("images/clpjbg1.png") center center no-repeat;
            background-size: 100% 100%;
            margin: 0 .0833rem .0833rem .0833rem;

            .c-title {
              width: 60%;
              float: left;
              font-family: PingFang-SC-Regular;
              font-size: .1433rem;
              color: #333E50;
              letter-spacing: 0;
              font-weight: 400;
              padding: 0 .1667rem;
            }

            .c-num {
              width: 40%;
              float: right;
              font-family: PingFangSC-Medium;
              font-size: 0.1833rem;
              color: #333E50;
              letter-spacing: 0;
              font-weight: 500;
            }
          }
        }
      }
    }
  }


  .third-flood {
    margin-top: .15rem;
    width: 100%;
    background: #ffffff;
    border-radius: .0667rem;
    // height: 2.9167rem;

    .third-flood-title {
      background: url(images/bt.png) center center no-repeat;
      height: 0.475rem;
      text-align: center;
      font-family: PingFang-SC-Semibold;
      font-size: .1667rem;
      color: #333E50;
      letter-spacing: 0.17px;
      line-height: 0.475rem;
      font-weight: 600;

      .t1 {
        color: #2364FB;
      }
    }

    .third-flood-first {
      width: 100%;
      border: .0083rem;
      height: 1.1167rem;
      display: flex;
      justify-content: center;

      .third-first-row-box {
        margin: 0 .1667rem;
        float: left;
        width: calc(50% - .3917rem);
        height: .9667rem;
        background: url("images/gdzxbg.png") center center no-repeat;
        background-size: 100% 100%;

        .third-first-row-title {
          width: 100%;
          padding: .0833rem;
          height: .3333rem;
          line-height: .3333rem;

          .row-title-icon {
            width: .2333rem;
            height: .2333rem;
            float: left;
            background: url("images/t1.png") center center no-repeat;
            background-size: contain;
          }

          .row-title-text {
            float: left;
            font-family: PingFangSC-Medium;
            font-size: .15rem;
            color: #1C1E22;
            letter-spacing: 0;
            font-weight: 600;
            margin-left: .0833rem;
            line-height: normal;
          }
        }

        .third-first-row-content {
          margin-top: .0833rem;
          width: calc(75%);
          height: .6667rem;

          .row-box {
            // padding-top: .125rem;
            float: left;
            width: calc(30%);
            height: .6667rem;
            text-align: center;

            .box-num {
              font-family: PingFangSC-Medium;
              font-size: .1833rem;
              letter-spacing: 0;
              font-weight: 500;
            }

            .box-text {
              font-family: PingFangSC-Medium;
              font-size: .1433rem;
              letter-spacing: 0;
              font-weight: 500;
            }
          }
        }
      }
      .third-first-row-box:last-child{

      }
    }

    .third-flood-second {
      padding: .0833rem .25rem;
      width: 100%;
      height: .8333rem;

      .s-title {
        font-family: PingFangSC-Medium;
        font-size: .1667rem;
        color: #1C1E22;
        letter-spacing: 0;
        font-weight: 600;
        float: left;
        width: .9333rem;
        line-height: .5167rem;
      }

      .s-value {
        float: left;
        width: calc(100% - .9583rem);
        // height: 100%;

        .s-value-item {
          float: left;
          margin: 0 .1rem;
          width: calc(25% - .15rem);
          // height: 100%;
          line-height: .5167rem;
          background: url("images/gdsbg.png") center center no-repeat;
          background-size: 100% 100%;

          div {
            float: left;
          }

          .v-item-title {
            font-family: PingFangSC-Medium;
            font-size: .1433rem;
            color: #333E50;
            letter-spacing: 0;
            height: .5167rem;
            width: 60%;
            font-weight: 500;
            text-align: center;
          }

          .v-item-num {
            font-family: PingFangSC-Medium;
            font-size: .2167rem;
            color: #2364FB;
            letter-spacing: 0;
            font-weight: 500;
            text-align: center;
            width: 40%;
            height: .5167rem;
            line-height: .5167rem;
            cursor: pointer;
          }
        }

        .s-value-item:first-child{
          margin-left:0px;
        }
        .s-value-item:last-child{
          margin-right:0px;
          float:right;
        }
      }
    }
  }
}
