<template>
  <div class="portal-body">
    <div class="analysis">
      <div class="search-wrapper box-bg">
        <el-form
          :model="queryParams"
          @submit.native.prevent
          ref="queryForm"
          label-width="1rem"
          label-position="left"
        >
          <el-row>
            <el-col :span="7">
              <el-form-item label="标签分类名称" prop="label">
                <el-input
                  v-model="queryParams.label"
                  placeholder="请输入"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="7" >
              <el-form-item>
                <el-button
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQuery"
                  >重置</el-button
                >
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  size="mini"
                  @click="handleQuery"
                  >查询</el-button
                >
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="container">
        <div class="con-left table-wrapper box-bg">
          <div class="flex-between">
            <div class="table-title">标签分类</div>
          </div>
          <div class="tab-tree">
            <el-tree
              :data="treeList"
              :props="defaultProps"
              default-expand-all
              :expand-on-click-node="false"
              @current-change="handleNodeClick"
            >
              <template v-slot="scope">
                <span class="custom-tree-node">
                  <i class="el-icon-price-tag tab-icon"></i>
                  <span>{{ scope.node.label }}</span>
                </span>
              </template>
            </el-tree>
          </div>
        </div>
        <div class="con-main table-wrapper box-bg">
          <div class="flex-between" >
            <div class="table-title">标签分类列表</div>
            <div>
              <el-button
                type="primary"
                size="small"
                icon="el-icon-plus"
                @click="handleEdit(true, {})"
                >新增</el-button
              >
            </div>
          </div>
          <el-divider />
          <div class="table-con">
            <uni-table
            action="/sanquan/labelClassify/findPageListByParentId"
            :columns="tableColumns"
            ref="uniTable"
            :params="queryParams"
            >

              <template v-slot:opt="scope">
                <div class="action-buttons flex-center">
                  <el-button
                    size="mini"
                    class="action-button"
                    type="button"
                    @click="handleEdit(false, scope.row)"
                  >修改
                  </el-button>
                  <el-button
                    size="mini"
                    class="action-button"
                    type="button"
                    @click="handleDel(scope.row.id)"
                  >删除
                  </el-button>
                </div>
              </template>

            </uni-table>
          </div>
        </div>
      </div>
    </div>
    <!-- 添加或修改标签弹框 -->
    <el-dialog :visible.sync="openTabDialog" width="35%">
      <template #title>
        <span
          style="font-size: 0.135rem; font-weight: 600; margin-left: 0.1rem"
          >{{ title }}</span
        >
      </template>
      <el-form ref="form" :model="form" :rules="rules" label-width="116px">
        <el-form-item label="标签分类编号" prop="code">
          <el-input v-model="form.code" placeholder="请输入分类编号" />
        </el-form-item>
        <el-form-item label="标签分类名称" prop="label">
          <el-input v-model="form.label" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="上级分类">
          <el-input
            placeholder="输入关键字进行过滤"
            v-model="form.parentLabel"
            :disabled="!showTreeForm"
          >
          </el-input>

          <el-tree
            v-if="showTreeForm"
            class="filter-tree"
            :data="treeListForm"
            default-expand-all
            :filter-node-method="filterNode"
            @current-change="changeNodeClick"
            ref="tree"
          >
          </el-tree>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import LabelClassifyApi from "@/api/sw/tags/labelClassify";
import {addError, addSuccess, deleteError, deleteSuccess, editError, editSuccess} from "@/utils/messageText";
import UniTable from '@/components/UniTable/UniTable.vue'
export default {
  // import引入的组件需要注入到对象中才能使用
  components: { UniTable },
  data() {
    return {
      tableColumns:[{
        props:'code',label:'标签分类编号'
      },{
        label:"标签分类名称",
        align:"center",
        props:"label",
      },{
        label:"上级分类",
        align:"center",
        props:"parentLabel"
      },{
        label:"创建时间",
        align:"center",
        props:"createDate"
      },{
        label:"操作",
        align:"center",
        props:"opt"
        ,slot:true
      }],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        label: "",
      },
      // 标签分类
      treeList: [],
      // 标签分类表格数据
      tableData: [],
      //  上级分类信息
      parentForm: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      openTabDialog: false,
      // 表单参数
      form: {
        parentId: "",
        parentLabel: "",
        parentCode: "",
      },
      // 表单校验
      rules: {
        label: [
          { required: true, message: "标签分类名称", trigger: "blur" },
          { validator: this.checkAccountName, trigger: 'blur'}
        ],
        code: [
          { required: true, message: "请输入标签分类编号", trigger: "blur" },
          { validator: this.checkAccountCode, trigger: 'blur'}
        ],
      },
      // 表格loading
      tableLoading: false,
      // 表格总数
      total: 0,
      defaultProps: {
        children: "children",
        label: "label",
      },
      showTreeForm: false,
      treeListForm: [],
    };
  },
  created() {
    this.getTreeList();
    // this.getList();
  },
  // 监听属性类似于data概念
  computed: {},
  watch: {
    "form.parentLabel" (newVal){
      if(newVal !== undefined){
        this.$refs.tree.filter(newVal);
      }
    },
  },
  // 方法集合
  methods: {
    async checkAccountName(rule, value, callback){
      await LabelClassifyApi.checkName({ id: this.form.id, name: value }).then((res) =>{
        if(res.data > 0){
          callback('分类名称已存在！');
        }
      })
      callback()
    },
    async checkAccountCode(rule, value, callback){
      await LabelClassifyApi.checkCode({ id: this.form.id, code: value }).then((res) =>{
        if(res.data > 0){
          callback('分类编号已存在！');
        }
      })
      callback()
    },
    changeNodeClick(node) {
      this.form.parentId = node.id;
      this.form.parentCode = node.code;
      this.form.parentLabel = node.label;
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 当前页码
    handleCurrentChange(page) {
      this.queryParams.pageNum = page;
      this.getList();
    },
    // 点击查询右侧数据
    handleNodeClick(node) {
      this.parentForm.id = node.id;
      this.getList(this.parentForm.id);
    },
    /** 查询列表 */
    getTreeList() {
      LabelClassifyApi.getAllTree({label: this.queryParams.label}).then((res) => {
        this.treeList = res.data;
      });
    },
    // 右侧表格数据
    getList(parentId) {
      const params = { ...this.queryParams };
      if (parentId) {
        params.parentId = parentId;
      }
      this.$refs.uniTable.loadData(params)
      // LabelClassifyApi.findPageListByParentId(params).then((res) => {
      //   this.tableData = res.data.records;
      //   this.total = res.data.total;
      // });
    },
    /**
     * 新增编辑弹窗
     * @param isAdd
     * @param row
     */
    handleEdit(isAdd, row) {
      //  重置表单
      this.reset();
      LabelClassifyApi.getAllTree().then((res) => {
        this.treeListForm = res.data;
        if (isAdd) {
          this.title = "新增标签分类";
          if (row?.id) {
            this.showTreeForm = false;
          } else {
            this.showTreeForm = true;
          }
          this.form = {
            parentId: row.id,
            parentLabel: row.label,
            parentCode: row.code,
          };
          this.openTabDialog = true;
        } else {
          if (row?.id) {
            this.title = "编辑标签分类";
            this.showTreeForm = false;
            LabelClassifyApi.findById(row.id).then((res) => {
              this.form = res.data;
              this.openTabDialog = true;
            });
          }
        }
      });
    },
    /**
     * 删除
     * @param id 分类主键
     */
    handleDel(id) {
      this.$confirm("此操作将永久该数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        LabelClassifyApi.deleteById(id).then((res) => {
          if(res.data === true){
            this.getTreeList();
            this.getList(this.parentForm.id);
            this.$message.success(deleteSuccess);
          }else{
            this.$message.success(deleteError);
          }
        });
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          LabelClassifyApi.addOrUpdate(this.form).then((res) => {
            if(res.data === true){
              this.$message.success(this.form.id ? editSuccess : addSuccess);
              this.openTabDialog = false;
              this.getTreeList();
              this.getList(this.parentForm.id);
            }else{
              this.$message.success(this.form.id ? editError : addError);
            }
          }).catch(err => {
            this.$message.error(err.response.data.message);
          });
        }
      });
    },
    // 取消按钮
    cancel() {
      this.openTabDialog = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {};
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.uniTable.resetPage()
      this.resetForm("queryForm");
      this.handleQuery();
    },
  },
};
</script>
<style lang="scss" scoped>
.container {
  display: flex;

  .con-left {
    width: 30%;
    margin-right: 0.16667rem;
    .flex-between {
      display: block;
      padding: 0 0.16667rem 0.08333rem 0;
      border-bottom: 0.00833rem solid;
      border-bottom: 0.00833rem solid #f1f1f1;
    }
    .tab-icon {
      padding-right: 0.08333rem;
    }
  }
  .con-main {
    flex: 1;
  }
}
</style>
