<template>
  <div class="divider">
    <section
      class="contan"
      :style="{
        height: datas.config.blankHeight + 'px',
        padding: datas.config.paddType === 0 ? '0px' : '0px 15px',
      }"
    >
      <div
        v-show="datas.config.segmentationtype === 1"
        style="height: 1px; width: 100%; border-top-width: 1px"
        :style="{
          'border-top-style': datas.config.bordertp,
          'border-top-color': datas.config.auxliarColor,
        }"
      />
    </section>

    <!-- 删除组件 -->
    <slot name="deles" />
  </div>
</template>

<script>
export default {
  name: 'divider',
  props: {
    datas: Object,
  },
}
</script>

<style scoped lang="less">
.divider {
  position: relative;
  .contan {
    display: flex;
    align-items: center;
  }
}
</style>
