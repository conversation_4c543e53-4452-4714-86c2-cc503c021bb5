<template>
  <div class="app-container">
    <el-card shadow="never">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
        @submit.native.prevent
      >
        <el-form-item
          v-if="$store.getters.customParam.userType === 'admin'"
          label="租户"
        >
          <el-select
            v-model="queryParams.tenantId"
            style="width: 200px"
            size="small"
            filterable
            remote
            :remote-method="getTenantList"
            :loading="getTenantLoading"
            @change="tenantChange(queryParams.tenantId)"
          >
            <el-option
              v-for="item in tenantList"
              :key="item.tenantId"
              :label="item.tenantName"
              :value="item.tenantId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="客户端类型" prop="clientType">
          <el-select v-model="queryParams.clientType" clearable>
            <el-option
              v-for="item in clienOptions"
              :key="item.value"
              :label="item.dictLabel"
              :value="item.dictValue"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="配置名称" prop="configName">
          <el-input
            v-model="queryParams.configName"
            placeholder="请输入配置名称"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item label="配置编码" prop="configCode">
          <el-input
            v-model="queryParams.configCode"
            placeholder="请输入配置编码"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item label="配置类型" prop="configType">
          <el-select v-model="queryParams.configType">
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.dictLabel"
              :value="item.dictValue"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            >新增</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="configList" stripe>
        <el-table-column label="序号" type="index" width="50" />
        <el-table-column
          label="配置名称"
          align="left"
          prop="configName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="配置编码"
          align="left"
          prop="configCode"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <a @click="copyToClipboard(scope.row.configCode)">{{
              scope.row.configCode
            }}</a>
          </template>
        </el-table-column>
        <el-table-column label="配置类型" align="left" prop="configType">
          <template slot-scope="scope">
            <el-tag
              effect="plain"
              :type="selectDictRemark(typeOptions, scope.row.configType)"
            >
              <span style="display: block; width: 32px; text-align: center">{{
                selectDictLabel(typeOptions, scope.row.configType)
              }}</span>
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          label="配置值"
          align="left"
          prop="configValue"
          style="width: 35px; height: 35px"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.configType === '1'">
              <a @click="copyToClipboard(scope.row.configValue)">{{
                scope.row.configValue
              }}</a>
            </span>
            <el-link
              v-if="scope.row.configType === '2'"
              type="primary"
              @click="handleLookEditorValueDailog(scope.row)"
              >查看详情</el-link
            >
            <img
              v-if="scope.row.configType === '3'"
              :src="scope.row.configValue"
              style="width: 35px; height: 35px; border: none"
            />
            <el-switch
              v-if="scope.row.configType === '4'"
              v-model="scope.row.configValue"
              disabled
              active-value="1"
              inactive-value="0"
              >>
            </el-switch>
          </template>
        </el-table-column>

        <el-table-column label="客户端类型" align="left" prop="clientType">
          <template slot-scope="scope">
            <el-tag
              effect="plain"
              :type="selectDictRemark(clienOptions, scope.row.clientType)"
            >
              <span style="display: block; width: 32px; text-align: center">{{
                selectDictLabel(clienOptions, scope.row.clientType)
              }}</span>
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              :loading="
                reloadId === scope.row.configId && reloadType === 'edit'
              "
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              :loading="
                reloadId === scope.row.configId && reloadType === 'remove'
              "
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改参数配置对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="open"
        width="600px"
        append-to-body
        @close="dialogReset"
        v-dialogDrag
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-form-item label="配置名称" prop="configName">
            <el-input v-model="form.configName" placeholder="请输入配置名称" />
          </el-form-item>
          <el-form-item label="配置编码" prop="configCode">
            <el-input v-model="form.configCode" placeholder="请输入配置编码" />
          </el-form-item>
          <el-form-item label="配置描述" prop="configDescribe">
            <el-input
              v-model="form.configDescribe"
              placeholder="请输入配置描述"
            />
          </el-form-item>

          <el-form-item label="客户端类型" prop="clientType">
            <el-select v-model="form.clientType">
              <el-option
                v-for="item in clienOptions"
                :key="item.value"
                :label="item.dictLabel"
                :value="item.dictValue"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="配置类型" prop="configType">
            <el-select v-model="form.configType">
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.dictLabel"
                :value="item.dictValue"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            label="配置值"
            prop="configInput"
            v-show="form.configType === '1'"
          >
            <el-input v-model="form.configInput" @input="$forceUpdate()" />
          </el-form-item>

          <el-form-item
            label="内容"
            prop="configEditor"
            v-show="form.configType === '2'"
          >
            <editor
              v-model="form.configEditor"
              :min-height="192"
              :read-only.sync="formDisabled"
            />
          </el-form-item>

          <el-form-item
            label="配置值"
            prop="configImage"
            v-show="form.configType === '3'"
          >
            <el-upload
              ref="coverUpload"
              class="upload-demo"
              action=""
              :http-request="uploadImage"
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :before-remove="beforeRemove"
              :on-exceed="handleExceed"
              :on-success="handleSueccss"
              :limit="1"
              :file-list="imageList"
              list-type="picture"
            >
              <el-button size="small" type="primary">点击上传</el-button>
            </el-upload>
            <el-dialog :visible.sync="previewDialogVisible" append-to-body>
              <img width="100%" :src="previewImageUrl" />
            </el-dialog>
          </el-form-item>

          <el-form-item
            label="配置值"
            prop="configSwitch"
            v-show="form.configType === '4'"
          >
            <el-switch
              active-text="开启"
              inactive-text="关闭"
              active-value="1"
              inactive-value="0"
              @change="$forceUpdate()"
              v-model="form.configSwitch"
            ></el-switch>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="saveLoading"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
      <!-- 查看富文本详情 -->
      <el-dialog
        title="提示"
        :visible.sync="lookEditorValueDailog"
        append-to-body
        v-dialogDrag
      >
        <editor
          v-model="configEditor"
          :min-height="192"
          :read-only.sync="formDisabledLook"
        />
        <span slot="footer" class="dialog-footer">
          <el-button @click="lookEditorValueDailog = false">关 闭</el-button>
        </span>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import {
  getClinet,
  delClient,
  addClient,
  updateClient,
  getClientPage,
} from "@/api/system/client";
import { uploadSingle } from "@/api/tool/upload";
import Editor from "@/components/Editor";
import { list as tenantList } from "@/api/system/tenant";
export default {
  name: "Client",
  components: {
    Editor,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      getTenantLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 参数表格数据
      configList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 类型数据字典
      typeOptions: [],
      clienOptions: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        configCode: undefined,
        configCodeList: undefined,
        configName: undefined,
        configType: undefined,
        clientType:undefined,
        tenantId: this.$store.getters.customParam.tenantId,
        tenantName: this.$store.getters.customParam.tenantName,
      },
      tenantList: [],
      // 表单参数
      form: {
        configId: undefined,
        configType: undefined,
        configContent: undefined,
        configName: undefined,
        configCode: undefined,
        configDescribe: undefined,
        clientType: undefined,
        configValue: undefined,
        configInput: "",
        configEditor: undefined,
        configImage: undefined,
        configSwitch: "1",
      },
      // 表单校验
      rules: {
        clientType: [
          { required: true, message: "客户端类型不能为空", trigger: "blur" },
        ],
        configName: [
          { required: true, message: "配置名称不能为空", trigger: "blur" },
        ],
        configCode: [
          { required: true, message: "配置编码不能为空", trigger: "blur" },
        ],
        configType: [
          { required: true, message: "配置类型不能为空", trigger: "blur" },
        ],
        configValue: [
          { required: true, message: "配置值不能为空", trigger: "blur" },
        ],
      },
      reloadId: undefined,
      reloadType: undefined,
      saveLoading: false,
      /* 图片管理 */
      previewDialogVisible: false,
      previewImageUrl: "",
      imageList: [],
      formDisabled: false,
      lookEditorValueDailog: false,
      configEditor: undefined,
      formDisabledLook: false,
    };
  },
  created() {
    this.getTenantList();
    this.getList();
    this.getDicts("client_config_type").then((response) => {
      this.typeOptions = response.data;
    });
    this.getDicts("client_type").then((response) => {
      this.clienOptions = response.data;
    });
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true;
      getClientPage(this.queryParams).then((response) => {
        this.configList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 参数系统内置字典翻译
    typeFormat(row, column) {
      return this.selectDictLabel(this.typeOptions, row.configType);
    },
    // 参数系统客户端配置字典翻译
    clientFormat(row, column) {
      return this.selectDictLabel(this.clienOptions, row.clientType);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {};
      this.resetForm("form");
      this.$refs.coverUpload && this.$refs.coverUpload.clearFiles();
      this.imageList = [];
      this.form.configId = undefined;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (this.queryParams.configCode) {
        var newCode = this.queryParams.configCode.split(",");
        this.queryParams.configCodeList = newCode;
      } else {
        this.queryParams.configCodeList = this.queryParams.configCode;
      }
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      // this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加参数";
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const configId = row.configId;
      this.reloadId = configId;
      this.reloadType = "edit";
      getClinet(configId).then((response) => {
        this.reloadId = undefined;
        this.reloadType = undefined;
        if (response.data) {
          this.form = response.data;
          if (response.data.configType) {
            switch (response.data.configType) {
              case "1":
                this.form.configInput = response.data.configValue;
                break;
              case "2":
                this.form.configEditor = response.data.configValue;
                break;
              case "3":
                this.imageList.push({
                  url: response.data.configValue,
                });
                break;
              case "4":
                this.form.configSwitch = response.data.configValue;
                break;
              default:
                this.$message.error("当前配置类型不支持!");
                break;
            }
          }
          this.open = true;
          this.title = "修改参数";
        } else {
          this.$message.error("数据异常！");
        }
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.saveLoading = true;
          if (this.form.configId != undefined) {
            if (this.form.configType) {
              switch (this.form.configType) {
                case "1":
                  this.form.configValue = this.form.configInput;
                  break;
                case "2":
                  this.form.configValue = this.form.configEditor;
                  break;
                case "3":
                  let imgUrls = "";
                  this.imageList.forEach((item) => {
                    imgUrls += item.url + ",";
                  });
                  if (imgUrls !== "")
                    imgUrls = imgUrls.substring(0, imgUrls.length - 1);
                  this.form.configValue = imgUrls;
                  break;
                case "4":
                  this.form.configValue = this.form.configSwitch;
                  break;
                default:
                  this.$message.error("当前配置类型不支持!");
                  break;
              }
            }
            updateClient(this.form).then((response) => {
              if (!response.success) {
                this.$message.error(response.message);
                this.saveLoading = false;
                this.reset();
              } else {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
                this.reset();
                this.saveLoading = false;
              }
            });
          } else {
            // 新增
            switch (this.form.configType) {
              case "1":
                this.form.configValue = this.form.configInput;
                break;
              case "2":
                this.form.configValue = this.form.configEditor;
                break;
              case "3":
                let imgUrls = "";
                this.imageList.forEach((item) => {
                  imgUrls += item.url + ",";
                });
                if (imgUrls !== "")
                  imgUrls = imgUrls.substring(0, imgUrls.length - 1);
                this.form.configValue = imgUrls;
                break;
              case "4":
                this.form.configValue = this.form.configSwitch;
                break;
              default:
                this.$message.error("当前配置类型不支持!");
                break;
            }
            addClient(this.form).then((response) => {
              if (!response.success) {
                this.$message.error(response.message);
                this.saveLoading = false;
                this.reset();
              } else {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
                this.reset();
                this.saveLoading = false;
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const configId = row.configId;
      this.reloadId = configId;
      this.reloadType = "remove";
      this.$confirm(
        '是否确认删除配置名称为"' + row.configName + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delClient(configId);
        })
        .then(() => {
          this.reloadId = undefined;
          this.reloadType = undefined;
          this.getList();
          this.msgSuccess("删除成功");
        })
        .catch(() => {
          this.reloadId = undefined;
          this.reloadType = undefined;
        });
    },
    /* 图片上传 */
    uploadImage(content) {
      let formData = new FormData();
      //content.file 	文件file对象
      formData.append("multipartFile", content.file);
      formData.append("appCode", this.appCode);
      uploadSingle(formData).then((res) => {
        this.imageList.push({
          url: res.data.url,
        });
      });
    },
    handleRemove(file, fileList) {
      this.imageList = [];
    },
    handlePreview(file) {
      this.previewImageUrl = this.form.configValue;
      this.previewDialogVisible = true;
    },
    handleExceed(files, fileList) {
      this.$message.warning(`只能上传一张图片,请先移除前一张`);
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除？`);
    },
    handleSueccss(response, file, fileList) {
      console.log("res", response);
    },
    // 点击查看富文本框内容
    handleLookEditorValueDailog(data) {
      this.lookEditorValueDailog = true;
      this.configEditor = data.configValue;
    },
    // 监听对话框重置
    dialogReset() {
      this.reset();
    },
    getTenantList(tenantName) {
      this.getTenantLoading = true;
      let query = {}
      if (tenantName !== undefined && tenantName !== '') {
        query.tenantName = tenantName
        query.tenantId = undefined
      } else {
        query.tenantId = this.queryParams.tenantId
      }
      tenantList(query).then((response) => {
        this.tenantList = response.data;
      }).finally(() => {
        this.getTenantLoading = false;
      });
    },
    tenantChange(tenantId) {
      if (tenantId !== "") {
        this.queryParams.tenantName = this.tenantList.find(
          (item) => item.tenantId === tenantId
        ).tenantName;
      }
      this.handleQuery();
    },
  },
  watch: {
    configList() {
      if (this.configList.length < 1) {
        if (this.queryParams.pageNum > 0) {
          this.queryParams.pageNum -= 1;
          this.getList();
        }
      }
    },
  },
};
</script>
