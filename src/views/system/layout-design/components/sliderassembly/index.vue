<template>
  <div class="sliderassembly">
    <el-collapse v-model="activeNames">
      <el-collapse-item
        :title="items.title"
        :name="index + 1"
        v-for="(items, index) in datas"
        :key="index"
      >
        <div
         class="componList"
          :style="systemThemeColor"
          draggable="true"
          @dragstart="drag($event)"
          @dragend="dragends($event)"
          :data-name="item.name"
          v-for="(item, ind) in items.comList"
          :key="ind"
        >
          <i class="iconfont-design" :class="item.icon"  v-if="item.icon" />
          <i :class="item.elIcon" v-else />
          <p>{{ item.text }}</p>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
export default {
  name: 'sliderassembly',
  props: {
    pointer: Object,
  },
  data() {
    return {
      activeNames: [1, 2, 3] /* 侧边栏组件显示 */,
      datas: [
        {
          title: '基础组件',
          comList: [
            {
              text: '搜索',
              type: '1-1',
              icon: 'design-icon-shangpinsousuo',
              name: 'search',
            },
            {
              text: '标题文本',
              type: '1-2',
              icon: 'design-icon-Component-biaotiwenzi',
              name: 'captiontext',
            },

            {
              text: '图片轮播',
              type: '1-3',
              icon: 'design-icon-tupianguanggao',
              name: 'swipe',
            },
            {
              text: '图文导航',
              type: '1-4',
              icon: 'design-icon-icon_tupiandaohang',
              name: 'graphicnavigation',
            },
            // {
            //   text: '底部导航',
            //   type: '1-5',
            //   icon: 'design-icon-daohang',
            //   name: 'tabBar',
            // },
            {
              text: '选项卡',
              type: '1-6',
              icon: 'design-icon-mofang',
              name: 'tab',
            },
            {
              text: '宫格',
              type: '1-7',
              icon: 'design-icon-mofang',
              name: 'grid',
            },
            {
              text: '公告',
              type: '1-8 ',
              icon: 'design-icon-gonggao',
              name: 'notify',
            },

            {
              text: '视频',
              type: '1-9',
              icon: 'design-icon-shipin',
              name: 'videos',
            },
            {
              text: '富文本',
              type: '1-10',
              icon: 'design-icon-fuwenben',
              name: 'richtext',
            },
            // {
            //   text: '辅助分割',
            //   type: '1-11',
            //   icon: 'design-icon-Component-fuzhufenge',
            //   name: 'divider',
            // },
            {
              text: '表单元素',
              type: '1-12',
              icon: 'design-icon-jinrudianpu',
              name: 'formitem',
            },
            {
              text: '悬浮',
              type: '1-13',
              icon: 'design-icon-wangye',
              name: 'suspension',
            },
            {
              text: '卡片',
              type: '1-14',
              icon: 'design-icon-wangye',
              name: 'card',
            },
          ],
        },
        {
          title: '业务组件',
          comList: [
            {
              text: '列表',
              type: '2-1',
              icon: 'design-icon-dianpubijikapian',
              name: 'list',
            },
            // {
            //   text: '文章模块',
            //   type: '2-2',
            //   icon: 'design-icon-dianpubijikapian',
            //   name: 'articlelist',
            // },
            {
              text: '表单',
              type: '2-3',
              elIcon: 'el-icon-tickets',
              name: 'investigate',
            }
          ]
        },
      ],
    }
  },
  computed: {
    systemThemeColor() {
      return {
        "--background-hover": this.$store.state.settings.theme,
      }
    }
  },
  methods: {
    /**
     * 当用户开始拖动元素或选择文本时触发此事件
     *
     * @param {Object} event event对象
     */
    drag(event) {
      /* 开启穿透 */
      this.pointer.show = true
      /* 传递参数 */
      event.dataTransfer.setData('componentName', event.target.dataset.name)
    },

    /**
     * 当拖动操作结束时（释放鼠标按钮或按下退出键），会触发此事件
     *
     * @param {Object} event event对象
     */
    dragends() {
      /* 关闭穿透 */
      this.pointer.show = false
    },
  },
}
</script>

<style scoped lang="less">
/* 组件 */
.sliderassembly {
  width: 275px;
  height: 100%;
  overflow-y: scroll;
  border-right: 1px solid #ebedf0;
  box-sizing: border-box;
  padding: 0 12px;
  background: #fff;
  /* 滚动条 */
  &::-webkit-scrollbar {
    width: 1px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: #155bd4;
  }
  /deep/.el-collapse-item__header,
  /deep/.el-collapse-item__wrap {
    border-bottom: 0 !important;
  }

  /* 组件列表 */
  .componList {
    display: inline-flex;
    flex-direction: column;
    justify-content: center;
    width: 80px;
    height: 88px;
    margin-bottom: 8px;
    align-items: center;
    cursor: all-scroll;
    transition: all 0.3s;
    &:hover {
      background: var(--background-hover);
      border-radius: 2px;
      font-weight: 700;
      i,
      p,
      span {
        color: #fff;
      }
    }
    /* 图标 */
    i {
      font-size: 32px;
      width: 32px;
      height: 32px;
      line-height: 32px;
      color: #b0a8a8;
      margin-top: 4px;
    }
    /* 标题 */
    p {
      font-size: 12px;
      color: #323233;
      margin-top: 4px;
    }
    /* 数量 */
    span {
      color: #7d7e80;
      margin-top: 4px;
      font-size: 10px;
    }
  }
}
</style>
