<template>
  <div class="layout">
    <section class="subject">
      <div class="home">
        <!-- 按钮集合 -->
        <section class="buttons">
          <p style="font-size: 12px;color: #4f4f4f;margin-left: 15px;cursor: pointer;">
            <!-- 返回 -->
          </p>
          <div>
            <el-button @click="catJson">查看JSON </el-button>
            <el-button :disabled="userObj.loginName !== 'sysman' && layoutDesignObject.layoutDesignCode === 'default'"
                       @click="addLayoutDesign" type="primary">保存</el-button>
          </div>
        </section>

        <!-- 装修操作 -->
        <section class="operation">
          <!-- 组件 -->
          <sliderassembly :pointer="pointer" />

          <!-- 手机 -->
          <div class="phone">
            <section class="phoneAll" ref="imageTofile" id="imageTofile">
              <img src="./assets/images/phoneTop.png" alt="" class="statusBar" />

              <!-- 头部导航 -->
              <header-top :pageSetup="pageSetup" @click.native="headTop" />

              <!-- 主体内容 -->
              <section
                class="phone-container"
                :style="{ 'background-color': pageSetup.bgColor, backgroundImage: 'url(' + pageSetup.bgImg + ')' }"
                @drop="drop($event)"
                @dragover="allowDrop($event)"
                @dragleave="dragleaves($event)"
              >
                <div :class="pointer.show ? 'pointer-events' : ''">
                  <!-- 动态组件 -->
                  <component
                    :is="item.component"
                    v-for="(item, index) in pageComponents"
                    :key="index"
                    :datas="item.itemContent"
                    :style="{ border: item.active && deleShow ? '2px solid #155bd4' : '' }"
                    @click.native="activeComponent(item, index)"
                    class="componentsClass"
                    :data-type="item.type"
                  >
                    <div
                      v-show="deleShow"
                      class="deles"
                      slot="deles"
                      @click.stop="deleteObj(index)"
                    >
                      <!-- 删除组件 -->
                      <span class="iconfont-design design-icon-sanjiaoxingzuo"></span>
                      {{ item.moduleTitle }}
                      <i class="el-icon-delete-solid" />
                    </div>
                  </component>
                </div>
              </section>
            </section>
            <!-- 底部 -->
          </div>

          <!-- 页面设置tab -->
          <div class="decorateTab" :style="systemThemeColor">
            <span
              :class="rightcom === 'decorate' ? 'active' : ''"
              @click="rightcom = 'decorate'"
            >
              <i class="iconfont-design design-icon-wangye" />
              页面设置
            </span>
            <span
              :class="rightcom === 'componenmanagement' ? 'active' : ''"
              @click="rightcom = 'componenmanagement'"
            >
              <i class="iconfont-design design-icon-zujian" />
              组件管理
            </span>
            <span
              class="active"
              v-show="rightcom !== 'componenmanagement' && rightcom !== 'decorate'"
            >
              <i class="iconfont-design design-icon-zujian" />
              组件设置
            </span>
          </div>

          <!-- 右侧工具栏 -->
          <div class="decorateAll">
            <!-- 页面设置 -->
            <transition name="decorateAnima">
              <!-- 动态组件 -->
              <component
                :is="rightcom"
                :datas="currentproperties"
                @componenmanagement="componenmanagement"
              />
            </transition>
          </div>
        </section>
      </div>
    </section>
    <el-dialog :visible.sync="viewJsonVisible" class="jsonview">
      <div v-html="syntaxHighlight(json)" />
    </el-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import layoutDesignUtils from "@/views/system/layout-design/utils/layoutDesign";
import componentProperties from "@/views/system/layout-design/utils/componentProperties"; // 组件数据
import { get, update } from "@/api/system/layoutDesign";
import headerTop from "@/views/system/layout-design/components/headerTop";
import sliderassembly from "@/views/system/layout-design/components/sliderassembly";
import {mapState} from "vuex";
export default {
  name: 'Design',
  components: { sliderassembly, headerTop },
  data() {
    return {
      id: null, //当前页面
      deleShow: true, //删除标签显示
      index: '', //当前选中的index
      rightcom: 'decorate', //右侧组件切换
      currentproperties: {}, //当前属性
      pageSetup: {
        // 页面设置属性
        name: '页面标题', //页面名称
        details: '', //页面描述
        isPerson: false, // 是否显示个人中心
        isBack: true, // 是否返回按钮
        titleHeight: 35, // 高度
        bgColor: 'rgba(249, 249, 249, 10)', //背景颜色
        bgImg: '', // 背景图片
      },
      pageComponents: [], //页面组件
      offsetY: 0, //记录上一次距离父元素高度
      pointer: { show: false }, //穿透
      onlyOne: ['1-5', '1-16'], // 只能存在一个的组件(组件的type)
      layoutDesignId: '', // 布局主键
      layoutDesignObject: {},
      viewJsonVisible: false,
      json: ''
    }
  },
  created() {
    // 组件自动化全局注册
    const requireComponent = require.context(
      // 其组件目录的相对路径
      './components',
      // 是否查询其子目录
      true,
      // 匹配基础组件文件名的正则表达式
      /\.vue$/
    )
    requireComponent.keys().forEach(fileName => {
      // 获取组件配置
      const componentConfig = requireComponent(fileName)
      // 全局注册组件
      Vue.component(
        componentConfig.default.name,   // 此处的name,是组件属性定义的name
        // 如果这个组件选项是通过 `export default` 导出的，
        // 那么就会优先使用 `.default`，
        // 否则回退到使用模块的根。
        componentConfig.default
      )
    })
    this.layoutDesignId = this.$route.params && this.$route.params.id;
    if(this.layoutDesignId !== null) {
      this.getLayoutDesign()
    }
  },
  computed: {
    systemThemeColor() {
      return {
        "--background-hover": this.$store.state.settings.theme,
      }
    },
    ...mapState({
      userObj: (state) => state.user.userInfo,
    })
  },
  mounted() {
    this.pageSetup.name = '页面标题'
    this.currentproperties = this.pageSetup
  },
  watch: {
    /* 监听右侧属性设置切换 */
    rightcom(newval) {
      if (newval === 'decorate') {
        layoutDesignUtils.forEach(this.pageComponents, (res) => {
          /* 修改选中 */
          if (res.active === true) res.active = false
        })
        this.currentproperties = this.pageSetup
        return
      }
      if (newval === 'componenmanagement') {
        /* 替换 */
        layoutDesignUtils.forEach(this.pageComponents, (res) => {
          /* 修改选中 */
          if (res.active === true) res.active = false
        })
        this.currentproperties = this.pageComponents
      }
    },
  },
  methods: {
    /* json预览 */
    catJson() {
      this.json = `{
            <br/>
            "id": ${this.id},
            <br/>
            "name": "${this.pageSetup.name}",
            <br/>
            "templateJson": ${JSON.stringify(this.pageSetup)},
            <br/>
            "component": ${JSON.stringify(this.pageComponents)}
            <br/>
          }`
      this.viewJsonVisible = true
      // this.$alert(
      //   `{
      //     <br/>
      //     "id": ${this.id},
      //     <br/>
      //     "name": "${this.pageSetup.name}",
      //     <br/>
      //     "templateJson": ${JSON.stringify(this.pageSetup)},
      //     <br/>
      //     "component": ${JSON.stringify(this.pageComponents)}
      //     <br/>
      //   }`,
      //   '查看JSON',
      //   {
      //     confirmButtonText: '确定',
      //     customClass: 'jsonview',
      //     dangerouslyUseHTMLString: true,
      //     callback: () => {},
      //   }
      // )
    },
    getLayoutDesign() {
      let form =  {
        id: this.layoutDesignId,
        layoutDesignValue: `{
        "id": ${this.id},
        "name": "${this.pageSetup.name}",
        "templateJson": ${JSON.stringify(this.pageSetup)},
        "component": ${JSON.stringify(this.pageComponents)}
        }`.replace(/[\r\n]/g,"").replace(/\ +/g,"")
      }
      get({ id: this.layoutDesignId }).then((response) => {
        if (!response.success) {
          this.$message.error(response.message);
        } else {
          const data = response.data
          this.layoutDesignObject = data
          if(data.componentId !== null) {
            this.id = data.componentId
          }
          if(data.componentTemplateJson !== null) {
            this.pageSetup = data.componentTemplateJson
          }
          if(data.component !== null) {
            this.pageComponents = data.component
          }
        }
      });
    },
    /* 保存布局设计 */
    addLayoutDesign() {
      let form =  {
        id: this.layoutDesignId,
        layoutDesignValue: `{
        "id": ${this.id},
        "name": "${this.pageSetup.name}",
        "templateJson": ${JSON.stringify(this.pageSetup)},
        "component": ${JSON.stringify(this.pageComponents)}
        }`.replace(/[\r\n]/g,"").replace(/\ +/g,"")
      }
      update(form).then((response) => {
        if (!response.success) {
          this.$message.error(response.message);
          this.saveLoading = false;
        } else {
          this.msgSuccess("修改成功");
          this.open = false;
          this.saveLoading = false;
        }
      });
    },
    /*  标题切换 */
    headTop() {
      this.rightcom = 'decorate'
      /* 替换 */
      layoutDesignUtils.forEach(this.pageComponents, (res) => {
        /* 修改选中 */
        if (res.active === true) res.active = false
      })
    },
    /**
     * 当将元素或文本选择拖动到有效放置目标（每几百毫秒）上时，会触发此事件
     *
     * @param {Object} event event对象
     */
    allowDrop(event) {
      //阻止浏览器的默认事件
      event.preventDefault()

      /* 获取鼠标高度 */
      let eventoffset = event.offsetY

      /* 如果没有移动不触发事件减少损耗 */
      if (this.offsetY === eventoffset) return
      else this.offsetY = eventoffset

      /* 获取组件 */
      const childrenObject = event.target.children[0]

      // 一个以上的组件计算
      if (this.pageComponents.length) {
        /* 如果只有一个组件并且第一个是提示组件直接返回 */
        if (
          this.pageComponents.length === 1 &&
          this.pageComponents[0].type === 0
        )
          return

        /* 如果鼠标的高度小于第一个的一半直接放到第一个 */
        if (eventoffset < childrenObject.children[0].clientHeight / 2) {
          /* 如果第一个是提示组件直接返回 */
          if (this.pageComponents[0].type === 0) return

          /* 删除提示组件 */
          this.pageComponents = this.pageComponents.filter(
            (res) => res.component !== 'placementarea'
          )

          /* 最后面添加提示组件 */
          this.pageComponents.unshift({
            component: 'placementarea',
            type: 0,
          })

          return
        }

        /* 记录距离父元素高度 */
        const childOff = childrenObject.offsetTop

        /* 鼠标在所有组件下面 */
        if (
          eventoffset > childrenObject.clientHeight ||
          childrenObject.lastChild.offsetTop -
          childOff +
          childrenObject.lastChild.clientHeight / 2 <
          eventoffset
        ) {
          /* 最后一个组件是提示组件返回 */
          if (this.pageComponents[this.pageComponents.length - 1].type === 0)
            return

          /* 清除提示组件 */
          this.pageComponents = this.pageComponents.filter(
            (res) => res.component !== 'placementarea'
          )

          /* 最后一个不是提示组件添加 */
          this.pageComponents.push({
            component: 'placementarea',
            type: 0,
          })

          return
        }

        const childrens = childrenObject.children

        /* 在两个组件中间，插入 */
        for (let i = 0, l = childrens.length; i < l; i++) {
          const childoffset = childrens[i].offsetTop - childOff

          if (childoffset + childrens[i].clientHeight / 2 > event.offsetY) {
            /* 如果是提示组件直接返回 */
            if (this.pageComponents[i].type === 0) break

            if (this.pageComponents[i - 1].type === 0) break

            /* 清除提示组件 */
            this.pageComponents = this.pageComponents.filter(
              (res) => res.component !== 'placementarea'
            )

            this.pageComponents.splice(i, 0, {
              component: 'placementarea',
              type: 0,
            })
            break
          } else if (childoffset + childrens[i].clientHeight > event.offsetY) {
            if (this.pageComponents[i].type === 0) break

            if (
              !this.pageComponents[i + 1] ||
              this.pageComponents[i + 1].type === 0
            )
              break

            this.pageComponents = this.pageComponents.filter(
              (res) => res.component !== 'placementarea'
            )

            this.pageComponents.splice(i, 0, {
              component: 'placementarea',
              type: 0,
            })

            break
          }
        }
      } else {
        /* 一个组件都没有直接push */
        this.pageComponents.push({
          component: 'placementarea',
          type: 0,
        })
      }
    },
    /**
     * 当在有效放置目标上放置元素或选择文本时触发此事件
     *
     * @param {Object} event event对象
     */
    drop(event) {
      /* 获取数据 */
      let data = layoutDesignUtils.deepClone(
        componentProperties.get(event.dataTransfer.getData('componentName'))
      )

      /* 查询是否只能存在一个的组件且在第一个 */
      let someOne = this.pageComponents.some((item, index) => {
        return (
          item.component === 'placementarea' &&
          index === 0 &&
          this.onlyOne.includes(data.type)
        )
      })
      if (someOne) {
        this.$message.info('固定位置的组件(如: 底部导航、悬浮)不能放在第一个!')
        /* 删除提示组件 */
        this.dragleaves()
        return
      }

      /* 查询是否只能存在一个的组件 */
      let someResult = this.pageComponents.some((item) => {
        return (
          this.onlyOne.includes(item.type) &&
          item.component === event.dataTransfer.getData('componentName')
        )
      })
      if (someResult) {
        this.$message.info('当前组件只能添加一个!')
        /* 删除提示组件 */
        this.dragleaves()
        return
      }

      /* 替换 */
      layoutDesignUtils.forEach(this.pageComponents, (res, index) => {
        /* 修改选中 */
        if (res.active === true) res.active = false
        /* 替换提示 */
        this.index = index
        if (res.component === 'placementarea')
          this.$set(this.pageComponents, index, data)
      })

      /* 切换组件 */
      this.rightcom = data.style
      /* 丢样式 */
      this.currentproperties = data.itemContent

      console.log(
        data,
        this.rightcom,
        this.currentproperties,
        '----------components data'
      )
    },
    /* 当拖动的元素或文本选择离开有效的放置目标时，会触发此事件 */
    dragleaves() {
      /* 删除提示组件 */
      this.pageComponents = this.pageComponents.filter(
        (res) => res.component !== 'placementarea'
      )
    },
    /**
     * 选择组件
     *
     * @param {Object} res 当前组件对象
     * @param {Object} index 当前组件坐标
     */
    activeComponent(res, index) {
      this.index = index
      /* 切换组件 */
      this.rightcom = res.style
      /* 丢样式 */
      this.currentproperties = res.itemContent
      /* 替换 */
      layoutDesignUtils.forEach(this.pageComponents, (res) => {
        /* 修改选中 */
        if (res.active === true) res.active = false
      })
      /* 选中样式 */
      res.active = true
    },
    /* 删除组件 */
    deleteObj(index) {
      this.pageComponents.splice(index, 1)
      if (this.index === index) this.rightcom = 'decorate'
      if (index < this.index) this.index = this.index - 1
    },
    /**
     * 切换组件位置
     *
     * @param {Object} res 组件切换后返回的位置
     */
    componenmanagement(res) {
      this.pageComponents = res
    },
    syntaxHighlight(json) {
      if (typeof json != 'string') {
        json = JSON.stringify(json, undefined, 2);
      }
      json = json.replace(/&/g, '&').replace(/</g, '<').replace(/>/g, '>');
      return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function (match) {
        var cls = 'number';
        if (/^"/.test(match)) {
          if (/:$/.test(match)) {
            cls = 'key';
          } else {
            cls = 'string';
          }
        } else if (/true|false/.test(match)) {
          cls = 'boolean';
        } else if (/null/.test(match)) {
          cls = 'null';
        }
        return '<span class="' + cls + '">' + match + '</span>';
      });
    }
}
}
</script>

<style lang="less" scoped>
.pointer-events {
  pointer-events: none;
}
@import "assets/icon/designfonts/design-iconfont.css";
.layout {
  width: 100%;
  height: 100%;
  /* 主体 */
  .subject {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    .home {
      width: 100%;
      height: 100%;

      /* 删除组件 */
      .deles {
        position: absolute;
        min-width: 80px;
        text-align: center;
        line-height: 25px;
        background: #fff;
        height: 25px;
        font-size: 12px;
        left: 103%;
        top: 50%;
        transform: translateY(-50%);
        .design-icon-sanjiaoxingzuo {
          position: absolute;
          left: -11px;
          color: #fff;
          font-size: 12px;
          top: 50%;
          transform: translateY(-50%);
        }
        &:hover {
          i {
            display: block;
            position: absolute;
            left: 0;
            font-size: 16px;
            top: 0;
            text-align: center;
            line-height: 25px;
            width: 100%;
            color: #fff;
            height: 100%;
            z-index: 10;
            background: rgba(0, 0, 0, 0.5);
          }
          .design-icon-sanjiaoxingzuo {
            color: rgba(0, 0, 0, 0.5);
          }
        }
        i {
          display: none;
        }
      }

      /* 按钮集合 */
      .buttons {
        height: 70px;
        border-bottom: 1px solid #ebedf0;
        display: flex;
        justify-content: space-between;
        box-sizing: border-box;
        padding-right: 15px;
        align-items: center;
        /* 下拉 */
        .frop {
          padding-right: 15px;
          .el-button.el-button--primary.el-dropdown-selfdefine {
            background: #fff;
            color: #000;
            border: 1px solid #dcdee0;
          }
        }
        .el-button {
          font-size: 14px;
          padding: 0 16px;
          height: 30px;
          //&.el-button--primary {
          //  background: #155bd4;
          //}
          //&.el-button--danger {
          //  background: red;
          //}
        }
      }

      /* 操作主体 */
      .operation {
        width: 100%;
        height: 92%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        background: #f7f8fa;
      }

      /* 手机 */
      .phone {
        width: 40%;
        height: 100%;
        overflow-y: scroll;
        display: flex;
        justify-content: center;
        background: #f7f8fa;
        &::-webkit-scrollbar {
          width: 1px;
        }
        // &::-webkit-scrollbar-thumb {
        //   background-color: #155bd4;
        // }

        /* 手机样式 */
        .phoneAll {
          width: 375px;
          min-height: 725px;
          max-height: 800px;
          box-shadow: 0 0 14px 0 rgba(0, 0, 0, 0.1);
          //margin: 45px 0;
          position: relative;
          /* 状态栏 */
          .statusBar {
            width: 100%;
            display: block;
          }

          /* 主体内容 */
          .phone-container {
            min-height: 603px;
            box-sizing: border-box;
            cursor: pointer;
            width: 100%;
            position: relative;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            .componentsClass {
              border: 1px solid #fff;
              &:hover {
                border: 1px dashed #155bd4;
              }
            }
          }
          &::-webkit-scrollbar {
            width: 10px;
          }
          &::-webkit-scrollbar-thumb {
            background-color: #155bd4;
            height: 50px;
            -webkit-border-radius: 4px;
            outline: 2px solid #fff;
            outline-offset: -2px;
            border: 2px solid #fff;
          }
        }
      }

      /* 右侧工具栏 */
      .decorateAll {
        width: 376px;
        height: 100%;
        overflow-y: scroll;
        overflow-x: hidden;
        position: relative;
        padding: 0 12px;
        background: #fff;
        max-height: 700px;
        &::-webkit-scrollbar {
          width: 10px;
        }
        &::-webkit-scrollbar-thumb {
          background-color: #155bd4;
          height: 50px;
          -webkit-border-radius: 4px;
          outline: 2px solid #fff;
          outline-offset: -2px;
          border: 2px solid #fff;
        }
      }

      /* 页面设置tab */
      .decorateTab {
        position: relative;
        display: flex;
        //right: 380px;
        //top: 115px;
        flex-direction: column;
        span {
          background-color: #fff;
          box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
          border-radius: 2px;
          width: 94px;
          height: 32px;
          display: inline-block;
          text-align: center;
          line-height: 32px;
          margin-bottom: 12px;
          transition: all 0.8s;
          cursor: pointer;
          &.active {
            background-color: var(--background-hover);
            color: #fff;
          }
          /* 图标 */
          i {
            font-size: 12px;
            margin-right: 5px;
          }
        }
      }
    }
  }
}

// 页面切换动画
.fade-enter-active {
  transition: all 0.5s ease;
}
.fade-leave-active {
  transition: all 0.5s ease;
}
/* // 页面切换动画 */
.layoutFade-enter-active {
  transition: all 0.5s ease;
}
.layoutFade-leave-active {
  transition: all 0.5s ease;
}
.layoutFade-enter {
  transform: translateY(5px);
  opacity: 0;
}
.layoutFade-leave-to {
  transform: translateY(5px);
  opacity: 0;
}
/* 动画 */
.decorateAnima-enter-active {
  transition: all 0.5s ease;
}
.decorateAnima-leave-active {
  transition: all 0.5s ease;
}
.decorateAnima-enter {
  transform: translate(8px, 8px);
  opacity: 0;
}
.decorateAnima-leave-to {
  transform: translate(8px, 8px);
  opacity: 0;
}
</style>
<style lang="less">
  .jsonview .el-dialog__body {
    padding: 3px 30px;
    overflow-y: auto;
    height: calc(100vh - 140px);
    &::-webkit-scrollbar-thumb {
       background-color: #155bd4;
       height: 50px;
       -webkit-border-radius: 4px;
       outline: 2px solid #fff;
       outline-offset: -2px;
       border: 2px solid #fff;
     }
  }
</style>
<style scoped>
/deep/ pre {
  outline: 1px solid #ccc;
  padding: 5px;
  margin: 5px;
}

/deep/ .string {
  color: green;
}

/deep/ .number {
  color: darkorange;
}

/deep/ .boolean {
  color: blue;
}

/deep/ .null {
  color: magenta;
}

/deep/ .key {
  color: red;
}
</style>
