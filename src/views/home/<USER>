<template>
  <div class="app-container" style="margin: 0; padding: 0; background: #fff">
    <div class="banner-img">
      <img src="@/assets/images/mail/banner.jpg" alt="" />
    </div>
    <div class="crumb">
      <div class="break">
        <el-breadcrumb separator="/" class="breadcrumb">
          <i
            class="el-icon-location-outline"
            style="margin-right: 10px; font-size: 20px; color: #c3c3c3"
          ></i>
          <span style="color: #333333">您的位置：</span>
          <el-breadcrumb-item :to="{ path: '/portal/home' }" replace
            >门户首页
          </el-breadcrumb-item>
          <el-breadcrumb-item
            ><a>{{ title }}</a></el-breadcrumb-item
          >
        </el-breadcrumb>

        <el-form
          :inline="true"
          v-if="$store.getters.customParam.userType === 'admin'"
        >
          <el-form-item label="租 户:">
            <el-select
              v-model="sendInfo.tenantId"
              style="width: 200px"
              @change="tenantChange"
            >
              <el-option
                v-for="item in tenantList"
                :key="item.tenantId"
                :label="item.tenantName"
                :value="item.tenantId"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-card class="note-card" shadow="never">
      <div slot="header" class="card-title">
        <!-- <el-row :gutter="2" type="flex" align="center">
          <el-col :span="5" class="c-r-t"
            ><span class="card-title-left"
              ><i class="icon iconfont icon-gonggao"></i>
              <span>{{ title }}列表</span></span
            ></el-col
          >
        </el-row> -->
      </div>

      <el-table
        ref="multipleTable"
        :data="mail"
        tooltip-effect="dark"
        @row-click="goAdd"
        v-loading="loading"
        v-if="mail"
      >
        <el-table-column type="selection" width="60" align="center">
        </el-table-column>
        <el-table-column
          label="消息标题"
          align="left"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span :class="scope.row.isRead == 0 ? 'unRead' : 'read'">{{
              scope.row.noticeTitle
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          label="接收时间"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span :class="scope.row.isRead == 0 ? 'unRead' : 'read'">{{
              scope.row.createDate.substring(0, 19)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="address"
          label="创建者"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span :class="scope.row.isRead == 0 ? 'unRead' : 'read'">{{
              scope.row.createByName
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="address"
          label="消息类型"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span :class="scope.row.isRead == 0 ? 'unRead' : 'read'">{{
              scope.row.noticeType === "notice_type_one"
                ? "新闻"
                : "" || scope.row.noticeType === "2"
                ? "通知"
                : "" || scope.row.noticeType === "notice_type_two"
                ? "公告"
                : ""
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="address"
          label="是否已读"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span :class="scope.row.isRead == 0 ? 'unRead' : 'read'">{{
              scope.row.isRead == 0 ? "未读" : "已读"
            }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="sendInfo.pageNum"
        :limit.sync="sendInfo.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script>
import AppLink from "@/components/Link";
import { getMailInfo } from "/src/api/system/notice";
import { list as tenantList } from "@/api/system/tenant";

export default {
  name: "detail",
  components: { AppLink },
  data() {
    return {
      loading: true,
      mail: [],
      sendInfo: {
        pageNum: 1,
        pageSize: 10,
        tenantId: this.$store.getters.customParam.tenantId,
      },
      total: 0,
      title: "",
      tenantList: [],
    };
  },

  created() {
    this.title = "站内信";
    this.getList();
    this.getTenantList();
  },
  methods: {
    getList() {
      this.loading = true;
      getMailInfo(this.sendInfo).then((res) => {
        this.mail = res.data.records;
        this.total = res.data.total;
        this.loading = false;
      });
    },
    goAdd(row, column) {
      this.$router.push({
        path: "addInfo",
        query: {
          noticeId: row.noticeId,
          content: row.noticeContent,
          time: row.createDate,
          title: this.title,
          mailTitle: row.noticeTitle,
        },
      });
    },
    getTenantList() {
      tenantList().then((response) => {
        this.tenantList = response.data;
      });
    },
    tenantChange() {
      this.getList();
    },
  },
};
</script>

<style scoped lang="scss">
@import "../../assets/styles/element-variables.scss";
@import "../../layout/portal/components/index.scss";
</style>
