// base color
$blue: #324157;
$light-blue: #3a71a8;
$red: #c03639;
$pink: #e65d6e;
$green: #30b08f;
$tiffany: #4ab7bd;
$yellow: #fec171;
$panGreen: #30b08f;

// sidebar
$menuText: #bfcbd9;
$menuActiveText: #409eff;
$subMenuActiveText: #f4f4f5; // https://github.com/ElemeFE/element/issues/12951

$menuBg: #304156;
$menuHover: #263445;
$sidebarTitle: #ffffff;

$menuLightBg: #ffffff;
$menuLightHover: #f0f1f5;
$sidebarLightTitle: #001529;

$subMenuBg: #1f2d3d;
$subMenuHover: #001528;

$sideBarWidth: 200px;
$potalSideBarWidth: 79px;
$potalMenuBg: linear-gradient(#51b3fa, #2776d8);
// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  menuLightBg: $menuLightBg;
  menuLightHover: $menuLightHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
  sidebarTitle: $sidebarTitle;
  sidebarLightTitle: $sidebarLightTitle;
  potalSideBarWidth: $potalSideBarWidth;
  potalMenuBg: $potalMenuBg;
}
