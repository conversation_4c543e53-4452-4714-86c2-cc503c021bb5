<template>
    <div class="portal-body">
        <div class="product-list">
            <div class="table-wrapper box-bg">
                <div class="search-wrapper  box-bg" style="padding: 0 0">
                    <el-form ref="form" :model="formQuery" label-width="200px" label-position="left">
                        <el-row>
                            <el-col :span="7">
                                <el-form-item label="地市">
                                    <el-col>
                                        <el-select v-model="formQuery.city" placeholder="请选择" clearable>
                                            <el-option v-for="item in cityList" :key="item.cityCode"
                                                :label="item.cityName" :value="item.cityCode">
                                            </el-option>
                                        </el-select>
                                    </el-col>
                                </el-form-item>
                            </el-col>
                            <el-col :span="7">
                                <el-form-item label="投资申请开始结束时间">
                                    <el-date-picker v-model="formDataRange" type="daterange" range-separator="至"
                                        start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd"
                                        style="width: 255px;">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="7">
                                <el-form-item label="客户名称">
                                    <el-col>
                                        <el-input v-model="formQuery.client" clearable placeholder="请输入"></el-input>
                                    </el-col>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="7">
                                <el-form-item label="投资申请审批状态">
                                    <el-col>
                                        <el-select v-model="formQuery.status" placeholder="请选择" clearable>
                                            <el-option label="全部" value=""></el-option>
                                            <el-option label="处理中" value="处理中"></el-option>
                                            <el-option label="已完成" value="已完成"></el-option>
                                        </el-select>
                                    </el-col>
                                </el-form-item>
                            </el-col>
                            <el-col :span="7">
                                <el-form-item label="投资申请流程实例号">
                                    <el-col>
                                        <el-input v-model="formQuery.inc" clearable placeholder="请输入"></el-input>
                                    </el-col>
                                </el-form-item>
                            </el-col>
                            <el-col :span="7">
                                <el-form-item label="项目名称">
                                    <el-col>
                                        <el-input v-model="formQuery.project" clearable
                                            placeholder="请输入项目名称"></el-input>
                                    </el-col>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="7">
                                <el-form-item label="5G场景">
                                    <el-col>
                                        <el-input v-model="formQuery.changjing" clearable
                                            placeholder="请输入投资决策单号"></el-input>
                                    </el-col>
                                </el-form-item>
                            </el-col>
                            <el-col :span="7">
                                <el-form-item label="投资决策单号">
                                    <el-col>
                                        <el-input v-model="formQuery.number" clearable
                                            placeholder="请输入投资决策单号"></el-input>
                                    </el-col>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <div class="btn-group" style="top: 4px;right: 0;">
                            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                            <el-button type="primary" icon="el-icon-search" size="mini"
                                @click="handleQuery">查询</el-button>
                        </div>
                    </el-form>
                </div>
                <div class="flex-between">
                    <div class="table-title">5G专网端到端流程进度</div>
                    <div>
                        <el-button size="small" icon="el-icon-upload2" @click="handleExport">导出</el-button>
                    </div>
                </div>
                <el-divider></el-divider>
                <div class="table-con">
                    <UniTable v-if="cityList.length > 0" ref="table" :columns="columns" :params="formQuery"
                        action="/sanquan/private5GWorkFlow/queryWorkFlowList">
                        <template v-slot:status="scope">
                            <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                        </template>
                        <template v-slot:operation="scope">
                            <el-button type="text" size="small" @click="handleDetail(scope.row)">查看流程详情
                            </el-button>
                        </template>
                    </UniTable>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import UniTable from "@/components/UniTable/UniTable.vue";
    import { getCityList, exportWorkFlowList } from "@/api/sw/5g/progress";

    export default {
        name: "Private5GProgress",
        components: { UniTable },
        data() {
            return {
                // 查询参数
                formDataRange: [this.getLastWeekDate(), this.getCurrentDate()],
                formQuery: {
                    pageNum: 1,
                    pageSize: 10,
                    inc: "",
                    city: "",
                    stime: this.getLastWeekDate(),
                    etime: this.getCurrentDate(),
                    client: "",
                    status: "",
                    project: "",
                    changjing: "",
                    number: ""
                },
                // 地市列表
                cityList: [],
                // 表格列配置
                columns: [
                    { props: "city", label: "地市", width: 150 },
                    { props: "inc", label: "申请流程实例号", width: 150 },
                    { props: "user", label: "申请人", width: 100 },
                    { props: "stime", label: "申请时间", width: 180 },
                    { props: "project", label: "项目名称", width: 200 },
                    { props: "projecttype", label: "项目类别", width: 100 },
                    { props: "changjing", label: "5G场景", width: 100 },
                    { props: "client", label: "客户名称", minWidth: 250 },
                    { props: "wtfx", label: "问题分析", width: 500 },
                    { props: "xqfx", label: "需求分析", width: 200 },
                    { props: "status", label: "投资申请审批状态", slot: true, width: 100 },
                    { props: "number", label: "投资决策单号", width: 200 },
                    { props: "operation", label: "操作", slot: true, width: 120, fixed: "right" }
                ]
            };
        },
        created() {
            this.getDict();
        },
        watch: {
            formDataRange(val) {
                if (!val || val.length === 0) {
                    this.formQuery.stime = "";
                    this.formQuery.etime = "";
                } else if (val.length === 2) {
                    this.formQuery.stime = val[0];
                    this.formQuery.etime = val[1];
                }
            }
        },
        methods: {
            async getDict() {
                try {
                    getCityList().then(response => {
                        if (response.code === '1') {
                            this.cityList = response.data;
                            if (this.cityList.length != 1) {
                                this.cityList.unshift({
                                    cityCode: "",
                                    cityName: "全省"
                                })
                            } else {
                                this.formQuery.city = this.cityList[0].cityCode
                            }
                        }
                    })
                } catch (error) {
                    console.error("获取字典数据失败", error);
                }
            },
            handleQuery() {
                const query = { ...this.formQuery };
                if (this.formDataRange && this.formDataRange.length === 2) {
                    query.stime = this.formDataRange[0];
                    query.etime = this.formDataRange[1];
                } else {
                    // 当日期范围为空时，清空时间查询参数
                    query.stime = "";
                    query.etime = "";
                }
                delete query.dateRange;
                this.$refs.table.loadData(query);
            },
            handleExport() {
                const query = { ...this.formQuery };
                if (this.formDataRange && this.formDataRange.length === 2) {
                    query.stime = this.formDataRange[0];
                    query.etime = this.formDataRange[1];
                }
                query.pageNum = 1;
                query.pageSize = 99999999;
                this.$confirm("是否确认导出流程进度列表?", "警告", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    this.exportExcel('/sanquan/private5GWorkFlow/exportWorkFlowList', JSON.stringify(query), '5G专网端到端流程进度列表.xlsx')
                })
            },
            resetQuery() {
                this.formDataRange = [this.getLastWeekDate(), this.getCurrentDate()];
                this.formQuery = {
                    pageNum: 1,
                    pageSize: 10,
                    city: "",
                    stime: this.getLastWeekDate(),
                    etime: this.getCurrentDate(),
                    client: "",
                    status: "",
                    processId: "",
                    project: "",
                    changjing: "",
                    number: ""
                };
                this.$nextTick(() => {
                    if (this.$refs.form) {
                        this.$refs.form.resetFields();
                    }
                    this.handleQuery();
                });
            },
            getStatusType(status) {
                const statusMap = {
                    "已完成": "success",
                    "处理中": "primary",
                };
                return statusMap[status] || "info";
            },
            handleDetail(row) {
                sessionStorage.setItem('Private5GProgressData', JSON.stringify(row));
                this.$router.push({
                    name: 'Private5GProgressDetail'
                });
            },
            getCurrentDate() {
                const date = new Date();
                return this.formatDate(date);
            },
            getLastWeekDate() {
                const date = new Date();
                date.setDate(date.getDate() - 7);
                return this.formatDate(date);
            },
            formatDate(date) {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            }
        }
    };
</script>
<style scoped lang="less">
    @import "../../../assets/sanquan/index";

    .dialog-footer {
        display: flex;
        justify-content: center;
    }

    .vertical-text {
        display: flex;
        /* flex-direction: column; */
        writing-mode: vertical-lr;
        overflow-y: auto;
        text-orientation: upright;
        padding: 5px;

        .type-name {
            min-width: 0.125rem;
            background-color: #409eff;
            color: white;
            font-size: 0.125rem;
            margin-left: 0px;
        }
    }

    .check-text {
        display: flex;
        flex-direction: column;
        margin-left: 0.1rem;
        margin-top: 0.1rem;
    }

    .right-text-title {
        width: 3rem;
        height: 0.3rem;
        line-height: 0.3rem;
        padding-left: 0.1rem;
        background-color: #409eff;
        color: white;
    }

    .underline {
        width: 100%;
        height: 0.01rem;
        margin-top: 0.1rem;
        background-color: #f0f0f0;
    }
</style>
