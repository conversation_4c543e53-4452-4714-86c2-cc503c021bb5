<template>
  <el-dialog
    :title="title"
    :visible.sync="openDetailDialog"
    width="80%"
    @close="handleClose">
      <div class="table-wrapper box-bg" >
<!--        <div class="flex-between" style="padding: 0 0.2rem;">
          <div class="table-title">{{title}}</div>
          <div>

          </div>
        </div>-->
        <div class="table-content">
          <el-table  :data="resultList" style="width: 100%" :row-class-name="tableRowStripeClassName" max-height="600">
            <el-table-column label="自然客户ID" align="center" prop="customerId" />
            <el-table-column label="自然客户名称" align="left" prop="customName" min-width="110">
              <template slot-scope="scope">
                {{ sliceText(scope.row.customName) }}
              </template>
            </el-table-column>

            <el-table-column label="商机编号" align="center" prop="oppoNumber" v-if="paramInfo.dataType==='opportunity'||paramInfo.dataType==='assoOpportunity'"/>
            <el-table-column label="商机名称" align="left" prop="oppoName" v-if="paramInfo.dataType==='opportunity'||paramInfo.dataType==='assoOpportunity'" min-width="110"/>
            <el-table-column label="商机当前流程" align="center" prop="oppoCurrentStep" v-if="paramInfo.dataType==='opportunity'||paramInfo.dataType==='assoOpportunity'"/>
            <el-table-column label="商机预计合同总额(万元)" align="center" prop="oppoAmount" v-if="paramInfo.dataType==='opportunity'||paramInfo.dataType==='assoOpportunity'"/>
            <el-table-column label="商机创建时间" align="center" prop="oppoCreatedDate" v-if="paramInfo.dataType==='opportunity'||paramInfo.dataType==='assoOpportunity'"/>
            <el-table-column label="商机客户经理" align="center" prop="oppoUserName" v-if="paramInfo.dataType==='opportunity'||paramInfo.dataType==='assoOpportunity'"/>
            <el-table-column label="商机客户经理账号" align="center" prop="oppoUserLogin" v-if="paramInfo.dataType==='opportunity'||paramInfo.dataType==='assoOpportunity'"/>

            <el-table-column label="项目编码" align="center" prop="projectNumber" v-if="paramInfo.dataType==='project'||paramInfo.dataType==='assoProject'"/>
            <el-table-column label="项目名称" align="center" prop="projectName" v-if="paramInfo.dataType==='project'||paramInfo.dataType==='assoProject'"/>
            <el-table-column label="项目状态" align="center" prop="projectStatus" v-if="paramInfo.dataType==='project'||paramInfo.dataType==='assoProject'"/>
            <el-table-column label="立项金额(万元)" align="center" prop="projectAmount" v-if="paramInfo.dataType==='project'||paramInfo.dataType==='assoProject'"/>
            <el-table-column label="项目起始时间" align="center" prop="projectStartDate" v-if="paramInfo.dataType==='project'||paramInfo.dataType==='assoProject'"/>
            <el-table-column label="项目经理" align="center" prop="pmUserName" v-if="paramInfo.dataType==='project'||paramInfo.dataType==='assoProject'"/>
            <el-table-column label="项目经理电话" align="center" prop="pmTel" v-if="paramInfo.dataType==='project'||paramInfo.dataType==='assoProject'"/>
            <el-table-column label="项目经理账号" align="center" prop="pmLoginName" v-if="paramInfo.dataType==='project'||paramInfo.dataType==='assoProject'"/>
            <el-table-column label="项目经理所在部门" align="center" prop="pmAttra" v-if="paramInfo.dataType==='project'||paramInfo.dataType==='assoProject'"/>
            <el-table-column label="销售经理" align="center" prop="salesUserName" v-if="paramInfo.dataType==='project'||paramInfo.dataType==='assoProject'"/>

          </el-table>
          <div class="flex-end" style="margin-top: 0.14rem;">
            <el-pagination @current-change="handleCurrentChange"
                           :current-page.sync="queryParams.pageNum" :page-size="queryParams.pageSize" layout="total, prev, pager, next"
                           :total="total">
            </el-pagination>
          </div>
        </div>
      </div>
  </el-dialog>
</template>
<script>
import {resultError} from "@/utils/messageText";
import  { getPageList }  from '@/api/sw/targetMarketing/targetOppoProjInfo'
import UniTable from '@/components/UniTable/UniTable.vue'
import { mapState } from 'vuex'
export default {
  // import引入的组件需要注入到对象中才能使用
  components: { UniTable },
  name: 'detail',
  props: {
    openDetailDialog: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    paramInfo: {
      dataType: {
        type: String,
        default: ''
      },
      city: {
        type: String,
        default: ''
      },
      strategyId: {
        type: String,
        default: ''
      },
      contactor: {
        type: String,
        default: '靶向营销'
      },
      beginDate: {
        type: String,
        default: ''
      },
      endDate: {
        type: String,
        default: ''
      }
    },
  },
  data() {
    return {
      resultList: [],//商机或项目列表
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0
    }
  },
  watch: {
    paramInfo: {
      deep: true,
      handler(newValue,oldVal){
        this.queryParams.pageNum = 1;//当重新打开时设置第一页,不然数据多的如果上次翻到第大页,再点击条数少的打开会无数据
        console.info("info："+JSON.stringify(this.paramInfo))
        this.queryParams.contactor = this.paramInfo.contactor
        this.getDetailList()
      }
    },
  },
  created() {

  },
  mounted() {

  },
  // 监听属性类似于data概念
  computed: {
  },
  // 方法集合
  methods: {
    tableRowStripeClassName({ row, rowIndex }) {
      let rowStyle = "row-bg-high";
      if (rowIndex % 2 === 0) {
        rowStyle = "row-bg-normal";
      }
      return rowStyle;
    },
    getDetailList() {
      this.resultList = [];
      this.total = 0;

      let type = (this.paramInfo.contactor==='要客管家') ? '0' : this.paramInfo.contactor==='靶向营销' ? '1' : '2'
      let strategyId = this.paramInfo.strategyId;
      let city = this.paramInfo.city
      let dataType = this.paramInfo.dataType
      // this.queryParams = {...this.queryParams,strategyId,type,city,dataType}
      this.queryParams = {...this.paramInfo,...this.queryParams,type,strategyId,city,dataType}
      let pageNum = this.queryParams.pageNum;
      // alert('ss'+JSON.stringify(this.queryParams))
      getPageList(this.queryParams).then(response => {
        if (response.code === '1') {
          this.resultList = response.data.records;
          // alert('ssssss'+JSON.stringify(this.resultList))
          this.total = response.data.total;
          this.queryParams.pageNum = pageNum;
        }else{
          this.$message.error(resultError);
        }
      });
    },
    // 当前页码
    handleCurrentChange(page) {
      // this.currentPage = page;
      this.queryParams.pageNum = page;
      this.getDetailList();
    },
    sliceText(text){
      return text.length > 20 ? text.slice(0, 20) + '...' : text;
    },
    // 搜索
    handleQuery() {
      console.log(this.search, 'search')
    },
    handleClose() {
      this.$emit('update-open-detail-dialog', false)
    },
  }
}
</script>
<style lang="scss" scoped>
.el-dialog__body {
  padding: 0;
}
::v-deep .el-dialog__body {
  padding: 0;
}
.header {
  font-size: 0.16667rem;

  padding: 0.16667rem;
  background-color: #fff;
  width: 100%;
  margin-top: 0.16667rem;
  border-radius: 0.04167rem;

  i {
    color: #3377ff;
  }
}


.m-con {
  margin: 0.08333rem;
  padding: 0.08333rem;
  background-color: #fff;

  .dict_search {
    width: 30%;
    padding-bottom: 0.08333rem;
  }
}

.table-content{
  padding: 0.1rem 0.2rem;
}

.action-button {
  padding: 0;
  font-size: 0.125rem;
  min-width: 0.125rem;
  background-color: transparent;
  border: none;
  cursor: pointer;
  color: #3377FF;
  font-family: PingFangSC-Regular;
  line-height: 0.18rem;
  font-weight: 400;
}
</style>
