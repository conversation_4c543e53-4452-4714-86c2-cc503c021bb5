<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6" :xs="24">
        <el-card class="boxCards">
          <div slot="header" class="clearfix">
            <span>个人信息</span>
          </div>
          <div>
            <ul class="list-group list-group-striped">
              <li class="list-group-item" style="border-top: 0;margin-top: -15px;">
                <svg-icon icon-class="user" />登录名称
                <div class="pull-right">{{ userObj.loginName }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="user" />用户名称
                <div class="pull-right">{{ userObj.staffName }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="user" />当前组织
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="userObj.orgName"
                  placement="top"
                >
                  <div
                    class="pull-right"
                    style="width:80px;
                           text-align: right;
                           white-space: nowrap;
                           overflow: hidden;
                           text-overflow: ellipsis;
                           float: right;"
                  >
                    {{ userObj.orgName }}
                  </div>
                </el-tooltip>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="phone" />手机号码
                <div class="pull-right">{{ userObj.cellphone }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="email" />用户邮箱
                <div class="pull-right">{{ userObj.email }}</div>
              </li>
            </ul>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18" :xs="24">
        <el-card>
          <div slot="header" class="clearfix">
            <span>基本资料</span>
          </div>
          <el-tabs v-model="activeTab">
            <el-tab-pane label="基本资料" name="userinfo">
              <userInfo :user="user" />
            </el-tab-pane>
            <el-tab-pane label="修改密码" name="resetPwd">
              <resetPwd :user="user" />
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import userAvatar from "./userAvatar";
import userInfo from "./userInfo";
import resetPwd from "./resetPwd";
import { mapState } from "vuex";
export default {
  name: "Profile",
  components: { userAvatar, userInfo, resetPwd },
  data() {
    return {
      user: {},
      roleGroup: {},
      postGroup: {},
      activeTab: "userinfo",
    };
  },
  computed: {
    ...mapState({
      userObj: (state) => state.user.userInfo,
    })
  },
  created(){
    this.user = {
      staffId: this.userObj.staffId,
      staffName: this.userObj.staffName,
      email: this.userObj.email,
      cellphone: this.userObj.cellphone,
      tenantId: this.$store.getters.customParam.tenantId,
    }
  },
  methods: {

  },
};
</script>
