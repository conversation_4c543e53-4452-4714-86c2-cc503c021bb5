<template>
	<div class="uni-list uni-border-top-bottom">
		<div v-if="border" class="uni-list--border-top"></div>
		<slot />
		<div v-if="border" class="uni-list--border-bottom"></div>
	</div>
</template>

<script>
/**
 * List 列表
 * @description 列表组件
 * @tutorial https://ext.dcloud.net.cn/plugin?id=24
 * @property {String} 	border = [true|false] 		标题
 */
export default {
	name: 'uniList',
	props: {
		enableBackToTop: {
			type: [Boolean, String],
			default: false
		},
		scrollY: {
			type: [Boolean, String],
			default: false
		},
		border: {
			type: Boolean,
			default: true
		}
	},
	created() {
		this.firstChildAppend = false;
	},
	methods: {
		loadMore(e) {
			this.$emit('scrolltolower');
		}
	}
};
</script>
<style lang="scss" >
$uni-bg-color:#ffffff;
$uni-border-color:#e5e5e5;
.uni-list {
	display: flex;
	background-color: $uni-bg-color;
	position: relative;
	flex-direction: column;
}

.uni-list--border {
	position: relative;
	z-index: -1;
}


.uni-list--border-top {
	position: absolute;
	top: 0;
	right: 0;
	left: 0;
	height: 1px;
	-webkit-transform: scaleY(0.5);
	transform: scaleY(0.5);
	background-color: $uni-border-color;
	z-index: 1;
}

.uni-list--border-bottom {
	position: absolute;
	bottom: 0;
	right: 0;
	left: 0;
	height: 1px;
	-webkit-transform: scaleY(0.5);
	transform: scaleY(0.5);
	background-color: $uni-border-color;
}

</style>
