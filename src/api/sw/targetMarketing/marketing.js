import request from '@/utils/request'

// 分页查询靶向营销审核记录表
export function getPageList(params) {
  return request({
    url: '/sanquan/targetMarketing/marketing/findPage',
    method: 'get',
    params,
  })
}

// 查询靶向营销审核记录表详细信息
export function getInfo(id) {
  return request({
    url: '/sanquan/targetMarketing/marketing/findInfo',
    method: 'get',
    params: {
        id
    }
  })
}

// 新增靶向营销审核记录表
export function addMarketing(data) {
  return request({
    url: '/sanquan/targetMarketing/marketing/add',
    method: 'post',
    data,
  })
}

// 修改靶向营销审核记录表
export function updateMarketing(data) {
  return request({
    url: '/sanquan/targetMarketing/marketing/update',
    method: 'post',
    data,
  })
}

// 删除靶向营销审核记录表
export function deleteMarketing(id) {
  return request({
    url: '/sanquan/targetMarketing/marketing/delete',
    method: 'post',
    params: {
        id
    }
  })
}
// 靶向营销审核
export function submitAudit(data) {
  return request({
    url: '/sanquan/targetMarketing/marketing/submitAudit',
    method: 'post',
    data,
  })
}
