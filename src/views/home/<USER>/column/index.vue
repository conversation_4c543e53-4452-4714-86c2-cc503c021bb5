<template>
  <el-card
    :class="this.bgColor === true ?'line-card':'unshow-line-card'"
    v-loading="line"
    shadow="never"
  >
    <div slot="header" v-if="showTitle">
      <div class="card-title">
        <div id="title">
          <span>
            <i class="icon iconfont icon-gonggao"></i>云监控-柱状图
          </span>
        </div>
        <div class="top">
          <el-button icon="el-icon-d-arrow-right" class="el-button" circle @click="cancel"></el-button>
          <transition name="forms" v-if="show">
            <el-select
              v-model="item.keyValue"
              placeholder="请选择监控类型"
              @change="column"
              style="margin-left:20px"
            >
              <el-option
                v-for="item in options"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictValue"
              ></el-option>
            </el-select>
          </transition>
        </div>
      </div>
    </div>
    <div class="home-card-body">
      <div class="ColumnCharts" id="ColumnCharts" v-if="borkenLineVal">
        <div id="ColumnChart"></div>
      </div>
    </div>
  </el-card>
</template>

<script>
import * as echarts from "echarts";
export default {
  name: "column",
  props: {
    item: {
      type: Object,
      default: {},
    },
    editLayout: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      bgColor: "",
      line: false,
      borkenLineVal: true,
      show: true,
      showTitle: "",
      options: [],
      xData: [],
      yData: [],
      timer: "",
      title: "",
    };
  },
  mounted() {
    this.column();
  },
  beforeDestroy() {
    // clearInterval(this.timer);
  },
  created() {
    this.showTitle = this.item.showTitle;
    this.bgColor = this.item.showBgColor;
    this.getDicts("barData").then((response) => {
      this.options = response.data;
    });
  },
  watch: {
    "item.showTitle": function (val) {
      this.showTitle = val;
    },
    "item.showBgColor": function (val) {
      this.bgColor = val;
    },
    "item.keyValue": function (newVal, oldVal) {
      if (newVal && newVal !== "") {
        this.column();
      } else {
        this.msgInfo("请选择监控类型");
      }
    },
    "item.w": function (val) {
      this.column();
    },
  },
  methods: {
    setColumn() {
      this.item.keyValue = this.options.find(
        (ele) => ele.dictValue === this.item.keyValue
      ).dictValue;
      that.$emit("setColumn", this.item);
    },
    cancel() {
      this.show = !this.show;
    },
    // // 接口请求走势图
    column() {
      var chartDom = document.getElementById("ColumnChart");
      var chart = document.getElementById("ColumnCharts");
      var myChart = echarts.init(chartDom);
      myChart.showLoading();
      var option;

      option = {
        xAxis: {},
        yAxis: {
          type: "category",
          data: [0, 0, 0, 0, 0],
          inverse: true,
          max: 4, // only the largest 3 bars will be displayed
        },
        series: [
          {
            realtimeSort: true,
            type: "bar",
            data: [0, 0, 0, 0, 0],
            label: {
              show: true,
              position: "right",
              valueAnimation: true,
            },
          },
        ],
        legend: {
          show: true,
        },
      };

      myChart.setOption(option);

      var that = this;

      if (that.item.keyValue !== undefined) {
        // that.timer = setInterval(() => {
        //   setTimeout(function () {
            that.$store
              .dispatch("monitor/getMonitordata", {
                monitorKey: that.item.keyValue,
                type: "integer",
                limitNum: 1,
              })
              .then((res) => {
                if (res !== []) {
                  that.title = that.selectDictLabels(
                    that.options,
                    that.item.keyValue
                  );
                  res.forEach((item) => {
                    item = item.split(",");
                    // 获取x轴的坐标集合
                    var level = item[1].split('"')[1];
                    that.xData.push(level);
                    // 获取y轴的坐标集合
                    var num = item[2].split(" ")[1].split(".")[0];
                    that.yData.push(num);

                    option = {
                      title: {
                        text: that.title,
                      },
                      xAxis: {},
                      yAxis: {
                        type: "category",
                        data: that.xData,
                        inverse: true,
                        max: 4, // only the largest 3 bars will be displayed
                      },
                      series: [
                        {
                          realtimeSort: true,
                          type: "bar",
                          data: that.yData,
                          label: {
                            show: true,
                            position: "right",
                            valueAnimation: true,
                          },
                        },
                      ],
                      legend: {
                        show: true,
                      },
                      animationEasing: "linear",
                      animationEasingUpdate: "linear",
                    };

                    myChart.setOption(option);
                    myChart.hideLoading();
                  });
                } else {
                  clearInterval(that.timer);
                }
              });
        //   }, 0);
        // }, 5000);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.line-card {
  height: 100%;
  background: #fff;
  .card-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .center {
    position: relative;
  }
  #titleTop {
    position: absolute;
    top: 50%;
    right: 50%;
    transform: translate(50%, -50%);
    margin: 0 auto;
    font-size: 16px;
    text-align: center;
  }

  .veRing {
    margin: 0 auto;
  }

  .top {
    display: flex;
    display: flex;
    margin-left: 20px;
  }
  .el-button {
    display: inline-block;
    height: 40px;
    width: 40px;
  }
  .ColumnCharts {
    width: 100%;
    height: 300px;
    margin-top: 10px;
  }
  #ColumnChart {
    width: 100%;
    height: 100%;
  }
  .demo-form-inline {
    width: 85%;
    margin: 0 auto;
    height: 40px;
  }
  .el-form-item__label {
    color: black;
    font-size: 12px;
  }
  .Box-enter-active,
  .Box-leave-active {
    transition: all 2s;
  }
  .Box-enter,
  .Box-leave-to {
    width: 0px;
    height: 0px;
  }
}
.unshow-line-card {
  border: 0px;
  height: 100%;
  background: #fff;
}
</style>
