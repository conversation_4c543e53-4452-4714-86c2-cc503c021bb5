<template>
  <el-card
    :class="this.bgColor === true ?'notice-card':'unshow-notice-card'"
    v-loading="noticeLoading"
    shadow="never"
  >
    <div slot="header" v-if="show">
      <span class="card-title">
        <div class="title">
          <span> <i class="icon iconfont icon-gonggao"></i>广告-文本 </span>
        </div>
      </span>
    </div>
    <div class="home-card-body">
      <div class="poster-content">
        <div class="myUl">
          <a class="li" :href="this.list.jumpAddress">
            <span v-html="this.list.content"></span>
          </a>
        </div>
        <div v-if="this.list.length === 0">
          <span style="color:#1890ff">暂无发布的文本广告数据</span>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script>
import { indexData } from "@/api/poster/index.js";
export default {
  props:['item'],
  name: "posterFont",
  data() {
    return {
      noticeLoading: false,
      list: [],
      form: {
        queryNum: 5,
        terminal:'PC',
      },
      show:"",
      bgColor:"",
    };
  },
  created() {
    this.getList();
    this.show = this.item.showTitle;
    this.bgColor = this.item.showBgColor;
  },
  watch:{
    'item.showTitle' :function (val){
      this.show = val
    },
    'item.showBgColor' :function(val) {
      this.bgColor = val
    }
  },
  methods: {
    moreExt(type) {
      window.open(`/portal/home/<USER>/${type}`);
    },
    getList() {
      indexData(this.form).then((res) => {
        var news = res.data;
        news.forEach((item) => {
          if (item.advertisingType === "text") {
            this.list = item;
          }
        });
      });
    },
  },
};
</script>

<style scoped lang="scss">
.home-card-body {
  padding-top: 20px;

  .poster-content {
    height: 100%;
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    .myUl {
      list-style: none;
     .li {
       width: 20%;
       font-size: 16px;
       color: #2440b3;
     }
     .li:hover {
       color: hsl(208, 84%, 59%);
     }
    }
    .font-title {
      width: 5%;
      background: #d1dae1;
      color: #4f7c87;
      padding: 10px;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      font-weight: 600;
      p {
        margin: 0;
        font-size: 16px;
      }
    }
  }
}
.notice-card {
  height: 100%;
  background: #fff;
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 0;
    }
  }
  .notice {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    padding: 4px 0;
    font-size: 14px;
    .notice-title {
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: nowrap;
      flex-basis: 70%;
    }
    .notice-time {
      flex-basis: 20%;
      text-align: right;
    }
  }
}
.unshow-notice-card {
  border: 0px;
  background: #f5f9fa;
  height: 100%;
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 0;
    }
  }
  .notice {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    padding: 4px 0;
    font-size: 14px;
    .notice-title {
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: nowrap;
      flex-basis: 70%;
    }
    .notice-time {
      flex-basis: 20%;
      text-align: right;
    }
  }
}
.moreButtom {
  display: flex;
  justify-content: flex-end;
}
</style>
