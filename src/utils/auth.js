
const TokenKey = 'unifast_token'

export function getToken() {
  // return sessionStorage.getItem(TokenKey)
  return  localStorage.getItem(TokenKey)
}

export function setToken(token) {
  // return sessionStorage.setItem(Token<PERSON><PERSON>,token)
  return  localStorage.setItem(Token<PERSON><PERSON>,token)
}

export function removeToken() {
  // return sessionStorage.removeItem(TokenKey)
  return  localStorage.removeItem(TokenKey)
}

export function getAuthorKey() {
  let start='Author';
  let end='ization';
  return start+end
}
