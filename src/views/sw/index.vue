<template>
  <div class="x-app-container hide-bar" v-loading="!showMenuBar">
    <Navbar v-if="showMenuBar" :myRoles="myRoles" :username="staffName"/>
    <!-- <tags-view /> -->
    <div class="wrapper hide-bar">
      <router-view/>
      <!-- <keep-alive>
        <router-view v-slot="{ Component }">
          <component :is="Component" />
        </router-view>
      </keep-alive> -->

    </div>

  </div>
</template>

<style lang="less">
@import "../../assets/sanquan/index";
</style>
<script>
import Navbar from '../../components/Navbar/index.vue'
import {TagsView} from '@/layout/components'
import {getCurrentUser, checkLoginSes, getTokenBySession} from '@/api/sw/analysis/analysis'
import {getToken} from '@/utils/auth.js'
import {removeWatermark, setWaterMark} from '@/utils/water-tag'

export default {
  name: 'index',
  data() {
    return {
      showMenuBar: false,
      myRoles: [],
      staffName: '',
      routerPath: '/sw/5g/progress',
      ladingBar: null,
      initTime: 0,
      endTime: 0,
      ssoTimeLogin:null,
    }
  },
  components: {
    Navbar, TagsView
  },
  methods: {
    /* 动态设置html字体大小 */
    setHtmlFontSize() {
      this.default_width = window.innerWidth
      // document.querySelector('html').style.fontSize = this.default_width / 16 + 'px'
      document.querySelector('html').style.fontSize = 120 + 'px'
      // let deviceWidth = document.documentElement.clientWidth//获取当前分辨率下的可是区域宽度
      // // 分母——设计稿的尺寸
      // document.body.style['zoom'] = deviceWidth / 1920
    },
    initPage() {
      this.default_width = window.innerWidth
      this.setHtmlFontSize()
      // 监听窗口变化
      window.addEventListener('resize', this.setHtmlFontSize)
    },
    getUserInfo() {
      // getCurrentUser().then(res => {
      //   this.$store.commit('SET_USER_INFO', res.data)
      //   this.$store.commit('SET_CUSTOM_PARAM', res.data.customParam)
      //   this.$store.commit('SET_AUTHORITIES', res.data.authorities)
      //   // 存储到本地存储
      //   localStorage.setItem('AUTH_SESSION_ID', res.data.customParam.authSessionId)
      //   this.myRoles = res.data.authorities
      //   this.staffName = res.data.staffName
      //   this.showMenuBar = true
      //   if (this.routerPath === '/sw/') {
      //     // 存储到本地存储
      //     this.$router.push('/sw/indexFirst')
      //   }
      //   if (this.ladingBar != null) {
      //     this.ladingBar.close()
      //   }
      //   this.endTime = new Date().getTime()
      //   console.log('耗时', this.endTime - this.initTime, this.endTime, this.initTime)
      //   setWaterMark('', res.data.staffId + ' ' + this.getCurrentTime())
      // })
    },
    getCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1; // 月份是从0开始的
      const day = now.getDate();
      const hours = now.getHours();
      const minutes = now.getMinutes();
      const seconds = now.getSeconds();
      // 格式化月份和日期，保持两位数
      const formattedMonth = month < 10 ? '0' + month : month;
      const formattedDay = day < 10 ? '0' + day : day;
      const formattedHours = hours < 10 ? '0' + hours : hours;
      const formattedMinutes = minutes < 10 ? '0' + minutes : minutes;
      const formattedSeconds = seconds < 10 ? '0' + seconds : seconds;
      return `${year}-${formattedMonth}-${formattedDay} ${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
    },
    ssoLogin(){
      let _this = this
      console.info("5g=======================")
      this.routerPath = this.$route.path
      let authSessionId = this.$route.query.authSessionId_pd || this.$route.query.authSessionId;
      if(authSessionId===undefined||authSessionId===null||authSessionId===''){
        authSessionId = localStorage.getItem('authSessionId_pd')
      }else{
        localStorage.setItem('authSessionId_pd',authSessionId);
      }
      console.info("5g=authSessionId======================"+authSessionId)
      // let authSessionId = localStorage.getItem('authSessionId_pd')
      // this.ladingBar = this.$loading({
      //   lock: true,
      //   text: '系统加载中',
      //   spinner: 'el-icon-loading',
      //   background: 'rgba(0, 0, 0, 0.7)'
      // })
      this.initTime = new Date().getTime()
      let beginTime = new Date().getTime();
      // _this.ssoTimeLogin=setTimeout(() => {
      //   _this.ssoLogin()
      // }, 4000)
      getTokenBySession({authSessionId: authSessionId}).then((res) => {
        console.log('登录后请求session信息:', res, '耗时:', (new Date().getTime() - beginTime) + '--'+authSessionId)
        if (res['success'] === 'true') {
          localStorage.setItem('unifast_token', res.sessionToken)
          let mallUser = res.mallUser
          mallUser.authorities = res.authoritys
          this.$store.commit('SET_USER_INFO', mallUser)
          this.$store.commit('SET_CUSTOM_PARAM', mallUser.customParam)
          this.$store.commit('SET_AUTHORITIES', res.authoritys)
          // 存储到本地存储
          localStorage.setItem('AUTH_SESSION_ID', mallUser.customParam.authSessionId)
          this.myRoles = mallUser.authorities
          this.staffName = mallUser.staffName
          this.showMenuBar = true
          clearTimeout(_this.ssoTimeLogin);

          if (this.routerPath === '/sw/') {
            // 存储到本地存储
            this.$router.push('/sw/5g/progress')
          }
          if (this.ladingBar != null) {
            this.ladingBar.close()
          }
          this.endTime = new Date().getTime()
          console.log('耗时', this.endTime - this.initTime, this.endTime, this.initTime)
          setWaterMark('', mallUser.staffId + ' ' + this.getCurrentTime())
          // setTimeout(() => {
          //   _this.getUserInfo()
          // }, 0)
        }
      }).catch(() => {
        console.log("未获取到认证信息,请重新登录::index")
        // this.$message.error('未获取到认证信息,请重新登录::index')
      })
    }
  },
  computed: {
    cachedViews() {
      console.log('cachedviews')
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.path
    }
  },
  created() {
    // 开发环境下直接显示菜单栏，跳过认证
    if (process.env.NODE_ENV === 'development') {
      this.showMenuBar = true;
      this.myRoles = ['ROLE_5G']; // 设置一个默认角色
      this.staffName = '开发用户';
    } else {
      this.ssoLogin();
    }
  },
  mounted() {
    this.initPage()
    // this.checkLogin()
  }

}
</script>
