<template>
  <div class="portal-body">
    <div class="feedback-list">
      <div class="search-wrapper box-bg">
        <el-form :model="formQuery"  ref="queryForm" label-position="left">
          <el-row >
            <el-col :span="7">
              <el-form-item label="菜单名称" prop="title">
                <el-input v-model="formQuery.title" clearable placeholder="请输入"  />
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item label="菜单类型" prop="menuType">
                <el-select v-model="formQuery.menuType" clearable placeholder="菜单状态"  size="small">
                  <el-option :key="0" label="目录" :value="0"/>
                  <el-option :key="1" label="菜单" :value="1"/>
                  <el-option :key="2" label="权限" :value="2"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item label="菜单权限编码" prop="menuPermission">
                <el-input v-model="formQuery.menuPermission" clearable placeholder="请输入"  />
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item label="是否显示" prop="showMenu">
                <el-select v-model="formQuery.showMenu" placeholder="是否显示" clearable size="small">
                  <el-option :key="1" label="显示" :value="1"/>
                  <el-option :key="0" label="隐藏" :value="0"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item label="所属系统" prop="menuSystem">
                <el-select v-model="formQuery.menuSystem" clearable placeholder="所属系统"  size="small">
                  <el-option v-for="item in menuSystemList" :key="item.systemCode" :label="item.systemName" :value="item.systemCode">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="btn-group" style="right: 0;">
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="conditionQuery">查询</el-button>
          </div>
        </el-form>
        <div class="table-wrapper box-bg">
            <div class="flex-between" style="border-bottom:2px #f5f5f5 solid;margin-bottom: 24px;padding:0 0 10px 0">
            <div class="table-title">菜单列表</div>
            <div>
              <el-button type="primary" size="small" icon="el-icon-plus" @click="handleEdit(true)">新增</el-button>
            </div>
          </div>
          <div class="container">
            <div class="con-left">
              <div class="flex-between">
                <el-input
                  v-model="searchInfo"
                  placeholder="输入关键字进行过滤"
                  suffix-icon="el-icon-search"
                >
                </el-input>
              </div>
              <div class="tab-tree">
                <el-tree
                  :data="treeList"
                  :props="defaultProps"
                  :expand-on-click-node="false"
                  :filter-node-method="filterNode"
                  @current-change="handleNodeClick"
                  v-loading="treeLoading"
                  ref="tree">
                  <template v-slot="{ node, data }">
                  <span class="custom-tree-node">
                    <i :class="data.icon" class="tab-icon"></i>
                  <span>{{ node.label }}</span>
                  <span class="tab-num" v-if="data.children">（{{ data.children.length }}）</span>
                  </span>
                  </template>
                </el-tree>
              </div>
            </div>
            <div class="con-main">
              <div class="table-con">
                <UniTable action="/sanquan/core/menu/findPage" ref="uniTable" :columns="tableColumns" :params="formQuery">
                  <template v-slot:menuType="{row}">
                    <el-tag v-if="row.menuType === '0'" size="mini" effect="Dark" type="warning">目录</el-tag>
                    <el-tag v-if="row.menuType === '1'" size="mini" effect="Dark" type="">菜单</el-tag>
                    <el-tag v-if="row.menuType === '2'" size="mini" effect="Dark" type="info">权限</el-tag>
                  </template>
                  <template v-slot:icon="{row}">
                    <svg-icon :icon-class="row.icon"/>
                  </template>
                  <template v-slot:showMenu="{row}">
                    <el-tag v-if="row.showMenu === '1'" size="mini" effect="Dark" type="">显示</el-tag>
                    <el-tag v-if="row.showMenu === '0'" size="mini" effect="Dark" type="info">隐藏</el-tag>
                  </template>
                  <template v-slot:opt="{row}">
                      <div class="action-buttons flex-center">
                        <el-button
                          size="mini"
                          class="action-button"
                          type="button"
                          @click="handleEdit(false, row)"
                        >修改
                        </el-button>
                        <span class="separator"></span>
                        <el-button
                          size="mini"
                          class="action-button"
                          type="button"
                          @click="handleEdit(true, row)"
                          >新增
                        </el-button>
                        <span class="separator"></span>
                        <el-button
                          size="mini"
                          class="action-button"
                          type="button"
                          @click="handleDel(row.id)"
                        >删除
                        </el-button>
                      </div>
                  </template>
                </UniTable>
              </div>
            </div>
          </div>
        </div>
      </div>
    <!-- 添加或修改标签弹框 -->
    <el-dialog :visible.sync="openTabDialog" width="1000px">
      <template #title>
        <span
          style="font-size: 0.135rem; font-weight: 600; margin-left: 0.1rem"
        >{{ title }}</span
        >
      </template>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-row>
          <el-col :span="12">
          <el-form-item label="上级菜单" prop="parentId">
            <treeselect
              v-model="form.parentId"
              :options="menuOptions"
              :normalizer="normalizer"
              :show-count="true"
              placeholder="选择上级菜单"
            />
<!--            <el-input v-model="form.title" placeholder="菜单名称" />-->
          </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="菜单名称" prop="title">
              <el-input v-model="form.title" placeholder="请输入菜单名称"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="18">
            <el-form-item label="菜单类型" prop="menuType">
              <el-col :span="2">
                <el-tooltip
                  class="tooltipItem"
                  effect="dark"
                  placement="bottom-start"
                >
                  <div slot="content">
                    【目录】<span style="color: red">用于归纳菜单</span>
                    仅在此功能中展示,不作为菜单加载;<br />
                    【菜单】<span style="color: red">用于常规菜单导航配置</span>
                    作为左侧菜单元素进行渲染;<br />
                    【权限】<span style="color: red">用于权限控制</span>
                    需配合权限标识使用,并编写对应代码控制逻辑;
                  </div>
                  <i class="el-icon-question" />
                </el-tooltip>
              </el-col>
              <el-col :span="22">
                <el-radio-group v-model="form.menuType">
                  <el-radio key="0" label="0" >目录</el-radio>
                  <el-radio key="1" label="1" >菜单</el-radio>
                  <el-radio key="2" label="2" >权限</el-radio>
                </el-radio-group>
              </el-col>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item
              v-if="form.menuType !== '2'"
              label="菜单图标"
            >
              <el-popover
                placement="bottom-start"
                width="460"
                trigger="click"
                @show="$refs['iconSelect'].reset()"
              >
                <IconSelect ref="iconSelect" @selected="selected" />
                <el-input
                  slot="reference"
                  v-model="form.icon"
                  placeholder="点击选择图标"
                >
                  <svg-icon
                    v-if="form.icon"
                    slot="prefix"
                    :icon-class="form.icon"
                    class="el-input__icon"
                    style="height: 32px; width: 16px"
                  />
                  <i
                    v-else
                    slot="prefix"
                    class="el-icon-search el-input__icon"
                  />
                </el-input>
              </el-popover>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="菜单地址" prop="path" v-if="form.menuType === '1'"  :rules="{required: true, message: '菜单地址', trigger: 'blur'}">
              <el-input v-model="form.path" placeholder="请输入菜单地址"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属系统" prop="menuSystem" >
              <el-select v-model="form.menuSystem" placeholder="请选择菜单所属系统" clearable size="small">
                  <el-option v-for="item in menuSystemList" :key="item.systemCode" :label="item.systemName" :value="item.systemCode">
                    <span style="float: left">{{ item.systemName }}</span>
                  </el-option>
                </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
          <el-form-item label="权限编码"  prop="menuPermission">
            <el-input v-model="form.menuPermission" placeholder="请输入权限编码"/>
          </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="menuSort">
              <el-input-number
                style="width: 100px;"
                v-model="form.menuSort"
                controls-position="right"
                de="1"
                :min="0"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
        <el-col :span="12">
          <el-form-item label="菜单显隐" prop="showMenu">
            <el-radio-group v-model="form.showMenu">
              <el-radio key="1" label="1">显示</el-radio>
              <el-radio key="0" label="0">隐藏</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
    </div>
  </div>
</template>
<script>
import MenusApi from "@/api/sw/platform/menus";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import IconSelect from "@/components/IconSelect";
import UniTable from "@/components/UniTable/UniTable.vue";
import {deleteError, deleteSuccess} from "@/utils/messageText";
export default {
  name: 'index',
  components: {UniTable, Treeselect, IconSelect },
  data() {
    return {
      formQuery: {
        pageNum: 1,
        pageSize: 10
      },
      // 查询树
      searchInfo: "",
      // 表格loading
      // 是否显示弹出层
      openTabDialog: false,
      // 弹出层标题
      title: "",
      // 表单参数
      form: {},
      // tree loading
      treeLoading: false,
      // 菜单树
      treeList: [],
      tableColumns: [
        { props: 'title', label: '菜单名称' },
        { props: 'menuType', label: '菜单类型',slot: true },
        { props: 'path', label: '菜单路径' },
        { props: 'icon', label: '图标' ,slot: true},
        { props: 'menuPermission', label: '菜单权限编码' },
        { props: 'showMenu', label: '是否显示', slot: true },
        { props: 'menuSort', label: '排序' },
        { props: 'opt', label: '操作', slot: true }
      ],
      defaultProps: {
        children: "children",
        label: "title",
      },
      // 菜单树选项
      menuOptions: [],
      /** 转换菜单数据结构 */
      normalizer(node) {
        if (node.children && !node.children.length) {
          delete node.children;
        }
        return {
          id: node.id,
          label: node.title,
          children: node.children,
        };
      },
      columns: [
        { props: 'title', label: '菜单名称' },
        { props: 'menuPath', label: '上级菜单' },
        { props: 'path', label: '加载路径' },
        { props: 'opt', label: '操作列' }
      ],
      // 表单校验
      rules: {
        parentId: [
          { required: true, message: "请选择父级节点", trigger: "blur" },
        ],
        title: [
          { required: true, message: "请填写菜单名称", trigger: "blur" },
        ],
        menuType: [
          { required: true, message: '请选择菜单类型', trigger: 'change' }
        ],
        menuPermission: [
          { required: true, message: '请输入权限编码', trigger: 'change' }
        ],
        menuSort: [
          { required: true, message: "菜单顺序不能为空", trigger: "blur" },
          {
            pattern: /^\d+$/,
            message: '只可以输入非负整数',
            trigger: "blur"
          }
        ],
        showMenu: [
          { required: true, message: '请选择菜单是否显示', trigger: 'change' }
        ],
      },
      menuSystemList: [], // 菜单系统列表
    }
  },
  created() {
    this.getTreeList();
    this.getMenuSystemList();
  },
  // 监听属性类似于data概念
  computed: {},
  //属性改变监听
  watch: {
    searchInfo(newVal) {
      this.$refs.tree.filter(newVal);
    },
  },
  methods:{
    /** 查询菜单下拉树结构 */
    getTreeList() {
      this.treeLoading = true;
      MenusApi.treeMenu({}).then((res) => {
        this.treeList = res.data;
        this.menuOptions = [];
        const menu = {
          id: '0',
          title: "主目录",
          children: [],
        };
        menu.children = res.data;
        this.menuOptions.push(menu);
        this.treeLoading = false;
      });
    },
    // 过滤树
    filterNode(value, data) {
      if (!value) return true;
      return data.title.indexOf(value) !== -1;
    },
    // 点击查询右侧数据
    handleNodeClick(node) {
      this.formQuery.parentId = node.id;
      this.conditionQuery();
    },
    // 选择图标
    selected(icon) {
      this.form = {
        ...this.form,
        icon,
      };
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.uniTable.resetPage()
      this.resetForm("queryForm");
      this.formQuery.parentId = null;
      this.conditionQuery();
    },
    // 条件查询
    conditionQuery() {
      this.$refs.uniTable.loadData(this.formQuery)
    },
    /**
     * 新增编辑
     */
    handleEdit(isAdd, row) {
      this.reset();
      if (isAdd) {
        this.title = "新增菜单";
        if (row?.id) {
          this.form = {
            parentId: row.id,
          };
        }
        this.openTabDialog = true;
      } else {
        this.title = "修改菜单";
        MenusApi.findInfo(row.id).then((res) => {
          this.form = res.data;
          this.openTabDialog = true;
        });
      }
    },
    /**
     * 删除
     * @param id 分类主键
     */
    handleDel(id) {
      MenusApi.checkHasChildren({parentId: id}).then((res) => {
        if (!res.data) {
          this.$confirm("此操作将永久该数据, 是否继续?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            MenusApi.deleteById(id).then((res) => {
              if (res.data === true) {
                this.getTreeList();
                this.conditionQuery();
                this.$message.success(deleteSuccess);
              } else {
                this.$message.success(deleteError);
              }
            });
          });
        }else{
          this.$confirm("该菜单下还有未删除的子菜单，不允许删除!", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "error",
          })
        }
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id !== undefined) {
            MenusApi.addMenu(this.form).then((response) => {
              if (!response.success) {
                this.$message.error(response.message);
              } else {
                this.msgSuccess("修改成功");
                this.openTabDialog = false;
                this.getTreeList();
                this.conditionQuery();
              }
            });
          } else {
            MenusApi.addMenu(this.form).then((response) => {
              if (response.success) {
                this.msgSuccess("新增成功");
                this.openTabDialog = false;
                this.getTreeList();
                this.conditionQuery();
              } else {
                this.$message.error(response.message);
              }
            });
          }
        }
      });
    },
    cancel() {
      this.openTabDialog = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {};
      this.resetForm("form");
    },
    // 查询菜单系统配置参数
    getMenuSystemList() {
      MenusApi.findMenusSystem().then((res) => {
        this.menuSystemList = res.data;
      });
    },
  }

}
</script>
<style scoped lang="scss">
.container {
  display: flex;
  .con-left {
    width: 20%;
    margin-right: 0.16667rem;
    .flex-between {
      display: block;
      //padding: 0.16667rem 0.08333rem;
      border-bottom: 0.00833rem solid;
      border-bottom: 0.00833rem solid #f1f1f1;
    }
    .tab-tree {
      height: calc(60vh - 10px);
      overflow-y: auto;
    }
    .tab-icon {
      padding-right: 0.08333rem;
    }
  }
  .con-main {
    width: calc(80% - 0.16667rem);
    //width: 100%;
  }
}
//.el-radio-group {
//  font-size: 0;
//  margin-top: -10px;
//}

</style>
