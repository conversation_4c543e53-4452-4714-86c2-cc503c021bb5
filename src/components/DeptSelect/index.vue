<template>
  <div v-if="visible">
    <el-dialog
        width="30%"
        title="组织选择"
        :visible.sync="visible"
        append-to-body
        destroy-on-close
    >
      <el-card
          class="select-dep-card"
          shadow="never"
          v-loading="selectTreeLoading"
          destroy-on-close
      >
        <div slot="header">
          <span>请选择</span>
          <el-button
              style="float: right; padding: 3px 0"
              type="text"
              icon="el-icon-refresh"
              @click="reloadTree"
          >刷新</el-button
          >
          <el-button
              style="float: right; padding: 3px 5px"
              type="text"
              :icon="expandAll ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
              @click="setExpandAll"
          >{{ expandAll ? "展开一级" : "展开全部" }}</el-button
          >
        </div>
        <el-tree
            v-if="reload"
            :props="lazyTreeProps"
            :load="selectLoadNode"
            lazy
            :expand-on-click-node="false"
            ref="selectAsyncTree"
            :default-expanded-keys="[defaultOrgId]"
            node-key="orgId"
            highlight-current
            :default-expand-all="expandAll"
        >
        </el-tree>
      </el-card>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="selectDept" :loading="selectDeptLoading"
        >确 定</el-button
        >
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { treeselect } from "@/api/system/dept";
export default {
  props: {
    name: {
      type: String,
      default: "",
    },
    value: {
      type: String,
      default: "",
    },
    tenantId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      selectTreeLoading: false,
      lazyTreeProps: {
        children: "children",
        label: "orgName",
        isLeaf: "leaf",
      },
      visible: false,
      reload: true,
      expandAll: false,
      selectDeptLoading: false,
      defaultOrgId: this.$store.getters.orgId,
    };
  },
  methods: {
    selectLoadNode(node, resolve) {
      if (!node.data) {
        this.selectTreeLoading = true;
      }
      treeselect({
        orgId: node.data ? node.data.orgId : this.defaultOrgId,
        queryType: node.data ? "down" : "current",
        tenantId: this.tenantId
      }).then((response) => {
        resolve(response.data);
        this.selectTreeLoading = false;
      });
    },
    selectDept() {
      this.selectDeptLoading = true;
      const cnode = this.$refs.selectAsyncTree.getCurrentNode();
      if (cnode) {
        this.$emit("selected", cnode);
      } else {
        this.$message({
          message: "请选择一条数据！",
          type: "warning",
        });
        this.selectDeptLoading = false;
      }
    },
    open() {
      this.selectDeptLoading = false;
      this.visible = true;
    },
    close() {
      this.visible = false;
    },
    reloadTree() {
      this.reload = true;
      const cnode = this.$refs.selectAsyncTree.getCurrentNode();
      if (cnode) {
        const node = this.$refs.selectAsyncTree.getNode(cnode);
        node.childNodes = [];
        node.loaded = false;
        node.expand();
      } else {
        this.$refs.selectAsyncTree.root.loaded = false;
        this.$refs.selectAsyncTree.root.expand();
      }
    },
    setExpandAll() {
      this.reload = false;
      this.expandAll = !this.expandAll;
      const that = this;
      setTimeout(() => {
        that.reload = true;
        that.$refs.selectAsyncTree.root.loaded = false;
        that.$refs.selectAsyncTree.root.expand();
      }, 100);
    },
  },
};
</script>

<style scoped lang="scss">
.select-dep-card {
  height: 500px;
  overflow-y: auto;
}
</style>
