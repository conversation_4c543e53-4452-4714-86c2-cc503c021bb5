<template>
    <div class="portal-body">
        <div class="analysis">
            <div class="flex-between  title-wrapper">
                <div class="flex-start">
                    <p class="title">整体转化率分析-地市</p>
                </div>
                <el-date-picker v-model="queryParams.monthId" type="month" placeholder="选择月" format="yyyyMM"
                    value-format="yyyyMM" @change="search">
                </el-date-picker>
            </div>

            <div>
              <div class="table-wrapper box-bg cl">
                <div class="flex-start p-2">
                  <div class="table-title">策略转化分析-地市</div>
                </div>
                <el-divider></el-divider>
                <div class="table-con" style="padding: 0.1rem 0.2rem !important;">
                  <div class="flex-between cl-title">
                    <div class="cl-item">地市</div>
                    <div class="cl-item">生成潜在机会数</div>
                    <div class="cl-item">工单数</div>
                    <div class="cl-item">商机数</div>
                    <div class="cl-item">商机金额(万元)</div>
                    <div class="cl-item">商机转化率</div>
                  </div>
                  <div class="flex-between cl-con" v-for="item in analysisData">
                    <div class="cl-item">{{ item.cityName }}</div>
                    <div class="cl-item">{{ item.potentialOpportunityNum }}</div>
                    <div class="cl-item">{{ item.orderNum }}</div>
                    <div class="cl-item">{{ item.businessOpportunityNum }}</div>
                    <div class="cl-item">{{ item.businessMoneyNum }}</div>
                    <div class="cl-item">{{item.orderNum > 0 ? (100 * item.businessOpportunityNum / item.orderNum).toFixed(2) : (0).toFixed(2)}}%</div>
                  </div>
                </div>
              </div>
            </div>
        </div>

    </div>
</template>

<style scoped lang="less">
p {
    margin: 0;
}

ul,
li {
    list-style: none;
}

.analysis {
    padding: 20px 0;
}

.table-wrapper {
    margin-bottom: 0.2rem;
}

.title-wrapper {
    margin: 5px 0 24px 0;
}

.title {
    margin-left: 5px;
    font-family: PingFangSC-Regular;
    font-size: 0.135rem;
    color: #181818;
    letter-spacing: 0;
    line-height: 0.26rem;
    font-weight: 600;
}

.p-2 {
    padding: 0 0.2rem;
}

.table-con {
    padding: 12px 0 12px 0.33rem !important;
}

.cl-title {
    background-color: #3377FF;
    color: #fff;
    line-height: 0.38rem;
}

.cl-con:nth-child(odd) {
    background-image: linear-gradient(92deg, #E7F0FF 0%, rgba(247, 250, 255, 0.80) 100%);
}

.industry {
    cursor: pointer;
}

.cl-item {
    width: 14%;
    text-align: center;
    font-size: 0.125rem;
    // color: #373D41;
    letter-spacing: 0;
    line-height: 0.38rem;
    font-weight: 500;

    .cl-num {
        font-family: AlibabaSans102Dec10-Bold;
        font-size: 0.25rem;
        color: #181818;
        letter-spacing: 0;
        font-weight: 700;
        line-height: 0.35rem;
    }
}
</style>

<script>

import { allConverRateByCity} from '@/api/sw/analysis/analysis'

export default {
    name: 'index',
    data() {
        return {
            modelChart: null,
            tagChart: null,
            queryParams: {
                monthId: new Date().getFullYear() + (new Date().getMonth() + 1 < 10 ? "0" : "") + (new Date().getMonth() + 1)
            },
            customerTop: [],
            analysisData: [],
        }
    },
    computed: {

    },
    created() {
      // let date = new Date()
      // this.queryParams.monthId = date.getFullYear() + (date.getMonth() + 1 < 10 ? "0" : "") + (date.getMonth() + 1);
    },
    mounted() {
        let _this = this
        console.log('overview-city')
        this.getData()
    },
    methods: {
        search() {
            this.getData()
        },
        getData() {
            this.getAllConverRateCity()
        },
        // 整体转化率分析
        getAllConverRateCity() {
            allConverRateByCity(this.queryParams).then(res => {
                if (res.success) {
                    this.analysisData = []
                    let allOpportunityNum = 0,
                        allorderNum = 0,
                        allpotentialOpportunityNum = 0,
                        allbusinessMoneyNum = 0;

                    res.data.forEach(item => {
                        allOpportunityNum += item.businessOpportunityNum;
                        allorderNum += item.orderNum;
                        allpotentialOpportunityNum += item.potentialOpportunityNum;
                        allbusinessMoneyNum += item.businessMoneyNum;
                    })
                    this.analysisData.push({
                        cityName: '全省',
                        businessOpportunityNum: allOpportunityNum,
                        orderNum: allorderNum,
                        potentialOpportunityNum: allpotentialOpportunityNum,
                        businessMoneyNum: allbusinessMoneyNum
                    })
                    res.data.forEach(item => {
                        this.analysisData.push(item)
                    })
                }
            })
        },
        //详情页
        go(name) {
            if (name != "全省") {
                this.$router.push({
                    name: "analysisOverview",
                    params: { industry: name }
                });
            }
        },
        //弹窗
        handleClose(done) {
            this.$confirm('确认关闭？')
                .then(_ => {
                    done();
                    this.dialogList = ''
                })
                .catch(_ => { });
        },
    }
}
</script>
