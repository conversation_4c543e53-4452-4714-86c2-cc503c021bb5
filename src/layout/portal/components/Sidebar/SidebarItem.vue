<template>
  <div v-if="!item.hidden">
    <template v-if="hasOneShowingChild(item)">
      <app-link :to="resolvePath(onlyOneChild.path)">
        <el-menu-item
          v-if="onlyOneChild.meta"
          :index="resolvePath(onlyOneChild.path)"
          :key="resolvePath(onlyOneChild.path)"
          :class="
            resolvePath(onlyOneChild.path) == pathname ? 'back-color' : ''
          "
        >
          <div class="menu-item">
            <item
              :title="onlyOneChild.meta.title"
            />
            <!-- :icon="onlyOneChild.meta.icon || (item.meta && item.meta.icon)" -->
            <!-- <span :class="`icon iconfont ${onlyOneChild.meta.icon}`"></span>
            <span class="menu-title">{{ onlyOneChild.meta.title }}</span> -->
          </div>
        </el-menu-item>
      </app-link>
    </template>
  </div>
</template>

<script>
import path from "path";
import { isExternal } from "@/utils/validate";
import Item from "./Item";
import AppLink from "./Link";
var pathToRegexp = require("path-to-regexp");
export default {
  name: "SidebarItem",
  components: { Item, AppLink },
  props: {
    item: {
      type: Object,
      required: true,
    },
    basePath: {
      type: String,
      default: "",
    },
    key: {
      type: String,
      default: "",
    },
  },
  data() {
    this.onlyOneChild = null;
    return {
      pathname: this.$route.path,
    };
  },
  watch: {
    "$route.path": function (newVal, oldVal) {
      this.pathname = newVal;
      if (this.onlyOneChild) {
        pathToRegexp(this.resolvePath(this.onlyOneChild.path)).exec(
          this.pathname
        ) &&
        this.$store.dispatch(
          "app/setHidennSidebar",
          this.onlyOneChild.hidden
        );
      }
    },
  },
  created() {
    this.pathname = this.$route.path;
  },
  methods: {
    hasOneShowingChild(item) {
      if (item.children) {
        this.onlyOneChild = item.children[0];
        if (this.onlyOneChild) {
          pathToRegexp(this.resolvePath(this.onlyOneChild.path)).exec(
            this.pathname
          ) &&
          this.$store.dispatch(
            "app/setHidennSidebar",
            this.onlyOneChild.hidden
          );
        }
        return true;
      }
      return false;
    },
    resolvePath(routePath) {
      if (isExternal(routePath)) {
        return routePath;
      }
      if (isExternal(this.basePath)) {
        return this.basePath;
      }
      return path.resolve(this.basePath, routePath);
    },
  },
};
</script>
<style lang="scss" scoped>
.menu-item {
  height: 94px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  align-self: center;
}
//.el-menu-item {
//  height: 94px;
//}
.el-menu-item:hover {
  background: #fff;
}
.back-color{
  outline: none;
}
</style>
