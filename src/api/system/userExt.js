import request from "@/utils/request";

// 查询分页
export function page(params) {
  return request({
    url: "/user/staffext",
    method: "get",
    params: params
  });
}

// 根据ID查询详细
export function getById(id) {
  return request({
    url: "/user/staffext/" + id,
    method: "get"
  });
}

// 修改用户
export function update(data) {
  return request({
    url: "/user/staffext/" + data.staffExtId,
    method: "put",
    data: data
  });
}

// 删除
export function del(id) {
  return request({
    url: "/user/staffext/" + id,
    method: "delete"
  });
}
