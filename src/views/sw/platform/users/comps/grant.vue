<template>
  <div>
    <!-- 添加或修改标签弹框 -->
    <el-dialog
      :title="title"
      :visible.sync="openTabDialog"
      @open="onOpen"
      @close="onCancel"
      width="900px"
    >
      <template slot="title">
        <span class="dialog_title">授权</span>
      </template>
      <el-row>
        <el-transfer v-model="userGrantVal"       :titles="['未选', '已选']"
          filterable :data="roleList"
          :render-content="renderContent"
        ></el-transfer>

      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button @click="onCancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { findRole, findByUserId, rolesList, saveRole, saveUser, saveUserRole } from '@/api/platform/role'
import { findCityGroup, findDistrictGroup } from '@/api/sw/customer/customerManager'
import { findIndustry } from '@/api/sw/product/product'
import { resultError } from '@/utils/messageText'

export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    reloadTable: {
      type: Object, default: function() {
      }
    }
  },
  data() {
    return {
      openTabDialog: false,
      open: false,
      // 表单参数
      formParams: {},
      // 表格loading
      tableLoading: false,
      titles: '',
      // 列表数据
      infoData: {},
      // 表格数据
      tableData: [],
      userGrantVal: [],
      roleList: [],
      // 表单校验
      rules: {},
      title: '',
      formData: {
        type: Object,
        default: {}
      },
      cityList: [],
      countyList: [],
      industryList: [],
      userId: ''
    }
  },
  // 方法集合
  methods: {

    initOpen(id) {
      this.userId = id
      this.openTabDialog = true
      this.userGrantVal=[]
      if(id){
        findByUserId({userId:id}).then((response)=>{
          this.userGrantVal= response.data.map(item=>{
            return item['id']
          })
        })
      }
      rolesList().then((response) => {
        this.roleList = response.data.map(item => {
          return {
            key: item['id'],
            label: item['roleName']
          }
        })
      })
    },
    // 打开窗口
    onOpen() {
      // this.form = { ...this.formData }
      // this.tableLoading = true
    },
    /** 提交按钮 */
    submitForm() {
      console.log('选中的角色id', this.userGrantVal)
      let userRoles = this.userGrantVal.map((item) => {
        return {
          userId: this.userId,
          roleId: item
        }
      })
      let saveParams = { userId: this.userId, userRoles: userRoles }
      saveUserRole(saveParams).then(() => {
        this.openTabDialog = false
      })
    },

    // 取消按钮
    onCancel() {
      this.openTabDialog = false
      this.reloadTable()
    },
    // 表单重置
    reset() {
      this.form = {}
      // this.resetForm("form");
    },
    // 自定义渲染每个列表项
    renderContent(h, option) {
      return h('span', {
        attrs: {
          title: option.label,
        },
      }, option.label);
    },
  }
}
</script>
<style lang="scss" scoped>
.title {
  padding: 0 0.08333rem;
  background-color: #f0f2f5;
  margin-bottom: 0.08333rem;
  font-weight: bold;
  font-size: 0.125rem;
}

.el-button--text {
  color: #3377ff !important;
}

.info {
  padding: 0.16667rem;
}

::v-deep .el-transfer-panel{
  width: 310px;
}
</style>
