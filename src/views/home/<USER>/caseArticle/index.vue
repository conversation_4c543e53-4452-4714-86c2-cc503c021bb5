<template>
  <el-card class="notice-card" v-loading="dataLoading" shadow="never">
    <div slot="header">
      <span class="card-title">
        <div class="title">新闻中心</div>
        <div class="tab">
          <button
            class="tab-item"
            v-for="item in programaList"
            :key="item.programaTypeId"
            @click="getNotice(item.programaTypeCode)"
          >{{ item.programaTypeName }}</button>
          <!-- <el-button
            type="text"
            style="margin-left:59px;font-size: 16px;
font-family: Microsoft YaHei;
font-weight: 400;
color: #C7C7C7;"
            @click="moreExt('business_news')"
          >MORE</el-button>-->
        </div>
      </span>
    </div>
    <div class="home-card-body">
      <div class="left-content">
        <div class="top">
          <img v-if="listNO1.coverUrl" :src="listNO1.coverUrl" alt="图片加载中..." />
          <img v-else src="../../../../assets/images/nopicture.png" alt />
        </div>

        <div class="bottom" v-if="listNO1">
          <div class="time" v-if="listNO1.createDate">
            <div class="data">{{ listNO1.createDate.substring(8, 10) }}</div>
            <div class="year">{{ listNO1.createDate.substring(0, 7) }}</div>
          </div>
          <div class="bottom-content">
            <div class="titles">{{ listNO1.noticeTitle }}</div>
            <div class="content">{{ listNO1.noticeContentText }}</div>
          </div>
        </div>
        <div
          v-else
          style="width:100%;height:100%;text-align:center;line-height:180px;color:white"
        >暂无数据</div>
      </div>
      <div class="right-content">
        <div class="right-top" v-if="listNO1">
          <a
            :href="`/portal/home/<USER>/${listNO1.programaType}/${listNO1.noticeId}`"
            class="right-top"
          >
            <div class="bottom-content">
              <div class="titles">{{ listNO1.noticeTitle }}</div>
              <div class="content">{{ listNO1.noticeContentText }}</div>
            </div>
            <div class="time" v-if="listNO1.createDate">
              <div class="data">{{ listNO1.createDate.substring(8, 10) }}</div>
              <div class="year">{{ listNO1.createDate.substring(0, 7) }}</div>
            </div>
          </a>
        </div>
        <div v-else style="width:100%;height:180px;text-align:center;line-height:180px;">暂无数据</div>

        <div class="right-bottom" v-if="list.length > 0">
          <div class="moreButton">
            <el-button
              class="exec"
              type="text"
              style="font-size: 12px"
              @click="moreExt(listNO1.programaType)"
            >MORE</el-button>
          </div>
          <a
            class="mainContent"
            v-for="item in list"
            :key="item.noticeId"
            :href="`/portal/home/<USER>/${item.programaType}/${item.noticeId}`"
            target="view_window"
          >
            <div class="main-title">
              「
              <span style="color: red">{{ item.noticeTypeName }}</span>
              」
              >
              <div class="titles">{{ item.noticeTitle }}</div>
            </div>
            <div class="main-time">{{ parseTime(item.createDate, "{y}-{m}-{d}") }}</div>
          </a>
        </div>
        <el-empty v-else :image-size="30" description="暂无数据" style="height:100%">
          <el-link icon="el-icon-refresh" @click="getNotice" :underline="false" type="primary">刷新</el-link>
        </el-empty>
      </div>
    </div>
  </el-card>
</template>

<script>
import { selectTop5Page, selectTopPage } from "@/api/system/notice";
import { findList } from "/src/api/system/programaType";

export default {
  name: "caseArtice",
  props: {
    editLayout: {
      type: Boolean,
      default: false,
    },
    item: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      list: [],
      listNO1: [],
      programaList: [],
      dataLoading: false,
      queryParams: {
        tenantId: this.$store.getters.customParam.tenantId,
        programaTypeCode: undefined,
        programaTypeName: undefined,
      },
    };
  },
  watch: {},
  created() {
    this.getNotice("system_notice");
    this.getProgramaTypeList();
  },
  methods: {
    moreExt(type) {
      window.open(`/portal/home/<USER>/${type}`);
    },
    getProgramaTypeList() {
      this.loading = true;
      findList({ ...this.queryParams }).then((response) => {
        this.programaList = response.slice(0, 2);
      });
    },
    getNotice(item) {
      this.dataLoading = true;
      selectTop5Page({
        programaType: item,
      }).then((res) => {
        if (res.success === true) {
          this.list = res.data.records;
          console.log(this.list)
          if (res.data.records[0]) {
            this.listNO1 = res.data.records[0];
            console.log(this.listNO1)
            this.listNO1.createDate = this.parseTime(
              this.listNO1.createDate,
              "{y}-{m}-{d}"
            );
            this.dataLoading = false;
          } else {
            this.dataLoading = false;
          }
        } else {
          console.log(res.error);
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.notice-card {
  border: 0px;
  height: 100%;
  background: #258bf0;
  background: #fff;
  ::v-deep .el-card__header {
    padding: 0;
    height: 20px;
    border: 0px;
    margin: 0px 0 53px 0;
  }
  ::v-deep .el-card__body {
    padding: 0;
  }
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      font-size: 24px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #646464;
    }
    .tab {
      display: flex;
      align-items: center;
      .tab-items {
        height: 32px;
        font-size: 14px;
        line-height: 32px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #646464;
        text-align: center;
        cursor: pointer;
        border: 0px;
        margin-left: 20px;
        color: #ffffff;
        background: #4d85e2;
      }
      .tab-item {
        height: 32px;
        font-size: 14px;
        line-height: 32px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #646464;
        text-align: center;
        cursor: pointer;
        background: #fff;
        border: 0px;
        margin-left: 20px;
      }
      .tab-item:focus {
        height: 32px;
        font-size: 14px;
        line-height: 32px;
        text-align: center;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        background: #4d85e2;
      }
    }
  }
  .home-card-body {
    width: 100%;
    height: 498px;
    display: flex;
    .left-content {
      width: 48%;
      height: 100%;
      background: #258bf0;
      .top {
        width: 100%;
        height: 318px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .bottom {
        padding: 43px;
        height: 180px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .time {
          width: 20%;
          text-align: center;
          .data {
            font-size: 46px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: #f7fafe;
            line-height: 28px;
          }
          .year {
            margin-top: 21px;
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #f7fafe;
            line-height: 28px;
          }
        }
        .bottom-content {
          width: 75%;
          .titles {
            width: 100%;
            word-break: keep-all;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #f7fafe;
            border-bottom: 1px solid #fff;
            padding-bottom: 15px;
            margin-bottom: 15px;
          }
          .content {
            text-overflow: ellipsis;

            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;

            display: -moz-box;
            -moz-line-clamp: 2;
            -moz-box-orient: vertical;

            overflow-wrap: break-word;
            word-break: break-all;
            white-space: normal;
            overflow: hidden;

            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #f7fafe;
            line-height: 28px;
          }
        }
      }
    }
    .right-content {
      width: 52%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .right-top {
        width: 100%;
        height: 180px;
        display: flex;
        align-items: center;
        .bottom-content {
          width: 70%;
          margin-left: 70px;
          .titles {
            width: 100%;
            word-break: keep-all;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            border-bottom: 1px solid #ececec;
            padding-bottom: 15px;
            margin-bottom: 15px;
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #797979;
          }
          .content {
            text-overflow: ellipsis;

            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;

            display: -moz-box;
            -moz-line-clamp: 2;
            -moz-box-orient: vertical;

            overflow-wrap: break-word;
            word-break: break-all;
            white-space: normal;
            overflow: hidden;

            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #979797;
            line-height: 28px;
            opacity: 0.8;
          }
        }
        .time {
          width: 15%;
          margin-left: 10px;
          text-align: center;
          .data {
            font-size: 46px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: #a6a6a6;
            line-height: 28px;
          }
          .year {
            margin-top: 21px;
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #a6a6a6;
            line-height: 28px;
          }
        }
      }
      .right-bottom {
        width: 100%;
        height: calc(100% - 180px);

        .moreButton {
          display: block;
          text-align: right;
        }
        .mainContent {
          display: flex;
          justify-content: space-between;
          flex-direction: row;
          margin-left: 70px;
          padding: 20px 0px;
          font-size: 14px;
          font-size: 16px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #777777;
          border-bottom: 1px solid #ebebeb;
          .main-title {
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
            white-space: nowrap;
            flex-basis: 70%;
            display: flex;
            .titles {
              width: 100%;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
          .main-time {
            flex-basis: 20%;
            text-align: center;
          }
        }
      }
    }
  }
  .card-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .icon {
      font-size: 18px;
      color: #419eee;
      padding: 0px 5px;
      -webkit-transition: font-size 0.25s linear, width 0.25s linear;
      -moz-transition: font-size 0.25s linear, width 0.25s linear;
      transition: font-size 0.25s linear, width 0.25s linear;
    }
    .exec {
      padding: 3px 0;
    }
  }
  .notice {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    padding: 4px 0;
    font-size: 14px;
    .notice-title {
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: nowrap;
      flex-basis: 70%;
    }
    .notice-time {
      flex-basis: 20%;
      text-align: right;
    }
  }
}
</style>
