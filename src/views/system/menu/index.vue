<template>
  <div class="app-container">
    <el-card shadow="never">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item
          label="租户"
          v-if="$store.getters.customParam.userType === 'admin'"
        >
          <el-select
            v-model="queryParams.tenantId"
            placeholder="请选择租户"
            size="small"
            style="width: 200px"
            filterable
            remote
            :remote-method="getTenantList"
            :loading="getTenantLoading"
            @change="tenantChange"
          >
            <el-option
              v-for="item in tenantList"
              :key="item.tenantId"
              :label="item.tenantName"
              :value="item.tenantId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="菜单名称" prop="permissionName">
          <el-input
            v-model="queryParams.permissionName"
            placeholder="请输入菜单名称"
            clearable
            style="width: 200px"
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="permissionStatus">
          <el-select
            v-model="queryParams.permissionStatus"
            placeholder="菜单状态"
            clearable
            style="width: 200px"
            size="small"
          >
            <el-option
              v-for="dict in permissionStatusOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="菜单归属" prop="permissionScope">
          <el-select
            v-model="queryParams.permissionScope"
            placeholder="菜单归属"
            clearable
            size="small"
            style="width: 200px"
          >
            <el-option
              v-for="dict in permissionScopeOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <el-row :gutter="5" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            >新增</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>
      <!-- 列表主体 -->
      <el-table
        v-loading="loading"
        :data="permissionList"
        row-key="permissionId"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :header-cell-style="{ 'text-align': 'center' }"
        height="500"
      >
        <el-table-column
          prop="permissionName"
          label="菜单名称"
          :show-overflow-tooltip="true"
          width="200"
        ></el-table-column>
        <el-table-column
          prop="permissionType"
          label="菜单类型"
          :show-overflow-tooltip="true"
          width="80"
        >
          <template slot-scope="scope">
            <el-tag
              effect="plain"
              :type="
                selectDictRemark(
                  permissionTypeOptions,
                  scope.row.permissionType
                )
              "
            >
              {{
                selectDictLabel(permissionTypeOptions, scope.row.permissionType)
              }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="icon" label="图标" align="center" width="80">
          <template slot-scope="scope">
            <svg-icon :icon-class="scope.row.icon" />
          </template>
        </el-table-column>
        <el-table-column prop="permissionSort" label="排序" width="100">
          <template slot-scope="scope">
            <a @click="move(scope.row, 'up')"><i class="el-icon-sort-up" /></a>
            {{ scope.row.permissionSort }}
            <a @click="move(scope.row, 'down')"
              ><i class="el-icon-sort-down"
            /></a>
          </template>
        </el-table-column>
        <el-table-column
          prop="checkCode"
          label="权限标识"
          :show-overflow-tooltip="true"
          width="100"
        >
          <template slot-scope="scope">
            <a @click="copyToClipboard(scope.row.checkCode)">{{
              scope.row.checkCode
            }}</a>
          </template>
        </el-table-column>
        <el-table-column
          prop="code"
          label="组件路径"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <a @click="copyToClipboard(scope.row.code)">{{ scope.row.code }}</a>
          </template>
        </el-table-column>
        <el-table-column
          prop="uri"
          label="路由地址"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <a @click="copyToClipboard(scope.row.uri)">{{ scope.row.uri }}</a>
          </template>
        </el-table-column>
        <el-table-column
          prop="permissionStatus"
          label="状态"
          :show-overflow-tooltip="true"
          width="80"
          align="center"
        >
          <template slot-scope="scope">
            <el-tag
              :type="
                selectDictLabel(
                  permissionStatusOptions,
                  scope.row.permissionStatus
                ) === '正常'
                  ? ''
                  : 'danger'
              "
            >
              {{
                selectDictLabel(
                  permissionStatusOptions,
                  scope.row.permissionStatus
                )
              }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="permissionVisible"
          label="是否显示"
          :show-overflow-tooltip="true"
          width="80"
          align="center"
        >
          <template slot-scope="scope">
            <el-tag
              :type="
                selectDictLabel(
                  menuVisibleOptions,
                  scope.row.permissionVisible
                ) === '显示'
                  ? ''
                  : 'danger'
              "
            >
              {{
                selectDictLabel(menuVisibleOptions, scope.row.permissionVisible)
              }}
            </el-tag>
          </template>
        </el-table-column>
        <!--        <el-table-column-->
        <!--          prop="terminal"-->
        <!--          label="终端类型"-->
        <!--          :show-overflow-tooltip="true"-->
        <!--          :formatter="permissionTerminalFormat"-->
        <!--        ></el-table-column>-->
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-plus"
              @click="handleAdd(scope.row)"
              >新增</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加或修改菜单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body v-dialogDrag>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="租户">
              <el-input
                v-model="form.tenantName"
                placeholder="租户"
                maxlength="50"
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="上级菜单" prop="parentId">
              <treeselect
                v-model="form.parentId"
                :options="menuOptions"
                :normalizer="normalizer"
                :show-count="true"
                @select="changeSelect"
                placeholder="选择上级菜单"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="(form.parentId === 0 || form.parentId === '0')">
            <el-form-item label="菜单归属" prop="permissionScope">
              <el-col :span="3">
                <el-tooltip
                  class="tooltipItem"
                  effect="dark"
                  content="不同归属代表不同的系统"
                  placement="bottom-end"
                >
                  <div slot="content">
                    不同归属代表不同的系统或功能界面<br />
                    【门户首页】作为门户首页（前台）菜单;<br />
                    【系统管理】作为系统管理（当前页面所处在系统管理）菜单;<br />
                    【查询中心】作为查询中心（入口在首页菜单处）菜单;
                  </div>
                  <i class="el-icon-question" />
                </el-tooltip>
              </el-col>
              <el-col :span="21">
                <el-select v-model="form.permissionScope">
                  <el-option
                    v-for="item in permissionScopeOptions"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  />
                </el-select>
              </el-col>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否移动端跳转">
              <el-col :span="5">
                <el-tooltip
                  class="tooltipItem"
                  effect="dark"
                  content="选中时【菜单类型】会增加客户端类型的菜单选项"
                  placement="bottom-start"
                >
                  <i class="el-icon-question" />
                </el-tooltip>
              </el-col>
              <el-col :span="19">
                <el-radio-group v-model="isMobileJump">
                  <el-radio label="Y">是</el-radio>
                  <el-radio label="N">否</el-radio>
                </el-radio-group>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              v-if="form.permissionType === 'menu'"
              label="是否单点登录"
            >
              <el-col :span="5">
                <el-tooltip
                  class="tooltipItem"
                  effect="dark"
                  content="当菜单地址为外部链接时,是否需要拼接单点登录的 mmy 参数"
                  placement="bottom-end"
                >
                  <i class="el-icon-question" />
                </el-tooltip>
              </el-col>
              <el-col :span="19">
                <el-radio-group v-model="form.permissionFrame">
                  <el-radio label="Y">是</el-radio>
                  <el-radio label="N">否</el-radio>
                </el-radio-group>
              </el-col>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="height: 80px">
            <el-form-item label="菜单类型" prop="permissionType">
              <el-col :span="2">
                <el-tooltip
                  class="tooltipItem"
                  effect="dark"
                  placement="bottom-start"
                >
                  <div slot="content">
                    【目录】<span style="color: red">用于归纳菜单</span>
                    仅在此功能中展示,不作为菜单加载;<br />
                    【菜单】<span style="color: red">用于常规菜单导航配置</span>
                    作为左侧菜单元素进行渲染;<br />
                    【按钮】<span style="color: red">用于权限控制</span>
                    需配合权限标识使用,并编写对应代码控制逻辑;
                  </div>
                  <i class="el-icon-question" />
                </el-tooltip>
              </el-col>
              <el-col :span="22">
                <el-radio-group v-model="form.permissionType">
                  <el-radio
                    v-for="dict in permissionTypeOptions"
                    :key="dict.dictValue"
                    :label="dict.dictValue"
                    style="margin-top: 10px"
                    >{{ dict.dictLabel }}</el-radio
                  >
                  <el-radio
                    v-for="dict in mobileJumpModeOptions"
                    :key="dict.dictValue"
                    :label="dict.dictValue"
                    v-if="isMobileJump === 'Y'"
                    style="margin-top: 10px"
                    >{{ dict.dictLabel }}</el-radio
                  >
                </el-radio-group>
              </el-col>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="菜单名称" prop="permissionName">
              <el-input
                v-model="form.permissionName"
                placeholder="请输入菜单名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              v-if="form.permissionType !== 'operation'"
              label="菜单图标"
            >
              <el-popover
                placement="bottom-start"
                width="460"
                trigger="click"
                @show="$refs['iconSelect'].reset()"
              >
                <IconSelect ref="iconSelect" @selected="selected" />
                <el-input
                  slot="reference"
                  v-model="form.icon"
                  placeholder="点击选择图标"
                  readonly
                >
                  <svg-icon
                    v-if="form.icon"
                    slot="prefix"
                    :icon-class="form.icon"
                    class="el-input__icon"
                    style="height: 32px; width: 16px"
                  />
                  <i
                    v-else
                    slot="prefix"
                    class="el-icon-search el-input__icon"
                  />
                </el-input>
              </el-popover>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="显示排序" prop="permissionSort">
              <el-input-number
                style="width: 100px;"
                v-model="form.permissionSort"
                controls-position="right"
                de="1"
                :min="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item
              label="菜单描述"
            >
              <el-input
                v-model="form.description"
                placeholder="请输入菜单描述"
                maxlength="255"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="终端类型" prop="terminal">
              <el-select v-model="form.terminal">
                <el-option
                  v-for="item in permissionTerminal"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              v-if="
                form.permissionType === 'menu' ||
                form.permissionType === 'operation'
              "
              label="权限标识"
            >
              <el-input
                v-model="form.checkCode"
                placeholder="请输入权限标识"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态" prop="permissionStatus">
              <el-radio-group v-model="form.permissionStatus">
                <el-radio
                  v-for="item in permissionStatusOptions"
                  :key="item.dictValue"
                  :label="item.dictValue"
                  >{{ item.dictLabel }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="菜单显隐" prop="permissionVisible">
              <el-radio-group v-model="form.permissionVisible">
                <el-radio
                  v-for="item in menuVisibleOptions"
                  :key="item.dictValue"
                  :label="item.dictValue"
                  >{{ item.dictLabel }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20">
            <el-form-item
              v-if="form.permissionType !== 'operation'"
              label="路由地址"
              prop="uri"
            >
              <el-col :span="2">
                <el-tooltip
                  class="tooltipItem"
                  effect="dark"
                  placement="bottom-start"
                >
                  <div slot="content">
                    当前菜单的【路由地址】与父级的【路由地址】逐级拼接,形成
                    url<br />
                    同节点下不建议使用相同的【路由地址】<br />
                    多级路径需使用<span style="color: red"> '/' </span> 分割
                  </div>
                  <i class="el-icon-question" />
                </el-tooltip>
              </el-col>
              <el-col :span="22">
                <el-input v-model="form.uri" placeholder="请输入路由地址" />
              </el-col>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20">
            <el-form-item
              v-if="form.permissionType !== 'operation'"
              label="组件路径"
              prop="componet"
            >
              <el-col :span="2">
                <el-tooltip
                  class="tooltipItem"
                  effect="dark"
                  placement="bottom-start"
                >
                  <div slot="content">
                    当前菜单加载的组件,且有对应的父子关系,子菜单的组件将加载在父级菜单的组件内<br /><br />
                    系统提供如下预制组件<br />
                    【Portal】门户首页（前台）左边栏与头部栏目;<br />
                    【Layout】系统管理（当前页面所处在系统管理）左边栏与头部栏目;<br />
                    【ParentView】没有左边栏与头部栏目,一般作为父级使用;<br />
                    【自定义】需确保路径对应正确的代码位置;<br /><br />
                     ！若是否外链单选框选择的为'是',则此处应当为外链地址;路由地址设为iframe;
                  </div>
                  <i class="el-icon-question" />
                </el-tooltip>
              </el-col>
              <el-col :span="22">
                <el-input v-model="form.code" placeholder="请输入组件路径"/>
              </el-col>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMenu,
  getMenu,
  delMenu,
  addMenu,
  updateMenu,
} from "@/api/system/menu";
import { list as tenantList } from "@/api/system/tenant";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import IconSelect from "@/components/IconSelect";

export default {
  name: "Menu",
  components: { Treeselect, IconSelect },
  data() {
    return {
      // 遮罩层
      loading: true,
      getTenantLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 菜单表格树数据
      permissionList: [],
      tenantList: [],
      // 菜单树选项
      menuOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 菜单类型
      permissionTypeOptions: [],
      // 菜单归属
      permissionScopeOptions: [],
      // 是否显示
      menuVisibleOptions: [],
      // 菜单状态
      permissionStatusOptions: [],
      // 终端类型
      permissionTerminal: [],
      // 查询参数
      queryParams: {
        permissionName: undefined,
        permissionStatus: undefined,
        permissionScope: undefined,
        tenantId: this.$store.getters.customParam.tenantId,
        tenantName: this.$store.getters.customParam.tenantName,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        permissionName: [
          { required: true, message: "菜单名称不能为空", trigger: "blur" },
        ],
        permissionSort: [
          { required: true, message: "菜单顺序不能为空", trigger: "blur" },
          {
            pattern: /^\d+$/,
            message: '只可以输入非负整数',
            trigger: "blur"
          }
        ],
        uri: [{ required: true, message: "路由地址不能为空", trigger: "blur" }],
        permissionStatus: [
          { required: true, message: "请选择状态", trigger: "blur" },
        ],
        parentId: [
          { required: true, message: "请选择父级节点", trigger: "blur" },
        ],
        permissionScope: [
          { required: true, message: "请选择菜单归属", trigger: "blur" },
        ],
      },
      isMobileJump: "N",
      mobileJumpModeOptions: [],
    };
  },
  created() {
    this.getTenantList();
    this.getList();
    this.getDicts("menu_type").then((response) => {
      this.permissionTypeOptions = response.data;
    });
    this.getDicts("scope").then((response) => {
      this.permissionScopeOptions = response.data;
    });
    this.getDicts("menu_visible").then((response) => {
      this.menuVisibleOptions = response.data;
    });
    this.getDicts("menu_status").then((response) => {
      this.permissionStatusOptions = response.data;
    });
    this.getDicts("mobile_jump_mode").then((response) => {
      this.mobileJumpModeOptions = response.data;
    });
    this.getDicts("menu_terminal").then((response) => {
      this.permissionTerminal = response.data;
    });
  },
  methods: {
    getTenantList(tenantName) {
      this.getTenantLoading = true;
      let query = {}
      if (tenantName !== undefined && tenantName !== '') {
        query.tenantName = tenantName
        query.tenantId = undefined
      } else {
        query.tenantId = this.queryParams.tenantId
      }
      tenantList(query).then((response) => {
        this.tenantList = response.data;
      }).finally(() => {
        this.getTenantLoading = false;
      });
    },
    permissionScopeFormat(row, column) {
      return this.selectDictLabel(this.permissionScopeOptions, row.menuScope);
    },
    menuVisibleFormat(row, column) {
      return this.selectDictLabel(
        this.menuVisibleOptions,
        row.permissionVisible
      );
    },
    permissionStatusFormat(row, column) {
      return this.selectDictLabel(
        this.permissionStatusOptions,
        row.permissionStatus
      );
    },
    permissionTerminalFormat(row, column) {
      return this.selectDictLabel(this.permissionTerminal, row.terminal);
    },
    // 选择图标
    selected(icon) {
      this.form = {
        ...this.form,
        icon,
      };
    },
    /** 查询菜单列表 */
    getList() {
      this.loading = true;
      listMenu(this.queryParams).then((response) => {
        this.permissionList = this.handleTree(response.data, "permissionId");
        this.loading = false;
      });
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.permissionId,
        label: node.permissionName,
        children: node.children,
      };
    },
    /** 查询菜单下拉树结构 */
    getTreeselect() {
      listMenu(this.queryParams).then((response) => {
        this.menuOptions = [];
        const menu = {
          permissionId: 0,
          permissionName: "主类目",
          children: [],
        };
        menu.children = this.handleTree(response.data, "permissionId");
        this.menuOptions.push(menu);
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        permissionId: undefined,
        parentId: 0,
        permissionName: undefined,
        icon: undefined,
        permissionType: "menu",
        permissionSort: 1,
        permissionStatus: "valid",
        permissionVisible: "show",
        permissionFrame: "N",
        terminal: "PC",
        tenantName: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      this.getTreeselect();
      if (row != null && row.permissionId) {
        this.form.parentId = row.permissionId;
        if (row.permissionId !== 0) {
          this.form.permissionScope = row.permissionScope;
        }
      } else {
        this.form.parentId = 0;
      }
      this.form.tenantName = this.queryParams.tenantName;
      this.form.tenantId = this.queryParams.tenantId;
      this.isMobileJump = "N";
      this.open = true;
      this.title = "添加菜单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getTreeselect();
      getMenu(row.permissionId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改菜单";
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.permissionId !== undefined) {
            updateMenu(this.form).then((response) => {
              if (!response.success) {
                this.$message.error(response.message);
              } else {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }
            });
          } else {
            addMenu(this.form).then((response) => {
              if (response.success) {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              } else {
                this.$message.error(response.message);
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const _this = this;
      this.$confirm(
        '是否确认删除名称为"' + row.permissionName + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(function () {
        delMenu({ permissionId: row.permissionId }).then((res) => {
          if (res.success) {
            _this.msgSuccess("删除成功");
          } else {
            _this.msgError(res.message);
          }
          _this.getList();
        });
      });
    },
    changeSelect(e) {
      if (e.parentId !== 0) {
        this.form.permissionScope = e.permissionScope;
      }
    },
    tenantChange(tenantId) {
      if (tenantId !== "") {
        this.queryParams.tenantName = this.tenantList.find(
          (item) => item.tenantId === tenantId
        ).tenantName;
      }
      this.handleQuery();
    },
    move(row, type) {
      if (type === "up") {
        if(row.permissionSort <= 0) {
          this.$message.error("序号不能小于0!");
          return
        } else {
          row.permissionSort -= 1;
        }
        // row.permissionSort -= 1;
      } else if (type === "down") {
        row.permissionSort += 1;
      }
      updateMenu(row).then((response) => {
        if (response.success) {
          this.$message.success((type === "up" ? "上" : "下") + "移成功");
        } else {
          this.$message.error(response.message);
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.tooltipItem {
  line-height: 40px;
  margin-right: 0px;
}
::v-deep .el-table__row:not([class*="el-table__row--level-"]) {
  td:first-child {
    padding-left: 24px !important;
  }
}
::v-deep .el-table__row:not([class*="el-table__row--level-"]) {
  background-color: #ffffff;
}
::v-deep .el-table__row--level-0 {
  background-color: #ffffff;
}
::v-deep .el-table__row--level-1 {
  background-color: #f7f7f7;
}
::v-deep .el-table__row--level-2 {
  background-color: #efefef;
}
::v-deep .el-table__row--level-3 {
  background-color: #e7e7e7;
}
::v-deep .el-table__row--level-4 {
  background-color: #dfdfdf;
}
</style>
