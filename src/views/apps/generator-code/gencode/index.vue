<template>
  <div>
    <el-row>
      <el-col :span="4">
        <div class="selectClass">
          <el-select
            v-model="datasourceId"
            @change="datasourceChange(datasourceId)"
            filterable
            placeholder="请选择"
          >
            <el-option
              v-for="item in datasourceListData"
              :key="item.id"
              :label="item.databaseName"
              :value="item.id"
            ></el-option>
          </el-select>
          <el-tree
            :data="tableTree"
            :props="defaultProps"
            default-expand-all
            @node-click="tableTreeClick"
            ref="tree"
          ></el-tree>
        </div>
      </el-col>
      <el-col :span="20">
        <div>
          <el-button type="primary" @click="genCodeDialog">生成代码</el-button>
          <el-table :data="tableInfo.columnList" border style="width: 100%">
            <el-table-column fixed prop="columnName" label="字段名"></el-table-column>
            <el-table-column fixed prop="propertyName" label="Java属性"></el-table-column>
            <el-table-column prop="propertyType" label="Java类型" width="130">
              <template slot-scope="scope">
                <el-select v-model="scope.row.propertyType" placeholder="请选择">
                  <el-option
                    v-for="item in propertyTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="columnComment" label="备注">
              <template slot-scope="scope">
                <el-input v-model="scope.row.columnComment"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="显示类型" width="130">
              <template slot-scope="scope">
                <el-select v-model="scope.row.textType" placeholder="请选择">
                  <el-option
                    v-for="item in textTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="表格展示" width="100">
              <template slot-scope="scope">
                <el-switch v-model="scope.row.tableShow"></el-switch>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
    </el-row>
    <el-dialog title="生成代码" :visible.sync="genCodeDialogVisible" ref="genCodeDialog" v-dialogDrag>
      <el-form ref="genCodeForm" :model="genCodeForm" :rules="genCodeFormRules" label-width="75px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="包名" prop="parentPackage">
              <el-input v-model="genCodeForm.parentPackage"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="模块名" prop="moduleName">
              <el-input v-model="genCodeForm.moduleName"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="作者" prop="author">
              <el-input v-model="genCodeForm.author"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="表名" prop="tableName">
              <el-input v-model="genCodeForm.tableName"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="表前缀" prop="tablePrefix">
              <el-input v-model="genCodeForm.tablePrefix"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="表后缀" prop="tableSuffix">
              <el-input v-model="genCodeForm.tableSuffix"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="Swagger" prop="enableSwagger">
              <el-switch v-model="genCodeForm.enableSwagger"></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Lombok" prop="enableLombok">
              <el-switch v-model="genCodeForm.enableLombok"></el-switch>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="genCode('genCodeForm')">确 定</el-button>
        <el-button @click="genCodeDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { findList } from "@/api/apps/datasource";
import { findTableInfo, findTableList } from "@/api/apps/table";
import { generatorCode } from "@/api/apps/genCode";

export default {
  data() {
    return {
      datasourceListData: [],
      genCodeDialogVisible: false,
      datasourceId: "",
      tableName: "",
      defaultProps: {
        children: "children",
        label: "tableName",
      },
      tableTree: [],
      genCodeForm: {
        parentPackage: "",
        moduleName: "",
        author: "",
        tableName: "",
        tablePrefix: "",
        tableSuffix: "",
        enableSwagger: true,
        enableLombok: true,
        datasourceId: "",
        tableInfo: {},
      },
      tableInfo: {},
      genCodeFormRules: {
        parentPackage: [
          { required: true, message: "请输入包名", trigger: "blur" },
        ],
        moduleName: [
          { required: true, message: "请输入模块名", trigger: "blur" },
        ],
        author: [{ required: true, message: "请输入作者", trigger: "blur" }],
        tableName: [{ required: true, message: "请输入表名", trigger: "blur" }],
      },
      textTypeOptions: [
        {
          value: "input",
          label: "输入框",
        },
        {
          value: "radio",
          label: "单选框",
        },
        {
          value: "checkbox",
          label: "复选框",
        },
        {
          value: "select",
          label: "选择器",
        },
        {
          value: "switch",
          label: "开关",
        },
        {
          value: "textarea",
          label: "文本域",
        },
        {
          value: "no",
          label: "禁止生成",
        },
      ],
      propertyTypeOptions: [
        {
          value: "String",
          label: "String",
        },
        {
          value: "Integer",
          label: "Integer",
        },
        {
          value: "Boolean",
          label: "Boolean",
        },
        {
          value: "Double",
          label: "Double",
        },
        {
          value: "Float",
          label: "Float",
        },
        {
          value: "Long",
          label: "Long",
        },
        {
          value: "BigDecimal",
          label: "BigDecimal",
        },
        {
          value: "Date",
          label: "Date",
        },
        {
          value: "LocalDate",
          label: "LocalDate",
        },
        {
          value: "LocalDateTime",
          label: "LocalDateTime",
        },
      ],
    };
  },
  created() {
    this.getDatasourceList();
  },
  methods: {
    getDatasourceList() {
      findList().then((res) => {
        console.log(res);
        this.datasourceListData = res.data;
        this.datasourceId = this.datasourceListData[0].id;
        this.getTableList(this.datasourceId);
      });
    },
    getTableList(datasourceId) {
      findTableList(datasourceId).then((res) => {
        this.tableTree = res.data;
        this.tableName = this.tableTree[0].tableName;
        this.tableTreeClick(this.tableTree[0]);
      });
    },
    datasourceChange(datasourceId) {
      if (datasourceId) {
        this.getTableList(datasourceId);
      }
    },
    tableTreeClick(data) {
      findTableInfo(data).then((res) => {
        this.tableInfo = res.data;
        this.tableName = data.tableName;
      });
    },
    genCodeDialog() {
      this.genCodeDialogVisible = true;
      this.genCodeForm.tableName = this.tableName;
      this.resetForm();
    },
    genCode(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.genCodeForm.datasourceId = this.datasourceId;
          this.genCodeForm.tableInfo = this.tableInfo;
          generatorCode(this.genCodeForm).then((res) => {
            const blob = new Blob([res], { type: "application/zip" });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement("a"); // 创建a标签
            link.href = url;
            link.download = "src.zip"; // 重命名文件
            this.genCodeDialogVisible = false;
            link.click();
            URL.revokeObjectURL(url); // 释放内存
          });
        } else {
          return false;
        }
      });
    },
    resetForm() {
      this.genCodeForm.parentPackage = "";
      this.genCodeForm.moduleName = "";
      this.genCodeForm.author = "";
      this.genCodeForm.tablePrefix = "";
      this.genCodeForm.tableSuffix = "";
    },
  },
};
</script>

<style lang="scss" scoped>
.el-row {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.el-col {
  border-radius: 4px;
}

.bg-purple-dark {
  background: #99a9bf;
}

.bg-purple {
  background: #d3dce6;
}

.bg-purple-light {
  background: #e5e9f2;
}

.grid-content {
  border-radius: 4px;
  min-height: 36px;
}

.row-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}
</style>
