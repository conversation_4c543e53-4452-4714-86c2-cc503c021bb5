import request from "@/utils/request";

// 查询菜单列表
export function listMenu(params) {
  return request({
    url: "/user/permissions/findAllMenu",
    method: "get",
    params: params
  });
}

// 查询菜单详细
export function getMenu(permissionId) {
  return request({
    url: "/user/permissions/" + permissionId,
    method: "get"
  });
}

// 查询用户组下菜单
export function getOrgMenu(data) {
  return request({
    url: "/user/roles/orgId/permissions",
    method: "post",
    data: data
  });
}

// 查询菜单下拉树结构
export function treeselect(params) {
  return request({
    url: "/user/permissions/findAllMenu",
    method: "get",
    params
  });
}

// 根据角色ID查询菜单下拉树结构
export function roleMenuTreeselect(roleId) {
  return request({
    url: "/system/menu/roleMenuTreeselect/" + roleId,
    method: "get"
  });
}

// 新增菜单
export function addMenu(data) {
  return request({
    url: "/user/permissions",
    method: "post",
    data: data
  });
}

// 修改菜单
export function updateMenu(data) {
  return request({
    url: "/user/permissions/edit",
    method: "post",
    data
  });
}

// 删除菜单
export function delMenu(data) {
  return request({
    url: "/user/permissions/delete",
    method: "post",
    data: data
  });
}
