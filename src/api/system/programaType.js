import request from "/src/utils/request";

// 新增系统栏目类型
export function add(data) {
  return request({
    url: "/cms/programaType",
    method: "post",
    data: data
  });
}

// 删除系统栏目类型
export function del(data) {
  return request({
    url: "/cms/programaType/delete",
    method: "post",
    data: data
  });
}

// 修改系统栏目类型
export function update(data) {
  return request({
    url: "/cms/programaType/update",
    method: "post",
    data: data
  });
}

// 根据主键查询单个系统栏目类型详情
export function findOne(id) {
  return request({
    url: "/cms/programaType/" + id,
    method: "get"
  });
}

// 根据分页信息查询系统栏目类型列表
export function findPageList(data) {
  return request({
    url: "/cms/programaType",
    method: "get",
    params: data
  });
}

// 查询系统栏目类型列表
export function findList(data) {
  return request({
    url: "/cms/programaType/list",
    method: "get",
    params: data
  });
}

// 同步数据至其他租户
export function syncData(data) {
  return request({
    url: "/cms/programaType/sync",
    method: "post",
    data: data
  });
}
