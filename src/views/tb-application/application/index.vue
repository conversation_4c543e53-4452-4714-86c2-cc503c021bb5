<template>
  <div class="app-container">
    <el-card shadow="never">
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        :inline="true"
      >
        <el-form-item label="应用名称" prop="applicationName">
          <el-input
            v-model="queryParams.applicationName"
            placeholder="请输入应用名称"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
          >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            >新增</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="dataList">
        <el-table-column label="应用主键" align="left" prop="applicationId" :show-overflow-tooltip="true" />
        <el-table-column label="应用名称" align="left" prop="applicationName" :show-overflow-tooltip="true" />
        <el-table-column label="钉钉cropId" align="left" prop="cropId" :show-overflow-tooltip="true" />
        <el-table-column label="钉钉agentId" align="left" prop="agentId" :show-overflow-tooltip="true" />
        <el-table-column label="钉钉appKey" align="left" prop="appKey" :show-overflow-tooltip="true" />
        <el-table-column label="钉钉appSecret" align="left" prop="appSecret" :show-overflow-tooltip="true" />
        <el-table-column label="是否启用" align="left" prop="appStatus" :show-overflow-tooltip ="true">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.appStatus"
              active-value="1"
              inactive-value="2"
              @change="switchStatus(scope.row)"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="260"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              :loading="
                reloadId === scope.row.id && reloadType === 'edit'
              "
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              :loading="
                reloadId === scope.row.id && reloadType === 'remove'
              "
            >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改参数配置对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="open"
        width="1000px"
        append-to-body
        :close-on-press-escape="false"
        @close="cancel"
        v-dialogDrag
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="150px">
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="应用主键" prop="applicationId">
                <el-input
                  v-model="form.applicationId"
                  placeholder="请输入应用主键"
                  maxlength="64"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="应用名称" prop="applicationName">
                <el-input
                  v-model="form.applicationName"
                  placeholder="请输入应用名称"
                  maxlength="64"
                  :disabled="reloadType === 'edit'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="钉钉cropId" prop="cropId">
                <el-input
                  v-model="form.cropId"
                  placeholder="请输入钉钉cropId"
                  maxlength="64"
                  :disabled="reloadType === 'edit'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="钉钉agentId" prop="agentId">
                <el-input
                  v-model="form.agentId"
                  placeholder="请输入钉钉agentId"
                  maxlength="64"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="钉钉appKey" prop="appKey">
                <el-input
                  v-model="form.appKey"
                  placeholder="请输入钉钉appKey"
                  maxlength="64"
                  :disabled="reloadType === 'edit'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="钉钉appSecret" prop="appSecret">
                <el-input
                  v-model="form.appSecret"
                  placeholder="请输入钉钉appSecret"
                  maxlength="64"
                  :disabled="reloadType === 'edit'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="url">
                <el-input
                  v-model="form.url"
                  placeholder="请输入url"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="消息模板id">
                <el-input
                  v-model="form.templateId"
                  placeholder="请输入消息模板id"
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="是否启用">
                <el-switch
                  v-model="form.appStatus"
                  active-value="1"
                  inactive-value="2"
                >

                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="saveLoading"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import {
  page,
  getById,
  del,
  add,
  updateById,
  checkApplication
} from "@/api/system/tbApplication";

export default {
  name: "TbApplication",
  data() {
    const validateApplicationId = (rule, value, callback) => {
      if (value === '') {
        callback(new Error("应用主键不能为空"));
      } else if (this.form.id == undefined) {
        //新增時校验钉钉应用id唯一性

        checkApplication(this.form).then((response) => {
          if (response.data) {
            callback()
          } else {
            callback(new Error(response.message));
          }
        });
      }else {

        callback()

      }
    }
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 参数表格数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        applicationName: undefined,
      },
      tenantId: this.$store.getters.customParam.tenantId,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        applicationId: [
          // { required: true, message: "应用主键不能为空", trigger: "blur" },
          { required: true, validator: validateApplicationId, trigger: "blur" },
        ],
        applicationName: [
          { required: true, message: "应用名称不能为空", trigger: "blur" },
        ],
        cropId: [
          { required: true, message: "钉钉cropId不能为空", trigger: "blur" },
        ],
        agentId: [
          { required: true, message: "钉钉agentId不能为空", trigger: "blur" },
        ],
        appKey: [
          { required: true, message: "钉钉appKey不能为空", trigger: "blur" },
        ],
        appSecret: [
          { required: true, message: "钉钉appSecret不能为空", trigger: "blur" },
        ],
      },
      reloadId: undefined,
      reloadType: undefined,
      saveLoading: false,
    };


  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true;
      page(this.queryParams).then(
        (response) => {
          this.dataList = response.data.records;
          this.total = response.data.total;
          this.loading = false;
        }
      );
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reloadId = undefined;
      this.reloadType = undefined;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        applicationId: '',
        applicationName: '',
        cropId: '',
        agentId: '',
        appKey: '',
        appSecret: '',
        url: '',
        templateId: '',
      };
      this.resetForm("queryForm");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加应用";
      this.reloadType = "add";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      this.reloadId = id;
      this.reloadType = "edit";
      getById(id).then((response) => {
        if (response.data) {
          this.form = response.data;
          this.open = true;
          this.title = "修改应用";
        } else {
          this.$message.error("数据异常！");
        }
      });
    },

    //钉钉应用状态开关
    switchStatus(row){
      const data = row;
      updateById(data).then((response) => {
        if (response.data) {

        } else {
          this.$message.error(response.message);
        }
      });

    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.saveLoading = true;
          if (this.form.id !== undefined) {
            updateById(this.form).then((response) => {
              if (!response.success) {
                this.$message.error(response.message);
                this.saveLoading = false;
              } else {
                this.msgSuccess("修改成功");
                this.open = false;
                this.saveLoading = false;
                this.getList();
              }
            });
          } else {
            add(this.form).then((response) => {
              if (!response.success) {
                this.$message.error(response.message);
                this.saveLoading = false;
              } else {
                this.msgSuccess("新增成功");
                this.open = false;
                this.saveLoading = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.reloadId = id;
      this.reloadType = "remove";
      this.$confirm(
        '是否确认删除应用名称为"' + row.applicationName + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return del(id);
        })
        .then(() => {
          this.reloadId = undefined;
          this.reloadType = undefined;
          this.getList();
          this.msgSuccess("删除成功");
        })
        .catch(() => {
          this.reloadId = undefined;
        });
    },
  },
};
</script>
