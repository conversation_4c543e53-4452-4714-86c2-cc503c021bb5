import {
  constantRoutes
} from "@/router";
import {
  getRouters
} from "@/api/menu";
import Layout from "@/layout/index";
import Portal from "@/layout/portal";
import ParentView from "@/components/ParentView";
import {
  arrayToTree
} from "@/utils";

const permission = {
  state: {
    routes: [],
    addRoutes: [],
    defaultRoutes: [],
    topbarRouters: [],
    sidebarRouters: []
  },
  mutations: {
    SET_ROUTES: (state, routes) => {
      state.addRoutes = routes;
      state.routes = constantRoutes.concat(routes);
    },
    SET_DEFAULT_ROUTES: (state, routes) => {
      state.defaultRoutes = constantRoutes.concat(routes);
    },
    SET_TOPBAR_ROUTES: (state, routes) => {
      // 顶部导航菜单默认添加统计报表栏指向首页
      const index = [];
      state.topbarRouters = routes.concat(index);
    },
    SET_SIDEBAR_ROUTERS: (state, routes) => {
      state.sidebarRouters = routes;
    }
  },
  actions: {
    // 生成路由
    GenerateRoutes({commit}, params) {
      return new Promise(resolve => {
        getRouters(params).then(res => {
          // 预处理的接口原始数据
          let dataList = [];
          // 循环接口的原始数据,预处理接口数据
          res.data.forEach(item => {
            // 菜单类型为按钮,不会参与生成导航列表
            item.permissionType !== "operation" &&
            dataList.push({
              name: item.uri,
              path: item.uri,
              id: item.permissionId,
              pid: item.parentId,
              component: item.code,
              permissionType: item.permissionType,
              hidden: item.permissionVisible === "hide",
              meta: {
                title: item.permissionName,
                permissionFrame: item.permissionFrame,
                icon: item.icon,
                external: /^(https?:|http:)/.test(item.code) ? item.code : null
              },
              permissionScope: item.permissionScope
            });
          });
          // 转为树状结构后的菜单数组
          let list = [];
          // 首先将原始数据转为树状结构
          // 接着判断,如果是菜单类型是目录,则将此菜单的子节点插入数组,此菜单节点不会参与生成导航列表
          arrayToTree(dataList).forEach(item => {
            if (item.permissionType === "folder" && item.children) {
              item.children.forEach(i => {
                list.push({
                  path: item.path,
                  hidden: i.hidden,
                  component: item.component,
                  permissionScope: item.permissionScope,
                  children: [i]
                });
              });
            } else {
              list.push(item);
            }
          });
          const exp = []
          list = list.map(v => {
            // 如果当前节点是顶级节点,且归属不是门户首页,且没有子节点
            if (v.pid === '0' && v.permissionScope !== 'portal' && !v.children) {
              // 不知道这是干啥,目前没有这个数据情况,可能是预处理一下初始化的菜单根节点,防止页面报路由表错误
              return {
                ...v,
                name: undefined,
                children: [v],
                component: 'Layout',
                meta: {
                  title: undefined,
                  icon: undefined
                },
              }
            } else {
              /*
              // 任何节点如果只有一个子节点
              if (typeof (v.children) !== 'undefined' && v.children.length === 1) {
                // 唯一子节点的全部子节点
                const vc = v.children[0].children || [];
                // 这里好像会将,唯一子节点的全部子节点提出来,然后唯一子节点没有子节点了
                vc.forEach(vi => {
                  if (vi.permissionType === "menu") {
                    exp.push({
                      ...v,
                      name: undefined,
                      hidden:vi.hidden,
                      children: [{
                        ...vi,
                        name: undefined,
                        meta: {
                          title: undefined,
                          icon: undefined
                        },
                        path: v.children[0].path + '/' + vi.path
                      }],
                      component: v.component,
                      meta: {
                        title: undefined,
                        icon: undefined
                      },
                    })
                  }
                })
                v.children[0].children = null
                return v
              }
              */
            }
            return v
          });
          list = list.concat(exp);
          // 隐藏系统管理原始的根节点,并将根节点下的所有节点暴露为一级节点
          list.forEach((item, index) => {
            if (item.pid === '0' && item.permissionScope === 'system' && item.children) {
              item.hidden = true;
              item.children.forEach(item => {
                item.path = '/'+ item.permissionScope +'/' + item.path
              })
              list = list.concat(item.children)
            }
          })
          const sdata = JSON.parse(JSON.stringify(list));
          const rdata = JSON.parse(JSON.stringify(list));
          const sidebarRoutes = filterAsyncRouter(sdata);
          const rewriteRoutes = filterAsyncRouter(rdata, false, true);
          rewriteRoutes.push({
            path: "*",
            redirect: "/404",
            hidden: true
          });
          commit("SET_ROUTES", rewriteRoutes);
          commit("SET_SIDEBAR_ROUTERS", constantRoutes.concat(sidebarRoutes));
          commit("SET_DEFAULT_ROUTES", sidebarRoutes);
          commit("SET_TOPBAR_ROUTES", sidebarRoutes);
          resolve(rewriteRoutes);
        });
      });
    }
  }
};

// 遍历后台传来的路由字符串，转换为组件对象
function filterAsyncRouter(asyncRouterMap, lastRouter = false, type = false) {
  return asyncRouterMap.filter(route => {
    if (type && route.children) {
      route.children = filterChildren(route.children);
    }
    if (route.component) {
      // Layout ParentView 组件特殊处理
      if (route.component === "Layout") {
        route.component = Layout;
      } else if (route.component === "Portal") {
        route.component = Portal;
      } else if (route.component === "ParentView") {
        route.component = ParentView;
      } else {
        route.component = loadView(route.component);
      }
    }
    if (route.children != null && route.children && route.children.length) {
      route.children = filterAsyncRouter(route.children, route, type);
    } else {
      delete route["children"];
      delete route["redirect"];
    }
    return true;
  });
}

function filterChildren(childrenMap, lastRouter = false) {
  var children = [];
  childrenMap.forEach((el, index) => {
    if (el.children && el.children.length) {
      if (el.component === "ParentView") {
        el.children.forEach(c => {
          c.path = el.path + "/" + c.path;
          if (c.children && c.children.length) {
            children = children.concat(filterChildren(c.children, c));
            return;
          }
          children.push(c);
        });
        return;
      }
    }
    if (lastRouter) {
      el.path = lastRouter.path + "/" + el.path;
    }
    children = children.concat(el);
  });
  return children;
}

export const loadView = view =>{
  return resolve => /^(https?:|http:)/.test(view) ? require(['@/views/iframe/index.vue'],resolve) : require([`@/views/${view}`], resolve)
}

export default permission;
