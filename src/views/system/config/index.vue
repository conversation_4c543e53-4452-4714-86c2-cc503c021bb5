<template>
  <div class="app-container">
    <el-card shadow="never">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
        @submit.native.prevent
      >
        <el-form-item label="参数键名" prop="configCode">
          <el-input
            v-model="queryParams.configCode"
            placeholder="请输入参数键名"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:config:add']"
            >新增</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="configList">
        <el-table-column label="序号" type="index" width="50" />
        <el-table-column
          label="参数键名"
          align="left"
          prop="configCode"
          :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <a @click="copyToClipboard(scope.row.configCode)">{{
              scope.row.configCode
            }}</a>
          </template>
          </el-table-column>
        <el-table-column
          label="参数键值"
          align="left"
          prop="configValue"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <a @click="copyToClipboard(scope.row.configValue)">{{
              scope.row.configValue
            }}</a>
          </template>
        </el-table-column>
        <el-table-column
          label="扩展A"
          align="left"
          prop="attra"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="扩展B"
          align="left"
          prop="attrb"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:config:edit']"
              :loading="reloadId === scope.row.configId && reloadType === 'edit'"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:config:remove']"
              :loading="reloadId === scope.row.configId && reloadType === 'remove'"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改参数配置对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body v-dialogDrag>
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="参数键名" prop="configCode">
            <el-input v-model="form.configCode" placeholder="请输入参数键名" />
          </el-form-item>
          <el-form-item label="参数键值" prop="configValue">
            <el-input v-model="form.configValue" placeholder="请输入参数键值" />
          </el-form-item>
          <el-form-item label="扩展A" prop="attra">
            <el-input v-model="form.attra" type="textarea" placeholder="请输入内容" />
          </el-form-item>
          <el-form-item label="扩展B" prop="attrb">
            <el-input v-model="form.attrb" type="textarea" placeholder="请输入内容" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="saveLoading"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import {
  listConfig,
  getConfig,
  delConfig,
  addConfig,
  updateConfig,
} from "@/api/system/config";

export default {
  name: "Config",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 参数表格数据
      configList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 类型数据字典
      typeOptions: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        configCode: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        configCode: [{ required: true, message: "参数键名不能为空", trigger: "blur" }],
        configValue: [{ required: true, message: "参数键值不能为空", trigger: "blur" }],
      },
      reloadId: undefined,
      reloadType: undefined,
      saveLoading: false,
    };
  },
  created() {
    this.getList();
    this.getDicts("sys_yes_no").then((response) => {
      this.typeOptions = response.data;
    });
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true;
      listConfig(this.addDateRange(this.queryParams, this.dateRange)).then((response) => {
        this.configList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 参数系统内置字典翻译
    typeFormat(row, column) {
      return this.selectDictLabel(this.typeOptions, row.configType);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        configId: undefined,
        configCode: undefined,
        configValue: undefined,
        attra: undefined,
        attrb: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加参数";
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const configId = row.configId;
      this.reloadId = configId;
      this.reloadType = "edit";
      getConfig(configId).then((response) => {
        this.reloadId = undefined;
        this.reloadType = undefined;
        if (response.data) {
          this.form = response.data;
          this.open = true;
          this.title = "修改参数";
        } else {
          this.$message.error("数据异常！");
        }
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.saveLoading = true;
          if (this.form.configId != undefined) {
            updateConfig(this.form).then((response) => {
              if (!response.success) {
                this.$message.error(response.message);
                this.saveLoading = false;
              } else {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
                this.saveLoading = false;
              }
            });
          } else {
            addConfig(this.form).then((response) => {
              if (!response.success) {
                this.$message.error(response.message);
                this.saveLoading = false;
              } else {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
                this.saveLoading = false;
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const configId = row.configId;
      this.reloadId = configId;
      this.reloadType = "remove";
      this.$confirm('是否确认删除参数键名为"' + row.configCode + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delConfig(configId);
        })
        .then(() => {
          this.reloadId = undefined;
          this.reloadType = undefined;
          this.getList();
          this.msgSuccess("删除成功");
        })
        .catch(() => {
          this.reloadId = undefined;
          this.reloadType = undefined;
          // this.$message.error("操作失败!");
        });
    },
  },
  watch: {
    configList() {
      if (this.configList.length < 1) {
        if (this.queryParams.pageNum > 0) {
            this.queryParams.pageNum -= 1
            this.getList();
        }
      }
    }
  }
};
</script>
