<template>
  <el-card
    :class="this.bgColor === true ?'news-card':'unshow-news-card'"
    v-loading="Loading"
    shadow="never"
  >
    <div slot="header" v-if="show">
      <span class="card-title" >
        <span> <i class="icon iconfont"></i>新闻 </span>
      </span>
    </div>
    <div class="hot home-card-body">
      <ul>
        <li v-for="(item, index) in newList" :key="index">
          <a
            :href="'https://www.163.com/dy/article/' + item.docid + '.html'"
            target="_blank"
            >{{ item.title }}</a
          >
        </li>
      </ul>
    </div>
  </el-card>
</template>

<script>
import { getDate } from "@/api/news/index";
export default {
  name:"news",
  props:['item'],
  data() {
    return {
      list: [],
      Loading: false,
      newList: [],
      show:"",
      bgColor:'',
    };
  },
  created() {
   this.show = this.item.showTitle;
   this.bgColor = this.item.showBgColor;
  },
  mounted() {
    this.getList();
  },
  watch:{
    'item.showTitle' :function (val) {
      this.show = val
    },
    'item.showBgColor' :function(val) {
      this.bgColor = val
    }
  },
  methods: {
    getList() {
      getDate().then((res) => {
        this.list = res.data.T1348647853363;
        this.newList = this.list.splice(0,10);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.news-card {
  height: 100%;
  background: #fff;
}
.unshow-news-card {
  height: 100%;
  border: 0px;
  background: #fff;
}
ul li {
  margin-bottom: 10px;
}
ul li::marker {
  color: gray;
}
ul li:hover {
  color: red;
}
</style>
