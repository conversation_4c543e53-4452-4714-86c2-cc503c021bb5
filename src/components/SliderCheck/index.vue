<template>
  <div class="slider-check-box">
    <div
      id="slider-check"
      class="slider-check"
      :class="rangeStatus ? 'success' : ''"
    >
      <i
        id="slider-check-button"
        @mousedown="rangeMove"
        :class="rangeStatus ? successIcon : startIcon"
      ></i>
      {{ rangeStatus ? successText : startText }}
    </div>
  </div>
</template>
<script>
export default {
  props: {
    // 成功之后的函数
    successFun: {
      type: Function,
    },
    //成功图标
    successIcon: {
      type: String,
      default: "el-icon-success",
    },
    //成功文字
    successText: {
      type: String,
      default: "验证成功",
    },
    //开始的图标
    startIcon: {
      type: String,
      default: "el-icon-d-arrow-right",
    },
    //开始的文字
    startText: {
      type: String,
      default: "请拖住滑块，拖动到最右边",
    },
    //失败之后的函数
    errorFun: {
      type: Function,
    },
    //或者用值来进行监听
    status: {
      type: String,
    },
  },
  data() {
    return {
      disX: 0,
      rangeStatus: false,
    };
  },
  methods: {
    //滑块移动
    rangeMove(e) {
      let ele = e.target;
      let startX = e.clientX;
      let eleWidth = ele.offsetWidth;
      let parentWidth = ele.parentElement.offsetWidth;
      let MaxX = parentWidth - eleWidth;
      if (this.rangeStatus) {
        //不运行
        return false;
      }
      document.onmousemove = (e) => {
        let endX = e.clientX;
        this.disX = endX - startX;
        if (this.disX <= 0) {
          this.disX = 0;
        }
        // MaxX - eleWidth
        if (this.disX >= MaxX) {
          //减去滑块的宽度,体验效果更好
          this.disX = MaxX;
        }
        ele.style.transition = ".1s all";
        ele.style.transform = "translateX(" + this.disX + "px)";
        document.getElementById(
          "slider-check"
        ).style.backgroundColor = `#e9e9e9${this.disX * 0.8}`;
        document.getElementById(
          "slider-check"
        ).style.border = `1px solid #198eeb${this.disX * 0.8}`;
        e.preventDefault();
      };
      document.onmouseup = () => {
        if (this.disX !== MaxX) {
          ele.style.transition = ".5s all";
          ele.style.transform = "translateX(0)";
          document.getElementById(
            "slider-check"
          ).style.backgroundColor = `#e9e9e9`;
          document.getElementById(
            "slider-check"
          ).style.border = `0px solid #DCDFE6`;

          //执行成功的函数
          this.errorFun && this.errorFun();
        } else {
          document.getElementById(
            "slider-check"
          ).style.backgroundColor = `#e9e9e9`;
          document.getElementById(
            "slider-check"
          ).style.border = `1px solid #198eeb`;
          this.rangeStatus = true;
          if (this.status) {
            this.$parent[this.status] = true;
          }
          //执行成功的函数
          this.successFun && this.successFun();
        }
        document.onmousemove = null;
        document.onmouseup = null;
      };
    },
    reset() {
      if (this.rangeStatus) {
        this.disX = 0;
        this.rangeStatus = false;
        const ele = document.getElementById("slider-check-button");
        ele.style.transition = ".5s all";
        ele.style.transform = "translateX(0)";
        document.getElementById(
          "slider-check"
        ).style.backgroundColor = `#e9e9e9`;
        document.getElementById(
          "slider-check"
        ).style.border = `0px solid #DCDFE6`;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
$blue: #1890ff;
@mixin jc-flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
.slider-check-box {
  .slider-check {
    border-radius: 4px;
    background-color: #e9e9e9;
    position: relative;
    transition: 1s all;
    user-select: none;
    color: #585858;
    @include jc-flex;
    height: 40px;
    &.success {
      background-color: #fff;
      border: 1px solid $blue;
      color: $blue;
      i {
        color: $blue;
      }
    }
    i {
      position: absolute;
      left: 0;
      width: 50px;
      height: 100%;
      border-radius: 4px;
      color: $blue;
      background-color: #fff;
      border: 1px solid #d8d8d8;
      cursor: pointer;
      font-size: 24px;
      @include jc-flex;
    }
  }
}
</style>