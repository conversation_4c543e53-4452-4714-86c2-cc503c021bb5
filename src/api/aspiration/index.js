import request from "@/utils/request";

// 查询部门列表
export function getList(data) {
  return request({
    url: "/gh/tu-aspiration/page",
    method: "get",
    params: data
  });
}

// 查询员工心声列表分页
export function getPage(action,data) {
  return request({
    url: "/gh/"+action,
    method: "get",
    params: data
  });
}
// 查询员工心声待办列表分页
export function getPageDb(action,data) {
  return request({
    url: "/gh/"+action,
    method: "get",
    params: data
  });
}
// 新增公告
export function save(data) {
  return request({
    url: "/gh/tu-aspiration/save",
    method: "post",
    data: data
  });
}
// 修改
export function update(data) {
  return request({
    url: "/gh/tu-aspiration/update",
    method: "post",
    data: data
  });
}
// 满意度评价
export function updateAppraise(data) {
  return request({
    url: "/gh/tu-aspiration/updateAppraise",
    method: "post",
    data: data
  });
}
// 修改
export function deleteTable(data) {
  return request({
    url: "/gh/tu-aspiration/deleteById/"+data,
    method: "get"
  });
}
// 回信
export function replay(data) {
  return request({
    url: "/gh/tu-aspiration/replay/"+data.aspirationsId+"/"+data.status,
    method: "post",
    data: {
      aspirationsId:data.aspirationsId,
      content:data.content,
      textContent:data.textContent,
    }
  });
}

// 通过id查询
export function findById(id) {
  return request({
    url: "/gh/tu-aspiration/getById/"+id,
    method: "get"
  });
}

// 通过id查询
export function returnCheck(data) {
  return request({
    url: "/gh/tu-aspiration/returnStatus",
    method: "post",
    data: data
  });
}
// 通过id查询
export function readCount(data) {
  console.info('dddddccccccccccccccccc'+JSON.stringify(data))
  return request({
    url: "/gh/tu-aspiration/readCount",
    method: "post",
    data: data
  });
}
