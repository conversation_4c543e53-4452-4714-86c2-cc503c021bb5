<template>
  <el-card class="line-card" v-loading="caseNotice" style="border: 0">
    <div class="home-card-body">
      <div style="font-size: 18px;font-family: Microsoft YaHei;font-weight: bold;color: #606060;">通知公告：</div>
      <a
       v-if="this.notice[index]"
        :href="`/portal/home/<USER>/${this.notice[index].programaType}/${this.notice[index].noticeId}`"
        target="view_window"
      >
        <div class="notice-title">
          「
          <span style="color: red">{{ this.notice[index].noticeTypeName }}</span>
          」{{ this.notice[index].noticeTitle }}
        </div>
      </a>
      <img
        src="../../../../assets/images/home/<USER>"
        alt
        style="margin:auto 16px auto 43px"
        @click="reduce"
      />
      <img src="../../../../assets/images/home/<USER>" alt @click="add" />
    </div>
  </el-card>
</template>

<script>
import { selectTopPage } from "@/api/system/notice";
export default {
  props: ["item"],
  data() {
    return {
      caseNotice: false,
      notice: [],
      index: 0,
    };
  },
  watch: {},
  created() {
    this.getNotice();
  },
  methods: {
    reduce() {
      if (this.index > 0) {
        this.index--;
      } else {
        this.msgInfo("没有更多数据了！");
      }
    },
    add() {
      if (this.index < this.notice.length - 1) {
        this.index++;
      } else {
        this.msgInfo("没有更多数据了！");
      }
    },
    getNotice() {
      this.caseNotice = true;
      selectTopPage({
        programaType: "system_notice",
      }).then((res) => {
        this.notice = res.data.records;
        this.caseNotice = false;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.line-card {
  width: 100%;
  border-radius: 0px;
  background: #ffffff;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  border: 0px;
  cursor: pointer;
  .home-card-body {
    display: flex;
    height: 79px;
    line-height: 79px;
    padding-left: 20px;
    img {
      width: 9px;
      height: 15px;
      margin: auto 0;
    }
  }
  .notice {
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #333333;
  }
  ::v-deep.el-card__body {
    padding: 0;
  }
}
</style>