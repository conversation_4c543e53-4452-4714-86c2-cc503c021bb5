<template>
  <div class="portal-body">
    <div class="feedback-list">
      <div class="search-wrapper box-bg">
        <el-form :model="formQuery"  ref="queryForm" label-width="0.8rem" label-position="left">
          <el-row >
            <el-col :span="6">
              <el-form-item label="工号" prop="jobNum">
                <el-input v-model="formQuery.jobNum" clearable placeholder="请输入"  />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="姓名" prop="name">
                <el-input v-model="formQuery.name" clearable placeholder="请输入"  />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="手机号" prop="phone">
                <el-input v-model="formQuery.phone" clearable placeholder="请输入"  />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="所属地市" prop="city">
                  <el-select
                    v-model="formQuery.city"
                    filterable
                    placeholder="请选择"
                    style="width: 100%"
                    clearable
                  >
                    <el-option
                      v-for="item in cityList"
                      :key="item"
                      :label="item"
                      :value="item"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item label="所属行业" prop="industry">
                <el-select
                  v-model="formQuery.industry"
                  placeholder="请选择"
                  style="width: 100%"
                  clearable
                  collapse-tags
                >
                  <el-option
                    v-for="item in industryList"
                    :key="item.label"
                    :label="item.label"
                    :value="item.label"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
<!--            <el-col :span="7">
              <el-form-item label="部门" prop="deptId">
                <el-cascader
                  v-model="formQuery.deptId"
                  placeholder="请选择"
                  :options="options"
                  :props="{ checkStrictly: true ,  emitPath: false, value: 'id', label: 'deptName', children: 'children'}"
                  clearable></el-cascader>
              </el-form-item>
            </el-col>-->
            <el-col :span="6">
              <el-form-item label="所属部门" prop="deptName">
                <el-input v-model="formQuery.deptName" clearable placeholder="请输入"  />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="状态" prop="deleted">
                <el-select
                  v-model="formQuery.deleted"
                  placeholder="请选择"
                  style="width: 100%"
                  clearable
                  collapse-tags
                >
                  <el-option
                    v-for="item in userStatusList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <div >
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="conditionQuery">查询</el-button>
              </div>
            </el-col>
          </el-row>

        </el-form>
      </div>
      <div class="table-wrapper box-bg">
        <div class="flex-between" style="border-bottom:2px #f5f5f5 solid;margin-bottom: 24px;padding:0 0 10px 0">
          <div class="table-title">用户列表</div>
          <div>
            <el-button type="primary" size="small" icon="el-icon-plus" @click="handleEdit('新增',{})">新增</el-button>
          </div>
        </div>
        <div class="table-con">
          <UniTable action="/sanquan/user/findPage" ref="uniTable" :columns="tableColumns" :params="formQuery">
            <template v-slot:opt="{row}">
                <div class="action-buttons flex-center">
                  <el-button type="text" class="action-button"
                             @click="handleEdit('编辑',row)"
                  >编辑
                  </el-button>
                  <span class="separator"></span>

                  <el-button type="text" class="action-button"
                             @click="deleteById(row['id'])"

                  >删除
                  </el-button>
                  <span class="separator"></span>
                  <el-button type="text" class="action-button"
                             @click="grantRole(row['id'])"

                  >赋权
                  </el-button>
                </div>
              </template>

              <template v-slot:deleted="{row}">
                <el-tag :type="row.deleted === 'normal' ? 'success' : 'info'">
                  {{ row.deleted === "normal" ? "正常" : "失效" }}
                </el-tag>
              </template>
          </UniTable>
        </div>
    </div>
  </div>
    <UserEdit ref="userEdit"  :reloadTable="conditionQuery"/>
    <UserGrant ref="userGrant"/>
  </div>
</template>
<script>
import UniTable from "@/components/UniTable/UniTable.vue";
import UserEdit from "@/views/sw/platform/users/comps/edit.vue";
import UserGrant from "@/views/sw/platform/users/comps/grant.vue";

import { findCityGroup } from '@/api/sw/customer/customerManager'
import { findIndustry } from '@/api/sw/product/product'
import { deletedRole, deleteUser } from '@/api/platform/role'
import { treeDept } from '@/api/sw/platform/department/index'

export default {
  name: 'index',
  components: {UniTable,UserEdit,UserGrant},
  data() {
    return {
      formQuery: {
        deleted: 'normal',
        pageNum: 1,
        pageSize: 10
      },
      cityList:[],
      industryList:[],
      userStatusList:[{
          label: '正常',
          value: 'normal'
        },{
          label: '失效',
          value: 'deleted'
        }
      ],
      tableColumns: [
        { props: 'jobNum', label: '工号' },
        { props: 'name', label: '姓名' },
        { props: 'city', label: '所属地市' },
        { props: 'county', label: '区县' },
        { props: 'industry', label: '所属行业' },
        { props: 'phone', label: '手机' },
        { props: 'deleted', label: '状态' ,slot: true},
        { props: 'deptName', label: '所属部门' },
        { props: 'opt', label: '操作', slot: true }
      ],
      options: [], // 部门列表
    }
  },
  created() {
    findCityGroup().then((res) => {
      this.cityList = res.data;
    });
    findIndustry().then((response) => {
      if (response['code'] === "1") {
        this.industryList = response.data;
      }
    });
    // 查询部门
    treeDept().then((response) => {
      if(response.success){
        this.options = response.data;
      }else {
        this.$message.error("部门查询失败！")
      }
    });
  },
  // 监听属性类似于data概念
  computed: {},
  //属性改变监听
  watch: {
  },
  methods:{
    grantRole(id){
      this.$refs.userGrant.initOpen(id)
    },
    deleteById(id){
      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteUser({id:id}).then((response)=>{
          this.$message.success('删除成功')
          this.conditionQuery()
        })
      });

    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.uniTable.resetPage()
      this.resetForm("queryForm");
      this.formQuery =  {
        pageNum: 1,
        pageSize: 10
      }
      this.conditionQuery();
    },
    // 条件查询
    conditionQuery() {
      this.$refs.uniTable.loadData(this.formQuery)
    },
    handleEdit(title, row) {
        //新增页面
        this.$refs.userEdit.initOpen(title,row)
    },
  }
}
</script>
<style scoped lang="scss">

</style>

