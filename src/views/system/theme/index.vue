<template>
  <div class="app-container">
    <el-card shadow="never">
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        :inline="true"
      >
        <el-form-item label="配置名称" prop="themeName">
          <el-input
            v-model="queryParams.themeName"
            placeholder="请输入配置名称"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
          >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            >新增</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>
      <el-row>
        <el-alert
          title="如果以下数据都未启用时，系统默认使用配置编码为【default】的主题配置"
          type="info"
          :closable="false"
          show-icon
          style="margin-bottom: 10px"
        />
      </el-row>
      <el-table v-loading="loading" :data="dataList">
        <el-table-column label="配置编码" align="center" prop="themeCode" :show-overflow-tooltip="true" />
        <el-table-column label="配置名称" align="center" prop="themeName" :show-overflow-tooltip="true" />
        <el-table-column label="主题风格" align="center" prop="sideTheme">
          <template slot-scope="scope">
            {{ scope.row.sideTheme === 'theme-dark' ? '深色' : '浅色' }}
          </template>
        </el-table-column>
        <el-table-column label="是否开启TopNav" align="center" prop="topNav">
          <template slot-scope="scope">
            <el-tag effect="plain" :type="scope.row.topNav === '1' ? 'success' : 'warning'">
              {{ scope.row.topNav === '1' ? '开启' : '未开启' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="是否开启Tags-Views" align="center" prop="tagsView" width="150px">
          <template slot-scope="scope">
            <el-tag effect="plain" :type="scope.row.tagsView === '1' ? 'success' : 'warning'">
              {{ scope.row.tagsView === '1' ? '开启' : '未开启' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="是否固定Header" align="center" prop="fixedHeader">
          <template slot-scope="scope">
            <el-tag effect="plain" :type="scope.row.fixedHeader === '1' ? 'success' : 'warning'">
              {{ scope.row.fixedHeader === '1' ? '固定' : '不固定' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="是否显示Logo" align="center" prop="sidebarLogo">
          <template slot-scope="scope">
            <el-tag effect="plain" :type="scope.row.sidebarLogo === '1' ? 'success' : 'warning'">
              {{ scope.row.sidebarLogo === '1' ? '显示' : '不显示' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="是否启用" align="center" prop="enabledStatus" :show-overflow-tooltip ="true">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.enabledStatus"
              active-value="1"
              inactive-value="0"
              @change="handleEnabled(scope.row)"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="260"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              :loading="reloadId === scope.row.id && reloadType === 'edit'"
              :disabled="scope.row.themeCode === 'default' && $store.getters.customParam.userType !== 'admin'"
            >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              :loading="reloadId === scope.row.id && reloadType === 'remove'"
              :disabled="scope.row.themeCode === 'default'"
            >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改参数配置对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="open"
        width="700px"
        append-to-body
        :close-on-press-escape="false"
        @close="cancel"
        v-dialogDrag
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="150px">
          <el-row>
            <el-col :span="20">
              <el-form-item label="主题编码" prop="themeCode">
                <el-input
                  v-model="form.themeCode"
                  placeholder="请输入主题编码"
                  maxlength="50"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="20">
              <el-form-item label="主题名称" prop="themeName">
                <el-input
                  v-model="form.themeName"
                  placeholder="请输入应用主键"
                  maxlength="50"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="15">
              <el-form-item label="主题风格" prop="sideTheme">
                <div class="setting-drawer-block-checbox">
                  <div
                    class="setting-drawer-block-checbox-item"
                    @click="handleTheme('theme-dark')"
                  >
                    <img src="@/assets/images/dark.svg" alt="dark" />
                    <div
                      v-if="form.sideTheme === 'theme-dark'"
                      class="setting-drawer-block-checbox-selectIcon"
                      style="display: block"
                    >
                      <i aria-label="图标: check" class="anticon anticon-check">
                        <svg
                          viewBox="64 64 896 896"
                          data-icon="check"
                          width="1em"
                          height="1em"
                          :fill="theme"
                          aria-hidden="true"
                          focusable="false"
                          class=""
                        >
                          <path
                            d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
                          />
                        </svg>
                      </i>
                    </div>
                    <span class="setting-drawer-block-checbox-text">深色</span>
                  </div>
                  <div
                    class="setting-drawer-block-checbox-item"
                    @click="handleTheme('theme-light')"
                  >
                    <img src="@/assets/images/light.svg" alt="light" />
                    <div
                      v-if="form.sideTheme === 'theme-light'"
                      class="setting-drawer-block-checbox-selectIcon"
                      style="display: block"
                    >
                      <i aria-label="图标: check" class="anticon anticon-check">
                        <svg
                          viewBox="64 64 896 896"
                          data-icon="check"
                          width="1em"
                          height="1em"
                          :fill="theme"
                          aria-hidden="true"
                          focusable="false"
                          class=""
                        >
                          <path
                            d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
                          />
                        </svg>
                      </i>
                    </div>
                    <span class="setting-drawer-block-checbox-text">浅色</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="15">
              <el-form-item label="主题颜色" prop="theme">
                <theme-picker
                  :theme-prop.sync="form.theme"
                  :form-operation="true"
                  style="margin-top: 5px;"
                  @change="themeChange"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="开启 TopNav" prop="topNav">
                <el-switch v-model="form.topNav" active-value="1" inactive-value="0" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开启 Tags-Views" prop="tagsView">
                <el-switch v-model="form.tagsView" active-value="1" inactive-value="0" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="固定 Header" prop="fixedHeader">
                <el-switch v-model="form.fixedHeader" active-value="1" inactive-value="0" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="显示 Logo" prop="sidebarLogo">
                <el-switch v-model="form.sidebarLogo" active-value="1" inactive-value="0" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="20">
              <el-form-item label="主题描述">
                <el-input
                  v-model="form.themeDescribe"
                  placeholder="请输入主题描述"
                  type="textarea"
                  maxlength="255"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="saveLoading"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import {add, del, find, page, update} from "@/api/system/theme";
import ThemePicker from "@/components/ThemePicker";

export default {
  name: "Theme",
  components: { ThemePicker },
  data() {
    const validateThemeCode = (rule, value, callback) => {
      if (value === '') {
        callback(new Error("应用主键不能为空"));
      } else if (this.form.id === '') {
        //新增時校验钉钉应用id唯一性
        find({ themeCode: this.form.themeCode }).then((response) => {
          if (response.success && response.data) {
            callback("配置编码已存在，请重新输入");
          } else {
            callback()
          }
        });
      }else {
        callback()
      }
    }
    const validateSideTheme = (rule, value, callback) => {
      if (this.form.sideTheme === '') {
        callback(new Error("请选择一种主题风格"));
      } else {
        callback()
      }
    }
    const validateTheme = (rule, value, callback) => {
      if (this.form.theme === '') {
        callback(new Error("请选择一种主题颜色"));
      } else {
        callback()
      }
    }
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 参数表格数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        themeName: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        themeCode: [
          { required: true, validator: validateThemeCode, trigger: "blur" },
        ],
        themeName: [
          { required: true, message: "主题名称不能为空", trigger: "blur" },
        ],
        sideTheme: [
          { required: true, validator: validateSideTheme, trigger: "blur" },
        ],
        theme: [
          { required: true, validator: validateTheme, trigger: "blur" },
        ],
        topNav: [
          { required: true, message: "请选择是否开启TopNav", trigger: "blur" },
        ],
        tagsView: [
          { required: true, message: "请选择是否开启Tags-Views", trigger: "blur" },
        ],
        fixedHeader: [
          { required: true, message: "请选择是否开启固定Header", trigger: "blur" },
        ],
        sidebarLogo: [
          { required: true, message: "请选择是否显示Logo", trigger: "blur" },
        ]
      },
      reloadId: undefined,
      reloadType: undefined,
      saveLoading: false,
      theme: this.$store.state.settings.theme,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true;
      page(this.queryParams).then(
        (response) => {
          this.dataList = response.data.records;
          this.total = response.data.total;
          this.loading = false;
        }
      );
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reloadId = undefined;
      this.reloadType = undefined;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: '',
        themeCode: '',
        themeName: '',
        theme: '',
        sideTheme: 'theme-dark',
        topNav: '0',
        tagsView: '1',
        fixedHeader: '0',
        sidebarLogo: '1',
        themeDescribe: '',
      };
      this.resetForm("queryForm");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加主题";
      this.reloadType = "add";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      this.reloadId = id;
      this.reloadType = "edit";
      find({ id: id }).then((response) => {
        if (response.data) {
          this.form = response.data;
          this.open = true;
          this.title = "修改主题";
        } else {
          this.$message.error("数据异常！");
        }
      });
    },
    // 启用
    handleEnabled(row){
      const tipText = row.enabledStatus === '1' ? '启用' : '停用'
      update(row).then((response) => {
        if (response.data) {
          this.msgSuccess(`${tipText}成功，请刷新浏览器查看效果！`);
          this.getList();
        } else {
          this.msgError(response.message);
        }
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.saveLoading = true;
          if (this.form.id !== '') {
            update(this.form).then((response) => {
              if (!response.success) {
                this.$message.error(response.message);
                this.saveLoading = false;
              } else {
                this.msgSuccess("修改成功");
                this.open = false;
                this.saveLoading = false;
                this.getList();
              }
            });
          } else {
            add(this.form).then((response) => {
              if (!response.success) {
                this.$message.error(response.message);
                this.saveLoading = false;
              } else {
                this.msgSuccess("新增成功");
                this.open = false;
                this.saveLoading = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.reloadId = id;
      this.reloadType = "remove";
      const _this = this
      const h = this.$createElement;
      this.$confirm(
        '是否确认删除主题名称为"' + row.themeName + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          del(id).then((response) => {
            if (response.success) {
              _this.msgSuccess('删除成功');
              _this.getList();
            } else {
              _this.$notify.error({
                title: '禁止删除提醒',
                duration: 6000,
                message: h('i', { style: 'color: #F56C6C'},
                  response.message)
              });
              _this.reloadType = undefined
            }
          });
        })
        .catch(() => {
          this.reloadId = undefined;
        });
    },
    themeChange(val) {
      this.form.theme = val;
    },
    handleTheme(val) {
      this.form.sideTheme = val;
    },
  },
};
</script>
<style lang="scss" scoped>
.setting-drawer-block-checbox {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 10px;
  margin-bottom: 20px;

  .setting-drawer-block-checbox-item {
    position: relative;
    margin-right: 16px;
    border-radius: 2px;
    cursor: pointer;

    img {
      width: 48px;
      height: 48px;
    }

    .setting-drawer-block-checbox-selectIcon {
      position: absolute;
      top: 0;
      right: 0;
      width: 100%;
      height: 100%;
      padding-top: 15px;
      padding-left: 24px;
      color: #1890ff;
      font-weight: 700;
      font-size: 14px;
    }
    .setting-drawer-block-checbox-text {
      position: absolute;
      top: 0;
      right: 0;
      width: 100%;
      height: 100%;
      padding-top: 40px;
      padding-left: 10px;
    }
  }
}
</style>
