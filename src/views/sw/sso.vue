<template>
  <div class="x-app-container hide-bar" v-loading="showMenuBar">

  </div>
</template>

<style lang="less">
@import "../../assets/sanquan/index";
</style>
<script>
import Navbar from '../../components/Navbar/index.vue'
import { TagsView } from '@/layout/components'
import { getCurrentUser, checkLoginSes, getTokenBySession } from '@/api/sw/analysis/analysis'
import { getToken } from '@/utils/auth.js'

export default {
  name: 'index',
  data() {
    return {
      showMenuBar: true
    }
  },
  components: {
    Navbar, TagsView
  },
  methods: {
    /* 动态设置html字体大小 */
    setHtmlFontSize() {
      this.default_width = window.innerWidth
      document.querySelector('html').style.fontSize = this.default_width / 16 + 'px'
    },
    getUserInfo() {

    }
  },
  computed: {
    cachedViews() {
      console.log('cachedviews')
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.path
    }
  },
  created() {
  },
  mounted() {

    // this.default_width = window.innerWidth
    // this.setHtmlFontSize()
    // setTimeout(() => {
    //   //this.getUserInfo()
    //   loading.close()
    // }, 2000)
    // // 监听窗口变化
    // window.addEventListener('resize', this.setHtmlFontSize)
    // let authSessionId = this.$route.query.authSessionId
    // if (authSessionId) {
    //   const baseURL = process.env.VUE_APP_BASE_API
    //   window.location.href = window.location.href.split('/#/')[0] + baseURL + '/auth/sso/cloud/token?authSessionId=' + authSessionId
    // }
  }

}
</script>
