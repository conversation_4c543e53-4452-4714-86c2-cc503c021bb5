// banner
#bannerSide {
  width: 100%;
  background: #fff;
  .banner1 {
    height: 50px;
    width: 100%;
    margin: 0 auto;
    background: #F7F8FA;

    .banner1-kb {
      background: #F7F8FA;
      // width:1353px ;
      height: 100%;
      display: flex;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      margin: auto;
    .banner1-kb-menu {
      width: 900px;
      height: 50px;
      background: #F7F8FA;
      overflow: hidden;
      float: left;
      .el-menu {
        background: #F7F8FA;
        display: flex;
        list-style: none;
        height: 100%;
        line-height: 50px;
        margin: 0px;
        border: 0px;
        //一定要添加！important提高优先级
        .is-active {
          //设置背景颜色
          background-color: #D01122;
          //设置字体颜色
          //color: #ffffff !important;

          .system-menu-title {
            color: #ffffff;
          }
        }

        .el-menu-item:hover {
          //设置背景颜色
          background: rgba(208, 16, 23, 0.5);
          //设置字体颜色
          //color: #ffffff !important;

          .system-menu-title {
            color: #0a0a0a;
          }
        }

        //.sidebar_item:hover {
        //  background-color: #fff;
        //}
      }
    }
      .banner1-serch{
        width: 400px;
        height: 50px;
        background: #F7F8FA;
        overflow: hidden;
        float: right;
        display: flex;
        justify-content: flex-end;
        //margin-right: 200px;
        .el-input {
          height: 100%;
          width: 180px;
          line-height: 49px;
          float: right;
          input {
            width: 180px;
            height: 34px;
            line-height: 30px;
            border-radius: 17px;
            border-color: #b6b0b0;
          }
        }
      }
    }
  }


  .menu-item {
    height: 50px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-item-align: center;
    align-self: center;
  }
  @media screen and (max-width:1366px){
    .banner,.banner1-kb {
      width: 100%;
    }
  }
  @media screen and (min-width:1400px){
    .banner,.banner1-kb {
      width: 1353px;
    }
  }
  .banner {
    height: 94px;
    // width:1353px ;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    background: white;
    //.header-img {
    //  float: left;
    //  margin-right: 10px;
    //  border: black solid 0px;
    //  width: 35px;
    //  height: 35px;
    //  object-fit: cover;/*图片完全填充*/
    //  object-position: center; /*设置头像选取照片的哪个区域object-position: x y*/
    //  border-radius: 50%;/*头像框圆形设置*/
    //}
    .banner-title {
      width: 492px;
      height: 100%;
      float: left;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      .title {
        width: 100%;
        height: 94px;
        display: flex;
        align-items: center;
        justify-content: flex-start;

        .sub-title {
          font-size: 18px;
          font-weight: 400;
          color: #1876f0;
        }

        ::v-deep .el-divider--vertical {
          height: 1.3em;
        }

        .logos {
          //background-image: url("~@/assets/ghLogo/logo.png") ;
          background-image: url("~@/assets/ghLogo/LOGO.jpg") ;
          background-repeat: no-repeat;
          background-position: center;
          background-size: 80% 80%;
          height: 70px;
          width: 200px;
          //height: 45px;
          //margin-right: 10px;
          margin: 10px 10px 10px -10px;

          //img {
          //  width: 100%;
          //  height: 100%;
          //}
        }

        .title-font {
          color: #343131;
          font-size: 18px;
          //font-family: Microsoft YaHei;
          font-weight: bold;
        }
      }

      .back-icon {
        color: #fff;
        margin-left: 20px;
        height: 100%;
        line-height: 50px;
        font-size: 24px;
      }
    }

    .potal-right-menu {
      width: 400px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 100%;
      float: right;
      margin-right: 53px;
      .header-img {
        float: left;
        margin-right: 10px;
        border: black solid 0px;
        width: 35px;
        height: 35px;
        object-fit: cover;/*图片完全填充*/
        object-position: center; /*设置头像选取照片的哪个区域object-position: x y*/
        border-radius: 50%;/*头像框圆形设置*/
      }

      .item {
        padding: 0px 10px;
        font-size: 18px;
        color: #858282;
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          color: #1890ff;
        }

        >span {
          display: flex;
          align-items: center;
        }

        .el-badge__content.is-dot {
          padding: 0;
          left: 0px;
          border-radius: 50%;
        }

        .icon {
          font-size: 24px;
          margin-right: 5px;
        }
      }

      .avatar-container {
        width: 30%;

        .username {
          font-size: 14px;
          color: #858282;
          padding: 0px 20px;

          .deptName {
            width: 120px;
            text-align: left;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding-left: 0px;
          }

          &:hover,
          &:focus {
            color: #1890ff;
          }

          color: #858282;
          cursor: pointer;
          transition: background 0.3s;
        }
      }
    }


  }


}


//首页
// banner图内容
.banner-content {
  width: 100%;
  height: 485px;
  margin: 0 auto;
  // background-color: #328dc7;
  position: relative;

  .bananer-background {
    width: 100%;
    height: 100%;
  }

  .search {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;

    .search-title {
      font-size: 48px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #FFFFFF;
      letter-spacing: 10px;
      margin-top: 0px;
    }

    .search-input {
      display: flex;
      margin-top: 40px;

      ::v-deep .el-input--medium .el-input__inner {
        height: 50px;
        text-align: center;
      }

      .input-with-select {
        width: 520px;
        display: flex;
      }

      .myButton {
        width: 130px;
        height: 50px;
        background: linear-gradient(90deg, #6ac5e2, #54b3a5);
        border-radius: 5px;
        color: #fff;
        border: 0px;
        font-size: 18px;
        letter-spacing: 5px;
        margin-left: 10px;
      }

      .el-select {
        width: 120px;
      }

      .input-with-select .el-input__icon:hover {
        color: black;
        cursor: pointer;
      }
    }
  }
}

//租户新增
::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.home-container {
  width: 1443px;
  overflow-y: hidden;
  overflow-x: hidden;
  z-index: 20;
  margin: -30px auto 30px auto;

  .vue-grid-layout {
    background-color: #f5f9fa;
    background: #FFFFFF;
  }

  .grid-item {
    background-color: #f5f9fa;
    background-color: #fff;
    height: 100%;
  }

  // 页面配置
  .public-components {
    background-color: #f5f9fa;
    padding-bottom: 10px;
    margin-bottom: 10px;
    margin-top: 20px;

    .drawer-custom {
      padding: 10px;
    }
  }

  .drawer-footer {
    position: absolute;
    padding: 10px 16px;
    border-top: 1px solid #f0f0f0;
    text-align: center;
    width: 100%;
    bottom: 0px;
    background: white;
  }

  //组件市场
  .grid-item-select {
    padding: 25px 35px 25px 25px;
    background-color: #f5f9fa;
  }

  .title {
    font-size: 30px;
    padding: 25px 35px 0px 25px;
    margin-top: 0px;
    margin-bottom: 0px;
  }

  .select-component {
    justify-content: space-between;
    height: 100%;

    .checkbox-component {
      padding-right: 10px;
      padding-bottom: 30px;
    }

    .component {
      width: 100%;
    }
  }

  .grid-item-delete {
    position: absolute;
    top: 0;
    right: 0;
    box-shadow: 0px 0px 6px 4px rgb(239 133 133 / 8%);
    border-radius: 20px;
    cursor: pointer;

    :hover {
      color: red;
    }
  }
}

.icons-warp {
  padding: 5px;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;

  .float-icon-item {
    padding: 0px;
  }
}

.errLog-container {
  display: inline-block;
  vertical-align: top;
}



//搜索列表页
.app-container {
  padding: 0;

  .banner-top {
    width: 100%;
    height: 141px;
    position: relative;

    .search-input {
      display: flex;
      align-items: center;

      .myInput {
        width: 520px;
        height: 50px;
      }

      ::v-deep .el-input--medium .el-input__inner {
        height: 50px;
      }

      .myButton {
        width: 124px;
        height: 50px;
        border-radius: 5px;
        background: #3d8be0;
        color: #fff;
        border: 0;
        text-align: center;
        font-size: 18px;
      }

      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }

    img {
      width: 100%;
      height: 100%;
    }
  }

  .container {
    margin: 0 auto;

    .container-title {
      height: 68px;
      border-bottom: 2px solid #e7e7e7;

      .title {
        max-width: 1443px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 100%;
        margin: 0 auto;

        .breadcrumb {
          display: flex;
          justify-content: flex-start;
          align-items: center;
        }
      }

      .card-title {
        width: 60%;

        .c-r-t {
          width: 100%;

          .filter {
            display: flex;
            justify-content: flex-end;
            align-items: center;

          }

          .card-title-left {
            display: flex;
            flex-direction: row;

            .icon {
              font-size: 24px;
              color: #419eee;
              padding: 0px 5px;
              -webkit-transition: font-size 0.25s linear, width 0.25s linear;
              -moz-transition: font-size 0.25s linear, width 0.25s linear;
              transition: font-size 0.25s linear, width 0.25s linear;
            }
          }
        }
      }
    }

  }
}



//站内信
.app-container {
  .banner-img {
    width: 100%;
    height: 380px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .crumb {
    height: 68px;
    width: 100%;
    border: 2px solid #e7e7e7;
    display: flex;

    .break {
      width: 1443px;
      margin: 0 auto;
      height: 68px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      ::v-deep .el-form--inline .el-form-item {
        margin: 0 auto;
      }

      .breadcrumb {
        height: 68px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-size: 16px;
      }
    }


  }

  .note-card {
    max-width: 1443px;
    margin: 0 auto;
    min-height: calc(100vh - 150px);
    border: 0px;

    .card-title {
      .filter {
        display: flex;
        flex-direction: row;
        align-items: center;

        >span {
          margin-right: 10px;
        }
      }

      .c-r-t {
        display: flex;
        flex-direction: row;
        align-items: center;
      }

      .card-title-left {
        display: flex;
        flex-direction: row;
        align-items: center;

        .icon {
          font-size: 24px;
          color: #419eee;
          padding: 0px 5px;
          -webkit-transition: font-size 0.25s linear, width 0.25s linear;
          -moz-transition: font-size 0.25s linear, width 0.25s linear;
          transition: font-size 0.25s linear, width 0.25s linear;
        }
      }
    }

    .impMail {
      width: 80%;
      background: gold;
      height: 200px;
      margin: 0 auto;
      display: none;
    }
  }

  .unRead {
    color: #006eff;
  }

  .read {
    color: gray;
  }
}


//新闻动态通知公告列表
.app-container {
  .banner-img {
    width: 100%;
    height: 380px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .crumb {
    width: 100%;
    display: flex;
    border: 2px solid #e7e7e7;
    height: 68px;

    .bread {
      width: 1443px;
      margin: 0 auto;
      height: 68px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      ::v-deep .el-form--inline .el-form-item {
        margin: 0 auto;
      }

      .breadCrumb {
        width: 30%;
        height: 68px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-size: 16px;
      }

      .search-title {
        ::v-deep .el-input__inner {
          border-radius: 20px;
          position: relative;
        }

        .myButton {
          position: absolute;
          right: 0;
          border-radius: 20px;
        }
      }
    }
  }
}





//登录页
.login {
  background-image: url("~@/assets/images/ghLTImg/bg.png");
  background-repeat: no-repeat;
  background-position: center;
  width: 100%;
  height: 100%;
  background-size: 100% 100%;
  display: flex;
  align-items: center;

  .login-container {
    //position: absolute;
    left: 0px;
    right: 0px;
    top: 0px;
    bottom: 0px;
    margin: auto;
    //width: 935px;
    //height: 617px;
    //min-height: visibility: hidden; ;
    //height: 10vh;
    //width: 10vw;
    //height: 70%;
    //min-height: 300px;
    display: flex;
    justify-content: center;


    .login-left {
      background-image: url("~@/assets/images/ghLTImg/login-left.png");
      -moz-background-size:100% 100%; background-size:100% 100%;
      border-radius: 2px 2px 0px 0px;
      border: 1px solid #eaeaea;
      //width: 50%;
      //height: 100%;
      width: 473px;
      height: 617px;

      .leftTop_img {
        margin: 30% 0px 0px 20%;
        width: 60%;
        height: auto;
        object-fit: contain;
      }
      .leftBottom_img {
        margin: 10% 0px 10% 20%;
        width: 60%;
        height: auto;
        object-fit: contain;
      }
    }

    .login-right {
      background-image: url("~@/assets/images/ghLTImg/login-right.png");
      background-repeat: no-repeat;
      background-position: center;
      //width: 50%;
      //height: 100%;
      width: 462px;
      height: 617px;
      //background: #fff;
      position: relative;
      //display: none;

      .functions {
        margin: 8px 12px;
        display: flex;
        flex-direction:row-reverse;
        justify-content: space-between;
        //color: red;
        .registerButton {
          font-size: 12px;
          margin-bottom: 20px;
          color: #ff1820;
          padding-left: 10px;
        }
      }

      .circle-left {
        width: 40%;
        height: 50px;
        position: absolute;
        top: 50px;
        left: 30%;

        img {
          width: 100%;
        }
      }

      #untenantLoginName {
        display: none;
      }

      .tenantLoginName {
        display: block;
      }

      .login-form {
        position: absolute;
        top: 50%;
        left: 55%;
        transform: translate(-50%, -50%);
        z-index: 999;
        border-radius: 6px;
        width: 100%;
        // height: 550px;
        border: 0px;
        padding: 10px auto;
        ::v-deep .el-input__prefix{
          top: 10px
        }
        ::v-deep .el-input input{
          width: 90%;
          background-color: transparent;
          border-radius: 0px;
          border-top-width: 0px;
          border-left-width: 0px;
          border-right-width: 0px;
          border-bottom-width: 1px;
          //.el-input__prefix{
          //  top: 5px
          //}
          /*outline: medium;*/
        }
        .op {
          display: flex;
          align-items: center;

          .captcha {
            display: block;
          }
        }

        .error-msg {
          color: #ff4949;
          font-size: 12px;
        }

        .login-code {
          display: flex;
          justify-content: center;
        }

        .title {
          margin: 0px auto 20px auto;
          text-align: center;
          font-size: 50px;
          font-family: Microsoft YaHei;
          font-weight: bold;
          color: #ffffff;
          white-space: nowrap;
          background: linear-gradient(0deg, #328dc7 0%, #68ca90 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          letter-spacing: 5px;
        }

        .login-code {
          width: 43%;
          height: 38px;
          float: right;

          img {
            cursor: pointer;
            vertical-align: middle;
            width: 100%;
          }
        }

        ::v-deep .el-tabs--card > .el-tabs__header {
          border: 0px;
        }

        ::v-deep .el-tabs__nav-scroll {
          width: 100%;
          height: 50px;
          margin: 0 auto;
          border: 0px;
          border-radius: 30px;
        }

        ::v-deep .el-tabs__item.is-active {
          background: #68ca90;
        }

        ::v-deep .el-tabs__nav {
          width: 100%;
          border: 0px;
          background: #eeeeee;

          .el-tabs__item {
            width: 50%;
            height: 50px;
            border-radius: 30px;
            text-align: center;
            line-height: 50px;
            border: 0px;
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: black;
          }

          .el-tabs__item.is-active {
            color: #fff;
          }

          .el-tabs__item:checked {
            width: 50%;
            height: 50px;
            border-radius: 20px;
            background: #68ca90;
            text-align: center;
            line-height: 50px;
            color: white !important;
          }
        }

        ::v-deep .el-input {
          height: 38px;

          input {
            height: 38px;
            border: 0px;
            border-bottom: 1px solid #c0c4cc;
          }
        }

        .input-icon {
          height: 39px;
          width: 14px;
          margin-left: 2px;
        }

        .switch-org {
          .switch-body {
            height: 170px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
          }
        }

        #changeTenant {
          font-size: 12px;
          margin-bottom: 20px;
          color: #1890ff;
          padding-left: 10px;
        }

        .forgetPassword {
          font-size: 12px;
          margin-bottom: 20px;
          color: #1890ff;
          padding-left: 10px;
        }

        .unforgetPassword {
          display: none;
        }

        .tenantAppliation {
          font-size: 12px;
          margin-bottom: 20px;
          color: #1890ff;
          padding-right: 10px;
        }

        .untenantAppliation {
          display: none;
        }

        .registerSwitchAppliation {
          font-size: 12px;
          margin-bottom: 20px;
          color: #1890ff;
          padding-right: 10px;
        }

        .login-button {
          width: 70%;
          height: 50px;
          background: linear-gradient(90deg, #E66556, #E5402D);
          border-radius: 36px;
          border: 0px;
          display: block;
          margin: 10% 10% 0% 10%;
          //font-size: 20px;
          //font-family: Microsoft YaHei;
          //font-weight: bold;
        }
      }

      .login-bottom-img {
        width: 400px;
        height: 180px;
        position: absolute;
        bottom: 0px;
        right: 0px;
        z-index: 1;

        .el-image {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}

// 租户申请以及忘记密码弹窗（！）
.changeAll {
  width: 90%;
  height: 520px;
  margin-left: 20px;
}

.applyAll {
  width: 90%;
  height: 520px;
  margin: 0 auto;
  overflow: hidden;
}

.boxCard {
  height: 300px;
  margin: 25px auto;
}

.tenantInfo {
  margin-top: 50px;
}

.text-item {
  width: 100%;
}

.text-Item {
  text-align: center;
  font-size: 18px;
}

.form {
  width: 100%;
}

.topChange {
  width: 100%;
  height: 60px;
  display: flex;
}

.changeButtom {
  width: 200px;
  height: 100%;
  line-height: 60px;
  background: #0f9af2;
  position: relative;
  text-align: center;
  color: white;
}

.unChangeButtom {
  width: 200px;
  height: 100%;
  line-height: 60px;
  background: #bebebe;
  position: relative;
  text-align: center;
  color: white;
}

.changeButtoms {
  width: 250px;
  height: 100%;
  line-height: 60px;
  background: #0f9af2;
  position: relative;
  text-align: center;
  color: white;
  padding-left: 60px;
  box-sizing: content-box;
}

.chooseChangeButtoms {
  width: 250px;
  height: 100%;
  line-height: 60px;
  background: #bebebe;
  position: relative;
  text-align: center;
  color: white;
  padding-left: 60px;
  box-sizing: content-box;
}

.tentantBUttoms {
  width: 100%;
  margin-top: 50px;
  margin-left: 400px;
}

.changeButtomFinish {
  width: 200px;
  height: 100%;
  line-height: 60px;
  background: #0f9af2;
  position: relative;
  text-align: center;
  color: white;
  padding-left: 60px;
  box-sizing: content-box;
}

.chooseChangeButtomFinish {
  width: 200px;
  height: 100%;
  line-height: 60px;
  background: #bebebe;
  position: relative;
  text-align: center;
  color: white;
  padding-left: 60px;
  box-sizing: content-box;
}

.finishButtoms {
  width: 100%;
  margin-left: 400px;
  margin-top: 50px;
}

.rights {
  width: 0;
  height: 0;
  border-top: 30px solid transparent;
  border-left: 60px solid #bebebe;
  border-bottom: 30px solid transparent;
  border-top-right-radius: 12px;
}

.chooseRights {
  width: 0;
  height: 0;
  border-top: 30px solid transparent;
  border-left: 60px solid #0f9af2;
  border-bottom: 30px solid transparent;
  border-top-right-radius: 12px;
}

.unRight {
  width: 0;
  height: 0;
  border-top: 30px solid transparent;
  border-left: 60px solid #bebebe;
  border-bottom: 30px solid transparent;
  border-top-right-radius: 12px;
}

.left {
  width: 0;
  height: 0;
  border-top: 30px solid transparent;
  border-left: 60px solid white;
  border-bottom: 30px solid transparent;
  position: absolute;
  left: 0;
  top: 0;
}

.right {
  width: 0;
  height: 0;
  border-top: 30px solid transparent;
  border-left: 60px solid #0f9af2;
  border-bottom: 30px solid transparent;
  border-top-right-radius: 12px;
}

.bottomContent {
  height: calc(100% - 60px);
}

.chose {
  width: 100%;
  height: 100%;
  // margin-top: 30px;
  overflow: hidden;
}

.box-card {
  width: 80%;
  margin: 100px auto;
  height: 200px;
}

.fillInformation {
  width: 100%;
  height: 200px;
  margin: 100px auto;
}

.buttoms {
  width: 100%;
  margin-top: 30px;
  display: flex;
  justify-content: center;
}

.steps-content {
  margin: 20px;
  border: 1px dashed #e9e9e9;
  border-radius: 6px;
  background-color: #fafafa;
  min-height: 200px;
  overflow: hidden;
}

.steps-action {
  margin-top: 24px;
  width: 100%;
  text-align: center;
}

.tenantInfo .el-input.is-disabled .el-input__inner {
  color: black;
}
.system-menu-title {
  list-style: none;
  cursor: pointer;
  position: relative;
  -webkit-transition: border-color 0.3s, background-color 0.3s, color 0.3s;
  transition: border-color 0.3s, background-color 0.3s, color 0.3s;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  white-space: nowrap;
  text-align: center;
  font-size: 16px;
  font-family: Microsoft YaHei;
  /* font-weight: 400; */
  /* color: #666666; */
}

//.system-menu-title :active {
//  list-style: none;
//  cursor: pointer;
//  position: relative;
//  -webkit-transition: border-color 0.3s, background-color 0.3s, color 0.3s;
//  transition: border-color 0.3s, background-color 0.3s, color 0.3s;
//  -webkit-box-sizing: border-box;
//  box-sizing: border-box;
//  white-space: nowrap;
//  text-align: center;
//  font-size: 16px;
//  font-family: Microsoft YaHei;
//  color: white;
//  /* font-weight: 400; */
//  /* color: #666666; */
//}

