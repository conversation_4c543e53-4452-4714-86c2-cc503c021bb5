<template>
  <el-card
    :class="
      this.bgColor === true
        ? 'notice-buisness-card'
        : 'unshow-notice-buisness-card'
    "
    shadow="never"
  >
    <div slot="header" v-if="show">
      <span class="card-title">
        <div class="title">
          <div><i class="icon iconfont icon-yewudongtai"></i>统计数据</div>
        </div>
      </span>
    </div>
    <div class="home-card-body">
      <div
        id="browser"
        style="width: 500px; height: 400px; display: inline-block"
      ></div>
      <div
        id="os"
        style="width: 500px; height: 400px; display: inline-block"
      ></div>
    </div>
  </el-card>
</template>

<script>
import { getBrowserTotal, getOsTotal } from "@/api/system/log";
import { walden } from "@/utils/walden";
export default {
  name: "browser",
  props: ["item"],
  data() {
    return {
      tenantId: this.$store.getters.customParam.tenantId,
      browserList: [],
      osList: [],
      show: "",
      bgColor: "",
    };
  },

  mounted() {},
  created() {
    this.getBrowser();
    this.getOs();
    this.show = this.item.showTitle;
    this.bgColor = this.item.showBgColor;
  },
  watch: {
    "item.showTitle": function (val) {
      this.show = val;
    },
    "item.showBgColor": function (val) {
      this.bgColor = val;
    },
  },
  methods: {
    getBrowser() {
      const data = {
        tenantId: this.tenantId,
        browser: "browser",
      };
      getBrowserTotal(data).then((res) => {
        this.browserList = res.data;
        this.$nextTick(() => {
          this.getBrowserEcharts();
        });
      });
    },
    getOs() {
      const data = {
        tenantId: this.tenantId,
        os: "os",
      };
      getOsTotal(data).then((res) => {
        this.osList = res.data;
        this.$nextTick(() => {
          this.getOsEcharts();
        });
      });
    },
    getBrowserEcharts() {
      let browserEchart = this.$echarts.init(
        document.getElementById("browser"),
        "walden"
      );
      //配置图表
      let option = {
        title: {
          text: "用户浏览器占比统计",
          left: "center",
        },
        tooltip: {
          trigger: "item",
        },
        legend: {
          orient: "vertical",
          bottom: "bottom",
        },
        series: [
          {
            name: "浏览器",
            type: "pie",
            radius: "50%",
            data: this.browserList,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
      browserEchart.setOption(option);
    },
    getOsEcharts() {
      let OsEchart = this.$echarts.init(
        document.getElementById("os"),
        "walden"
      );
      //配置图表
      let option = {
        title: {
          text: "用户操作系统占比统计",
          left: "center",
        },
        tooltip: {
          trigger: "item",
        },
        legend: {
          orient: "vertical",
          bottom: "bottom",
        },
        series: [
          {
            name: "操作系统",
            type: "pie",
            radius: "50%",
            data: this.osList,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
      OsEchart.setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
.notice-buisness-card {
  height: 100%;
  background: #fff;
  .home-card-body {
    display: flex;
    justify-content:space-around;
  }
}
.unshow-notice-buisness-card {
  height: 100%;
  background: #f5f9fa;
  border: 0px;
}
</style>
