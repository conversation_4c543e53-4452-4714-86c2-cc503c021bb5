<template>
  <div class="flex-between nav-wrapper">
    <div class="flex-start">
      <div class="flex-start logo-container">
        <div class="logo flex-center">
          <img src="../../assets/images/sw/logo-new.png" alt=""/>
        </div>
        <!-- <h1>三全画像</h1> -->
      </div>
      <!-- 菜单根据角色权限显示 -->
      <div v-if="menuDisplay">
        <el-menu
          :default-active="activeIndex"
          class="el-menu-demo"
          mode="horizontal"
          @select="handleSelect"
        >
<!--          <el-menu-item
            index="/sw/indexFirst"
            key="/sw/indexFirst"
          >
            <template slot="title">
              <i class="el-icon-s-platform"></i>首页
            </template>
          </el-menu-item
          >-->
          <template v-for="(item, index) in menus.slice(0, visibleMenuCount)">
            <el-submenu
              v-if="item.children.length > 0 && hasMenuPermissionShow(item)"
              :index="item.path"
              :key="index"
            >
              <template slot="title">
                <i :class="item.icon"></i>{{ item.title }}
              </template>
              <el-menu-item
                v-for="citem in item.children"
                :index="citem.path"
                :key="citem.path+''+index"
                v-if="hasMenuPermissionShow(citem)"
              >
                {{ citem.title }}
              </el-menu-item>
            </el-submenu>
            <el-menu-item
              v-else-if="item.children.length === 0 && hasMenuPermissionShow(item)"
              :index="item.path"
              :key="index-'item'"
            >{{ item.title }}
            </el-menu-item
            >
          </template>
          <!-- 顶部菜单超出数量折叠 -->
          <template v-if="menus.length > visibleMenuCount + 1">
            <el-submenu index="more">
              <template slot="title">更多菜单</template>
              <template v-for="(item, index) in menus.slice(visibleMenuCount)">
                <el-submenu
                  v-if="item.children && item.children.length > 0"
                  :index="item.path"
                  :key="item.path"
                >
                  <template slot="title">
                    <div style="font-size: 14px;">{{ item.title }}</div>
                  </template>
                  <el-menu-item
                    v-for="citem in item.children"
                    :index="citem.path"
                    :key="citem.path"
                  >
                    {{ citem.title }}
                  </el-menu-item>
                </el-submenu>
                <el-menu-item v-else :index="item.path" :key="index">
                  {{ item.title }}
                </el-menu-item>
              </template>
            </el-submenu>
          </template>
          <template v-else>
            <template v-for="(item, index) in menus.slice(visibleMenuCount, visibleMenuCount+1)">
              <el-submenu
                v-if="item.children.length > 0"
                :index="item.path"
                :key="item.path"
              >
                <template slot="title">
                  <i :class="item.icon"></i>{{ item.title }}
                </template>
                <el-menu-item
                  v-for="citem in item.children"
                  :index="citem.path"
                  :key="citem.path"
                >
                  {{ citem.title }}
                </el-menu-item>
              </el-submenu>
              <el-menu-item v-else :index="item.path" :key="index">{{
                  item.title
                }}
              </el-menu-item>
            </template>
          </template>
        </el-menu>
      </div>
      <!-- 菜单根据角色权限是否可点击跳转 -->
      <div v-else>
        <el-menu
          :default-active="activeIndex"
          class="el-menu-demo"
          mode="horizontal"
          @select="handleSelect"
        >
<!--          <el-menu-item
            index="/sw/indexFirst"
            key="/sw/indexFirst"
            class="menu-home"
          >
            <template slot="title">
              <i class="el-icon-s-platform"></i>首页
            </template>
          </el-menu-item
          >-->
          <template v-for="(item, index) in menus.slice(0, visibleMenuCount)">
            <el-submenu
              v-if="item.children.length > 0"
              :index="item.path"
              :key="item.path"
            >
              <template slot="title">
                <i :class="item.icon"></i>{{ item.title }}
              </template>
              <el-menu-item
                v-for="citem in item.children"
                :index="citem.path"
                :key="citem.path"
              >
                {{ citem.title }}
              </el-menu-item>
            </el-submenu>
            <el-menu-item v-else :index="item.path" :key="index">{{
                item.title
              }}
            </el-menu-item>
          </template>
          <!-- 顶部菜单超出数量折叠 -->
          <template v-if="menus.length > visibleMenuCount + 1">
            <el-submenu index="more">
              <template slot="title">更多菜单</template>
              <template v-for="(item, index) in menus.slice(visibleMenuCount)">
                <el-submenu
                  v-if="item.children && item.children.length > 0"
                  :index="item.path"
                  :key="item.path"
                >
                  <template slot="title">
                    <div style="font-size: 14px;">{{ item.title }}</div>
                  </template>
                  <el-menu-item
                    v-for="citem in item.children"
                    :index="citem.path"
                    :key="citem.path"
                  >
                    {{ citem.title }}
                  </el-menu-item>
                </el-submenu>
                <el-menu-item v-else :index="item.path" :key="index">
                  {{ item.title }}
                </el-menu-item>
              </template>
            </el-submenu>
          </template>
          <template v-else>
            <template v-for="(item, index) in menus.slice(visibleMenuCount, visibleMenuCount+1)">
              <el-submenu
                v-if="item.children.length > 0"
                :index="item.path"
                :key="item.path"
              >
                <template slot="title">
                  <i :class="item.icon"></i>{{ item.title }}
                </template>
                <el-menu-item
                  v-for="citem in item.children"
                  :index="citem.path"
                  :key="citem.path"
                >
                  {{ citem.title }}
                </el-menu-item>
              </el-submenu>
              <el-menu-item v-else :index="item.path" :key="index">{{
                  item.title
                }}
              </el-menu-item>
            </template>
          </template>
        </el-menu>
      </div>
    </div>

    <div class="userinfo flex-start">
      <img src="@/assets/images/sw/avatar.png" alt="">

      <!-- <p>{{roleNames}}</p> -->
       <el-dropdown>
        <span class="username-box">
          <div class="username">欢迎您，{{ username }}</div>
         （{{ actionName }}）
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="(item, index) in roleNames" :key="item">{{  item }}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>
<script>
import menuArray from '@/views/sw/menu'
import {resultError} from '@/utils/messageText'
import {findTree} from '@/api/sw/menu/api'
import { active } from 'sortablejs';

export default {
  props: {
    myRoles: {
      type: Array,
      default: function () {
        return []
      }
    },
    username: {type: String, default: ''}
  },
  data() {
    return {
      // 顶部栏初始数
      visibleNumber: 5,
      // 是否为首次加载
      isFrist: false,
      // 当前激活菜单的 index
      currentIndex: undefined,
      activeIndex: '/sw/tags',
      menus: menuArray,
      roles: [],
      roleNames:'',
      actionName: "",
      menuMap: null,
      menuDisplay: true,//true为菜单根据角色显示，false为菜单根据角色是否可点击跳转
      visibleMenuCount: 5,
      rightImageWidth: 170, // 假设右侧图片的宽度为170px
      leftFixedWidth: 156, // 假设左侧固定宽度的部分为300px
      menuWidth: 144, // 每个菜单项的宽度
    }
  },
  watch: {
    '$store.state.user.authorities'(newv, oldv) {
      console.log('store watch')
      this.roles = newv
    }
  },
  computed: {
    hasMenuPermissionShow() {
      return function (item) {
        //console.log('菜单根据角色显示:' + item.title)
        let hasMenu = true
        //console.log('菜单根据角色显示:', hasMenu)
        // this.roles = this.myRoles
        // this.roles.forEach((ele) => {
        //   if (item.role.includes(ele.authority)) {
        //     console.log('has perm',this.roles,ele.authority,item)
        //     hasMenu = true
        //   }
        // })
        return hasMenu
      }
    }
  },
  created() {
    let queryParams = {
      menuSystem: 'netendtoend',
    }
    findTree(queryParams).then(response => {
      if (response.code === '1') {
        console.log(response.data)
        this.menus = response.data
        const srt = response.data.length > 0 ? response.data[0].roleName : '';
        if (srt) {
          this.roleNames = srt.split(',')
          this.actionName = srt.split(',')[0]
        }
        this.dealMenuMap()
      } else {
        this.$message.error(resultError)
      }
    })
  },
  beforeMount() {
    window.addEventListener('resize', this.setVisibleNumber)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.setVisibleNumber)
  },
  mounted() {
    let showMenu = process.env.VUE_APP_SHOW_MENU
    if (showMenu === 'true') {
      this.menuDisplay = false
    }
    this.roles = this.myRoles
    this.setVisibleNumber();
  },
  methods: {
    // 菜单json处理为map，方便获取role
    dealMenuMap() {
      let menumap = new Map()
      this.menus.forEach((m) => {
        if (!menumap.has(m.path)) {
          menumap.set(m.path, m.role)
        }
        if (m.children.length > 0) {
          m.children.forEach((i) => {
            if (!menumap.has(i.path)) {
              menumap.set(i.path, i.role)
            }
          })
        }
      })
      this.menuMap = menumap
    },
    // 点击菜单判断是否具备权限
    hasMenuPermission(item) {
      let hasMenu = true
      // this.roles = this.myRoles
      // this.roles.forEach((ele) => {
      //   let m = this.menuMap.get(item)
      //   if (m.includes(ele.authority)) {
      //     hasMenu = true
      //   }
      // })
      // if (process.env.VUE_APP_SHOW_MENU === 'true') {
      //   hasMenu = true
      // }
      return hasMenu
    },
    // 点击菜单
    handleSelect(index, indexPath) {
      if (this.menuDisplay) {
        this.$router.push(index)
      } else {
        let hasPerm = this.hasMenuPermission(index)
        // let hasPerm = true;
        if (hasPerm) {
          this.$router.push(index)
        } else {
          this.$message({message: '暂无权限', type: 'warning'})
        }
      }
    },
    // 根据宽度计算设置显示栏数
    setVisibleNumber() {
      // 获取页面实际可用宽度
      const availableWidth = window.innerWidth - this.rightImageWidth - this.leftFixedWidth - this.menuWidth;
      // 计算菜单项宽度
      const menuWithMargin = this.menuWidth;
      // 计算可显示的菜单项数量
      const visibleItems = Math.floor(availableWidth / menuWithMargin) - 1;
      // 确保菜单项数量不为负
      this.visibleMenuCount = Math.max(1, visibleItems);
    },
  }
}
</script>
<style scoped lang="less">
.nav-wrapper {
  padding: .1067rem 0;
  // background-color: #fff;
  .logo-container {
    width: 300px;
  }

  .logo {
    margin-left: .1917rem;
    margin-right: .117rem;
    // width: 30px;
    // height: 30px;
    // background-color: #dee0e3;
    // border-radius: 50%;

    img {
      // width: 23.81px;
      height: .3417rem;
      // font-size: 12px;
    }
  }

  .el-menu-item:hover, .el-menu-item:focus {
    background-color: #2266FD;
    border-radius: 8px;
    box-shadow: 0px 2px 4px 2px rgba(34, 102, 253, 0.24);
    color: #fff;

    i {
      color: #fff;
    }
  }

  .el-menu-item {
    border-radius: 8px;
    width: 100px;
    text-align: center;
  }

  h1 {
    margin: 0 .6667rem 0 .0833rem;
    font-family: SourceHanSansSC-Bold;
    font-size: .1667rem;
    color: #101010;
    text-align: center;
    font-weight: 700;
    line-height: .425rem;
  }


}

::v-deep .el-menu {
  background-color: transparent;
}

::v-deep .el-menu.el-menu--horizontal {
  border-bottom: 0;
  border-radius: 8px;
  overflow: visible;
  font-family: PingFangSC-Regular !important;
}

::-webkit-scrollbar {
  width: 0;
}

::v-deep .el-menu--horizontal > .el-submenu .el-submenu__title {
  margin: 0 1px;
  color: #333E50;
  border-radius: 8px;
  border-bottom: 0;
  font-size: 16px !important;
}

::v-deep .el-menu--horizontal > .el-submenu .el-submenu__title:hover, .el-menu--horizontal > .is-active {
  background-color: #2266FD;
  border-radius: 8px;
  box-shadow: 0px 2px 4px 2px rgba(34, 102, 253, 0.24);
  color: #fff;

  i {
    color: #fff;
  }

}

::v-deep .el-menu--horizontal .is-active .el-submenu__title {
  color: #fff !important;

  i {
    color: #fff;
  }
}

::v-deep .el-menu--popup-bottom-start, .el-menu--popup-bottom-start {
  border-radius: 8px !important;
  overflow: hidden;
}

::v-deep .el-menu--horizontal > .el-submenu.is-active .el-submenu__title {
  border-bottom: 0;
}

::v-deep .el-menu--horizontal .el-menu-item:not(.is-disabled):focus, .el-menu--horizontal .el-menu-item:not(.is-disabled):hover {
  &::before {
    display: inline-block;
    content: '';
    height: .1667rem;
    position: absolute;
    border-radius: 8px;
    top: .0667rem;
    left: 0;
    background: url(../../assets/images/sw/menu-active.png) no-repeat center;
    background-size: 100%;
  }
}

.userinfo {
  margin-right: 10px;
  // width: 170px;
  height: 100%;
  font-size: .12rem;
  .username-box {
    text-align: center;
  }
  // .username {
  //   padding: 0;
  //   padding-bottom: 5px;
  // }
  img {
    width: .3167rem;
    height: .3167rem;
    // margin-right: .0917rem;
  }
}

.el-menu-demo .el-menu-item {
  font-size: 16px !important;
}

.el-menu-item {
  font-size: 14px !important;
}
</style>
