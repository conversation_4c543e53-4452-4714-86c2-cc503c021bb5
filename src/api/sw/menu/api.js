import request from '@/utils/request'

// 客户打标接口
/**
 * 分页
 * @param params
 * @returns {AxiosPromise}
 */
export function findTree(query) {
  return request({
    url: '/sanquan/core/menu/findTree',
    method: 'get',
    params: query
  })
}
/**
 * 分页
 * @param params
 * @returns {AxiosPromise}
 */
export function findModelList() {
  return request({
    url: '/sanquan/menu/strategy/findModel',
    method: 'get'
  })
}
