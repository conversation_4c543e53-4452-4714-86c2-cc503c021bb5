<template>
  <div class="app-container" style="padding: 0; margin: 0; background: #fff">
    <div class="banner-img">
      <img
        src="../../assets/images/newsTendency/banner.png"
        alt="图片加载中..."
      />
    </div>
    <div class="crumb">
      <div class="bread">
        <el-breadcrumb separator="/" class="breadCrumb">
          <i
            class="el-icon-location-outline"
            style="margin-right: 10px; font-size: 20px; color: #c3c3c3"
          ></i>
          <span style="color: #333333">您的位置：</span>
          <el-breadcrumb-item :to="{ path: '/portal/home' }" replace
            >门户首页
          </el-breadcrumb-item>
          <el-breadcrumb-item
            ><a>{{ title }}</a></el-breadcrumb-item
          >
        </el-breadcrumb>

        <el-form
          :inline="true"
          v-if="$store.getters.customParam.userType === 'admin'"
        >
          <el-form-item label="租 户:">
            <el-select
              v-model="queryParams.tenantId"
              style="width: 200px"
              @change="tenantChange"
            >
              <el-option
                v-for="item in tenantList"
                :key="item.tenantId"
                :label="item.tenantName"
                :value="item.tenantId"
              />
            </el-select>
          </el-form-item>
        </el-form>

        <div class="search-title">
          <el-input
            placeholder="标题搜索"
            v-model="queryParams.noticeTitle"
            clearable
            style="width: 250px"
            @keyup.enter.native="getList"
          >
            <!-- <el-button
              slot="append"
              icon="el-icon-search"
              @click="getList"
              class="myButton"
            ></el-button> -->
            <i
              slot="suffix"
              class="el-icon-search"
              @click="getList"
              style="line-height: 36px; margin-right: 10px"
            ></i>
          </el-input>
        </div>

        <div class="filter">
          <span>类型：</span>
          <!-- <div style="margin: 0 15px"></div> -->
          <el-radio-group v-model="queryParams.noticeType" @change="change">
            <el-radio
              v-for="item in noticeTypeList"
              :label="item.dictValue"
              :key="item.dictValue"
              >{{ item.dictLabel }}</el-radio
            >
          </el-radio-group>
        </div>
        <!-- <el-col :span="3" class="c-r-t"
            ><span class="card-title-left"
              ><i
                v-if="queryParams.programaType === 'system_notice'"
                class="icon iconfont icon-gonggao"
              ></i
              ><i
                v-if="queryParams.programaType === 'business_news'"
                class="icon iconfont icon-yewudongtai"
              ></i
              ><span>{{ title }}列表</span></span
            ></el-col
          > -->
      </div>
    </div>

    <el-card class="note-card" shadow="never" v-loading="loading">
      <div slot="header" class="card-title"></div>
      <div class="home-card-body">
        <a
          v-for="item in list"
          :key="item.noticeId"
          :href="`/portal/home/<USER>/${item.programaType}/${item.noticeId}`"
          target="view_window"
        >
          <div class="notice">
            <div class="noticeContent">
              <div class="imgs" v-if="item.coverUrl">
                <img :src="item.coverUrl" alt="" />
              </div>
              <div class="noimgs" v-if="!item.coverUrl">
                <img src="@/assets/images/nopicture.png" alt="" />
              </div>
              <div class="contentFont">
                <div class="notice-title">
                  <div>{{ item.noticeTitle }}</div>
                </div>
                <div class="content" v-html="item.noticeContentText">
                  {{ item.noticeContentText }}
                </div>
              </div>
              <div class="notice-time">
                {{ parseTime(item.createDate, "{y}-{m}-{d}") }}
              </div>
            </div>
            <el-button class="more" @click="item">MORE</el-button>
            <!-- 初始的构造 -->
            <!-- <div class="notice-title">
              「<span style="color: red">{{ item.noticeTypeName }}</span
              >」{{ item.noticeTitle }}
            </div>
            <div class="notice-time">
              {{ parseTime(item.createDate, "{y}-{m}-{d}") }}
            </div> -->
          </div>
        </a>
        <el-empty
          style="padding: 20px 0"
          v-if="list.length <= 0"
          :image-size="30"
          :description="`暂无${title}`"
        >
        </el-empty>
      </div>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script>
import AppLink from "@/components/Link";
import { selectAllForPage } from "@/api/system/notice";
import { list as tenantList } from "@/api/system/tenant";

export default {
  name: "detail",
  components: { AppLink },
  data() {
    return {
      noticeTypeList: [],
      list: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        programaType: undefined,
        noticeTitle: undefined,
        noticeType: "all",
        showType: 2,
        tenantId: this.$store.getters.customParam.tenantId,
      },
      loading: false,
      title: "",
      tenantList: [],
    };
  },
  created() {
    this.getDicts("notice_type_list").then((response) => {
      this.noticeTypeList = response.data;
      this.noticeTypeList.unshift({
        dictLabel: "全部",
        dictValue: "all",
      });
    });
    this.queryParams.programaType = this.$route.params.type;
    this.title =
      this.queryParams.programaType === "business_news"
        ? "业务动态"
        : "通知公告";
    this.getList();
    this.getTenantList();
  },
  methods: {
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.cities.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.cities.length;
    },
    getList() {
      this.loading = true;
      selectAllForPage({
        ...this.queryParams,
        noticeType:
          this.queryParams.noticeType === "all"
            ? undefined
            : this.queryParams.noticeType,
      }).then((response) => {
        this.list = response.data.records;
        for (var i = 0; i < this.list.length; i++) {
          this.list[i].createDate = this.list[i].createDate.substr(0, 10);
        }
        this.total = response.data.total;
        this.loading = false;
      });
    },
    change(value) {
      this.queryParams.noticeType = value;
      this.getList();
    },
    getTenantList() {
      tenantList().then((response) => {
        this.tenantList = response.data;
      });
    },
    tenantChange() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
  },
};
</script>

<style scoped lang="scss">
@import "../../assets/styles/element-variables.scss";
@import "../../layout/portal/components/index.scss";
.note-card {
  max-width: 1443px;
  min-height: calc(100vh - 150px);
  border: 0px;
  margin: 0 auto;

  ::v-deep .el-card__body {
    margin: 0;
    padding: 0;
  }

  .card-title {
    .el-card__header {
      border: 0px;
    }
    .filter {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      > span {
        margin-left: 10px;
      }
    }

    .c-r-t {
      display: flex;
      flex-direction: row;
      align-items: center;
    }

    .card-title-left {
      display: flex;
      flex-direction: row;
      align-items: center;

      .icon {
        font-size: 24px;
        color: #419eee;
        padding: 0px 5px;
        -webkit-transition: font-size 0.25s linear, width 0.25s linear;
        -moz-transition: font-size 0.25s linear, width 0.25s linear;
        transition: font-size 0.25s linear, width 0.25s linear;
      }
    }
  }

  .home-card-body {
    display: flex;
    flex-direction: column;

    a {
      width: 100%;
      font-size: 14px;
      font-weight: 500;
      color: #606266;

      :hover {
        color: $--color-primary;
      }

      .notice {
        padding: 20px 0px 20px 0px;
        width: 100%;
        height: 198px;
        position: relative;
        border-bottom: 2px solid #e7e7e7;

        .noticeContent {
          height: 100%;
          display: -webkit-box;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          display: flex;
          justify-content: space-between;

          .contentFont {
            width: 62%;
            padding: 10px 0 10px 0;

            .notice-title {
              overflow: hidden;
              text-overflow: ellipsis;
              word-break: break-all;
              white-space: nowrap;
              flex-basis: 70%;
              margin-bottom: 15px;
              font-size: 18px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: #4c86e3;

              .content {
                width: 70%;
                display: inline;
                font-size: 14px;
                font-weight: 400;
                color: #999999;
                text-overflow: ellipsis;
                word-break: break-all;
                white-space: nowrap;
              }

              .content {
                ::v-deep span {
                  color: red;
                }
              }
            }

            .content {
              text-overflow: -o-ellipsis-lastline;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              -webkit-box-orient: vertical;
              width: 100%;

              ::v-deep span {
                color: red;
              }
            }
          }

          .noimgs {
            width: 23%;
            height: 100%;
            margin-right: 10px;
            img {
              width: 100%;
              height: 100%;
            }
          }

          .imgs {
            width: 23%;
            height: 100%;
            margin-right: 10px;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .notice-time {
            margin-top: 10px;
            width: 10%;
            text-align: center;
          }
        }

        .more {
          height: 38px;
          position: absolute;
          bottom: 40px;
          right: 20px;
          background: #4c86e3;
          border: 1px solid #4c86e3;
          color: #fff;
        }
      }
    }
  }
}
</style>
