<template>
  <div class="portal-body">
    <div class="fiveg-pn-container">
      <!-- 左侧菜单 -->
      <div class="left-menu">
        <div class="menu-header">
          <h3>5G专网管理</h3>
        </div>
        <ul class="menu-list">
          <li
            v-for="menu in menuList"
            :key="menu.key"
            :class="['menu-item', { active: activeMenu === menu.key }]"
            @click="handleMenuClick(menu)"
          >
            <i :class="menu.icon"></i>
            <span>{{ menu.title }}</span>
          </li>
        </ul>
      </div>

      <!-- 右侧内容区域 -->
      <div class="right-content">
        <!-- Tab标签页 -->
        <div class="tab-container">
          <el-tabs
            v-model="activeTab"
            type="card"
            closable
            @tab-remove="removeTab"
            @tab-click="handleTabClick"
          >
            <el-tab-pane
              v-for="tab in openTabs"
              :key="tab.name"
              :label="tab.title"
              :name="tab.name"
            >
              <!-- 共享随行专网 -->
              <GxsxTab v-if="tab.name === 'gxsx'" @close-tab="removeTab" />
              
              <!-- 独享随行专网 -->
              <DxsxTab v-if="tab.name === 'dxsx'" @close-tab="removeTab" />
              
              <!-- 物网-虚拟专网 -->
              <WwxnTab v-if="tab.name === 'wwxn'" @close-tab="removeTab" />
              
              <!-- 物网-混合/独享专网 -->
              <WwhhTab v-if="tab.name === 'wwhh'" @close-tab="removeTab" />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import GxsxTab from './components/GxsxTab.vue'
import DxsxTab from './components/DxsxTab.vue'
import WwxnTab from './components/WwxnTab.vue'
import WwhhTab from './components/WwhhTab.vue'

export default {
  name: "FiveGPN",
  components: {
    GxsxTab,
    DxsxTab,
    WwxnTab,
    WwhhTab
  },
  data() {
    return {
      activeMenu: 'gxsx',
      activeTab: 'gxsx',
      openTabs: [
        {
          name: 'gxsx',
          title: '共享随行专网'
        }
      ],
      menuList: [
        {
          key: 'gxsx',
          title: '共享随行专网',
          icon: 'el-icon-document'
        },
        {
          key: 'dxsx',
          title: '独享随行专网',
          icon: 'el-icon-money'
        },
        {
          key: 'wwxn',
          title: '物网-虚拟专网',
          icon: 'el-icon-connection'
        },
        {
          key: 'wwhh',
          title: '物网-混合/独享专网',
          icon: 'el-icon-cpu'
        }
      ]
    }
  },
  methods: {
    // 菜单点击
    handleMenuClick(menu) {
      this.activeMenu = menu.key;
      
      // 检查是否已经打开了该tab
      const existingTab = this.openTabs.find(tab => tab.name === menu.key);
      if (!existingTab) {
        this.openTabs.push({
          name: menu.key,
          title: menu.title
        });
      }
      this.activeTab = menu.key;
    },

    // 移除tab
    removeTab(targetName) {
      let tabs = this.openTabs;
      let activeName = this.activeTab;
      
      if (activeName === targetName) {
        tabs.forEach((tab, index) => {
          if (tab.name === targetName) {
            let nextTab = tabs[index + 1] || tabs[index - 1];
            if (nextTab) {
              activeName = nextTab.name;
            }
          }
        });
      }
      
      this.activeTab = activeName;
      this.openTabs = tabs.filter(tab => tab.name !== targetName);
    },

    // tab点击
    handleTabClick(tab) {
      this.activeTab = tab.name;
      this.activeMenu = tab.name;
    }
  }
}
</script>

<style lang="scss" scoped>
.portal-body {
  padding: 0;
  margin: 0;
  background: #f5f7fa;
}

.fiveg-pn-container {
  display: flex;
  height: calc(100vh - 80px);
  background: #f5f7fa;
  
  .left-menu {
    width: 200px;
    background: #fff;
    border-right: 1px solid #e4e7ed;
    
    .menu-header {
      padding: 20px;
      border-bottom: 1px solid #e4e7ed;
      background: #fafafa;
      
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        text-align: center;
      }
    }
    
    .menu-list {
      list-style: none;
      padding: 0;
      margin: 0;
      
      .menu-item {
        padding: 16px 20px;
        cursor: pointer;
        border-bottom: 1px solid #f0f2f5;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        position: relative;
        
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 3px;
          background: #409eff;
          transform: scaleY(0);
          transition: transform 0.2s ease;
        }
        
        i {
          margin-right: 10px;
          font-size: 16px;
          color: #606266;
          transition: color 0.2s ease;
        }
        
        span {
          font-size: 14px;
          color: #303133;
          font-weight: 400;
          transition: color 0.2s ease;
        }
        
        &:hover {
          background: #f5f7fa;
          
          i {
            color: #409eff;
          }
          
          span {
            color: #409eff;
          }
        }
        
        &.active {
          background: #ecf5ff;
          
          &::before {
            transform: scaleY(1);
          }
          
          i {
            color: #409eff;
          }
          
          span {
            color: #409eff;
            font-weight: 500;
          }
        }
      }
    }
  }
  
  .right-content {
    flex: 1;
    background: #f5f7fa;
    
    .tab-container {
      height: 100%;
      
      ::v-deep .el-tabs {
        height: 100%;
        
        .el-tabs__header {
          margin: 0;
          background: #fff;
          border-bottom: 1px solid #e4e7ed;
          padding: 0 15px;
          
          .el-tabs__nav {
            border: none;
          }
          
          .el-tabs__item {
            border: none;
            background: transparent;
            margin-right: 20px;
            padding: 0 0 15px 0;
            font-weight: 400;
            color: #606266;
            border-bottom: 2px solid transparent;
            
            &:hover {
              color: #409eff;
            }
            
            &.is-active {
              color: #409eff;
              border-bottom-color: #409eff;
              font-weight: 500;
            }
          }
        }
        
        .el-tabs__content {
          height: calc(100% - 50px);
          overflow-y: auto;
          background: #f5f7fa;
          
          &::-webkit-scrollbar {
            width: 6px;
          }
          
          &::-webkit-scrollbar-track {
            background: #f1f1f1;
          }
          
          &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
            
            &:hover {
              background: #a8a8a8;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .fiveg-pn-container {
    .left-menu {
      width: 180px;
    }
  }
}

@media (max-width: 768px) {
  .fiveg-pn-container {
    flex-direction: column;
    
    .left-menu {
      width: 100%;
      height: auto;
      
      .menu-list {
        display: flex;
        overflow-x: auto;
        
        .menu-item {
          white-space: nowrap;
          min-width: 120px;
          justify-content: center;
        }
      }
    }
    
    .right-content {
      margin-left: 0;
      margin-top: 8px;
    }
  }
}
</style>
