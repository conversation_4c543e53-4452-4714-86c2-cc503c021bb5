import request from "@/utils/request";

// 查询部门列表
export function getList(data) {
  return request({
    url: "/gh/tu-mn-activity/page",
    method: "get",
    params: data
  });
}

// 查询部门列表
export function getPage(action,data) {
  return request({
    url: "/gh/"+action,
    method: "get",
    params: data
  });
}

// 新增公告
export function save(data) {
  return request({
    url: "/gh/tu-mn-activity/save",
    method: "post",
    data: data
  });
}
// 修改
export function update(data) {
  return request({
    url: "/gh/tu-mn-activity/update",
    method: "post",
    data: data
  });
}

// 修改
export function deleteTable(data) {
  return request({
    url: "/gh/tu-mn-activity/deleteById/"+data,
    method: "get"
  });
}
// 审核
export function check(data) {
  return request({
    url: "/gh/tu-mn-activity/check",
    method: "post",
    data: data
  });
}

// 通过id查询
export function findById(id) {
  return request({
    url: "/gh/tu-mn-activity/getById/"+id,
    method: "get"
  });
}

// 通过id查询
export function returnCheck(data) {
  return request({
    url: "/gh/tu-mn-activity/returnStatus",
    method: "post",
    data: data
  });
}
// 通过id查询
export function readCount(data) {
  return request({
    url: "/gh/tu-mn-activity/readCount",
    method: "post",
    data: data
  });
}

// 通过id删除
export function deleteById(id) {
  return request({
    url: "/gh/tu-mn-activity/deleteById/"+id,
    method: "get",
  });
}

// 撤回
export function withdraw(data) {
  return request({
    url: "/gh/tu-mn-activity/withdraw",
    method: "post",
    data: data
  });
}
