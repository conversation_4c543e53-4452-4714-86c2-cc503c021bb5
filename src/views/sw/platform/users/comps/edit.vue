<template>
  <div>
    <!-- 添加或修改标签弹框 -->
    <el-dialog
      :title="title"
      :visible.sync="openTabDialog"
      @open="onOpen"
      @close="onCancel"
      width="1000px"
    >
      <template slot="title">
        <span class="dialog_title">{{ title }}</span>
      </template>
      <el-row>
        <el-form ref="myForm" :model="formParams" :rules="rules" label-width="80px">
          <el-col :span="12">
            <el-form-item label="工号" prop="jobNum">
              <el-input v-model="formParams['jobNum']" placeholder="请输入工号"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="formParams['name']" placeholder="请输入姓名"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phone">
              <el-input v-model="formParams['phone']" placeholder="请输入手机号码"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属行业" prop="industry">
              <el-select
                v-model="formParams.industry"
                placeholder="请选择"
                style="width: 100%"
                clearable
                multiple
                collapse-tags
                @change="handleIndustryChange"
              >
                <el-option
                  v-for="item in industryList"
                  :key="item.label"
                  :label="item.label"
                  :value="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="地市" prop="city">
              <el-select
                v-model="formParams.city"
                filterable
                placeholder="请选择"
                @change="getDistrictsAndCounties"
                style="width: 100%"
              >
                <el-option
                  v-for="item in cityList"
                  :key="item"
                  :label="item.replace('市','')"
                  :value="item.replace('市','')"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="区/县" prop="county">
              <el-select
                v-model="formParams['county']"
                filterable
                placeholder="请选择"
                style="width: 100%"
                clearable
                multiple
                collapse-tags
              >
                <el-option
                  v-for="item in countyList"
                  :key="item"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select> </el-form-item
            ></el-col>
            <el-col :span="12">
              <el-form-item label="组织部门" prop="dept">
                <el-cascader
                  v-model="formParams['deptNameList']"
                  placeholder="请选择"
                  :options="options"
                  :props="{ checkStrictly: true ,  emitPath: false, value: 'id', label: 'deptName', children: 'children'}"
                  @change="deptChange"
                  ref="cascaderRef"
                clearable></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="12" >
              <el-form-item label="细分行业" prop="segments">
                <el-select
                  v-model="formParams.segments"
                  placeholder="请选择"
                  style="width: 100%"
                  clearable
                  multiple
                  collapse-tags
                >
                  <el-option
                    v-for="item in segmentsList"
                    :key="item.label"
                    :label="item.label"
                    :value="item.label"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

<!--          <el-col :span="12">-->
<!--            <el-form-item label="机构类型" prop="orgType">-->
<!--              <el-select v-model="formParams.orgType" clearable placeholder="机构类型" style="width: 100%" size="small">-->
<!--                <el-option :key="0" label="省" :value="'省'"/>-->
<!--                <el-option :key="1" label="地市" :value="'地市'"/>-->
<!--              </el-select>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--          <el-col :span="12">-->
<!--            <el-form-item label="用户类型" prop="userType">-->
<!--              <el-select v-model="formParams.userType" clearable placeholder="用户类型" style="width: 100%" size="small">-->
<!--                <el-option :key="0" label="省级用户" value="省级用户"/>-->
<!--                <el-option :key="1" label="地市接口人" value="地市接口人"/>-->
<!--                <el-option :key="2" label="客户经理" value="客户经理"/>-->
<!--              </el-select>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
        </el-form>

      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button @click="onCancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { findRole, findUser, saveRole, saveUser } from '@/api/platform/role'
import { findCityGroup, findDistrictGroup } from '@/api/sw/customer/customerManager'
import { findIndustry, findSegments } from '@/api/sw/product/product'
import { resultError } from '@/utils/messageText'
import { treeDept } from '@/api/sw/platform/department/index'

export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    reloadTable: {
      type: Function, default: function() {
      }
    }
  },
  data() {
    return {
      openTabDialog: false,
      open: false,
      // 表单参数
      formParams: {},
      // 表格loading
      tableLoading: false,
      titles: '',
      // 列表数据
      infoData: {},
      // 表格数据
      tableData: [],
      // 表单校验
      rules: {
      },
      title: '',
      formData: {
        type: Object,
        default: {}
      },
      cityList:[],
      countyList:[],
      industryList:[],
      options: [], // 部门列表
      segmentsList: [], // 细分行业
    }
  },
  // 方法集合
  methods: {

    initOpen(title, formData) {
      this.openTabDialog = true
      this.title = title
      setTimeout(() => {
        this.formParams={}
        if (this.$refs['myForm']) {
          this.$refs['myForm'].resetFields()
        }
        findCityGroup().then((res) => {
          this.cityList = res.data;
        });
        findIndustry().then((response) => {
          if (response['code'] === "1") {
            this.industryList = response.data;
          }
        });
         // 查询部门
         treeDept().then((response) => {
          if(response.success){
            this.options = response.data;
          }else {
            this.$message.error("部门查询失败！")
          }
        });
        this.formData = formData
        if (formData.id) {
          findUser({ id: formData.id }).then((result) => {
            console.log(result, 'findOne的result')
            const deptId = result.data.deptId
            this.formParams = result['data']
             // 细分行业
            this.formParams['industry'] = this.formParams['industry'] 
            ? this.formParams['industry'].includes(",") 
              ? this.formParams['industry'].split(",") 
              : [this.formParams['industry']] 
            : [];
            this.formParams['county'] = this.formParams['county'] 
              ? this.formParams['county'].includes(",") 
                ? this.formParams['county'].split(",") 
                : [this.formParams['county']] 
              : [];
            // 细分行业
            this.formParams['segments'] = this.formParams['segments'] 
              ? this.formParams['segments'].includes(",") 
                ? this.formParams['segments'].split(",") 
                : [this.formParams['segments']] 
              : [];
            // 确保 deptId 存在
            if (deptId) {
              this.formParams.deptNameList = deptId
            }
            this.getDistrictsAndCounties('');
          })
        }
      }, 100)

    },
    // 获取区县
    getDistrictsAndCounties(val) {
      findDistrictGroup({
        city: this.formParams.city,
      }).then((response) => {
        this.countyList = response.data;
      });
    },
    // 打开窗口
    onOpen() {
      // this.form = { ...this.formData }
      // this.tableLoading = true
    },
    /** 提交按钮 */
    submitForm() {
      console.log(this.$refs, 'refs')
      this.$refs['myForm'].validate(async(valid) => {
        if (valid) {
          this.formParams['industry']=this.formParams['industry']?this.formParams['industry']+'':'';
          this.formParams['county']=this.formParams['county']?this.formParams['county']+'':'';
          this.formParams['segments']=this.formParams['segments']?this.formParams['segments']+'':'';
          this.formParams['deptNameList'] = undefined
          saveUser(this.formParams).then((response) => {
            this.$message.success('保存成功')
            this.onCancel()
          }).catch(() => {
            this.$message.error('保存失败')
          })
        }
      })
    },

    // 取消按钮
    onCancel() {
      this.openTabDialog = false
      this.reloadTable()
    },
    // 表单重置
    reset() {
      this.form = {}
      // this.resetForm("form");
    },
    // 部门修改
    deptChange(val) {
      const node = this.$refs.cascaderRef.getCheckedNodes()
      console.log('多选框对象：',node,this.formParams.deptName)
      // 根据选中的值找到对应的 label
      // console.log('获取名单制客户id：',val.rosterCustomerId)
      this.formParams.deptId = node[0].value
      this.formParams.deptName = node[0].label
      console.log('名单制数据',this.formParams)
    },
    // 行业选择触发的方法
    handleIndustryChange(){
      console.log('当前选择的行业信息：', this.formParams.industry);
      if (this.formParams.industry && this.formParams.industry.length > 0) {
        this.getSegments(this.formParams.industry);
      }else{
        this.formParams.segments = []
      }
    },
    // 获取细分行业信息
    getSegments(industry){
      findSegments(industry).then((response) => {
        if (response['code'] === "1") {
          this.segmentsList = response.data;
        }
      });
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  padding: 0 0.08333rem;
  background-color: #f0f2f5;
  margin-bottom: 0.08333rem;
  font-weight: bold;
  font-size: 0.125rem;
}

.el-button--text {
  color: #3377ff !important;
}

.info {
  padding: 0.16667rem;
}
</style>
