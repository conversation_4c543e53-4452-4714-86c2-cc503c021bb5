<template>
  <div class="login" :style="defaultStyle">
    <div class="login-container">
      <div class="login-left">
        <el-image class="leftTop_img" :src="leftTop_img">
          <div slot="error">
            <div style="color: #f5f7fa">图片加载中...</div>
          </div>
        </el-image>
        <el-image class="leftBottom_img" :src="leftBottom_img">
        </el-image>
      </div>
      <div class="login-right">
        <!--      <div class="functions">-->
        <!--        <el-button-->
        <!--          type="text"-->
        <!--          class="registerButton"-->
        <!--          @click="handleForget">-->
        <!--          <span>注册</span>-->
        <!--        </el-button>-->
        <!--        </div>-->
        <div class="circle-left">
          <el-image :src="rightTopTitle_img"></el-image>
        </div>
        <!--      <div class="circle-right">-->
        <!--        <el-image :src="rightTopTitle_img"></el-image>-->
        <!--      </div>-->
        <div class="login-form">
          <!--        <h2 class="title" id="title">{{ systemTitle !== "" ? systemTitle : "统一门户" }}</h2>-->
          <!-- 不显示手机号登录 -->
          <div>
            <el-form
              ref="userLoginForm"
              :model="loginForm"
              :rules="userLoginRules"
              style="margin-top: 30px"
              v-if="!switchOrg"
            >
              <el-form-item
                prop="tenantLoginName"
                :id="
                  this.url.indexOf('tenantName') !== -1 &&
                  this.loginForm.tenantId
                    ? 'untenantLoginName'
                    : ''
                "
              >
                <el-input
                  v-model="loginForm.tenantLoginName"
                  type="text"
                  auto-complete="off"
                  placeholder="租户名"
                  @blur="getUserTenantId"
                  style="display: none"
                >
                  <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon"/>
                </el-input>
                <el-input
                  v-model="loginForm.tenantId"
                  auto-complete="off"
                  placeholder="租户ID"
                  @blur="getTenantId"
                  style="display: none"
                >
                  <svg-icon
                    slot="prefix"
                    icon-class="user"
                    class="el-input__icon input-icon"
                    style="display: none"
                  />
                  {{ loginForm.tenantId }}
                </el-input>
              </el-form-item>
              <div v-if="errorMsgs" style="margin: -22px 0 4px">
                <span class="error-msg">{{ errorMsgs }}</span>
              </div>
              <el-form-item prop="uni_name">
                <el-input
                  v-model="loginForm.uni_name"
                  type="text"
                  auto-complete="off"
                  placeholder="请输入用户名"
                >
                  <i slot="prefix" style="display: flex;align-items: center;">
                    <el-image :src="userIcon" style="width:16px;height:16px;"></el-image>
                  </i>
                  <!--                <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />-->
                </el-input>
              </el-form-item>
              <el-form-item prop="inpk">
                <el-input
                  v-model="loginForm.inpk"
                  type="password"
                  auto-complete="off"
                  placeholder="请输入密码"
                >
                  <i slot="prefix" style="display: flex;align-items: center;">
                    <el-image :src="passwordIcon" style="width:16px;height:16px;"></el-image>
                  </i>
                  <!--                <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />-->
                </el-input>
              </el-form-item>
              <el-form-item prop="captcha">
                <div class="op">
                  <el-input
                    type="input"
                    placeholder="请输入验证码"
                    v-model="loginForm.captcha"
                    style="width: 45%; height: 100%"
                    @keyup.enter.native="handleLogin"
                  >
                    <i slot="prefix" style="display: flex;align-items: center;">
                      <el-image :src="captchaIcon" style="width:12px;height:12px;"></el-image>
                    </i>
                  </el-input>
                  <img
                    style="width: 40%; margin-left: 15px"
                    v-if="captchaUrl"
                    class="captcha"
                    @click="refreshCaptcha"
                    alt="图形验证码"
                    :src="captchaUrl"
                  />
                </div>
              </el-form-item>
              <div v-if="errorMsg" style="margin: -22px 0 4px">
                <span class="error-msg">{{ errorMsg }}</span>
              </div>


              <el-form-item style="width: 100%">
                <el-button
                  :loading="loading"
                  size="medium"
                  type="primary"
                  class="login-button"
                  @click.native.prevent="handleLogin"
                >
                  <span v-if="!loading">登 录</span>
                  <span v-else>登 录 中...</span>
                </el-button>
              </el-form-item>
            </el-form>

            <!-- 租户申请的弹窗 -->
            <el-dialog
              title="租户申请"
              :visible.sync="dialogVisible"
              :before-close="handleClose"
              :append-to-body="true"
            >
              <!-- 租户申请填写 -->
              <div v-if="fillInformation">
                <a-steps :current="current">
                  <a-step v-for="item in steps" :key="item.title" :title="item.title"/>
                </a-steps>
                <!-- 第一步 -->
                <div class="steps-content" v-if="steps[0].content">
                  <div class="chose">
                    <el-card class="box-card">
                      <div slot="header" class="clearfix">
                        <span style="font-size: 18px">租户注册</span>
                      </div>
                      <div class="text-Item">欢迎进入租户申请页面，请根据需求选择合适的入口进入</div>
                      <div class="buttoms">
                        <el-button type="primary" @click="newApp">新申请</el-button>
                        <el-button @click="applying">已申请</el-button>
                      </div>
                    </el-card>
                  </div>
                </div>
                <!-- 第二步 -->
                <div class="steps-content" v-if="steps[1].content">
                  <div class="fillInformation">
                    <el-form
                      ref="submitList"
                      :model="lists"
                      :rules="rules"
                      class="form"
                      label-position="right"
                      label-width="135px"
                    >
                      <el-row>
                        <el-col :span="12">
                          <el-form-item label="租户名称" prop="tenantName">
                            <el-input
                              v-model="lists.tenantName"
                              placeholder="请输入租户名称"
                              maxlength="64"
                              style="width: 200px"
                            />
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-form-item label="租户登录名" prop="tenantLoginName">
                            <el-input
                              v-model="lists.tenantLoginName"
                              placeholder="请输入租户登录名"
                              maxlength="64"
                              style="width: 200px"
                            />
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-col :span="12">
                          <el-form-item label="租户管理员登录名" prop="displayName">
                            <el-input
                              v-model="lists.displayName"
                              placeholder="请输入管理员登录名"
                              maxlength="64"
                              style="width: 200px"
                            />
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-form-item label="租户管理员姓名" prop="staffName">
                            <el-input
                              v-model="lists.staffName"
                              placeholder="请输入管理员姓名"
                              maxlength="11"
                              style="width: 200px"
                            />
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-col :span="12">
                          <el-form-item label="最大用户数" prop="maxStaff">
                            <el-input-number
                              v-model="lists.maxStaff"
                              controls-position="right"
                              :min="0"
                              style="width: 200px"
                            />
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-form-item label="有效截止时间" prop="effectiveDate">
                            <el-date-picker
                              v-model="lists.effectiveDate"
                              size="small"
                              type="datetime"
                              style="width: 200px"
                              value-format="yyyy-MM-dd HH:mm:ss"
                            ></el-date-picker>
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-col :span="12">
                          <el-form-item label="组织名称" prop="orgName">
                            <el-input
                              v-model="lists.orgName"
                              placeholder="请输入组织名称"
                              maxlength="50"
                              style="width: 200px"
                            />
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-form-item label="组织编码" prop="code">
                            <el-input
                              v-model="lists.code"
                              placeholder="请输入组织编码"
                              maxlength="50"
                              style="width: 200px"
                            />
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </el-form>
                  </div>
                </div>
                <!-- 第三步 -->
                <div class="steps-content" v-if="steps[2].content">
                  <div class="finish">
                    <el-card class="box-card">
                      <div slot="header" class="clearfix">
                        <span style="font-size: 18px">租户完成注册</span>
                      </div>
                      <div
                        class="text item"
                        style="
                      margin: 0 auto;
                      line-height: 100px;
                      text-align: center;
                    "
                      >
                        <span style="font-size: 20px">您已提交申请，请耐心等待管理员审核通过...</span>
                      </div>
                    </el-card>
                  </div>
                </div>
                <div class="steps-action">
                  <el-button
                    v-if="current !== 0 && current < steps.length - 1"
                    type="primary"
                    @click="submit('submitList')"
                    :loading="iconLoading"
                  >提交申请
                  </el-button>
                  <el-button
                    v-if="current === steps.length - 1"
                    type="primary"
                    @click="closeDialog"
                  >关闭
                  </el-button>
                  <el-button v-if="current === 1" style="margin-left: 8px" @click="prev">上一步</el-button>
                </div>
              </div>

              <!-- 租户信息查看 -->
              <div class="applyAll" v-if="applys">
                <el-card>
                  <div class="textItem" style="height: 25px">
                    <el-form
                      ref="findForm"
                      label-width="180px"
                      class="findForm"
                      :model="loginNameList"
                      @submit.native.prevent
                    >
                      <el-row>
                        <el-col :span="5">
                          <el-form-item label="租户名称" prop="tenantName">
                            <el-input
                              maxlength="64"
                              style="width: 200px"
                              v-model="loginNameList.tenantName"
                              placeholder="请输入租户名称"
                            />
                          </el-form-item>
                        </el-col>
                        <el-col :span="19">
                          <el-button type="primary" @click="findInfo" style="margin-left: 300px">查询</el-button>
                        </el-col>
                      </el-row>
                    </el-form>
                  </div>
                </el-card>

                <el-card class="boxCard">
                  <div class="text-item">
                    <el-form
                      ref="lists"
                      :model="lists"
                      class="tenantInfo"
                      label-position="right"
                      label-width="135px"
                    >
                      <el-row>
                        <el-col :span="12">
                          <el-form-item label="租户名称" prop="tenantName">
                            <el-input
                              v-model="lists.tenantName"
                              maxlength="64"
                              style="width: 200px; color: red"
                              :disabled="true"
                            />
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-form-item label="租户登录名" prop="tenantLoginName">
                            <el-input
                              v-model="lists.tenantLoginName"
                              maxlength="64"
                              style="width: 200px"
                              :disabled="true"
                            />
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-col :span="12">
                          <el-form-item label="租户管理员登录名" prop="displayName">
                            <el-input
                              v-model="lists.displayName"
                              maxlength="64"
                              style="width: 200px"
                              :disabled="true"
                            />
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-form-item label="租户管理员姓名" prop="staffName">
                            <el-input
                              v-model="lists.staffName"
                              maxlength="11"
                              style="width: 200px"
                              :disabled="true"
                            />
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-col :span="12">
                          <el-form-item label="最大用户数" prop="maxStaff">
                            <el-input-number
                              v-model="lists.maxStaff"
                              controls-position="right"
                              style="width: 200px"
                              :disabled="true"
                            />
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-form-item label="有效截止时间" prop="effectiveDate">
                            <el-date-picker
                              v-model="lists.effectiveDate"
                              size="small"
                              type="datetime"
                              style="width: 200px"
                              value-format="yyyy-MM-dd HH:mm:ss"
                              :disabled="true"
                            ></el-date-picker>
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </el-form>
                  </div>
                </el-card>
                <div style="width: 100%">
                  <el-button
                    type="primary"
                    @click="returnIndex"
                    style="margin: 0 auto; display: block"
                  >返 回
                  </el-button>
                </div>
              </div>
              <!-- 租户信息查看结束 底部关闭按钮 -->
              <span slot="footer" class="dialog-footer">
              <el-button type="primary" @click="closeDialog">关闭窗口</el-button>
            </span>
              <!-- 底部关闭按钮结束 -->
            </el-dialog>
            <!-- 忘记密码的弹窗 -->
            <el-dialog
              title="重置密码"
              :visible.sync="open"
              width="500px"
              append-to-body
              :close-on-click-modal="false"
              :close-on-press-escape="false"
            >
              <el-form
                ref="forgetPassWordForm"
                class="forget-form"
                :model="forgetPassWordForm"
                :rules="forgetPassWordRules"
              >
                <el-form-item prop="loginName">
                  <el-input placeholder="请输入需要重置的账号" v-model="forgetPassWordForm.loginName"/>
                </el-form-item>
                <el-form-item prop="cellphone">
                  <el-input
                    v-model="forgetPassWordForm.cellphone"
                    type="text"
                    auto-complete="off"
                    placeholder="手机号"
                  >
                    <svg-icon slot="prefix" icon-class="phone" class="el-input__icon input-icon"/>
                  </el-input>
                </el-form-item>
                <el-form-item prop="verificationCode">
                  <el-input
                    v-model="forgetPassWordForm.verificationCode"
                    auto-complete="off"
                    placeholder="验证码"
                    style="width: 53%"
                  >
                    <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon"/>
                  </el-input>
                  <div class="login-code">
                    <el-button
                      type="text"
                      :disabled="forgetPassWordFormTime > 0"
                      @click.stop.prevent="forGetSmsTime"
                    >
                      {{
                        forgetPassWordFormTime <= 0
                          ? '获取验证码'
                          : `${forgetPassWordFormTime}s 后重新获取`
                      }}
                    </el-button>
                  </div>
                </el-form-item>
                <el-form-item label-width="180px">
                  <el-button type="primary" @click="nextStep">下一步</el-button>
                </el-form-item>
              </el-form>
            </el-dialog>
            <!-- 忘记密码的弹窗2 -->
            <el-dialog
              title="重置密码"
              :visible.sync="next"
              width="500px"
              append-to-body
              :close-on-click-modal="false"
              :close-on-press-escape="false"
            >
              <el-form
                ref="forgetPassWordForm"
                class="forget-form"
                :model="forgetPassWordForm"
                :rules="forgetPassWordRules"
              >
                <el-form-item prop="newPassWord">
                  <el-input
                    v-model="forgetPassWordForm.newPassWord"
                    type="password"
                    auto-complete="off"
                    placeholder="新的密码"
                    @keyup.enter.native="handleLogin"
                  >
                    <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon"/>
                  </el-input>
                </el-form-item>
                <el-form-item label-width="180px">
                  <el-button type="primary" @click="updatePassWord">确定</el-button>
                </el-form-item>
              </el-form>
            </el-dialog>

            <!-- 选择岗位 -->
            <div v-if="switchOrg" class="switch-org">
              <h4 class="switch-title">请选择岗位</h4>
              <div class="switch-body">
                <el-radio
                  style="
                margin-left: 0;
                margin-top: 10px;
                padding: 10px;
                margin-right: 30px;
              "
                  :label="item.orgId"
                  border
                  v-for="item in org"
                  :key="item.orgId"
                  v-model="selectOrgId"
                >{{ item.orgName }}
                </el-radio>
              </div>
              <el-button
                :loading="selectloading"
                size="medium"
                type="primary"
                style="width: 100%; margin-top: 30px"
                @click="loginByOrgId"
              >
                <span v-if="!selectloading">进入系统</span>
                <span v-else>进入系统中...</span>
              </el-button>
            </div>
          </div>
        </div>
        <!--      <div class="login-bottom-img">-->
        <!--        <el-image :src="rightBottom_img"></el-image>-->
        <!--      </div>-->
      </div>
      <!-- <div class="login-bottom">
        <div style="text-align: center; z-index: 999">
          <a href=" " target="_blank" style="color: #303133"
            >鲁ICP备2022000335号-1</a
          >
          <a
            target="_blank"
            href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=37010102001288"
            style="text-decoration: none; height: 20px; line-height: 20px"
          >
            <img src="@/assets/login/ga_icon.png" style="float: left" />
            <p
              style="
                float: left;
                height: 20px;
                line-height: 20px;
                margin: 0px 0px 0px 5px;
              "
            >
              鲁公网安备 37010102001288号
            </p>
          </a>
        </div>
      </div>-->

      <!--  底部  -->
      <div class="el-login-footer">
        <!-- <span
          >Copyright © 2018-2021 Technology Department All Rights Reserved.</span
        >-->
      </div>
    </div>
  </div>
</template>

<script>
import refreshCaptcha from '@/utils/refreshCaptcha'
import UMarquee from '../components/UMarquee'
import { sendSms, jobSelect, add, tenant, configData } from '@/api/login'
import { getVerificationCode, forgottenPassword } from '@/api/system/user'
import SliderCheck from '@/components/SliderCheck'
import doEncrypt from '@/utils/crypto'
import { setToken } from '@/utils/auth'
// import { getPersonalConfig, statSpeed } from "@/api/system/config";
import { mapState } from 'vuex'
import { checkLoginSes, getTokenBySession } from '@/api/sw/analysis/analysis'

export default {
  name: 'Login',
  components: {
    UMarquee,
    SliderCheck
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.user.userInfo
    })
  },
  data() {
    return {
      defaultStyle: {},
      show: '',
      // 租户名称是否显示
      showTenant: '',
      // background_img: require("../assets/images/ghLTImg/bg.png"),
      // 左侧顶部图
      // leftTop_img: require("../assets/images/ghLTImg/login-left-top2.png"),
      // 左侧顶部图
      leftBottom_img: require('../assets/images/ghLTImg/login-left-bottom.png'),
      // 右侧底部图片
      rightBottom_img: require('../assets/images/LTImg/bottom.png'),
      // 右侧顶部左边图片
      // rightTopLeft_img: require("../assets/images/LTImg/Oval-left.png"),
      // 右侧顶部右边图片
      // rightTopTitle_img: require("../assets/images/ghLTImg/title.png"),
      userIcon: require('../assets/images/ghLTImg/user1.png'),
      passwordIcon: require('../assets/images/ghLTImg/password1.png'),
      captchaIcon: require('../assets/images/ghLTImg/captcha1.png'),
      // 忘记密码开关
      forgetPassword: 1,
      // 租户申请开关
      tenantApply: 1,
      // 手机号登录开关
      //telLogin: '1',
      // 系统标题
      systemTitle: '',
      iconLoading: false,
      current: 0,
      steps: [
        {
          title: '用户选择入口',
          content: false
        },
        {
          title: '填写申请信息',
          content: false
        },
        {
          title: '等待审核',
          content: false
        }
      ],
      //忘记密码
      forgetPassWordForm: {
        loginName: '',
        newPassWord: '',
        cellphone: '',
        verificationCode: ''
      },
      next: false,
      open: false,
      noticeLists: [],
      codeUrl: '',
      errorMsgs: '',
      errorMsg: '',
      loginForm: {
        uni_name: '',
        inpk: '',
        captcha: '',
        token: '',
        phone: '',
        tenantLoginName: 'system',
        loginType: 'pc_web',
        tenantId: 'system',
        pgp: '' //随机数
      },
      loginNameList: {
        tenantName: ''
      },
      rules: {
        tenantName: [
          { required: true, message: '租户名称不能为空', trigger: 'blur' }
        ],
        tenantLoginName: [
          { required: true, message: '租户登录名不能为空', trigger: 'blur' }
        ],
        staffName: [
          {
            required: true,
            message: '租户管理员姓名不能为空',
            trigger: 'blur'
          }
        ],
        maxStaff: [
          { required: true, message: '最大用户数不能为空', trigger: 'blur' }
        ],
        effectiveDate: [
          { required: true, message: '有效截止时间不能为空', trigger: 'blur' }
        ],
        displayName: [
          {
            required: true,
            message: '租户管理员登录名不能为空',
            trigger: 'blur'
          }
        ],
        orgName: [
          { required: true, message: '组织名称不能为空', trigger: 'blur' }
        ],
        kind: [{ required: true, message: '请选择组织类型', trigger: 'blur' }],
        code: [
          { required: true, message: '组织编码不能为空', trigger: 'blur' }
        ],
        cellphone: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur'
          }
        ]
      },
      forgetPassWordRules: {
        loginName: [
          { required: true, trigger: 'blur', message: '用户名不能为空' }
        ],
        cellphone: [
          { required: true, trigger: 'blur', message: '手机号不能为空' },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur'
          }
        ],
        verificationCode: [
          { required: true, trigger: 'blur', message: '验证码不能为空' }
        ],
        newPassWord: [
          { required: true, trigger: 'blur', message: '新密码不能为空' }
        ]
      },
      userLoginRules: {
        uni_name: [
          { required: true, trigger: 'blur', message: '用户名不能为空' }
        ],
        inpk: [{ required: true, trigger: 'blur', message: '密码不能为空' }]
        // captcha: [{ required: true, message: "请输入验证码", trigger: "blur" }],
      },
      phoneLoginRules: {
        captcha: [
          { required: true, trigger: 'blur', message: '验证码不能为空' }
        ],
        phone: [
          { required: true, trigger: 'blur', message: '手机号不能为空' },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur'
          }
        ]
      },
      loading: false,
      redirect: undefined,
      loginStatus: false,
      loginType: 'web',
      smsTime: 0,
      forgetPassWordFormTime: 0,
      timer: null,
      forgetPassWordFormTimer: null,
      switchOrg: false,
      org: [],
      selectOrgId: undefined,
      selectloading: false,
      dialogVisibleOne: false,
      dialogVisible: false,
      lists: {
        tenantSaveType: 'settled'
      },
      labelPosition: 'left',
      information: false,
      finish: false,
      tenantEnter: true,
      applys: false,
      fillInformation: true,
      // 获取验证码api
      captchaApi: process.env.VUE_APP_BASE_API + '/auth/captcha',
      // 获取验证码url
      captchaUrl: undefined,
      url: '',
      // 路由参数等于号后的值
      url_tenantName: '',
      registerSwitch: ''
    }
  },
  watch: {
    //监听是否输入正确的租户名称参数
    url: function(newVal, oldVal) {
      console.log(this.$route)
      if (
        newVal.indexOf('tenantName') === -1 &&
        newVal.indexOf('redirect') === -1
      ) {
        this.requestError()
        this.$message.error('参数拼错！请输入正确参数(tenantName)')
      }
    },
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    },
    'loginForm.tenantLoginName': function(newVal, oldVal) {
      this.errorMsg = ''
    },
    'loginForm.uni_name': function(newVal, oldVal) {
      this.checkReset()
      this.errorMsg = ''
    },
    'loginForm.inpk': function(newVal, oldVal) {
      this.checkReset()
      this.errorMsg = ''
    },
    'loginForm.captcha': function(newVal, oldVal) {
      this.errorMsg = ''
    }
  },
  beforeCreate() {
    let _this = this
    checkLoginSes().then(res => {
      console.log('login session:', res)
      if (res.data.authSessionId) {
        getTokenBySession({ authSessionId: res.data.authSessionId }).then((res) => {
          console.log('请求session信息:', res)
          if (res['success'] === 'true') {
            localStorage.setItem('unifast_token', res.data)
            _this.$router.push('/sw/firstIndex')
          }
        }).catch(() => {
          this.$message.error('未获取到认证信息,请重新登录')
        })
      } else if (process.env['VUE_APP_SSO_LOGIN']) {
        window.location.href = process.env['VUE_APP_SSO_LOGIN']
      }
    }).catch(() => {
      if (process.env['VUE_APP_SSO_LOGIN']) {
        window.location.href = process.env['VUE_APP_SSO_LOGIN']
      }
    })
  },
  created() {
    // this.getCode();
    // this.refreshCaptcha();

  },
  beforeMount() {
    this.getTanentIds()
    this.judgeParameter()
  },
  mounted() {
    //设置动态 缩小比
    this.defaultStyle = {
      // "-webkit-transform": "scale("+this.$store.state.sceenRate+")"
      '-webkit-transform': 'scale(1)'
    }
    if (this.$route.query.errorMsg) {
      this.$message.error(this.$route.query.errorMsg)
    }
    let ssoLoginAddr = process.env['VUE_APP_SSO_LOGIN']

    if (store.getters.token) {
      this.$router.push('/sw/')
    }
    if (ssoLoginAddr) {
  //    window.location.href = ssoLoginAddr
    }
    //window.location.href=process.env['VUE_APP_SSO_LOGIN ']
  },
  methods: {
    // 切换租户
    changeTenant() {
      document.getElementById('untenantLoginName').style.display = 'block'
      this.loginForm.tenantLoginName = ''
    },
    // 获取路由参数等于号后的值
    judgeParameter() {
      if (this.url.indexOf('redirect') !== -1) {
        var test = this.url.indexOf('&')
        var length = this.url.length
        this.url_tenantName = this.url.substring(test + 12, length)
      } else {
        var test = this.url.indexOf('=')
        var length = this.url.length
        this.url_tenantName = this.url.substring(test + 1, length)
      }
    },
    // 获取动态配置数据
    getConfigData() {
      configData({
        clientType: '1',
        tenantId: this.loginForm.tenantId
      }).then((res) => {
        // this.$nextTick(() => {
        if (res.success === true) {
          res.data.forEach((element) => {
            if (element.configCode === 'login_tenant_left_img') {
              this.left_img = element.configValue
            } else if (element.configCode === 'login_tenant_rightBottom_img') {
              this.rightBottom_img = element.configValue
            } else if (element.configCode === 'login_tenant_rightTopLeft_img') {
              this.rightTopLeft_img = element.configValue
            } else if (
              element.configCode === 'login_tenant_rightTopRight_img'
            ) {
              this.rightTopRight_img = element.configValue
            } else if (element.configCode === 'login_tenant_forgetPassword') {
              this.forgetPassword = element.configValue
            } else if (element.configCode === 'login_tenant_tenantApply') {
              this.tenantApply = element.configValue
            } else if (element.configCode === 'login_tenant_telLogin') {
              this.telLogin = element.configValue
            } else if (element.configCode === 'tenant_title') {
              this.systemTitle = element.configValue
              // 设置网站标题
              document.title = this.systemTitle
            } else if (element.configCode === 'login_tenant_registerSwitch') {
              this.registerSwitch = element.configValue
            }
          })
        } else {
          this.$nextTick(() => {
            this.requestError()
          })
        }
        // });
      })
    },
    // 请求失败和默认值
    requestError() {
      this.$nextTick(() => {
        (this.left_img = require('../assets/images/LTImg/login-left.jpg')),
          (this.rightTopLeft_img = require('../assets/images/LTImg/Oval-left.png')),
          (this.rightTopRight_img = require('../assets/images/LTImg/Oval-left.png')),
          (this.rightBottom_img = require('../assets/images/LTImg/bottom.png')),
          (this.forgetPassword = 1),
          (this.telLogin = 1),
          (this.tenantApply = 1)
      })
    },
    // 判断路由参数中是否有tentantName
    getTanentIds() {
      this.url = location.search //获取url中"?"符后的字串
      if (this.url.indexOf('tenantName') !== -1) {
        var tantentData = new Object()
        if (this.url.indexOf('?') !== -1) {
          var str = this.url.substr(1) //substr()方法返回从参数值开始到结束的字符串；
          var strs = str.split('&')
          for (var i = 0; i < strs.length; i++) {
            tantentData[strs[i].split('=')[0]] = strs[i].split('=')[1]
          }
          this.loginForm.tenantLoginName = tantentData.tenantName
          this.getTenantId()
        }
      }
    },
    // 刷新验证码
    refreshCaptcha() {
      this.captchaUrl = `${this.captchaApi}`
      this.loginForm.captcha = ''
    },
    // 租户提交申请
    submit(submitList) {
      this.$refs['submitList'].validate((valid) => {
        if (valid) {
          this.iconLoading = true
          if (this.lists !== '') {
            add(this.lists).then((response) => {
              if (response.success === true) {
                this.iconLoading = false
                this.current++
                if (this.current === 0) {
                  this.steps[this.current].content = true
                } else if (this.current === 1) {
                  this.steps[this.current].content = true;
                  (this.steps[0].content = false),
                    (this.steps[2].content = false)
                } else if (this.current === 2) {
                  this.steps[this.current].content = true;
                  (this.steps[0].content = false),
                    (this.steps[1].content = false)
                }
              } else {
                this.$message.error(response.message)
                this, (this.iconLoading = false)
              }
            })
          }
        } else {
          return false
        }
      })
    },
    // 返回上一步
    prev() {
      this.current--
      if (this.current === 2) {
        this.steps[this.current].content = true
      } else if (this.current === 1) {
        this.steps[this.current].content = true;
        (this.steps[0].content = false), (this.steps[2].content = false)
      } else if (this.current === 0) {
        this.steps[this.current].content = true;
        (this.steps[1].content = false), (this.steps[2].content = false)
      }
    },
    //查询用户信息
    findInfo() {
      tenant({ tenant: this.loginNameList.tenantName }).then((res) => {
        if (res.data !== null) {
          this.lists = res.data
        } else {
          this.$message.error('无此租户，请核实后再输入')
        }
      })
    },
    // 根据路由参数获取租户名 获取该id 表单中（用户输入完毕后去请求tenantId）
    getTenantId() {
      if (this.loginForm.tenantLoginName) {
        this.errorMsgs = ''
        tenant({ tenant: this.loginForm.tenantLoginName }).then(
          (res) => {
            if (res.success === true && res.data !== null) {
              this.loginForm.tenantId = res.data.tenantId
              this.loginForm.tenantLoginName = res.data.tenantLoginName
              this.getConfigData()
            } else {
              this.requestError()
              this.loginForm.tenantLoginName = undefined
              this.loginForm.tenantId = undefined
              this.$message.error('该租户名有误或已过期，请重新输入')
            }
          }
        )
      }
    },
    // 根据用户输入获取租户名 获取该id 表单中（用户输入完毕后去请求tenantId）
    getUserTenantId() {
      if (this.loginForm.tenantLoginName) {
        this.errorMsgs = ''
        tenant({ tenant: this.loginForm.tenantLoginName }).then(
          (res) => {
            if (res.success === true && res.data !== null) {
              this.loginForm.tenantId = res.data.tenantId
              this.loginForm.tenantLoginName = res.data.tenantLoginName
            } else {
              this.$nextTick(() => {
                this.requestError()
              })
              this.loginForm.tenantLoginName = undefined
              this.loginForm.tenantId = undefined
              this.errorMsgs = '该租户名有误或已过期，请重新输入'
            }
          }
        )
      }
    },
    // 返回租户申请页面
    returnIndex() {
      (this.lists = {
        tenantName: '',
        tenantLoginName: '',
        displayName: '',
        staffName: '',
        maxStaff: '',
        effectiveDate: ''
      }),
        (this.applys = false),
        (this.fillInformation = true)
    },
    // 进入租户信息展示页面
    applying() {
      (this.lists = {
        tenantName: '',
        tenantLoginName: '',
        displayName: '',
        staffName: '',
        maxStaff: '',
        effectiveDate: '',
        orgName: '',
        code: ''
      }),
        (this.applys = true),
        (this.fillInformation = false)
    },
    // 关闭el-dialog弹窗
    closeDialog() {
      this.dialogVisible = false
    },
    // 返回上一步
    back() {
      (this.information = false), (this.tenantEnter = true)
    },
    // 新申请
    newApp() {
      this.steps[1].content = true
      this.steps[0].content = false
      this.steps[2].content = false
      this.current = 1
    },
    // 点击弹窗外的区域关闭弹窗
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then((_) => {
          done()
        })
        .catch((_) => {
        })
    },
    // 租户申请的弹窗
    tenant() {
      this.dialogVisible = true
      this.current = 0
      this.dialogVisible = true
      this.steps[0].content = true
      this.steps[1].content = false
      this.steps[2].content = false
    },
    // 忘记密码弹窗的打开
    handleForget() {
      this.open = true
    },
    // 下一步
    nextStep() {
      this.$refs['forgetPassWordForm'].validateField(
        ['loginName', 'cellphone', 'verificationCode'],
        (valid) => {
          if (!valid) {
            this.next = true;
            (this.title = '重置密码'), (this.open = false)
          }
        }
      )
    },
    getCode() {
      this.loginForm.token = refreshCaptcha()
      this.codeUrl = `/dev-api/auth/captcha?token=${this.loginForm.token}`
    },// 切换tab标签
    handleClick(tab, event) {
      this.errorMsg = ''
    },
    // 账号登录
    handleLogin() {
      this.$refs.userLoginForm.validate((valid) => {
        if (valid) {
          this.loading = true

          let max = 12,
            min = 9,
            stra = '',
            arr =
              '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ' // 随机值的长度
          const range = Math.round(Math.random() * (max - min)) + min

          //随机数值的产生
          for (var i = 0; i < range; i++) {
            const random = Math.round(Math.random() * (arr.length - 1))
            stra = stra + arr[random]
            this.loginForm.pgp = stra
          }

          this.$store
            .dispatch('Login', this.loginForm)
            .then((data) => {
              if (
                data &&
                data.additionalInformation &&
                data.additionalInformation.userJobDetailVOList &&
                Array.isArray(data.additionalInformation.userJobDetailVOList) &&
                data.additionalInformation.userJobDetailVOList.length > 1
              ) {
                this.switchOrg = true
                document.getElementById('title').style.display = 'none'
                this.org = data.additionalInformation.userJobDetailVOList

                //排序  按照isMaster的大小排序
                function compare(pro) {
                  return function(a, b) {
                    var topValue = a[pro]
                    var bottomValue = b[pro]
                    return topValue - bottomValue
                  }
                }

                this.org = this.org.sort(compare('staffOrgType'))
                //如果isMaster全等于0  就拼接字符串
                for (var i = 0; i < this.org.length; i++) {
                  if (this.org[i].staffOrgType === 'F') {
                    this.org[i].orgName += '(主岗)'
                  }
                }
              } else {
                // 跳转到登录后的主页
                //this.$router.push("/system/base/home")
                this.$router.push('/sw/')

              }
            })
            .catch((msg) => {
              this.$message.error(msg)
              this.refreshCaptcha()
              this.loading = false
            })
        }
      })
    },
    // 手机号短信验证登录
    handlePhoneLogin() {
      this.$refs.phoneLoginForm.validate((valid) => {
        if (valid) {
          this.loading = true
          this.$store
            .dispatch('smscodeLogin', this.loginForm)
            .then((res) => {
              if (
                res &&
                res.additionalInformation &&
                res.additionalInformation.userJobDetailVOList &&
                Array.isArray(res.additionalInformation.userJobDetailVOList) &&
                res.additionalInformation.userJobDetailVOList.length > 1
              ) {
                this.switchOrg = true
                document.getElementById('title').style.display = 'none'
                this.org = res.additionalInformation.userJobDetailVOList

                //排序  按照isMaster的大小排序
                function compare(pro) {
                  return function(a, b) {
                    var topValue = a[pro]
                    var bottomValue = b[pro]
                    return topValue - bottomValue
                  }
                }

                this.org = this.org.sort(compare('staffOrgType'))
                //如果isMaster全等于0  就拼接字符串
                for (var i = 0; i < this.org.length; i++) {
                  if (this.org[i].staffOrgType === '0') {
                    this.org[i].orgName += '(主岗)'
                  }
                }
              } else {
                location.href = this.redirect || '/portal/home'
              }
            })
            .catch((msg) => {
              this.errorMsg = msg
              this.checkReset()
              this.loading = false
              this.getCode()
            })
        }
      })
    },
    checkReset() {
      this.loginStatus = false
    },
    // 发送验证码
    sendSms() {
      this.$refs.phoneLoginForm.validateField('phone', (valid) => {
        if (!valid) {
          this.smsLoading = true
          sendSms({
            authkey: doEncrypt(`0@bVS46ElU@${this.loginForm.phone}`)
          }).then((r) => {
            this.smsLoading = true
            if (r.success) {
              if (r.data.resultcode === 0) {
                this.msgSuccess('验证码发送成功！')
                const TIME_COUNT = 60
                if (!this.timer) {
                  this.smsTime = TIME_COUNT
                  this.show = false
                  this.timer = setInterval(() => {
                    if (this.smsTime > 0 && this.smsTime <= TIME_COUNT) {
                      this.smsTime--
                    } else {
                      this.show = true
                      clearInterval(this.timer)
                      this.timer = null
                    }
                  }, 1000)
                }
              } else if (r.data.resultcode === 4) {
                this.$message.error(r.data.resultmsg)
              } else if (r.data.resultcode === 6) {
                this.$message.error(r.data.resultmsg)
              }
            } else {
              this.msgError(r.message)
            }
          })
        }
      })
    },
    // 选择岗位后登陆
    loginByOrgId() {
      if (!this.selectOrgId) {
        this.msgError('请选择岗位')
        return
      }
      this.selectloading = true
      const form = new FormData()
      form.append('orgId', this.selectOrgId)
      jobSelect(form).then((r) => {
        this.selectloading = false
        if (r.success) {
          setToken(r.data.value)
          location.href = this.redirect || '/portal/home'
        } else {
          this.msgError('数据异常')
        }
      })
    },
    // 手机号登录中的获取验证码
    forGetSmsTime() {
      this.$refs['forgetPassWordForm'].validateField(
        ['loginName', 'cellphone'],
        (valid) => {
          if (!valid) {
            getVerificationCode({
              ...this.forgetPassWordForm,
              getVerificationCodeType: '1'
            }).then((r) => {
              if (r.success) {
                this.msgSuccess('验证码发送成功')
                const TIME_COUNT = 60
                if (!this.forgetPassWordFormTimer) {
                  this.forgetPassWordFormTime = TIME_COUNT
                  this.show = false
                  this.forgetPassWordFormTimer = setInterval(() => {
                    if (
                      this.forgetPassWordFormTime > 0 &&
                      this.forgetPassWordFormTime <= TIME_COUNT
                    ) {
                      this.forgetPassWordFormTime--
                    } else {
                      this.show = true
                      clearInterval(this.forgetPassWordFormTimer)
                      this.forgetPassWordFormTimer = null
                    }
                  }, 1000)
                }
              } else {
                this.msgError(r.message)
              }
            })
          }
        }
      )
    },
    // 忘记密码-提交
    updatePassWord() {
      this.$refs['forgetPassWordForm'].validateField(
        ['newPassWord'],
        (valid) => {
          if (!valid) {
            forgottenPassword({
              ...this.forgetPassWordForm,
              newPassWord: doEncrypt(this.forgetPassWordForm.newPassWord)
            }).then((r) => {
              if (r.success) {
                this.msgSuccess('密码修改成功')
                this.next = false
              } else {
                this.msgError(r.message)
              }
            })
          }
        }
      )
    },
    forgetPassWordFormClose() {
      this.resetForm('forgetPassWordForm')
    },
    // 注册
    register() {
      window.open(`register`)
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../layout/portal/components/index.scss";
</style>
