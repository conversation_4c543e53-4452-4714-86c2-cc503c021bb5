<script>
import Navbar from '../../components/Navbar/index.vue'
export default {
  name: 'menus',
  methods:{
    toMenu(_path){
      this.$router.push(_path)
    }
  },
  components:{
    Navbar
  }
}
</script>

<template>

  <div class="portal-body">
    <!-- <Navbar /> -->
    <!-- <div class="menus-body">
      
      <ul class="menus">
        <li class="menus-item">
          <div class="title">
            <div class="text">
              策略运营中心
            </div>
          </div>
          <ul>
            <li @click="toMenu('/sw/opportunity')">
              <div class="content">
                <span class="icon-star">&#9733;</span>
                <div class="content-text">潜在机会</div>
              </div>
            </li>
            <li @click="toMenu('/sw/strategyRecommend')">
              <div class="content">
                <span class="icon-star">&#9733;</span>
                <div class="content-text">策略推荐结果</div>
              </div>
            </li>
          </ul>
        </li>
        <li class="menus-item">
          <div class="title">
            <div class="text">
              产品中心
            </div>
          </div>
          <ul>
            <li @click="toMenu('/sw/productManage')">
              <div class="content">
                <span class="icon-star">&#9733;</span>
                <div class="content-text">产品管理</div>
              </div>
            </li>
          </ul>
        </li>
        <li class="menus-item">
          <div class="title">
            <div class="text">
              客户管理
            </div>
          </div>
          <ul>
            <li @click="toMenu('/sw/customer')">
              <div class="content">
                <span class="icon-star">&#9733;</span>
                <div class="content-text">客户打标</div>
              </div>
            </li>
          </ul>
        </li>
      </ul>
    </div> -->
    <div class="wrapper">
      1
      <!-- <router-view></router-view> -->
    </div>
    
  </div>
</template>

<style scoped lang="less">
@import "../../assets/sanquan/index";
</style>
