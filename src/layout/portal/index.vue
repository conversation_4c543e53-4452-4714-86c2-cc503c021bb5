<template>
  <div>
    <div class="fixed-header">
      <navbar />
    </div>
    <app-main />
    <!-- <sidebar v-show="!hidennSidebar" class="potal-sidebar-container" /> -->
    <!-- <div
      class="potal-main-container"
      :class="!hidennSidebar ? 'potal-main-container-left' : ''"
      id="1.23452123412415384164.123412415"
    > -->

    <!-- </div> -->
    <!-- <el-backtop target=".potal-main-container"></el-backtop> -->
    <change-pwd />
  </div>
</template>

<script>
import { AppMain, Navbar, Sidebar } from "./components";
import { mapState } from "vuex";
import ChangePwd from "../changePWD";
import Watermark from "@/utils/warterMark";
import { getPersonalConfig, statSpeed } from "@/api/system/config";
export default {
  name: "Layout",
  components: {
    AppMain,
    Navbar,
    Sidebar,
    ChangePwd,
  },
  computed: {
    ...mapState({
      hidennSidebar: (state) => state.app.hidennSidebar,
      userInfo: (state) => state.user.userInfo,
    }),
  },
  data() {
    return {
      pathname: "",
    };
  },
  watch: {
    "$route.path": function (newVal, oldVal) {
      this.pathname = newVal;
    },
  },
  created() {},
  mounted() {
    if (this.userInfo.userid) {
      getPersonalConfig({
        configCode: "Watermark",
      }).then((r) => {
        if (r.success) {
          try {
            const obj = JSON.parse(r.data.attra);
            Object.keys(this.userInfo).map((v) => {
              obj.content = obj.content.replace(`$${v}$`, this.userInfo[v]);
            });
            if (obj.status) {
              Watermark.set(obj);
              Watermark.setH5(obj);
            }
          } catch (error) {}
        } else {
          this.$message.error(r.message);
        }
      });
      statSpeed({}).then((r) => {
        if (r.success) {
          try {
            Watermark.setBase64(r.data);
          } catch (error) {}
        }
      });
    }
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/mixin.scss";
@import "~@/assets/styles/variables.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 999;
  // width: calc(100% - #{$sideBarWidth});
  width: 100%;
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: 100%;
}

.mobile .fixed-header {
  width: 100%;
}
</style>
