<template>
  <div>
    <el-dialog
      title="该密码为初始化密码，请修改密码！"
      :visible="userInfo.userPwdFlag === 1"
      width="30%"
      :show-close="false"
      :before-close="() => {}"
    >
      <el-form
        :model="ruleForm"
        status-icon
        :rules="rules"
        ref="pwdForm"
        label-width="100px"
      >
        <el-form-item label="旧密码" prop="oldPassword">
          <el-input
            type="password"
            v-model="ruleForm.oldPassword"
            autocomplete="off"
            :show-password="true"
          ></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="password">
          <el-input
            type="password"
            v-model="ruleForm.password"
            autocomplete="off"
            :show-password="true"
          ></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="prePassword">
          <el-input
            type="password"
            v-model="ruleForm.prePassword"
            autocomplete="off"
            :show-password="true"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button type="primary" @click="submit" :loading="loading"
        >提交</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState,mapGetters } from "vuex";
import { updateUserPwd } from "@/api/login";
import doEncrypt from "@/utils/crypto";
export default {
  name: "changePWD",
  data() {
    var validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.ruleForm.password) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      ruleForm: {
        password: "",
        prePassword: "",
        oldPassword: "",
      },
      rules: {
        password: [
          { required: true, message: "请输入新密码", trigger: "blur" },
          { min: 6, max: 20, message: "长度在 6 到 20 个字符", trigger: "blur" }
        ],
        prePassword: [
          { required: true, message: "请再次输入新密码", trigger: "blur" },
          { validator: validatePass, trigger: "blur" },
        ],
        oldPassword: [
          { required: true, message: "请输入旧密码", trigger: "blur" },
        ],
      },
      loading: false,
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.user.userInfo,
    }),
  },

  methods: {
    submit() {
      this.$refs["pwdForm"].validate((valid) => {
        if (valid) {
          this.loading = true;
          updateUserPwd({
            newPassword: doEncrypt(this.ruleForm.prePassword),
            oldPassword: doEncrypt(this.ruleForm.oldPassword),
            confirmPassword: doEncrypt(this.ruleForm.prePassword),
          }).then((r) => {
            this.loading = false;
            if (r.success) {
              this.$alert("密码更新成功，请重新登陆！", "密码更新成功", {
                confirmButtonText: "去登陆",
                showClose: false,
                callback: (action) => {
                  this.$store.dispatch("LogOut").then(() => {
                    var tenantName = this.$store.getters.customParam.tenantLoginName;
                    this.$router.push({path:`/login`,query:{tenantName:tenantName}})
                    // location.href = `/login?redirect=${location.pathname}`;
                  });
                },
              });
            } else {
              this.$message.error(r.message);
            }
          });
        } else {
          return false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
