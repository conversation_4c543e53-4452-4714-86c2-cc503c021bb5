<template>
  <div class="app-container">
    <el-card shadow="never">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
        @submit.native.prevent
      >
        <el-form-item
          v-if="$store.getters.customParam.userType === 'admin'"
          label="租户"
        >
          <el-select
            v-model="queryParams.tenantId"
            style="width: 200px"
            filterable
            remote
            :remote-method="getTenantList"
            :loading="getTenantLoading"
            @change="tenantChange(queryParams.tenantId)"
            size="small"
          >
            <el-option
              v-for="item in tenantList"
              :key="item.tenantId"
              :label="item.tenantName"
              :value="item.tenantId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="应用名称" prop="softName">
          <el-select
            v-model="queryParams.softName"
            placeholder="应用名称"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in softNameOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="版本号" prop="softVersion">
          <el-input
            v-model="queryParams.softVersion"
            placeholder="请输入版本号"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="应用平台" prop="softPlat">
          <el-select
            v-model="queryParams.softPlat"
            placeholder="应用平台"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in softPlatOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="应用类型" prop="softType">
          <el-select
            v-model="queryParams.softType"
            placeholder="应用类型"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in softTypeOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="打包方式" prop="packType">
          <el-select
            v-model="queryParams.packType"
            placeholder="打包方式"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in packTypeOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="版本更新类型" prop="updateType">
          <el-select
            v-model="queryParams.updateType"
            placeholder="版本更新类型"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in updateTypeOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否绑定其他应用" prop="isBindOrtherSoft">
          <el-select
            v-model="queryParams.isBindOrtherSoft"
            placeholder="是否绑定其他应用"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in isBindOrtherSoftOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            v-hasPermi="['system:notice:add']"
            @click="handleAdd"
            >新增
          </el-button>
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="versionList">
        <el-table-column label="序号" type="index" width="50" />
        <el-table-column
          label="参数主键"
          align="left"
          prop="softId"
          v-if="false"
        />
        <el-table-column
          label="应用名称"
          align="left"
          prop="softName"
          :show-overflow-tooltip="true"
          :formatter="softNameFormat"
        />
        <el-table-column
          label="应用平台"
          align="left"
          prop="softPlat"
          :show-overflow-tooltip="true"
          :formatter="softPlatFormat"
        />
        <el-table-column
          label="应用类型"
          align="left"
          prop="softType"
          :show-overflow-tooltip="true"
          :formatter="softTypeFormat"
        >
          <!-- <template slot-scope="scope">
            <el-tag
              effect="plain"
              :type="
                selectDictRemark(noticePriorityOptions, scope.row.softType)
              "
            >
              {{ selectDictLabel(scope.row.softType) }}
            </el-tag>
          </template> -->
        </el-table-column>
        <el-table-column
          label="应用版本号"
          align="left"
          prop="softVersion"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="打包方式"
          align="left"
          prop="packType"
          :show-overflow-tooltip="true"
          :formatter="packTypeFormat"
        />
        <el-table-column
          label="版本更新类型"
          align="left"
          prop="updateType"
          :show-overflow-tooltip="true"
          :formatter="updateTypeFormat"
        >
          <template slot-scope="scope">
            <el-tag
              effect="plain"
              :type="
                selectDictRemark(updateTypeOptions, scope.row.updateType)
              "
            >
              {{ selectDictLabel(updateTypeOptions,scope.row.updateType)}}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="是否绑定其他应用"
          prop="isBindOrtherSoft"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-tag
              :type="
                selectDictLabel(
                  isBindOrtherSoftOptions,
                  scope.row.isBindOrtherSoft
                ) === '已绑定'
                  ? 'success'
                  : ''
              "
            >
              {{
                selectDictLabel(
                  isBindOrtherSoftOptions,
                  scope.row.isBindOrtherSoft
                )
              }}
            </el-tag>
            <el-button
              v-if="scope.row.isBindOrtherSoft != 0"
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="lookBindList(scope.row)"
              >绑定列表
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-position"
              @click="
                updateBindStatus(scope.row, scope.row.isBindOrtherSoft == '0')
              "
              :loading="loadId === scope.row.versionId"
              >{{ scope.row.isBindOrtherSoft == "0" ? "绑定" : "解绑" }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.noticeStatus != 'notice_status_release'"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              :loading="editLoadId === scope.row.versionId"
              >修改
            </el-button>
            <el-button
              v-if="scope.row.noticeStatus != 'notice_status_release'"
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              :loading="delLoadId === scope.row.versionId"
              >删除
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              :loading="detailLoadId === scope.row.versionId"
              @click="detail(scope.row)"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改版本信息 -->
      <el-dialog
        :title="title"
        :visible.sync="open"
        width="980px"
        append-to-body
        v-dialogDrag
      >
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="150px"
          :disabled="formDisabled"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item
                label="应用名称"
                prop="softName"
              >
                <el-select
                  v-model="form.softName"
                  placeholder="请选择应用名称"
                  :disabled="versionDisabled"
                >
                  <el-option
                    v-for="dict in softNameOptions"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="版本号"
                prop="softVersion"
              >
                <el-input
                  v-model="form.softVersion"
                  placeholder="请输入版本号"
                  :disabled="versionDisabled"
                  style="width:220px"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="应用平台"
                prop="softPlat"
              >
                <el-select
                  v-model="form.softPlat"
                  placeholder="请选择应用平台"
                  :disabled="versionDisabled"
                >
                  <el-option
                    v-for="dict in softPlatOptions"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="应用类型"
                prop="softType"
              >
                <el-select
                  v-model="form.softType"
                  placeholder="请选择应用类型"
                  :disabled="versionDisabled"
                >
                  <el-option
                    v-for="dict in softTypeOptions"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="this.form.softType == 'app'">
              <el-form-item label="包名" prop="packName">
                <el-input
                  v-model="form.packName"
                  placeholder="请输入包名"
                  :disabled="versionDisabled"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="this.form.softType == 'certificate' && this.form.expirationDate != undefined">
              <el-form-item label="证书到期时间" prop="expirationDate">
                <el-input
                  v-model="form.expirationDate"
                  :disabled="true"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="打包方式"
                prop="packType"
              >
                <el-select v-model="form.packType" placeholder="打包方式">
                  <el-option
                    v-for="dict in packTypeOptions"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="租户">
                <el-input
                  v-model="form.tenantName"
                  placeholder="租户"
                  maxlength="50"
                  disabled
                  style="width:220px"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="版本更新类型"
                prop="updateType"
              >
                <el-select v-model="form.updateType" placeholder="版本更新类型">
                  <el-option
                    v-for="dict in updateTypeOptions"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="是否绑定其他应用"
                prop="isBindOrtherSoft"
              >
                <el-switch
                  v-model="form.isBindOrtherSoft"
                  active-text="绑定其他应用"
                  inactive-text="不绑定"
                  :active-value="1"
                  :inactive-value="0"
                >
                </el-switch>
                &emsp;&emsp;&emsp;&emsp;
                <el-button
                  v-if="form.isBindOrtherSoft"
                  size="mini"
                  type="text"
                  icon="el-icon-position"
                  @click="bindOrtherSoft(form)"
                  >绑定
                </el-button>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="更新日志"
                prop="updateMessage"
              >
                <el-input
                  v-model="form.updateMessage"
                  type="textarea"
                  placeholder="请输入更新日志"
                  style="width:690px"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="应用包上传"
                prop="fileUrl"
              >
                <el-upload
                  ref="softfileUpload"
                  class="upload-demo"
                  action=""
                  :http-request="uploadFile"
                  :on-preview="handlePreview"
                  :on-remove="handleRemove"
                  :before-remove="beforeRemove"
                  :on-exceed="handleExceed"
                  :on-success="handleSueccss"
                  :limit="1"
                  :file-list="fileList"
                >
                  <el-button size="small" type="primary">选择</el-button>
                  <div slot="tip" class="el-upload__tip">
                    只能上传zip/rar/apk/ipa/hpk/wgt/key/csr/crt/pem/pfx/jks/cer文件，且不超过100M
                  </div>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button
            type="primary"
            @click="submitForm"
            :loading="saveLoading"
            :disabled="formDisabled"
            >确 定
          </el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>

      <!-- 绑定其他版本的数据 -->
      <el-dialog
        :title="bindTitle"
        :visible.sync="openBind"
        width="980px"
        append-to-body
        v-dialogDrag
      >
        <el-card class="dep-card" shadow="never" v-loading="bindLoading">
          <el-row v-if="this.lookFormDisabled == false">
            <el-col>
              <el-form
                :model="queryBindParams"
                ref="queryToBindForm"
                :inline="true"
                v-show="showSearch"
              >
                <el-form-item label="应用名称" prop="softName">
                  <el-select
                    v-model="queryBindParams.softName"
                    placeholder="应用名称"
                    clearable
                    size="small"
                  >
                    <el-option
                      v-for="dict in softNameOptions"
                      :key="dict.dictValue"
                      :label="dict.dictLabel"
                      :value="dict.dictValue"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="版本号" prop="softVersion">
                  <el-input
                    v-model="queryBindParams.softVersion"
                    placeholder="请输入版本号"
                    clearable
                    size="small"
                    style="width: 240px"
                    @keyup.enter.native="handleQuery"
                  />
                </el-form-item>
                <el-form-item label="应用平台" prop="softPlat">
                  <el-select
                    v-model="queryBindParams.softPlat"
                    placeholder="应用平台"
                    clearable
                    size="small"
                  >
                    <el-option
                      v-for="dict in softPlatOptions"
                      :key="dict.dictValue"
                      :label="dict.dictLabel"
                      :value="dict.dictValue"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="应用类型" prop="softType">
                  <el-select
                    v-model="queryBindParams.softType"
                    placeholder="应用类型"
                    clearable
                    size="small"
                  >
                    <el-option
                      v-for="dict in softTypeOptions"
                      :key="dict.dictValue"
                      :label="dict.dictLabel"
                      :value="dict.dictValue"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="打包方式" prop="packType">
                  <el-select
                    v-model="queryBindParams.packType"
                    placeholder="打包方式"
                    clearable
                    size="small"
                  >
                    <el-option
                      v-for="dict in packTypeOptions"
                      :key="dict.dictValue"
                      :label="dict.dictLabel"
                      :value="dict.dictValue"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item
                  label="版本更新类型"
                  prop="
                    "
                >
                  <el-select
                    v-model="queryBindParams.updateType"
                    placeholder="版本更新类型"
                    clearable
                    size="small"
                  >
                    <el-option
                      v-for="dict in updateTypeOptions"
                      :key="dict.dictValue"
                      :label="dict.dictLabel"
                      :value="dict.dictValue"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    icon="el-icon-search"
                    size="mini"
                    @click="handleBindQuery"
                    >搜索</el-button
                  >
                  <el-button
                    icon="el-icon-refresh"
                    size="mini"
                    @click="resetBindQuery"
                    >重置</el-button
                  >
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
          <el-table
            v-loading="bindLoading"
            ref="multipleTable"
            :data="bindversionList"
            @selection-change="handleSelectionChange"
            :disabled="lookFormDisabled"
          >
            <el-table-column
              type="selection"
              width="100"
              align="center"
              v-if="this.lookFormDisabled == false"
            />
            <el-table-column
              label="参数主键"
              align="left"
              prop="versionId"
              v-if="false"
            />
            <el-table-column
              label="应用名称"
              align="left"
              prop="softName"
              :show-overflow-tooltip="true"
              :formatter="softNameFormat"
            />
            <el-table-column
              label="应用平台"
              align="left"
              prop="softPlat"
              :show-overflow-tooltip="true"
              :formatter="softPlatFormat"
            />
            <el-table-column
              label="应用类型"
              align="left"
              prop="softType"
              :show-overflow-tooltip="true"
              :formatter="softTypeFormat"
            />
            <el-table-column
              label="应用版本号"
              align="left"
              prop="softVersion"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="打包方式"
              align="left"
              prop="packType"
              :show-overflow-tooltip="true"
              :formatter="packTypeFormat"
            />
            <el-table-column
              label="版本更新类型"
              align="left"
              prop="updateType"
              :show-overflow-tooltip="true"
              :formatter="updateTypeFormat"
            />
          </el-table>
          <pagination
            v-show="bindTotal > 0"
            :total="bindTotal"
            :page.sync="queryBindParams.pageNum"
            :limit.sync="queryBindParams.pageSize"
            @pagination="getBindList"
          />
        </el-card>
        <div slot="footer" class="dialog-footer">
          <el-button
            type="primary"
            @click="doBindOrtherSoft"
            :loading="saveBindLoading"
            :disabled="lookFormDisabled"
            >确 定
          </el-button>
          <el-button @click="cancelBind">取 消</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import {
  listVersion,
  bindListVersion,
  selectVersionByVersionId,
  addVersion,
  validCert,
  delVersion,
  updateVersion,
} from "@/api/system/version";
import { list as tenantList } from "@/api/system/tenant";
import { uploadSingle } from "@/api/tool/upload";

export default {
  name: "Version",
  data() {
    //包名规则验证
    var checkPackName = (rule ,value,callback)=>{
      console.log("this.form.softType:"+this.form.softType);
      if(this.form.softType !="app") {
        callback()
      } else {
        const reg = /[a-zA-Z]+[0-9a-zA-Z_]*(\.[a-zA-Z]+[0-9a-zA-Z_]*)*/;
        if(reg.test(value)) {
          callback()
        } else {
          return callback(new Error('app应用类型需配置指定的正确包名！'))
        }
      }
    }
    return {
      // 遮罩层
      loading: true,
      // 遮罩层
      bindLoading: false,
      // 导出遮罩层
      exportLoading: false,
      getTenantLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 总条数
      bindTotal: 0,
      // 参数表格数据
      versionList: [],
      // 参数表格数据
      bindversionList: [],
      // 参数表格数据
      bindList: [],
      // 弹出层标题
      title: "",
      // 弹出层标题
      title2: "",
      bindTitle: "",
      // 是否显示弹出层
      open: false,
      // 是否显示绑定的弹出层
      openBind: false,
      // 类型数据字典
      typeOptions: [],
      //已绑定的版本列表
      arr: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        softName: undefined,
        softPlat: undefined,
        softType: undefined,
        softVersion: undefined,
        tenantId: this.$store.getters.customParam.tenantId,
        tenantName: this.$store.getters.customParam.tenantName,
      },
      // 查询要绑定的数据参数
      queryBindParams: {
        pageNum: 1,
        pageSize: 10,
        softName: undefined,
        softPlat: undefined,
        softType: undefined,
        softVersion: undefined,
      },
      // 表单参数
      form: {},
      // 表单参数
      oldForm: {
        oldVersionId: undefined,
      },
      // 表单校验
      rules: {
        softVersion: [
          { required: true, message: "版本号不能为空", trigger: "blur" },
        ],
        packName: [
          {validator:checkPackName, trigger: "blur" },
        ],
        softName: [
          { required: true, message: "应用名称不能为空", trigger: "change" },
        ],
        softType: [
          { required: true, message: "应用类型不能为空", trigger: "change" },
        ],
        softPlat: [
          { required: true, message: "应用平台不能为空", trigger: "change" },
        ],
        packType: [
          { required: true, message: "打包方式不能为空", trigger: "change" },
        ],
        updateType: [
          {
            required: true,
            message: "版本更新类型不能为空",
            trigger: "change",
          },
        ],
      },
      loadId: undefined,
      reloadId: undefined,
      editLoadId: undefined,
      bdlbLoadId: undefined,
      delLoadId: undefined,
      detailLoadId: undefined,
      reloadType: undefined,
      saveLoading: false,
      saveBindLoading: false,
      tenantList: [],
      formDisabled: false,
      lookFormDisabled: false,
      versionDisabled: false,
      softTypeOptions: [],
      softPlatOptions: [],
      softNameOptions: [],
      packTypeOptions: [],
      updateTypeOptions: [],
      isBindOrtherSoftOptions: [],
      fileList: [],
      //多选的数据
      multipleSelection: [],
      memberMultipleSelection: [],
      currentMember: [],
      bindVersionIds: [],
    };
  },
  created() {
    this.getList();
    this.getDicts("packType").then((response) => {
      this.packTypeOptions = response.data;
    });
    this.getDicts("softName").then((response) => {
      this.softNameOptions = response.data;
    });
    this.getDicts("softType").then((response) => {
      this.softTypeOptions = response.data;
    });
    this.getDicts("softPlat").then((response) => {
      this.softPlatOptions = response.data;
    });
    this.getDicts("updateType").then((response) => {
      this.updateTypeOptions = response.data;
    });
    this.getDicts("isBindOrtherSoft").then((response) => {
      this.isBindOrtherSoftOptions = response.data;
    });
    this.getTenantList();
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true;
      listVersion(this.addDateRange(this.queryParams, this.dateRange)).then(
        (response) => {
          this.versionList = response.data.records;
          this.total = response.data.total;
          this.loading = false;
        }
      );
    },
    handleSelectionChange(val) {
      let versionIds = val.map(function (value, index, array) {
        return value.versionId;
      });
      this.multipleSelection = versionIds;
    },
    /** 查询参数列表 */
    getBindList() {
      this.loading = true;
      bindListVersion({ ...this.queryBindParams, ...this.oldForm }).then(
        (response) => {
          this.bindversionList = response.data.records;
          this.bindTotal = response.data.total;
          this.loading = false;
        }
      );
    },
    // 参数系统内置字典翻译
    packTypeFormat(row, column) {
      return this.selectDictLabel(this.packTypeOptions, row.packType);
    },
    softNameFormat(row, column) {
      return this.selectDictLabel(this.softNameOptions, row.softName);
    },
    softPlatFormat(row, column) {
      return this.selectDictLabel(this.softPlatOptions, row.softPlat);
    },
    softTypeFormat(row, column) {
      return this.selectDictLabel(this.softTypeOptions, row.softType);
    },
    updateTypeFormat(row, column) {
      return this.selectDictLabel(this.updateTypeOptions, row.updateType);
    },
    isBindOrtherSoftFormat(row, column) {
      return this.selectDictLabel(
        this.isBindOrtherSoftOptions,
        row.isBindOrtherSoft
      );
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.versionDisabled = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        tenantId: undefined,
        softVersion: undefined,
        softPlat: undefined,
        softType: undefined,
        softName: undefined,
        isBindOrtherSoft: 0,
        tenantName: undefined,
        fileUrl: undefined,
        expirationDate:undefined,
      };
      this.resetForm("form");
      this.$refs.softfileUpload && this.$refs.softfileUpload.clearFiles();
      this.fileList = [];
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.formDisabled = false;
      this.versionDisabled = false;
      this.reset();
      this.open = true;
      this.title = "版本上传";
      this.form.tenantName = this.queryParams.tenantName;
      this.form.tenantId = this.queryParams.tenantId;
    },
    //绑定操作-弹出绑定框
    bindOrtherSoft(form) {
      this.resetBindQuery();
      this.bindTitle = "绑定应用";
      this.oldForm.oldVersionId = form.versionId;
      this.form = form;
      if (form.bindVersionid != undefined) {
        this.arr = form.bindVersionid.split(",");
        bindListVersion({ ...this.queryBindParams, ...this.oldForm }).then(
          (response) => {
            this.bindversionList = response.data.records;
            this.bindTotal = response.data.total;
            //console.log("form.bindVersionid:"+form.bindVersionid);
            if (form.bindVersionid != undefined) {
              //处理是否需要默认选中
              if (this.bindversionList.length > 0) {
                //console.log("bindversionList111:"+this.bindversionList);
                this.$nextTick((_) => {
                  if (form.isBindOrtherSoft === 0) {
                    this.$refs.multipleTable.clearSelection();
                  }
                  this.bindversionList.forEach((i) => {
                    if (this.arr.indexOf(i.versionId) > -1) {
                      this.$refs.multipleTable.toggleRowSelection(i, true); // 设置默认选中
                    } else {
                      this.$refs.multipleTable.toggleRowSelection(i, false);
                    }
                  });
                });
              }
            }
          }
        );
      }
      this.openBind = true;
      this.bindLoading = false;
      this.lookFormDisabled = false;
    },
    updateBindStatus(row, flag) {
      const versionId = row.versionId;
      if (flag) {
        //绑定
        this.openBind = true;
        this.bindTitle = "绑定应用";
        this.bindLoading = false;
        this.lookFormDisabled = false;
        this.oldForm.oldVersionId = versionId;
        this.form = row;
        this.getBindList();
      } else {
        //解绑
        var name = this.selectDictLabel(this.softNameOptions, row.softName);
        var plat = this.selectDictLabel(this.softPlatOptions, row.softPlat);
        var type = this.selectDictLabel(this.softTypeOptions, row.softType);
        var version = row.softVersion;
        this.$confirm(
          '是否确认与应用"' +
            plat +
            "_" +
            type +
            "_" +
            name +
            "_" +
            version +
            '"解除绑定?',
          "警告",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(function () {
            return updateVersion({ ...row, isBindOrtherSoft: 0 });
          })
          .then((response) => {
            if (response.success == true && response.code == "1") {
              this.msgSuccess("解绑成功");
              this.getList();
            } else {
              this.msgError("解绑失败：" + response.data.msg);
            }
          });
      }
    },
    /** 绑定其他应用 */
    doBindOrtherSoft: function () {
      if (this.oldForm.oldVersionId != undefined) {
        //修改时的绑定
        let bindVersionIds = Array.from(
          new Set([...this.multipleSelection, ...this.currentMember])
        );
        this.form.bindVersionIds = bindVersionIds;
        this.form.isBindOrtherSoft = 1;
        //修改版本信息
        updateVersion(this.form).then((response) => {
          if (response.success == true && response.code == "1") {
            this.msgSuccess("绑定成功");
            this.openBind = false;
            this.saveBindLoading = false;
            this.getList();
          } else {
            if (response.data != undefined) {
              this.msgError("绑定失败：" + response.data.msg);
            } else {
              this.msgError("绑定失败：" + response.message);
            }
            this.openBind = true;
            this.bindTitle = "绑定应用";
            this.saveBindLoading = false;
          }
        });
      } else {
        //新增时的绑定
        console.log("新增时的绑定");
        let bindVersionIds = Array.from(
          new Set([...this.multipleSelection, ...this.currentMember])
        );
        this.form.bindVersionIds = bindVersionIds;
        this.openBind = false;
        this.saveBindLoading = false;
      }
    },
    /** 关闭绑定 */
    cancelBind: function () {
      this.openBind = false;
      this.resetForm("queryForm");
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.saveLoading = true;
          if (this.form.fileUrl == "" || this.form.fileUrl == undefined) {
            this.$message.error(`请先上传应用包附件或确认应用包上传完成！`);
            this.saveLoading = false;
          } else {
            if (this.form.versionId !== undefined) {
              //修改版本信息
              updateVersion(this.form).then((response) => {
                if (response.success == true && response.code == "1") {
                  this.msgSuccess("修改成功");
                  this.open = false;
                  this.getList();
                  this.saveLoading = false;
                  this.versionDisabled = false;
                } else {
                  if (response.data != undefined) {
                    this.msgError("修改失败：" + response.data.msg);
                  } else {
                    this.msgError("修改失败：" + response.message);
                  }
                  this.open = true;
                  this.saveLoading = false;
                }
              });
            } else {
              //新增版本数据
              addVersion({
                ...this.form,
              }).then((response) => {
                if (response.success == true && response.code == "1") {
                  this.msgSuccess("新增成功");
                  this.open = false;
                  this.getList();
                  this.saveLoading = false;
                } else {
                  this.msgError("新增失败：" + response.data.msg);
                  this.open = true;
                  this.saveLoading = false;
                }
              });
            }
          }
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.formDisabled = false;
      this.reset();
      const versionId = row.versionId;
      this.editLoadId = versionId;
      selectVersionByVersionId({ versionId }).then((response) => {
        this.versionDisabled = true;
        this.editLoadId = undefined;
        if (response.data) {
          this.form = response.data;
          this.open = true;
          this.title = "修改版本信息";
          this.form.fileUrl &&
            this.fileList.push({
              name: this.form.originalName,
              url: this.form.fileUrl,
            });
        } else {
          this.$message.error("数据异常！");
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const versionId = row.versionId;
      this.delLoadId = versionId;
      var name = this.selectDictLabel(this.softNameOptions, row.softName);
      var plat = this.selectDictLabel(this.softPlatOptions, row.softPlat);
      var type = this.selectDictLabel(this.softTypeOptions, row.softType);
      var version = row.softVersion;
      this.$confirm(
        '是否确认删除应用为"' +
          plat +
          "_" +
          type +
          "_" +
          name +
          "_" +
          version +
          '"的版本数据吗',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delVersion({ versionId });
        })
          .then(() => {
            this.delLoadId = undefined;
            this.getList();
            this.msgSuccess("删除成功");
          })
        .catch(() => {
          this.delLoadId = undefined;
        });
    },
    /** 绑定列表按钮 操作 */
    lookBindList(row) {
      this.openBind = true;
      this.bindLoading = false;
      this.lookFormDisabled = true;
      this.bindList = [];
      var arr = row.bindVersionid.split(",");
      arr.forEach((elem, index) => {
        //console.log(elem, index);
        var versionId = elem;
        selectVersionByVersionId({ versionId }).then((response) => {
          if (response.data) {
            this.bindList.push(response.data);
          } else {
            this.$message.error("数据异常！");
          }
        });
      });
      this.bindversionList = this.bindList;
      this.bindTotal = this.bindList.length;
      this.bindTitle = "已绑定列表";
    },
    /** 查看按钮操作 */
    detail(row) {
      this.reset();
      const versionId = row.versionId;
      this.detailLoadId = versionId;
      selectVersionByVersionId({ versionId }).then((response) => {
        this.formDisabled = true;
        this.detailLoadId = undefined;
        if (response.data) {
          this.form = response.data;
          this.open = true;
          this.title = "查看版本信息";
          this.form.fileUrl &&
            this.fileList.push({
              name: this.form.originalName,
              url: this.form.fileUrl,
            });
        } else {
          this.$message.error("数据异常！");
        }
      });
    },
    /** 搜索按钮操作 */
    handleBindQuery() {
      this.queryBindParams.pageNum = 1;
      bindListVersion({ ...this.queryBindParams, ...this.oldForm }).then(
        (response) => {
          this.bindversionList = response.data.records;
          this.bindTotal = response.data.total;
          //console.log("form.bindVersionid:"+form.bindVersionid);
          if (this.form.bindVersionid != undefined) {
            //处理是否需要默认选中
            if (this.bindversionList.length > 0) {
              //console.log("bindversionList111:"+this.bindversionList);
              this.$nextTick((_) => {
                if (this.form.isBindOrtherSoft === 0) {
                  this.$refs.multipleTable.clearSelection();
                }
                this.bindversionList.forEach((i) => {
                  if (this.arr.indexOf(i.versionId) > -1) {
                    this.$refs.multipleTable.toggleRowSelection(i, true); // 设置默认选中
                  } else {
                    this.$refs.multipleTable.toggleRowSelection(i, false);
                  }
                });
              });
            }
          }
        }
      );
    },
    /** 重置按钮操作 */
    resetBindQuery() {
      this.resetForm("queryToBindForm");
      this.handleBindQuery();
    },
    getTenantList(tenantName) {
      this.getTenantLoading = true;
      let query = {}
      if (tenantName !== undefined && tenantName !== '') {
        query.tenantName = tenantName
        query.tenantId = undefined
      } else {
        query.tenantId = this.queryParams.tenantId
      }
      tenantList(query).then((response) => {
        this.tenantList = response.data;
      }).finally(() => {
        this.getTenantLoading = false;
      });
    },
    tenantChange(tenantId) {
      if (tenantId !== "") {
        this.queryParams.tenantName = this.tenantList.find(
          (item) => item.tenantId === tenantId
        ).tenantName;
      }
      this.handleQuery();
    },
    doCertValid(content){
      let formData = new FormData();
      formData.append("multipartFile", content.file);
      formData.append("appCode", this.appCode);
      validCert(formData).then((res) => {
        if (res.code == "1" && res.data.isValid == true ) {
          this.form.expirationDate = res.data.expirationDate;
          this.form.startDate = res.data.startDate;
          this.form.validDay = res.data.validDay;
          this.$confirm(
              "是否确认上传文件名为 " +res.data.originalName +
              "【证书有效期为"+
                res.data.startDate +
                " 至 " +
                res.data.expirationDate +
                " 有效期剩余 " +
                res.data.validDay +
                " 天】的证书吗",
              "警告",
              {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
              }
            )
            .then(function () {
                return uploadSingle(formData);
              }).then((res) => {
                if (res.code == "1") {
                  this.msgSuccess("应用包上传成功");
                  this.form.fileUrl = res.data.url;
                  this.form.originalName = res.data.originalName;
                  this.form.fileSize = res.data.fileSize;
                  this.fileList.push({
                    name: this.form.originalName,
                    url: this.form.fileUrl,
                  });
                } else {
                  if (res.success != undefined) {
                    alert("上传失败，请重新操作：" + res.message);
                  } else {
                    alert("上传失败，请重新操作：" + res.msg);
                  }
                  this.$refs.softfileUpload.clearFiles();
                }
              })
            .catch(() => {
                this.$refs.softfileUpload.clearFiles();
              });
        } else {
          if(res.data.isValid == false){
            alert("证书 "+res.data.originalName +"校验已过期，请上传可用证书文件！");
          }
          if (res.success != undefined) {
            alert("校验证书失败，请重新操作：" + res.message);
          } else {
            alert("校验证书失败，请重新操作：" + res.msg);
          }
          this.$refs.softfileUpload.clearFiles();
        }
      });
    },
    doUploadFile(content) {
      let formData = new FormData();
      //content.file 	文件file对象
      formData.append("multipartFile", content.file);
      formData.append("appCode", this.appCode);
      uploadSingle(formData).then((res) => {
        if (res.code == "1") {
          this.msgSuccess("应用包上传成功");
          this.form.fileUrl = res.data.url;
          this.form.originalName = res.data.originalName;
          this.form.fileSize = res.data.fileSize;
          this.fileList.push({
            name: this.form.originalName,
            url: this.form.fileUrl,
          });
        } else {
          if (res.success != undefined) {
            alert("上传失败，请重新操作：" + res.message);
          } else {
            alert("上传失败，请重新操作：" + res.msg);
          }
          this.$refs.softfileUpload.clearFiles();
        }
      });
    },
    //文件上传相关
    uploadFile(content) {
      if (content.file != undefined) {
        if (content.file.size > 104857600) {
          this.$message.error(`上传文件过大，不能超过100M !!`);
          this.$refs.softfileUpload.clearFiles();
        } else {
          let fileName = content.file.name.lastIndexOf("."); //取到文件名开始到最后一个点的长度
          let fileNameLength = content.file.name.length; //取到文件名长度
          let fileFormat = content.file.name.substring(
            fileName + 1,
            fileNameLength
          );
          //console.log(fileFormat);
          if (
            fileFormat !== "zip" &&
            fileFormat !== "rar" &&
            fileFormat !== "apk" &&
            fileFormat !== "ipa" &&
            fileFormat !== "hpk" &&
            fileFormat !== "wgt" &&
            fileFormat !== "key" &&
            fileFormat !== "csr" &&
            fileFormat !== "crt" &&
            fileFormat !== "pem" &&
            fileFormat !== "pfx" &&
            fileFormat !== "jks" &&
            fileFormat !== "cer"
          ) {
            this.$message.error(
              "文件必须为.zip .rar .apk .ipa .hpk .wgt .key .csr .crt .pem .pfx .jks .cer类型 !!!"
            );
            this.$refs.softfileUpload.clearFiles();
          } else {
            //证书类型 取证书的到期时间
            if(this.form.softType =="certificate" && (fileFormat !== "zip" &&
            fileFormat !== "rar" &&
            fileFormat !== "apk" &&
            fileFormat !== "ipa" &&
            fileFormat !== "hpk" &&
            fileFormat !== "wgt")) {
              this.doCertValid(content);
            }else{
              this.doUploadFile(content);
            }
          }
        }
      }
    },
    handleRemove(file, fileList) {
      this.form.fileUrl = "";
      this.fileList = [];
    },
    //点击文件下载文件
    handlePreview(file) {
      window.open(this.form.fileUrl, "_blank");
    },
    handleExceed(files, fileList) {
      this.$message.warning(`只能上传一个文件,请先移除前一个`);
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    handleSueccss(response, file, fileList) {
      console.log("res:" + response);
    },
  },
};
</script>
